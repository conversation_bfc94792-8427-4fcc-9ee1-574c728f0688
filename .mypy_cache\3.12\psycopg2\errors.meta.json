{"data_mtime": 1759503829, "dep_lines": [1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 20, 20, 30, 30], "dependencies": ["psycopg2._psycopg", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "typing"], "hash": "ba1707637302c852635fb7e0ee3c64340d889fcfb6f00765e8d3f10fa91d49bd", "id": "psycopg2.errors", "ignore_all": true, "interface_hash": "a5c9039295ca39fb7a4516bca32e67a9439511f89f57ce74c31b61e31e5243ba", "mtime": 1757092195, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\psycopg2-stubs\\errors.pyi", "plugin_data": null, "size": 12895, "suppressed": [], "version_id": "1.8.0"}