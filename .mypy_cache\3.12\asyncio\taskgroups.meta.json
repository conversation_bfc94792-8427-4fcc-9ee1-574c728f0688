{"data_mtime": 1759503833, "dep_lines": [8, 9, 1, 2, 3, 4, 5, 7, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 5, 5, 5, 20, 20, 30, 30, 30], "dependencies": ["asyncio.events", "asyncio.tasks", "sys", "<PERSON><PERSON><PERSON>", "types", "typing", "typing_extensions", "asyncio", "builtins", "pyexpat.model", "pyexpat.errors", "_typeshed", "abc", "asyncio.futures"], "hash": "9cf19255233f6332b837b99b25369427e3436f7c6bf207e42b5f66fc8a1fa52d", "id": "asyncio.taskgroups", "ignore_all": true, "interface_hash": "66ee27b0f663fea9227e87241d5cf62a379f3b54e55c015d0e4c3d204eccb4d2", "mtime": 1757092231, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\asyncio\\taskgroups.pyi", "plugin_data": null, "size": 794, "suppressed": [], "version_id": "1.8.0"}