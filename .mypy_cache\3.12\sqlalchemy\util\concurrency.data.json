{".class": "MypyFile", "_fullname": "sqlalchemy.util.concurrency", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AsyncAdaptedLock": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._concurrency_py3k.AsyncAdaptedLock", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Coroutine": {".class": "SymbolTableNode", "cross_ref": "typing.Coroutine", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "_AsyncUtil": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.util.concurrency._AsyncUtil", "name": "_AsyncUtil", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.util.concurrency._AsyncUtil", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.util.concurrency", "mro": ["sqlalchemy.util.concurrency._AsyncUtil", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.util.concurrency._AsyncUtil.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.util.concurrency._AsyncUtil"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _AsyncUtil", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.util.concurrency._AsyncUtil.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.util.concurrency._AsyncUtil"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of _AsyncUtil", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "fn", "args", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.util.concurrency._AsyncUtil.run", "name": "run", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "fn", "args", "kwargs"], "arg_types": ["sqlalchemy.util.concurrency._AsyncUtil", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.util.concurrency._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run of _AsyncUtil", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.util.concurrency._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.util.concurrency._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "run_in_greenlet": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "fn", "args", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.util.concurrency._AsyncUtil.run_in_greenlet", "name": "run_in_greenlet", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "fn", "args", "kwargs"], "arg_types": ["sqlalchemy.util.concurrency._AsyncUtil", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.util.concurrency._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_in_greenlet of _AsyncUtil", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.util.concurrency._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.util.concurrency._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "runner": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.util.concurrency._AsyncUtil.runner", "name": "runner", "type": "sqlalchemy.util._concurrency_py3k._Runner"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.util.concurrency._AsyncUtil.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.util.concurrency._AsyncUtil", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_Runner": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._concurrency_py3k._Runner", "kind": "Gdef"}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.util.concurrency._T", "name": "_T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.util.concurrency.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.util.concurrency.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.util.concurrency.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.util.concurrency.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.util.concurrency.__package__", "name": "__package__", "type": "builtins.str"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef"}, "await_fallback": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._concurrency_py3k.await_fallback", "kind": "Gdef"}, "await_only": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._concurrency_py3k.await_only", "kind": "Gdef"}, "e": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.util.concurrency.e", "name": "e", "type": {".class": "DeletedType", "source": "e"}}}, "greenlet": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "sqlalchemy.util.concurrency.greenlet", "name": "greenlet", "type": {".class": "AnyType", "missing_import_name": "sqlalchemy.util.concurrency.greenlet", "source_any": null, "type_of_any": 3}}}, "greenlet_error": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.util.concurrency.greenlet_error", "name": "greenlet_error", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "greenlet_spawn": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._concurrency_py3k.greenlet_spawn", "kind": "Gdef"}, "have_greenlet": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.util.concurrency.have_greenlet", "name": "have_greenlet", "type": "builtins.bool"}}, "in_greenlet": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._concurrency_py3k.in_greenlet", "kind": "Gdef"}, "is_exit_exception": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._concurrency_py3k.is_exit_exception", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\concurrency.py"}