{"data_mtime": 1759503836, "dep_lines": [9, 16, 3, 6, 8, 9, 15, 1, 4, 6, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 25, 5, 10, 5, 20, 25, 5, 5, 5, 5, 20, 20, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._core_utils", "pydantic._internal._core_metadata", "collections.abc", "pydantic_core.core_schema", "pydantic.errors", "pydantic._internal", "pydantic.types", "__future__", "typing", "pydantic_core", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "_typeshed", "abc", "types", "typing_extensions"], "hash": "68c9744914b24321df5b8fa49673131cdbf0491a2a1371f73d157fd39bd1b138", "id": "pydantic._internal._discriminated_union", "ignore_all": true, "interface_hash": "63976bf0af90f4ca3271bc8092ca211522dfa2da07fd93e750e6f75d47819ca4", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\_internal\\_discriminated_union.py", "plugin_data": null, "size": 25478, "suppressed": [], "version_id": "1.8.0"}