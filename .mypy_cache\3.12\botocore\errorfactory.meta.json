{"data_mtime": 1759504549, "dep_lines": [7, 9, 11, 12, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 20, 20, 30, 30], "dependencies": ["collections.abc", "botocore.exceptions", "botocore.model", "botocore.utils", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "typing"], "hash": "68fe872bf9ad5f21f7814d7443b541611b2611540599675e803b86fa2b8c9c47", "id": "botocore.errorfactory", "ignore_all": true, "interface_hash": "e44f55c4fc744cbac154c6d643ea7f2c4f56b8b91cccc8a16e29e5e59441e817", "mtime": 1757092235, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\botocore-stubs\\errorfactory.pyi", "plugin_data": null, "size": 816, "suppressed": [], "version_id": "1.8.0"}