{"data_mtime": 1759504512, "dep_lines": [12, 1, 3, 4, 5, 6, 7, 8, 10, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 10, 5, 5, 5, 5, 5, 20, 20, 30, 30, 30], "dependencies": ["pydantic.warnings", "__future__", "json", "pickle", "warnings", "enum", "pathlib", "typing", "typing_extensions", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "json.decoder", "os"], "hash": "1b377a6ff83ccc95dcb84ed046ae5a761c7f10c25a8577dca57085d1182baaa2", "id": "pydantic.deprecated.parse", "ignore_all": true, "interface_hash": "8bb99fa5d471077add34d3a4ed11fdd2560ac6c320bc91ede5767bd1e845cbb9", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\deprecated\\parse.py", "plugin_data": null, "size": 2511, "suppressed": [], "version_id": "1.8.0"}