{"data_mtime": 1759504512, "dep_lines": [6, 3, 4, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 20, 20, 30], "dependencies": ["smartanalytics_domain.ports.repositories", "types", "abc", "builtins", "pyexpat.model", "pyexpat.errors", "typing"], "hash": "7b858d40f357ffad8ddbfd57d18f7af9efa11170bed047e16cb04b6ef3b924c2", "id": "smartanalytics_domain.ports.unit_of_work", "ignore_all": false, "interface_hash": "f94aa601d87fcc3d88a2331c9f220ff9d23a3ac0899955e66a25f9819417d6e6", "mtime": 1759421752, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "layers\\domain\\python\\smartanalytics_domain\\ports\\unit_of_work.py", "plugin_data": null, "size": 1227, "suppressed": [], "version_id": "1.8.0"}