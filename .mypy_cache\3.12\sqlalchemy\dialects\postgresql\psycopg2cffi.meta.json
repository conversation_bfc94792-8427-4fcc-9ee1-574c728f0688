{"data_mtime": 1759504551, "dep_lines": [25, 26, 26, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.dialects.postgresql.psycopg2", "sqlalchemy.util", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "sqlalchemy.dialects.postgresql._psycopg_common", "sqlalchemy.dialects.postgresql.base", "sqlalchemy.engine", "sqlalchemy.engine.default", "sqlalchemy.engine.interfaces", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.util.langhelpers", "typing"], "hash": "fabd5ec61045bca5a7cf1a9af649b971703096c129a49885ff6b7657e6ccffa5", "id": "sqlalchemy.dialects.postgresql.psycopg2cffi", "ignore_all": true, "interface_hash": "34961c0dfd98fb629939ba58913edb7d8e8ff1e7a897a0b01ba88dffba47eddd", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg2cffi.py", "plugin_data": null, "size": 1817, "suppressed": [], "version_id": "1.8.0"}