{"data_mtime": 1759503869, "dep_lines": [8, 11, 7, 9, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 20, 20, 30], "dependencies": ["collections.abc", "botocore.session", "logging", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "abc"], "hash": "5f0606af8eec4869686d20e0b79bd89a39c36751998cf48f00e1501d394f506a", "id": "botocore", "ignore_all": true, "interface_hash": "9ac5fdda997bd1f971da0b0ef6862d6bc04e5950ac1a72334d1f9f103c352510", "mtime": 1757092235, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\botocore-stubs\\__init__.pyi", "plugin_data": null, "size": 684, "suppressed": [], "version_id": "1.8.0"}