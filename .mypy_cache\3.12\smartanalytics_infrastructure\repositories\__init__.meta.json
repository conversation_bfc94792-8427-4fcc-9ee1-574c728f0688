{"data_mtime": 1759504553, "dep_lines": [7, 10, 13, 16, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 20, 20, 30, 30], "dependencies": ["smartanalytics_infrastructure.repositories.agent_event_repository", "smartanalytics_infrastructure.repositories.agent_repository", "smartanalytics_infrastructure.repositories.ring_group_repository", "smartanalytics_infrastructure.repositories.tenant_repository", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "typing"], "hash": "df1bd1df6881b9bf6e9a8f3e95ef7889c1baa3dd028150ad760e9956479b3792", "id": "smartanalytics_infrastructure.repositories", "ignore_all": false, "interface_hash": "4777f3173d18600d80551a634125c87fcbb5cf38eeaf8be34af183ffe5f56ae6", "mtime": 1759501803, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "layers\\infrastructure\\python\\smartanalytics_infrastructure\\repositories\\__init__.py", "plugin_data": null, "size": 798, "suppressed": [], "version_id": "1.8.0"}