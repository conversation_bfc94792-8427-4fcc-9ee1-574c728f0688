{"data_mtime": 1759503829, "dep_lines": [6, 30, 1, 3, 4, 33, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 5, 20, 20, 30, 30], "dependencies": ["pydantic_core._pydantic_core", "pydantic_core.core_schema", "__future__", "sys", "typing", "typing_extensions", "builtins", "pyexpat.model", "pyexpat.errors", "_typeshed", "abc"], "hash": "97fba68b21328d4af0816785bad91cff4980d2f1d539436106811599d0801f57", "id": "pydantic_core", "ignore_all": true, "interface_hash": "eead91f58ea6b5d0cb678d9c8e6402a66f5f7e333ed88bba21d35ef68f60bf6e", "mtime": 1757091872, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic_core\\__init__.py", "plugin_data": null, "size": 4547, "suppressed": [], "version_id": "1.8.0"}