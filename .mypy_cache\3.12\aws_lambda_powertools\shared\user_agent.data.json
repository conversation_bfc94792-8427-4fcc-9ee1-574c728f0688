{".class": "MypyFile", "_fullname": "aws_lambda_powertools.shared.user_agent", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "DEFAULT_FEATURE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "aws_lambda_powertools.shared.user_agent.DEFAULT_FEATURE", "name": "DEFAULT_FEATURE", "type": "builtins.str"}}, "EXEC_ENV": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "aws_lambda_powertools.shared.user_agent.EXEC_ENV", "name": "EXEC_ENV", "type": "builtins.str"}}, "FEATURE_PREFIX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "aws_lambda_powertools.shared.user_agent.FEATURE_PREFIX", "name": "FEATURE_PREFIX", "type": "builtins.str"}}, "HEADER_NO_OP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "aws_lambda_powertools.shared.user_agent.HEADER_NO_OP", "name": "HEADER_NO_OP", "type": "builtins.str"}}, "TARGET_SDK_EVENT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "aws_lambda_powertools.shared.user_agent.TARGET_SDK_EVENT", "name": "TARGET_SDK_EVENT", "type": "builtins.str"}}, "VERSION": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.shared.version.VERSION", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aws_lambda_powertools.shared.user_agent.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aws_lambda_powertools.shared.user_agent.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aws_lambda_powertools.shared.user_agent.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aws_lambda_powertools.shared.user_agent.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aws_lambda_powertools.shared.user_agent.__package__", "name": "__package__", "type": "builtins.str"}}, "_create_feature_function": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["feature"], "dataclass_transform_spec": null, "flags": [], "fullname": "aws_lambda_powertools.shared.user_agent._create_feature_function", "name": "_create_feature_function", "type": null}}, "_initializer_botocore_session": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["session"], "dataclass_transform_spec": null, "flags": [], "fullname": "aws_lambda_powertools.shared.user_agent._initializer_botocore_session", "name": "_initializer_botocore_session", "type": null}}, "botocore": {".class": "SymbolTableNode", "cross_ref": "botocore", "kind": "Gdef"}, "inject_header": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "aws_lambda_powertools.shared.user_agent.inject_header", "name": "inject_header", "type": "builtins.bool"}}, "inject_user_agent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "flags": [], "fullname": "aws_lambda_powertools.shared.user_agent.inject_user_agent", "name": "inject_user_agent", "type": null}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "aws_lambda_powertools.shared.user_agent.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "powertools_version": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "aws_lambda_powertools.shared.user_agent.powertools_version", "name": "powertools_version", "type": "builtins.str"}}, "register_feature_to_botocore_session": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["botocore_session", "feature"], "dataclass_transform_spec": null, "flags": [], "fullname": "aws_lambda_powertools.shared.user_agent.register_feature_to_botocore_session", "name": "register_feature_to_botocore_session", "type": null}}, "register_feature_to_client": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["client", "feature"], "dataclass_transform_spec": null, "flags": [], "fullname": "aws_lambda_powertools.shared.user_agent.register_feature_to_client", "name": "register_feature_to_client", "type": null}}, "register_feature_to_resource": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["resource", "feature"], "dataclass_transform_spec": null, "flags": [], "fullname": "aws_lambda_powertools.shared.user_agent.register_feature_to_resource", "name": "register_feature_to_resource", "type": null}}, "register_feature_to_session": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["session", "feature"], "dataclass_transform_spec": null, "flags": [], "fullname": "aws_lambda_powertools.shared.user_agent.register_feature_to_session", "name": "register_feature_to_session", "type": null}}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\aws_lambda_powertools\\shared\\user_agent.py"}