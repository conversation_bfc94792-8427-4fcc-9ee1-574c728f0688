{"data_mtime": 1759504547, "dep_lines": [86, 12, 13, 47, 48, 60, 82, 223, 8, 10, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 30], "dependencies": ["sqlalchemy.sql.expression", "sqlalchemy.util", "sqlalchemy.engine", "sqlalchemy.inspection", "sqlalchemy.pool", "sqlalchemy.schema", "sqlalchemy.sql", "sqlalchemy.types", "__future__", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "abc"], "hash": "064789c5ff20a8ac7ca6e085c1b3bbdfb2192accf3e5a1e67e6195fa2c219a2b", "id": "sqlalchemy", "ignore_all": true, "interface_hash": "003fa5eec5c0b07c3b8fae4a7ce1fc219f1dfc85e52263d1ec567d1561f732fa", "mtime": 1759106563, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\__init__.py", "plugin_data": null, "size": 12942, "suppressed": [], "version_id": "1.8.0"}