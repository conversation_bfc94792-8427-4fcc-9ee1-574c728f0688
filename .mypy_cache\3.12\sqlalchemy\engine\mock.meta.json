{"data_mtime": 1759503866, "dep_lines": [19, 24, 25, 29, 30, 31, 32, 19, 20, 8, 10, 11, 20, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 25, 25, 25, 25, 25, 25, 20, 10, 5, 5, 5, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.engine.url", "sqlalchemy.engine.base", "sqlalchemy.engine.interfaces", "sqlalchemy.sql.base", "sqlalchemy.sql.ddl", "sqlalchemy.sql.schema", "sqlalchemy.sql.visitors", "sqlalchemy.engine", "sqlalchemy.util", "__future__", "operator", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "_operator", "abc", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.sql", "sqlalchemy.sql.compiler", "sqlalchemy.sql.roles", "sqlalchemy.util.langhelpers"], "hash": "fcf8b18f7919300da64e1b609e5c54f832ed0ab3de97b7cd17563ec0966f4cd2", "id": "sqlalchemy.engine.mock", "ignore_all": true, "interface_hash": "a27028271f3dd9762734563776a86dcda07a4fb48db738c557b8899ef13c0032", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\mock.py", "plugin_data": null, "size": 4290, "suppressed": [], "version_id": "1.8.0"}