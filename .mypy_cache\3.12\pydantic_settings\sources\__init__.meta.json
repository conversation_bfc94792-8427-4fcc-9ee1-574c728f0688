{"data_mtime": **********, "dep_lines": [11, 12, 13, 24, 25, 26, 27, 28, 29, 30, 31, 3, 32, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 30, 30], "dependencies": ["pydantic_settings.sources.providers.aws", "pydantic_settings.sources.providers.azure", "pydantic_settings.sources.providers.cli", "pydantic_settings.sources.providers.dotenv", "pydantic_settings.sources.providers.env", "pydantic_settings.sources.providers.gcp", "pydantic_settings.sources.providers.json", "pydantic_settings.sources.providers.pyproject", "pydantic_settings.sources.providers.secrets", "pydantic_settings.sources.providers.toml", "pydantic_settings.sources.providers.yaml", "pydantic_settings.sources.base", "pydantic_settings.sources.types", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "typing"], "hash": "4e2d5b4596f4afb23190efb02562b2faa1297815052984696b7035010a1d3b29", "id": "pydantic_settings.sources", "ignore_all": true, "interface_hash": "f098390c7ccd898f18eb09506fc5a347946a42b750fa41c1696f437684032083", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic_settings\\sources\\__init__.py", "plugin_data": null, "size": 2052, "suppressed": [], "version_id": "1.8.0"}