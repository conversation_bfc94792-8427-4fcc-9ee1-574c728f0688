{"data_mtime": 1759504512, "dep_lines": [3, 8, 8, 8, 8, 9, 11, 11, 11, 11, 11, 11, 12, 14, 1, 2, 4, 5, 6, 8, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 10, 5, 10, 5, 10, 10, 10, 10, 5, 5, 10, 5, 5, 5, 5, 20, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "urllib3.exceptions", "urllib3.fields", "urllib3.filepost", "urllib3.util", "urllib3.response", "requests.auth", "requests.cookies", "requests.exceptions", "requests.hooks", "requests.status_codes", "requests.utils", "requests.adapters", "requests.structures", "datetime", "_typeshed", "json", "typing", "typing_extensions", "urllib3", "requests", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "http", "http.cookiejar", "io", "json.decoder", "urllib3.connectionpool", "urllib3.util.url"], "hash": "770816764b334b6629953e0dd8d0b1e0af0cd2fb8ffa6c114069757da17cd952", "id": "requests.models", "ignore_all": true, "interface_hash": "78c5866dc2dcd447f3d48db6651ff10477dfe82b8772de9a64573cdf9d0e3362", "mtime": 1757363991, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\requests-stubs\\models.pyi", "plugin_data": null, "size": 5616, "suppressed": [], "version_id": "1.8.0"}