{"data_mtime": 1759504553, "dep_lines": [3, 6, 9, 12, 15, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 20, 20, 30, 30], "dependencies": ["smartanalytics_domain.command_handlers.get_or_create_agent_handler", "smartanalytics_domain.command_handlers.get_or_create_ring_group_handler", "smartanalytics_domain.command_handlers.get_or_create_tenant_handler", "smartanalytics_domain.command_handlers.process_agent_event_handler", "smartanalytics_domain.command_handlers.process_agent_events_batch_handler", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "typing"], "hash": "563fa0d1bd454ae044e8797adc2b06892dae631a30449f4af5c003124a2f76c4", "id": "smartanalytics_domain.command_handlers", "ignore_all": false, "interface_hash": "86143d83200d5c04da8fc088812e9473e3fb3229e409b0db301d4e27521fd449", "mtime": 1759501803, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "layers\\domain\\python\\smartanalytics_domain\\command_handlers\\__init__.py", "plugin_data": null, "size": 891, "suppressed": [], "version_id": "1.8.0"}