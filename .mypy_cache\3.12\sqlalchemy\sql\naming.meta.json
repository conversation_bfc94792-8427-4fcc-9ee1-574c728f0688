{"data_mtime": 1759504547, "dep_lines": [15, 16, 17, 18, 15, 26, 27, 11, 13, 26, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 20, 10, 10, 5, 10, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.events", "sqlalchemy.sql.base", "sqlalchemy.sql.elements", "sqlalchemy.sql.schema", "sqlalchemy.sql", "sqlalchemy.event", "sqlalchemy.exc", "__future__", "re", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "enum", "sqlalchemy.event.api", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util", "sqlalchemy.util.langhelpers", "types", "typing"], "hash": "c4baa443bf43a44236050f829c6542b63c4cae574d97e7d99b850755a51311bc", "id": "sqlalchemy.sql.naming", "ignore_all": true, "interface_hash": "aa678f0da199ccaaa696ee289e876deb219b3ea6f4b5fa8a15a1008e525baeee", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\naming.py", "plugin_data": null, "size": 7064, "suppressed": [], "version_id": "1.8.0"}