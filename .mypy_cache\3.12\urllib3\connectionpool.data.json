{".class": "MypyFile", "_fullname": "urllib3.connectionpool", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BaseHTTPConnection": {".class": "SymbolTableNode", "cross_ref": "urllib3._base_connection.BaseHTTPConnection", "kind": "Gdef"}, "BaseHTTPResponse": {".class": "SymbolTableNode", "cross_ref": "urllib3.response.BaseHTTPResponse", "kind": "Gdef"}, "BaseHTTPSConnection": {".class": "SymbolTableNode", "cross_ref": "urllib3._base_connection.BaseHTTPSConnection", "kind": "Gdef"}, "BaseSSLError": {".class": "SymbolTableNode", "cross_ref": "urllib3.connection.BaseSSLError", "kind": "Gdef"}, "BrokenPipeError": {".class": "SymbolTableNode", "cross_ref": "builtins.BrokenPipeError", "kind": "Gdef"}, "CertificateError": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.ssl_match_hostname.CertificateError", "kind": "Gdef"}, "ClosedPoolError": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.ClosedPoolError", "kind": "Gdef"}, "ConnectionPool": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.connectionpool.ConnectionPool", "name": "ConnectionPool", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "urllib3.connectionpool.ConnectionPool", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.connectionpool", "mro": ["urllib3.connectionpool.ConnectionPool", "builtins.object"], "names": {".class": "SymbolTable", "QueueCls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "urllib3.connectionpool.ConnectionPool.QueueCls", "name": "QueueCls", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["maxsize"], "arg_types": ["builtins.int"], "bound_args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "queue._T", "id": 1, "name": "_T", "namespace": "queue.LifoQueue", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "queue.LifoQueue"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "queue._T", "id": 1, "name": "_T", "namespace": "queue.LifoQueue", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "queue.LifoQueue"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "queue._T", "id": 1, "name": "_T", "namespace": "queue.LifoQueue", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.connectionpool.ConnectionPool.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.connectionpool.ConnectionPool.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.connectionpool.ConnectionPool", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__enter__ of ConnectionPool", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.connectionpool.ConnectionPool.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.connectionpool.ConnectionPool", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.connectionpool.ConnectionPool.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.connectionpool.ConnectionPool", "values": [], "variance": 0}]}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.connectionpool.ConnectionPool.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["urllib3.connectionpool.ConnectionPool", {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__exit__ of ConnectionPool", "ret_type": {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "host", "port"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.connectionpool.ConnectionPool.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "host", "port"], "arg_types": ["urllib3.connectionpool.ConnectionPool", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ConnectionPool", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.connectionpool.ConnectionPool.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["urllib3.connectionpool.ConnectionPool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of ConnectionPool", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_tunnel_host": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.connectionpool.ConnectionPool._tunnel_host", "name": "_tunnel_host", "type": "builtins.str"}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.connectionpool.ConnectionPool.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.connectionpool.ConnectionPool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of ConnectionPool", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "host": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.connectionpool.ConnectionPool.host", "name": "host", "type": "builtins.str"}}, "port": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.connectionpool.ConnectionPool.port", "name": "port", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}}}, "scheme": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "urllib3.connectionpool.ConnectionPool.scheme", "name": "scheme", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.connectionpool.ConnectionPool.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.connectionpool.ConnectionPool", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DummyConnection": {".class": "SymbolTableNode", "cross_ref": "urllib3.connection.DummyConnection", "kind": "Gdef"}, "EmptyPoolError": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.EmptyPoolError", "kind": "Gdef"}, "FullPoolError": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.FullPoolError", "kind": "Gdef"}, "HTTPConnection": {".class": "SymbolTableNode", "cross_ref": "urllib3.connection.HTTPConnection", "kind": "Gdef"}, "HTTPConnectionPool": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib3.connectionpool.ConnectionPool", "urllib3._request_methods.RequestMethods"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.connectionpool.HTTPConnectionPool", "name": "HTTPConnectionPool", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "urllib3.connectionpool.HTTPConnectionPool", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.connectionpool", "mro": ["urllib3.connectionpool.HTTPConnectionPool", "urllib3.connectionpool.ConnectionPool", "urllib3._request_methods.RequestMethods", "builtins.object"], "names": {".class": "SymbolTable", "ConnectionCls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "urllib3.connectionpool.HTTPConnectionPool.ConnectionCls", "name": "ConnectionCls", "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": "urllib3._base_connection.BaseHTTPConnection"}, {".class": "TypeType", "item": "urllib3._base_connection.BaseHTTPSConnection"}]}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "host", "port", "timeout", "maxsize", "block", "headers", "retries", "_proxy", "_proxy_headers", "_proxy_config", "conn_kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.connectionpool.HTTPConnectionPool.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "host", "port", "timeout", "maxsize", "block", "headers", "retries", "_proxy", "_proxy_headers", "_proxy_config", "conn_kw"], "arg_types": ["urllib3.connectionpool.HTTPConnectionPool", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.connectionpool._TYPE_TIMEOUT"}, {".class": "NoneType"}]}, "builtins.int", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.retry.Retry", "builtins.bool", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HTTPConnectionPool", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_get_conn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "timeout"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.connectionpool.HTTPConnectionPool._get_conn", "name": "_get_conn", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "timeout"], "arg_types": ["urllib3.connectionpool.HTTPConnectionPool", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_conn of HTTPConnectionPool", "ret_type": "urllib3._base_connection.BaseHTTPConnection", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_get_timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "timeout"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.connectionpool.HTTPConnectionPool._get_timeout", "name": "_get_timeout", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "timeout"], "arg_types": ["urllib3.connectionpool.HTTPConnectionPool", {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.connectionpool._TYPE_TIMEOUT"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_timeout of HTTPConnectionPool", "ret_type": "urllib3.util.timeout.Timeout", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_make_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "conn", "method", "url", "body", "headers", "retries", "timeout", "chunked", "response_conn", "preload_content", "decode_content", "enforce_content_length"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.connectionpool.HTTPConnectionPool._make_request", "name": "_make_request", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "conn", "method", "url", "body", "headers", "retries", "timeout", "chunked", "response_conn", "preload_content", "decode_content", "enforce_content_length"], "arg_types": ["urllib3.connectionpool.HTTPConnectionPool", "urllib3._base_connection.BaseHTTPConnection", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection._TYPE_BODY"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.retry.Retry", {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.connectionpool._TYPE_TIMEOUT"}, "builtins.bool", {".class": "UnionType", "items": ["urllib3._base_connection.BaseHTTPConnection", {".class": "NoneType"}]}, "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make_request of HTTPConnectionPool", "ret_type": "urllib3.response.BaseHTTPResponse", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_new_conn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.connectionpool.HTTPConnectionPool._new_conn", "name": "_new_conn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.connectionpool.HTTPConnectionPool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_new_conn of HTTPConnectionPool", "ret_type": "urllib3._base_connection.BaseHTTPConnection", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_prepare_proxy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "conn"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.connectionpool.HTTPConnectionPool._prepare_proxy", "name": "_prepare_proxy", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "conn"], "arg_types": ["urllib3.connectionpool.HTTPConnectionPool", "urllib3._base_connection.BaseHTTPConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prepare_proxy of HTTPConnectionPool", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_put_conn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "conn"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.connectionpool.HTTPConnectionPool._put_conn", "name": "_put_conn", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "conn"], "arg_types": ["urllib3.connectionpool.HTTPConnectionPool", {".class": "UnionType", "items": ["urllib3._base_connection.BaseHTTPConnection", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_put_conn of HTTPConnectionPool", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_raise_timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "err", "url", "timeout_value"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.connectionpool.HTTPConnectionPool._raise_timeout", "name": "_raise_timeout", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "err", "url", "timeout_value"], "arg_types": ["urllib3.connectionpool.HTTPConnectionPool", {".class": "UnionType", "items": ["ssl.SSLError", "builtins.OSError", "builtins.TimeoutError"]}, "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.connectionpool._TYPE_TIMEOUT"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_raise_timeout of HTTPConnectionPool", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_validate_conn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "conn"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.connectionpool.HTTPConnectionPool._validate_conn", "name": "_validate_conn", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "conn"], "arg_types": ["urllib3.connectionpool.HTTPConnectionPool", "urllib3._base_connection.BaseHTTPConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_validate_conn of HTTPConnectionPool", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "block": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.connectionpool.HTTPConnectionPool.block", "name": "block", "type": "builtins.bool"}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.connectionpool.HTTPConnectionPool.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.connectionpool.HTTPConnectionPool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of HTTPConnectionPool", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "conn_kw": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.connectionpool.HTTPConnectionPool.conn_kw", "name": "conn_kw", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}}}, "is_same_host": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "url"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.connectionpool.HTTPConnectionPool.is_same_host", "name": "is_same_host", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "url"], "arg_types": ["urllib3.connectionpool.HTTPConnectionPool", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_same_host of HTTPConnectionPool", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "num_connections": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.connectionpool.HTTPConnectionPool.num_connections", "name": "num_connections", "type": "builtins.int"}}, "num_requests": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.connectionpool.HTTPConnectionPool.num_requests", "name": "num_requests", "type": "builtins.int"}}, "pool": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "urllib3.connectionpool.HTTPConnectionPool.pool", "name": "pool", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "queue.LifoQueue"}, {".class": "NoneType"}]}}}, "proxy": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.connectionpool.HTTPConnectionPool.proxy", "name": "proxy", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "NoneType"}]}}}, "proxy_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.connectionpool.HTTPConnectionPool.proxy_config", "name": "proxy_config", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "NoneType"}]}}}, "proxy_headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.connectionpool.HTTPConnectionPool.proxy_headers", "name": "proxy_headers", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "typing.Mapping"}}}, "retries": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.connectionpool.HTTPConnectionPool.retries", "name": "retries", "type": {".class": "UnionType", "items": ["urllib3.util.retry.Retry", "builtins.int"]}}}, "scheme": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "urllib3.connectionpool.HTTPConnectionPool.scheme", "name": "scheme", "type": "builtins.str"}}, "timeout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.connectionpool.HTTPConnectionPool.timeout", "name": "timeout", "type": "urllib3.util.timeout.Timeout"}}, "urlopen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "method", "url", "body", "headers", "retries", "redirect", "assert_same_host", "timeout", "pool_timeout", "release_conn", "chunked", "body_pos", "preload_content", "decode_content", "response_kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.connectionpool.HTTPConnectionPool.urlopen", "name": "urlopen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "method", "url", "body", "headers", "retries", "redirect", "assert_same_host", "timeout", "pool_timeout", "release_conn", "chunked", "body_pos", "preload_content", "decode_content", "response_kw"], "arg_types": ["urllib3.connectionpool.HTTPConnectionPool", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection._TYPE_BODY"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.retry.Retry", "builtins.bool", "builtins.int", {".class": "NoneType"}]}, "builtins.bool", "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.connectionpool._TYPE_TIMEOUT"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.request._TYPE_BODY_POSITION"}, {".class": "NoneType"}]}, "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "urlopen of HTTPConnectionPool", "ret_type": "urllib3.response.BaseHTTPResponse", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.connectionpool.HTTPConnectionPool.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.connectionpool.HTTPConnectionPool", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPException": {".class": "SymbolTableNode", "cross_ref": "http.client.HTTPException", "kind": "Gdef"}, "HTTPHeaderDict": {".class": "SymbolTableNode", "cross_ref": "urllib3._collections.HTTPHeaderDict", "kind": "Gdef"}, "HTTPSConnection": {".class": "SymbolTableNode", "cross_ref": "urllib3.connection.HTTPSConnection", "kind": "Gdef"}, "HTTPSConnectionPool": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib3.connectionpool.HTTPConnectionPool"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.connectionpool.HTTPSConnectionPool", "name": "HTTPSConnectionPool", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "urllib3.connectionpool.HTTPSConnectionPool", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.connectionpool", "mro": ["urllib3.connectionpool.HTTPSConnectionPool", "urllib3.connectionpool.HTTPConnectionPool", "urllib3.connectionpool.ConnectionPool", "urllib3._request_methods.RequestMethods", "builtins.object"], "names": {".class": "SymbolTable", "ConnectionCls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "urllib3.connectionpool.HTTPSConnectionPool.ConnectionCls", "name": "ConnectionCls", "type": {".class": "TypeType", "item": "urllib3._base_connection.BaseHTTPSConnection"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "host", "port", "timeout", "maxsize", "block", "headers", "retries", "_proxy", "_proxy_headers", "key_file", "cert_file", "cert_reqs", "key_password", "ca_certs", "ssl_version", "ssl_minimum_version", "ssl_maximum_version", "assert_hostname", "assert_fingerprint", "ca_cert_dir", "conn_kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.connectionpool.HTTPSConnectionPool.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "host", "port", "timeout", "maxsize", "block", "headers", "retries", "_proxy", "_proxy_headers", "key_file", "cert_file", "cert_reqs", "key_password", "ca_certs", "ssl_version", "ssl_minimum_version", "ssl_maximum_version", "assert_hostname", "assert_fingerprint", "ca_cert_dir", "conn_kw"], "arg_types": ["urllib3.connectionpool.HTTPSConnectionPool", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.connectionpool._TYPE_TIMEOUT"}, {".class": "NoneType"}]}, "builtins.int", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.retry.Retry", "builtins.bool", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HTTPSConnectionPool", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_new_conn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.connectionpool.HTTPSConnectionPool._new_conn", "name": "_new_conn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.connectionpool.HTTPSConnectionPool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_new_conn of HTTPSConnectionPool", "ret_type": "urllib3._base_connection.BaseHTTPSConnection", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_prepare_proxy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "conn"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.connectionpool.HTTPSConnectionPool._prepare_proxy", "name": "_prepare_proxy", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "conn"], "arg_types": ["urllib3.connectionpool.HTTPSConnectionPool", "urllib3.connection.HTTPSConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prepare_proxy of HTTPSConnectionPool", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_validate_conn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "conn"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.connectionpool.HTTPSConnectionPool._validate_conn", "name": "_validate_conn", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "conn"], "arg_types": ["urllib3.connectionpool.HTTPSConnectionPool", "urllib3._base_connection.BaseHTTPConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_validate_conn of HTTPSConnectionPool", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "assert_fingerprint": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.connectionpool.HTTPSConnectionPool.assert_fingerprint", "name": "assert_fingerprint", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "assert_hostname": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.connectionpool.HTTPSConnectionPool.assert_hostname", "name": "assert_hostname", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "NoneType"}]}}}, "ca_cert_dir": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.connectionpool.HTTPSConnectionPool.ca_cert_dir", "name": "ca_cert_dir", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "ca_certs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.connectionpool.HTTPSConnectionPool.ca_certs", "name": "ca_certs", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "cert_file": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.connectionpool.HTTPSConnectionPool.cert_file", "name": "cert_file", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "cert_reqs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.connectionpool.HTTPSConnectionPool.cert_reqs", "name": "cert_reqs", "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}]}}}, "key_file": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.connectionpool.HTTPSConnectionPool.key_file", "name": "key_file", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "key_password": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.connectionpool.HTTPSConnectionPool.key_password", "name": "key_password", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "scheme": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "urllib3.connectionpool.HTTPSConnectionPool.scheme", "name": "scheme", "type": "builtins.str"}}, "ssl_maximum_version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.connectionpool.HTTPSConnectionPool.ssl_maximum_version", "name": "ssl_maximum_version", "type": {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}}}, "ssl_minimum_version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.connectionpool.HTTPSConnectionPool.ssl_minimum_version", "name": "ssl_minimum_version", "type": {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}}}, "ssl_version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.connectionpool.HTTPSConnectionPool.ssl_version", "name": "ssl_version", "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.connectionpool.HTTPSConnectionPool.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.connectionpool.HTTPSConnectionPool", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HostChangedError": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.HostChangedError", "kind": "Gdef"}, "InsecureRequestWarning": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.InsecureRequestWarning", "kind": "Gdef"}, "LocationValueError": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.LocationValueError", "kind": "Gdef"}, "MaxRetryError": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.MaxRetryError", "kind": "Gdef"}, "NewConnectionError": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.NewConnectionError", "kind": "Gdef"}, "ProtocolError": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.ProtocolError", "kind": "Gdef"}, "ProxyConfig": {".class": "SymbolTableNode", "cross_ref": "urllib3._base_connection.ProxyConfig", "kind": "Gdef"}, "ProxyError": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.ProxyError", "kind": "Gdef"}, "ReadTimeoutError": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.ReadTimeoutError", "kind": "Gdef"}, "RequestMethods": {".class": "SymbolTableNode", "cross_ref": "urllib3._request_methods.RequestMethods", "kind": "Gdef"}, "Retry": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.retry.Retry", "kind": "Gdef"}, "SSLError": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.SSLError", "kind": "Gdef"}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing.Self", "kind": "Gdef"}, "SocketTimeout": {".class": "SymbolTableNode", "cross_ref": "_socket.timeout", "kind": "Gdef"}, "Timeout": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.timeout.Timeout", "kind": "Gdef"}, "TimeoutError": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.TimeoutError", "kind": "Gdef"}, "TracebackType": {".class": "SymbolTableNode", "cross_ref": "types.TracebackType", "kind": "Gdef"}, "Url": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.url.Url", "kind": "Gdef"}, "_DEFAULT_TIMEOUT": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.timeout._DEFAULT_TIMEOUT", "kind": "Gdef"}, "_TYPE_BODY": {".class": "SymbolTableNode", "cross_ref": "urllib3._base_connection._TYPE_BODY", "kind": "Gdef"}, "_TYPE_BODY_POSITION": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.request._TYPE_BODY_POSITION", "kind": "Gdef"}, "_TYPE_DEFAULT": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.timeout._TYPE_DEFAULT", "kind": "Gdef"}, "_TYPE_TIMEOUT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "urllib3.connectionpool._TYPE_TIMEOUT", "line": 63, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": ["urllib3.util.timeout.Timeout", "builtins.float", "urllib3.util.timeout._TYPE_DEFAULT", {".class": "NoneType"}]}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.connectionpool.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.connectionpool.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.connectionpool.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.connectionpool.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.connectionpool.__package__", "name": "__package__", "type": "builtins.str"}}, "_blocking_errnos": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "urllib3.connectionpool._blocking_errnos", "name": "_blocking_er<PERSON>s", "type": {".class": "Instance", "args": ["builtins.int"], "type_ref": "builtins.set"}}}, "_close_pool_connections": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["pool"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.connectionpool._close_pool_connections", "name": "_close_pool_connections", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["pool"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "queue.LifoQueue"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_close_pool_connections", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_encode_target": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.url._encode_target", "kind": "Gdef"}, "_normalize_host": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "urllib3.connectionpool._normalize_host", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["host", "scheme"], "dataclass_transform_spec": null, "flags": ["is_overload"], "fullname": "urllib3.connectionpool._normalize_host", "name": "_normalize_host", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["host", "scheme"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_normalize_host", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["host", "scheme"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "urllib3.connectionpool._normalize_host", "name": "_normalize_host", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["host", "scheme"], "arg_types": [{".class": "NoneType"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_normalize_host", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "urllib3.connectionpool._normalize_host", "name": "_normalize_host", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["host", "scheme"], "arg_types": [{".class": "NoneType"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_normalize_host", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["host", "scheme"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "urllib3.connectionpool._normalize_host", "name": "_normalize_host", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["host", "scheme"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_normalize_host", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "urllib3.connectionpool._normalize_host", "name": "_normalize_host", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["host", "scheme"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_normalize_host", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["host", "scheme"], "arg_types": [{".class": "NoneType"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_normalize_host", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["host", "scheme"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_normalize_host", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}]}}}, "_url_from_pool": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["pool", "path"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.connectionpool._url_from_pool", "name": "_url_from_pool", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["pool", "path"], "arg_types": [{".class": "UnionType", "items": ["urllib3.connectionpool.HTTPConnectionPool", "urllib3.connectionpool.HTTPSConnectionPool"]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_url_from_pool", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_wrap_proxy_error": {".class": "SymbolTableNode", "cross_ref": "urllib3.connection._wrap_proxy_error", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "connection_from_url": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["url", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.connectionpool.connection_from_url", "name": "connection_from_url", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["url", "kw"], "arg_types": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connection_from_url", "ret_type": "urllib3.connectionpool.HTTPConnectionPool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "connection_requires_http_tunnel": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.proxy.connection_requires_http_tunnel", "kind": "Gdef"}, "errno": {".class": "SymbolTableNode", "cross_ref": "errno", "kind": "Gdef"}, "is_connection_dropped": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.connection.is_connection_dropped", "kind": "Gdef"}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "urllib3.connectionpool.log", "name": "log", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "normalize_host": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.url._normalize_host", "kind": "Gdef"}, "parse_url": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.url.parse_url", "kind": "Gdef"}, "port_by_scheme": {".class": "SymbolTableNode", "cross_ref": "urllib3.connection.port_by_scheme", "kind": "Gdef"}, "queue": {".class": "SymbolTableNode", "cross_ref": "queue", "kind": "Gdef"}, "set_file_position": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.request.set_file_position", "kind": "Gdef"}, "ssl": {".class": "SymbolTableNode", "cross_ref": "ssl", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "to_str": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.util.to_str", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}, "weakref": {".class": "SymbolTableNode", "cross_ref": "weakref", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\urllib3\\connectionpool.py"}