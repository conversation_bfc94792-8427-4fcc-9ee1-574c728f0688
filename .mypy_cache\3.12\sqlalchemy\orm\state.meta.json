{"data_mtime": 1759503866, "dep_lines": [64, 29, 30, 31, 32, 43, 47, 54, 57, 58, 59, 61, 62, 29, 44, 45, 46, 63, 15, 17, 27, 44, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 5, 10, 10, 5, 5, 5, 25, 25, 25, 25, 25, 25, 20, 10, 10, 10, 25, 5, 5, 10, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.ext.asyncio.session", "sqlalchemy.orm.base", "sqlalchemy.orm.exc", "sqlalchemy.orm.interfaces", "sqlalchemy.orm._typing", "sqlalchemy.orm.path_registry", "sqlalchemy.util.typing", "sqlalchemy.orm.attributes", "sqlalchemy.orm.collections", "sqlalchemy.orm.identity", "sqlalchemy.orm.instrumentation", "sqlalchemy.orm.mapper", "sqlalchemy.orm.session", "sqlalchemy.orm", "sqlalchemy.exc", "sqlalchemy.inspection", "sqlalchemy.util", "sqlalchemy.engine", "__future__", "typing", "weakref", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "_typeshed", "_weakref", "abc", "enum", "sqlalchemy.engine._py_row", "sqlalchemy.engine.row", "sqlalchemy.engine.util", "sqlalchemy.event", "sqlalchemy.event.attr", "sqlalchemy.event.base", "sqlalchemy.event.registry", "sqlalchemy.ext", "sqlalchemy.ext.asyncio", "sqlalchemy.ext.asyncio.base", "sqlalchemy.log", "sqlalchemy.orm.state_changes", "sqlalchemy.sql", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.elements", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util._collections", "sqlalchemy.util._py_collections", "sqlalchemy.util.deprecations", "sqlalchemy.util.langhelpers", "sqlalchemy.util.preloaded"], "hash": "1a2a7a87179c591507988904c6ebd5d0f3b5ea48042812431c12f8a1bcfcb875", "id": "sqlalchemy.orm.state", "ignore_all": true, "interface_hash": "b1211cd02ec266c13cf2c3d7acf54ad37e49293f3290182b3fa13e4dae705eb9", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\state.py", "plugin_data": null, "size": 38813, "suppressed": [], "version_id": "1.8.0"}