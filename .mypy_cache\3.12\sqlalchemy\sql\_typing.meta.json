{"data_mtime": 1759504546, "dep_lines": [27, 31, 43, 44, 47, 49, 55, 57, 58, 70, 72, 76, 27, 28, 29, 30, 73, 8, 10, 11, 28, 36, 40, 41, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 20, 10, 10, 5, 25, 5, 10, 5, 20, 25, 25, 25, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.roles", "sqlalchemy.util.typing", "sqlalchemy.sql.base", "sqlalchemy.sql.compiler", "sqlalchemy.sql.dml", "sqlalchemy.sql.elements", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.type_api", "sqlalchemy.engine.mock", "sqlalchemy.sql", "sqlalchemy.exc", "sqlalchemy.util", "sqlalchemy.inspection", "sqlalchemy.engine", "__future__", "operator", "typing", "sqlalchemy", "datetime", "decimal", "uuid", "builtins", "pyexpat.model", "pyexpat.errors", "_typeshed", "abc", "sqlalchemy.engine.interfaces", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.operators", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers"], "hash": "ac6cb36bd48d495d36f8251659819b258cc21a19d62eefba66253d068782fbe2", "id": "sqlalchemy.sql._typing", "ignore_all": true, "interface_hash": "bc1c78a86a3ffc3cc677dd7eddea9e3cc686d1922602c8963ac8782b8dd08e1b", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\_typing.py", "plugin_data": null, "size": 13497, "suppressed": [], "version_id": "1.8.0"}