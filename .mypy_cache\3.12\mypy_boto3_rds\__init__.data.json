{".class": "MypyFile", "_fullname": "mypy_boto3_rds", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Client": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "mypy_boto3_rds.Client", "line": 186, "no_args": true, "normalized": false, "target": "mypy_boto3_rds.client.RDSClient"}}, "DBClusterAvailableWaiter": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.waiter.DBClusterAvailableWaiter", "kind": "Gdef"}, "DBClusterDeletedWaiter": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.waiter.DBClusterDeletedWaiter", "kind": "Gdef"}, "DBClusterSnapshotAvailableWaiter": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.waiter.DBClusterSnapshotAvailableWaiter", "kind": "Gdef"}, "DBClusterSnapshotDeletedWaiter": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.waiter.DBClusterSnapshotDeletedWaiter", "kind": "Gdef"}, "DBInstanceAvailableWaiter": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.waiter.DBInstanceAvailableWaiter", "kind": "Gdef"}, "DBInstanceDeletedWaiter": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.waiter.DBInstanceDeletedWaiter", "kind": "Gdef"}, "DBSnapshotAvailableWaiter": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.waiter.DBSnapshotAvailableWaiter", "kind": "Gdef"}, "DBSnapshotCompletedWaiter": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.waiter.DBSnapshotCompletedWaiter", "kind": "Gdef"}, "DBSnapshotDeletedWaiter": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.waiter.DBSnapshotDeletedWaiter", "kind": "Gdef"}, "DescribeBlueGreenDeploymentsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeBlueGreenDeploymentsPaginator", "kind": "Gdef"}, "DescribeCertificatesPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeCertificatesPaginator", "kind": "Gdef"}, "DescribeDBClusterAutomatedBackupsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBClusterAutomatedBackupsPaginator", "kind": "Gdef"}, "DescribeDBClusterBacktracksPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBClusterBacktracksPaginator", "kind": "Gdef"}, "DescribeDBClusterEndpointsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBClusterEndpointsPaginator", "kind": "Gdef"}, "DescribeDBClusterParameterGroupsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBClusterParameterGroupsPaginator", "kind": "Gdef"}, "DescribeDBClusterParametersPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBClusterParametersPaginator", "kind": "Gdef"}, "DescribeDBClusterSnapshotsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBClusterSnapshotsPaginator", "kind": "Gdef"}, "DescribeDBClustersPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBClustersPaginator", "kind": "Gdef"}, "DescribeDBEngineVersionsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBEngineVersionsPaginator", "kind": "Gdef"}, "DescribeDBInstanceAutomatedBackupsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBInstanceAutomatedBackupsPaginator", "kind": "Gdef"}, "DescribeDBInstancesPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBInstancesPaginator", "kind": "Gdef"}, "DescribeDBLogFilesPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBLogFilesPaginator", "kind": "Gdef"}, "DescribeDBMajorEngineVersionsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBMajorEngineVersionsPaginator", "kind": "Gdef"}, "DescribeDBParameterGroupsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBParameterGroupsPaginator", "kind": "Gdef"}, "DescribeDBParametersPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBParametersPaginator", "kind": "Gdef"}, "DescribeDBProxiesPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBProxiesPaginator", "kind": "Gdef"}, "DescribeDBProxyEndpointsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBProxyEndpointsPaginator", "kind": "Gdef"}, "DescribeDBProxyTargetGroupsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBProxyTargetGroupsPaginator", "kind": "Gdef"}, "DescribeDBProxyTargetsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBProxyTargetsPaginator", "kind": "Gdef"}, "DescribeDBRecommendationsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBRecommendationsPaginator", "kind": "Gdef"}, "DescribeDBSecurityGroupsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBSecurityGroupsPaginator", "kind": "Gdef"}, "DescribeDBSnapshotTenantDatabasesPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBSnapshotTenantDatabasesPaginator", "kind": "Gdef"}, "DescribeDBSnapshotsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBSnapshotsPaginator", "kind": "Gdef"}, "DescribeDBSubnetGroupsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBSubnetGroupsPaginator", "kind": "Gdef"}, "DescribeEngineDefaultClusterParametersPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeEngineDefaultClusterParametersPaginator", "kind": "Gdef"}, "DescribeEngineDefaultParametersPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeEngineDefaultParametersPaginator", "kind": "Gdef"}, "DescribeEventSubscriptionsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeEventSubscriptionsPaginator", "kind": "Gdef"}, "DescribeEventsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeEventsPaginator", "kind": "Gdef"}, "DescribeExportTasksPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeExportTasksPaginator", "kind": "Gdef"}, "DescribeGlobalClustersPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeGlobalClustersPaginator", "kind": "Gdef"}, "DescribeIntegrationsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeIntegrationsPaginator", "kind": "Gdef"}, "DescribeOptionGroupOptionsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeOptionGroupOptionsPaginator", "kind": "Gdef"}, "DescribeOptionGroupsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeOptionGroupsPaginator", "kind": "Gdef"}, "DescribeOrderableDBInstanceOptionsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeOrderableDBInstanceOptionsPaginator", "kind": "Gdef"}, "DescribePendingMaintenanceActionsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribePendingMaintenanceActionsPaginator", "kind": "Gdef"}, "DescribeReservedDBInstancesOfferingsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeReservedDBInstancesOfferingsPaginator", "kind": "Gdef"}, "DescribeReservedDBInstancesPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeReservedDBInstancesPaginator", "kind": "Gdef"}, "DescribeSourceRegionsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeSourceRegionsPaginator", "kind": "Gdef"}, "DescribeTenantDatabasesPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeTenantDatabasesPaginator", "kind": "Gdef"}, "DownloadDBLogFilePortionPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DownloadDBLogFilePortionPaginator", "kind": "Gdef"}, "RDSClient": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.client.RDSClient", "kind": "Gdef"}, "TenantDatabaseAvailableWaiter": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.waiter.TenantDatabaseAvailableWaiter", "kind": "Gdef"}, "TenantDatabaseDeletedWaiter": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.waiter.TenantDatabaseDeletedWaiter", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "mypy_boto3_rds.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mypy_boto3_rds.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mypy_boto3_rds.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mypy_boto3_rds.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mypy_boto3_rds.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mypy_boto3_rds.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mypy_boto3_rds.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\mypy_boto3_rds\\__init__.pyi"}