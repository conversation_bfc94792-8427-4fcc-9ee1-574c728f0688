{"data_mtime": 1759504508, "dep_lines": [6, 8, 1, 2, 3, 4, 5, 7, 8, 10, 11, 12, 1, 1, 1], "dep_prios": [5, 10, 5, 10, 5, 5, 5, 5, 20, 5, 5, 5, 20, 20, 30], "dependencies": ["collections.abc", "multiprocessing.connection", "pickle", "sys", "_typeshed", "abc", "builtins", "copyreg", "multiprocessing", "socket", "typing", "typing_extensions", "pyexpat.model", "pyexpat.errors", "_collections_abc"], "hash": "3ff0e1669f3d0457d08b49ca9beed71d000d28230488494d019df662c3080336", "id": "multiprocessing.reduction", "ignore_all": true, "interface_hash": "22d4dc67b21bf4f4da6ea9739823cb95d4f9a287432d3e5834b9ef1d70d7abf9", "mtime": 1757092231, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\multiprocessing\\reduction.pyi", "plugin_data": null, "size": 3330, "suppressed": [], "version_id": "1.8.0"}