{"data_mtime": 1759504508, "dep_lines": [19, 20, 15, 17, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 20, 20, 30], "dependencies": ["sqlalchemy.engine._py_processors", "sqlalchemy.util._has_cy", "__future__", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "abc"], "hash": "4563637dbdd87c078d25b12cbccdcd3ef14681ce757d6b12b73bf917ebc95ea0", "id": "sqlalchemy.engine.processors", "ignore_all": true, "interface_hash": "8b69a8aaf7cd1087f95d89279da4f34f2579debcd5e8d5d8332e85623be486dc", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\processors.py", "plugin_data": null, "size": 2440, "suppressed": [], "version_id": "1.8.0"}