{"data_mtime": 1759503866, "dep_lines": [22, 23, 24, 25, 31, 45, 22, 26, 27, 28, 29, 31, 40, 8, 10, 11, 26, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 10, 25, 20, 10, 10, 5, 5, 20, 25, 5, 10, 5, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.engine.base", "sqlalchemy.engine.url", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.mock", "sqlalchemy.sql.compiler", "sqlalchemy.util.typing", "sqlalchemy.engine", "sqlalchemy.event", "sqlalchemy.exc", "sqlalchemy.util", "sqlalchemy.pool", "sqlalchemy.sql", "sqlalchemy.log", "__future__", "inspect", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "enum", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.pool.base", "sqlalchemy.util._py_collections", "sqlalchemy.util.deprecations"], "hash": "b663f59a44aa3a46a4b041aa68aee8e7cad27cf9d09ab0db137caab81c93c667", "id": "sqlalchemy.engine.create", "ignore_all": true, "interface_hash": "68ac4a7bfd0bec8ec99d0e5f8e45e426dea9a88fed196859fe488cfc9a2e18c9", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\create.py", "plugin_data": null, "size": 34740, "suppressed": [], "version_id": "1.8.0"}