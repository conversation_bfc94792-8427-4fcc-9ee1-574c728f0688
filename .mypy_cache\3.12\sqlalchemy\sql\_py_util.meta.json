{"data_mtime": 1759503866, "dep_lines": [16, 19, 8, 10, 1, 1, 1, 1, 1], "dep_prios": [5, 25, 5, 5, 5, 20, 20, 30, 30], "dependencies": ["sqlalchemy.util.typing", "sqlalchemy.sql.cache_key", "__future__", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "enum"], "hash": "5944f9308a680fa5c1c8343d33f02b3f271651d64ee0f560dea29339f4e448ab", "id": "sqlalchemy.sql._py_util", "ignore_all": true, "interface_hash": "00d8ddcf83020a9a6279f2718dee6728678621cabb6f6868aef731dd240506c3", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\_py_util.py", "plugin_data": null, "size": 2248, "suppressed": [], "version_id": "1.8.0"}