{"data_mtime": 1759503866, "dep_lines": [36, 37, 38, 43, 44, 47, 34, 35, 10, 12, 13, 14, 15, 16, 34, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 25, 25, 25, 10, 10, 5, 5, 5, 10, 10, 5, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.util.langhelpers", "sqlalchemy.util._has_cy", "sqlalchemy.util.typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.elements", "sqlalchemy.sql._py_util", "sqlalchemy.exc", "sqlalchemy.util", "__future__", "collections", "enum", "itertools", "operator", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "_operator", "abc", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.util._py_collections", "sqlalchemy.util.preloaded", "types"], "hash": "bac8c4ec8778445fa59e010b6c83354d12ff475c60e48f4991f817d3ded62bfa", "id": "sqlalchemy.sql.visitors", "ignore_all": true, "interface_hash": "5d2db2b05b9be2fdc296845d5f76d94919d1f2d379d9344b22d9097a886f2069", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\visitors.py", "plugin_data": null, "size": 37480, "suppressed": [], "version_id": "1.8.0"}