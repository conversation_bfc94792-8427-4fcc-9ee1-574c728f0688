{"data_mtime": 1759504553, "dep_lines": [7, 8, 9, 11, 12, 3, 5, 6, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["smartanalytics_utilities.config.database_config", "smartanalytics_utilities.config.settings", "smartanalytics_utilities.utils.logging_helper", "sqlalchemy.ext.asyncio", "sqlalchemy.pool", "functools", "boto3", "mypy_boto3_rds", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "_typeshed", "abc", "aws_lambda_powertools", "aws_lambda_powertools.logging", "aws_lambda_powertools.logging.logger", "botocore", "botocore.client", "botocore.config", "contextlib", "enum", "mypy_boto3_rds.client", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "pydantic_settings", "pydantic_settings.main", "smartanalytics_utilities", "smartanalytics_utilities.config", "smartanalytics_utilities.config.aws_config", "smartanalytics_utilities.config.base", "smartanalytics_utilities.config.logging_config", "smartanalytics_utilities.utils", "sqlalchemy.engine", "sqlalchemy.engine._py_row", "sqlalchemy.engine.cursor", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.result", "sqlalchemy.engine.row", "sqlalchemy.event", "sqlalchemy.event.base", "sqlalchemy.event.registry", "sqlalchemy.ext", "sqlalchemy.ext.asyncio.base", "sqlalchemy.ext.asyncio.engine", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.pool.base", "sqlalchemy.pool.impl", "sqlalchemy.sql", "sqlalchemy.sql._elements_constructors", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.compiler", "sqlalchemy.sql.elements", "sqlalchemy.sql.roles", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util", "sqlalchemy.util.langhelpers", "types", "typing"], "hash": "cb0195927129f9aacb8c2c86e9127006bf29d5b1eee2f4090d69bf2b80194550", "id": "smartanalytics_infrastructure.database.connection", "ignore_all": false, "interface_hash": "52597f4ad48b32f04ec1d97dea460305a61d6eab15086538d539cfcf3d6460c2", "mtime": 1759504022, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "layers\\infrastructure\\python\\smartanalytics_infrastructure\\database\\connection.py", "plugin_data": null, "size": 5865, "suppressed": [], "version_id": "1.8.0"}