{"data_mtime": 1759503833, "dep_lines": [5, 3, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 20, 20, 30, 30], "dependencies": ["pydantic.version", "__future__", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "typing"], "hash": "82a0d3436157ef018b6495775dba104a2457287927b2cde97c83972f40435d39", "id": "pydantic.warnings", "ignore_all": true, "interface_hash": "fb5baaee703a9bc210bda0f111d1389974d6bdc02dfb180e636af1bb95bdf553", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\warnings.py", "plugin_data": null, "size": 3772, "suppressed": [], "version_id": "1.8.0"}