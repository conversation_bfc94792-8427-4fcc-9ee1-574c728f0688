{"data_mtime": 1759503828, "dep_lines": [31, 32, 42, 48, 49, 50, 51, 1, 2, 3, 4, 5, 6, 7, 8, 29, 45, 46, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 25, 25, 25, 25, 10, 10, 10, 5, 5, 5, 5, 5, 5, 25, 25, 5, 20, 20, 30, 30, 30, 30, 30], "dependencies": ["pydantic.v1.errors", "pydantic.v1.typing", "pydantic.v1.version", "pydantic.v1.config", "pydantic.v1.dataclasses", "pydantic.v1.fields", "pydantic.v1.main", "keyword", "warnings", "weakref", "collections", "copy", "itertools", "types", "typing", "typing_extensions", "inspect", "pathlib", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "_typeshed", "_weakref", "abc", "os"], "hash": "7ef8d708d79a5358c1fbf9a2d12524e1fccde0ea6f5fe48a4eee3224c3d12254", "id": "pydantic.v1.utils", "ignore_all": true, "interface_hash": "c04d9e128410330838abeaa791f448de20b095aad8306b81bb5c7d2c76939580", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\v1\\utils.py", "plugin_data": null, "size": 25941, "suppressed": [], "version_id": "1.8.0"}