{"data_mtime": 1759503828, "dep_lines": [31, 32, 33, 35, 36, 51, 62, 87, 493, 1163, 4, 31, 1, 2, 3, 5, 29, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 25, 20, 20, 5, 20, 10, 10, 5, 5, 5, 5, 20, 20, 30, 30, 30, 30, 30], "dependencies": ["pydantic.v1.errors", "pydantic.v1.class_validators", "pydantic.v1.error_wrappers", "pydantic.v1.types", "pydantic.v1.typing", "pydantic.v1.utils", "pydantic.v1.validators", "pydantic.v1.config", "pydantic.v1.schema", "pydantic.v1.main", "collections.abc", "pydantic.v1", "copy", "re", "collections", "typing", "typing_extensions", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "_typeshed", "abc", "pydantic.v1.dataclasses", "types"], "hash": "56a5890ab894362132a6d5eba03549e74d49a550347a7d9500d724b2a5cbd9bf", "id": "pydantic.v1.fields", "ignore_all": true, "interface_hash": "8e4ec01a38f07928142767117d702f635f40f746890b7ff117a8e9f958c4f16b", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\v1\\fields.py", "plugin_data": null, "size": 50649, "suppressed": [], "version_id": "1.8.0"}