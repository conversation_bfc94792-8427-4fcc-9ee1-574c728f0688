{"data_mtime": **********, "dep_lines": [7, 12, 13, 14, 15, 16, 18, 19, 8, 9, 10, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 30], "dependencies": ["collections.abc", "botocore.auth", "botocore.compat", "botocore.crt", "botocore.endpoint_provider", "botocore.exceptions", "botocore.hooks", "botocore.model", "enum", "logging", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "abc"], "hash": "ff817667d587b50af2b3639a9d00122bf1c60c424c94285a8df7ef6e1b567540", "id": "botocore.regions", "ignore_all": true, "interface_hash": "5ea2a39971579807b4d6239819ca6d47c8925c78771fc0b4cd72c7501e7276da", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\botocore-stubs\\regions.pyi", "plugin_data": null, "size": 3732, "suppressed": [], "version_id": "1.8.0"}