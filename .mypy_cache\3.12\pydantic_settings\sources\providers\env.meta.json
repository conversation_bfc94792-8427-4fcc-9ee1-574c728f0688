{"data_mtime": 1759503867, "dep_lines": [10, 17, 18, 19, 4, 11, 12, 14, 16, 27, 1, 3, 5, 13, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 25, 5, 10, 5, 5, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._utils", "pydantic_settings.sources.base", "pydantic_settings.sources.types", "pydantic_settings.sources.utils", "collections.abc", "pydantic.dataclasses", "pydantic.fields", "typing_inspection.introspection", "pydantic_settings.utils", "pydantic_settings.main", "__future__", "os", "typing", "typing_extensions", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "pathlib", "pydantic", "pydantic._internal", "pydantic._internal._generate_schema", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.main", "re"], "hash": "136abd6078c5ac5516022776569637ebbf0f0d2b880d073fe3b896733fe88d0e", "id": "pydantic_settings.sources.providers.env", "ignore_all": true, "interface_hash": "f8dc2eaf0dc438eb509a65c0ea847e575a1d256f9e7e948b7a4b0ab0757b92a1", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic_settings\\sources\\providers\\env.py", "plugin_data": null, "size": 10717, "suppressed": [], "version_id": "1.8.0"}