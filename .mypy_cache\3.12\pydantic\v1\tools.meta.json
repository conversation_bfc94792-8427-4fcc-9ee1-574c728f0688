{"data_mtime": 1759503828, "dep_lines": [6, 7, 8, 1, 2, 3, 4, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 5, 5, 20, 20, 30, 30, 30, 30], "dependencies": ["pydantic.v1.parse", "pydantic.v1.types", "pydantic.v1.typing", "json", "functools", "pathlib", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "enum", "json.decoder", "os"], "hash": "d650dd5c79348cbe6e3f7bb9442600bd402518296000efb8e6592af63edfc810", "id": "pydantic.v1.tools", "ignore_all": true, "interface_hash": "a00b5ba92ba0c90d3444dc3da2b0d065fa1ab39409b4fb53ee71b6a0dc546e17", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\v1\\tools.py", "plugin_data": null, "size": 2881, "suppressed": [], "version_id": "1.8.0"}