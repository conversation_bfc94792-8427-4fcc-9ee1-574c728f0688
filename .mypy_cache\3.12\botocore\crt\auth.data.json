{".class": "MypyFile", "_fullname": "botocore.crt.auth", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "AWSRequest": {".class": "SymbolTableNode", "cross_ref": "botocore.awsrequest.AWSRequest", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseSigner": {".class": "SymbolTableNode", "cross_ref": "botocore.auth.BaseSigner", "kind": "Gdef"}, "CRT_AUTH_TYPE_MAPS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "botocore.crt.auth.CRT_AUTH_TYPE_MAPS", "name": "CRT_AUTH_TYPE_MAPS", "type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeType", "item": "botocore.auth.BaseSigner"}], "type_ref": "builtins.dict"}}}, "CrtS3SigV4AsymAuth": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.crt.auth.CrtSigV4AsymAuth"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.crt.auth.CrtS3SigV4AsymAuth", "name": "CrtS3SigV4AsymAuth", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.crt.auth.CrtS3SigV4AsymAuth", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.crt.auth", "mro": ["botocore.crt.auth.CrtS3SigV4AsymAuth", "botocore.crt.auth.CrtSigV4AsymAuth", "botocore.auth.BaseSigner", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.crt.auth.CrtS3SigV4AsymAuth.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.crt.auth.CrtS3SigV4AsymAuth", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CrtS3SigV4AsymQueryAuth": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.crt.auth.CrtSigV4AsymQueryAuth"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.crt.auth.CrtS3SigV4AsymQueryAuth", "name": "CrtS3SigV4AsymQueryAuth", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.crt.auth.CrtS3SigV4AsymQueryAuth", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.crt.auth", "mro": ["botocore.crt.auth.CrtS3SigV4AsymQueryAuth", "botocore.crt.auth.CrtSigV4AsymQueryAuth", "botocore.crt.auth.CrtSigV4AsymAuth", "botocore.auth.BaseSigner", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.crt.auth.CrtS3SigV4AsymQueryAuth.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.crt.auth.CrtS3SigV4AsymQueryAuth", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CrtS3SigV4Auth": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.crt.auth.CrtSigV4Auth"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.crt.auth.CrtS3SigV4Auth", "name": "CrtS3SigV4Auth", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.crt.auth.CrtS3SigV4Auth", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.crt.auth", "mro": ["botocore.crt.auth.CrtS3SigV4Auth", "botocore.crt.auth.CrtSigV4Auth", "botocore.auth.BaseSigner", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.crt.auth.CrtS3SigV4Auth.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.crt.auth.CrtS3SigV4Auth", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CrtS3SigV4QueryAuth": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.crt.auth.CrtSigV4QueryAuth"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.crt.auth.CrtS3SigV4QueryAuth", "name": "CrtS3SigV4QueryAuth", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.crt.auth.CrtS3SigV4QueryAuth", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.crt.auth", "mro": ["botocore.crt.auth.CrtS3SigV4QueryAuth", "botocore.crt.auth.CrtSigV4QueryAuth", "botocore.crt.auth.CrtSigV4Auth", "botocore.auth.BaseSigner", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.crt.auth.CrtS3SigV4QueryAuth.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.crt.auth.CrtS3SigV4QueryAuth", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CrtSigV4AsymAuth": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.auth.BaseSigner"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.crt.auth.CrtSigV4AsymAuth", "name": "CrtSigV4AsymAuth", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.crt.auth.CrtSigV4AsymAuth", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.crt.auth", "mro": ["botocore.crt.auth.CrtSigV4AsymAuth", "botocore.auth.BaseSigner", "builtins.object"], "names": {".class": "SymbolTable", "REQUIRES_REGION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.crt.auth.CrtSigV4AsymAuth.REQUIRES_REGION", "name": "REQUIRES_REGION", "type": "builtins.bool"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "credentials", "service_name", "region_name"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.crt.auth.CrtSigV4AsymAuth.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "credentials", "service_name", "region_name"], "arg_types": ["botocore.crt.auth.CrtSigV4AsymAuth", "botocore.crt.auth._Credentials", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CrtSigV4AsymAuth", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "add_auth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.crt.auth.CrtSigV4AsymAuth.add_auth", "name": "add_auth", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["botocore.crt.auth.CrtSigV4AsymAuth", "botocore.awsrequest.AWSRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_auth of CrtSigV4AsymAuth", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "credentials": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.crt.auth.CrtSigV4AsymAuth.credentials", "name": "credentials", "type": "botocore.crt.auth._Credentials"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.crt.auth.CrtSigV4AsymAuth.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.crt.auth.CrtSigV4AsymAuth", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CrtSigV4AsymQueryAuth": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.crt.auth.CrtSigV4AsymAuth"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.crt.auth.CrtSigV4AsymQueryAuth", "name": "CrtSigV4AsymQueryAuth", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.crt.auth.CrtSigV4AsymQueryAuth", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.crt.auth", "mro": ["botocore.crt.auth.CrtSigV4AsymQueryAuth", "botocore.crt.auth.CrtSigV4AsymAuth", "botocore.auth.BaseSigner", "builtins.object"], "names": {".class": "SymbolTable", "DEFAULT_EXPIRES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.crt.auth.CrtSigV4AsymQueryAuth.DEFAULT_EXPIRES", "name": "DEFAULT_EXPIRES", "type": "builtins.int"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "credentials", "service_name", "region_name", "expires"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.crt.auth.CrtSigV4AsymQueryAuth.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "credentials", "service_name", "region_name", "expires"], "arg_types": ["botocore.crt.auth.CrtSigV4AsymQueryAuth", "botocore.crt.auth._Credentials", "builtins.str", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CrtSigV4AsymQueryAuth", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.crt.auth.CrtSigV4AsymQueryAuth.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.crt.auth.CrtSigV4AsymQueryAuth", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CrtSigV4Auth": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.auth.BaseSigner"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.crt.auth.CrtSigV4Auth", "name": "CrtSigV4Auth", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.crt.auth.CrtSigV4Auth", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.crt.auth", "mro": ["botocore.crt.auth.CrtSigV4Auth", "botocore.auth.BaseSigner", "builtins.object"], "names": {".class": "SymbolTable", "REQUIRES_REGION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.crt.auth.CrtSigV4Auth.REQUIRES_REGION", "name": "REQUIRES_REGION", "type": "builtins.bool"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "credentials", "service_name", "region_name"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.crt.auth.CrtSigV4Auth.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "credentials", "service_name", "region_name"], "arg_types": ["botocore.crt.auth.CrtSigV4Auth", "botocore.crt.auth._Credentials", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CrtSigV4Auth", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "add_auth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.crt.auth.CrtSigV4Auth.add_auth", "name": "add_auth", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["botocore.crt.auth.CrtSigV4Auth", "botocore.awsrequest.AWSRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_auth of CrtSigV4Auth", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "credentials": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.crt.auth.CrtSigV4Auth.credentials", "name": "credentials", "type": "botocore.crt.auth._Credentials"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.crt.auth.CrtSigV4Auth.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.crt.auth.CrtSigV4Auth", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CrtSigV4QueryAuth": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.crt.auth.CrtSigV4Auth"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.crt.auth.CrtSigV4QueryAuth", "name": "CrtSigV4QueryAuth", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.crt.auth.CrtSigV4QueryAuth", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.crt.auth", "mro": ["botocore.crt.auth.CrtSigV4QueryAuth", "botocore.crt.auth.CrtSigV4Auth", "botocore.auth.BaseSigner", "builtins.object"], "names": {".class": "SymbolTable", "DEFAULT_EXPIRES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.crt.auth.CrtSigV4QueryAuth.DEFAULT_EXPIRES", "name": "DEFAULT_EXPIRES", "type": "builtins.int"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "credentials", "service_name", "region_name", "expires"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.crt.auth.CrtSigV4QueryAuth.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "credentials", "service_name", "region_name", "expires"], "arg_types": ["botocore.crt.auth.CrtSigV4QueryAuth", "botocore.crt.auth._Credentials", "builtins.str", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CrtSigV4QueryAuth", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.crt.auth.CrtSigV4QueryAuth.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.crt.auth.CrtSigV4QueryAuth", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPHeaders": {".class": "SymbolTableNode", "cross_ref": "botocore.compat.HTTPHeaders", "kind": "Gdef"}, "NoCredentialsError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.NoCredentialsError", "kind": "Gdef"}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing.Protocol", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SIGNED_HEADERS_BLACKLIST": {".class": "SymbolTableNode", "cross_ref": "botocore.auth.SIGNED_HEADERS_BLACKLIST", "kind": "Gdef"}, "STREAMING_UNSIGNED_PAYLOAD_TRAILER": {".class": "SymbolTableNode", "cross_ref": "botocore.auth.STREAMING_UNSIGNED_PAYLOAD_TRAILER", "kind": "Gdef"}, "UNSIGNED_PAYLOAD": {".class": "SymbolTableNode", "cross_ref": "botocore.auth.UNSIGNED_PAYLOAD", "kind": "Gdef"}, "_Credentials": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["access_key", 1], ["secret_key", 1], ["token", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.crt.auth._Credentials", "name": "_Credentials", "type_vars": []}, "deletable_attributes": [], "flags": ["is_abstract", "is_protocol"], "fullname": "botocore.crt.auth._Credentials", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "botocore.crt.auth", "mro": ["botocore.crt.auth._Credentials", "builtins.object"], "names": {".class": "SymbolTable", "access_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_abstract_var", "is_ready"], "fullname": "botocore.crt.auth._Credentials.access_key", "name": "access_key", "type": "builtins.str"}}, "secret_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_abstract_var", "is_ready"], "fullname": "botocore.crt.auth._Credentials.secret_key", "name": "secret_key", "type": "builtins.str"}}, "token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_abstract_var", "is_ready"], "fullname": "botocore.crt.auth._Credentials.token", "name": "token", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.crt.auth._Credentials.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.crt.auth._Credentials", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.crt.auth.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.crt.auth.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.crt.auth.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.crt.auth.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.crt.auth.__package__", "name": "__package__", "type": "builtins.str"}}, "parse_qs": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.parse_qs", "kind": "Gdef"}, "percent_encode_sequence": {".class": "SymbolTableNode", "cross_ref": "botocore.utils.percent_encode_sequence", "kind": "Gdef"}, "urlsplit": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urlsplit", "kind": "Gdef"}, "urlunsplit": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urlunsplit", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\botocore-stubs\\crt\\auth.pyi"}