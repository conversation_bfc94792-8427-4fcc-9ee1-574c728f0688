{"data_mtime": 1759503829, "dep_lines": [3, 6, 10, 1, 4, 5, 8, 10, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 10, 10, 20, 5, 20, 20, 30, 30], "dependencies": ["collections.abc", "urllib.parse", "markdown_it._punycode", "__future__", "contextlib", "re", "mdurl", "markdown_it", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "typing"], "hash": "6af3979cb77dc70e63535ab93cb7ed8c033da6f1b1f25f500c4926652cab3208", "id": "markdown_it.common.normalize_url", "ignore_all": true, "interface_hash": "0a9d1a7b9b8e32fc0e7414c8a84c66e9341ad74a5b5359fcad380b30b493706f", "mtime": 1757092234, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\markdown_it\\common\\normalize_url.py", "plugin_data": null, "size": 2568, "suppressed": [], "version_id": "1.8.0"}