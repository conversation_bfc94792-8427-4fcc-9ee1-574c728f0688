{"data_mtime": 1759503832, "dep_lines": [4, 5, 6, 7, 8, 12, 79, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["rich.jupyter", "rich.segment", "rich.style", "rich._emoji_codes", "rich._emoji_replace", "rich.console", "rich.columns", "sys", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "_random", "_typeshed", "abc", "datetime", "enum", "rich.terminal_theme", "rich.text", "rich.theme"], "hash": "fdb4dfd58dc9aa2324e8d7e7e15fd83a1ab5c003c01cd3838462c98fde51dee2", "id": "rich.emoji", "ignore_all": true, "interface_hash": "ef7cc8f921e3fa2ee69962495e41a5ca04ac93d0f89473c3a1cecc55d239067b", "mtime": 1757092238, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\rich\\emoji.py", "plugin_data": null, "size": 2343, "suppressed": [], "version_id": "1.8.0"}