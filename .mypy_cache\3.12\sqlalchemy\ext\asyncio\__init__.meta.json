{"data_mtime": 1759503867, "dep_lines": [8, 14, 18, 19, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 20, 20, 30, 30], "dependencies": ["sqlalchemy.ext.asyncio.engine", "sqlalchemy.ext.asyncio.result", "sqlalchemy.ext.asyncio.scoping", "sqlalchemy.ext.asyncio.session", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "typing"], "hash": "abcfe0071ff62094c387ca4684dc3645647049d69bc2a4002bb61d8bc2ba7ddb", "id": "sqlalchemy.ext.asyncio", "ignore_all": true, "interface_hash": "60b954da89cc0a0aa57452bdac8d45eb1a48a8d7f48c222608020944db0ca88d", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\__init__.py", "plugin_data": null, "size": 1342, "suppressed": [], "version_id": "1.8.0"}