{"data_mtime": 1759504552, "dep_lines": [8, 3, 4, 5, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 5, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["smartanalytics_utilities.config.logging_config", "json", "logging", "sys", "aws_lambda_powertools", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "_typeshed", "abc", "aws_lambda_powertools.logging", "aws_lambda_powertools.logging.logger", "json.encoder", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "pydantic_settings", "pydantic_settings.main", "smartanalytics_utilities.config", "typing"], "hash": "07afbbd1246dec9fc94016267174a945479507b720f54d57c13943d69a819f84", "id": "smartanalytics_utilities.utils.logging_helper", "ignore_all": false, "interface_hash": "091bfe872884e58b94e1f78a0f4b46bd79123c78aaa3abdcc3fdd57c1dd07661", "mtime": 1759502092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "layers\\utilities\\python\\smartanalytics_utilities\\utils\\logging_helper.py", "plugin_data": null, "size": 2281, "suppressed": [], "version_id": "1.8.0"}