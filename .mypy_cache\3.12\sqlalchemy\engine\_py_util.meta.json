{"data_mtime": 1759504547, "dep_lines": [18, 15, 7, 9, 15, 1, 1, 1, 1], "dep_prios": [25, 10, 5, 5, 20, 5, 20, 20, 30], "dependencies": ["sqlalchemy.engine.interfaces", "sqlalchemy.exc", "__future__", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "abc"], "hash": "b9d2d0c902687c0dd97e629c96b63f8913d0b39d7688a07ec70c2e0e35f76b47", "id": "sqlalchemy.engine._py_util", "ignore_all": true, "interface_hash": "5221f76ff1a9a118cd712b9e12ea8c74ae42bbc013d1586ac472372b08d30a51", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\_py_util.py", "plugin_data": null, "size": 2539, "suppressed": [], "version_id": "1.8.0"}