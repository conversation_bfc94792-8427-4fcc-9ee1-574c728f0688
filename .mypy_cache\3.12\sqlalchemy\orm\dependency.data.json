{".class": "MypyFile", "_fullname": "sqlalchemy.orm.dependency", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "DependencyProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.dependency.DependencyProcessor", "name": "DependencyProcessor", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.dependency.DependencyProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.dependency", "mro": ["sqlalchemy.orm.dependency.DependencyProcessor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "prop"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.DependencyProcessor.__init__", "name": "__init__", "type": null}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.DependencyProcessor.__repr__", "name": "__repr__", "type": null}}, "_get_reversed_processed_set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "uow"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.DependencyProcessor._get_reversed_processed_set", "name": "_get_reversed_processed_set", "type": null}}, "_passive_delete_flag": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.dependency.DependencyProcessor._passive_delete_flag", "name": "_passive_delete_flag", "type": "sqlalchemy.orm.base.PassiveFlag"}}, "_passive_update_flag": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.dependency.DependencyProcessor._passive_update_flag", "name": "_passive_update_flag", "type": "sqlalchemy.orm.base.PassiveFlag"}}, "_pks_changed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "uowcommit", "state"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.DependencyProcessor._pks_changed", "name": "_pks_changed", "type": null}}, "_post_update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "state", "uowcommit", "related", "is_m2o_delete"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.DependencyProcessor._post_update", "name": "_post_update", "type": null}}, "_synchronize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "state", "child", "associationrow", "clearkeys", "uowcommit"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.DependencyProcessor._synchronize", "name": "_synchronize", "type": null}}, "_verify_canload": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.DependencyProcessor._verify_canload", "name": "_verify_canload", "type": null}}, "cascade": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.dependency.DependencyProcessor.cascade", "name": "cascade", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "direction": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.dependency.DependencyProcessor.direction", "name": "direction", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "enable_typechecks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.dependency.DependencyProcessor.enable_typechecks", "name": "enable_typechecks", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "from_relationship": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "prop"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.dependency.DependencyProcessor.from_relationship", "name": "from_relationship", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.dependency.DependencyProcessor.from_relationship", "name": "from_relationship", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "prop"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.dependency.DependencyProcessor"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_relationship of DependencyProcessor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "hasparent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.DependencyProcessor.hasparent", "name": "hasparent", "type": null}}, "key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.dependency.DependencyProcessor.key", "name": "key", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "mapper": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.dependency.DependencyProcessor.mapper", "name": "mapper", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "parent": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.dependency.DependencyProcessor.parent", "name": "parent", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "passive_deletes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.dependency.DependencyProcessor.passive_deletes", "name": "passive_deletes", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "passive_updates": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.dependency.DependencyProcessor.passive_updates", "name": "passive_updates", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "per_property_flush_actions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "uow"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.DependencyProcessor.per_property_flush_actions", "name": "per_property_flush_actions", "type": null}}, "per_property_preprocessors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "uow"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.DependencyProcessor.per_property_preprocessors", "name": "per_property_preprocessors", "type": null}}, "per_state_flush_actions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "uow", "states", "isdelete"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.DependencyProcessor.per_state_flush_actions", "name": "per_state_flush_actions", "type": null}}, "post_update": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.dependency.DependencyProcessor.post_update", "name": "post_update", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "presort_deletes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "uowcommit", "states"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.DependencyProcessor.presort_deletes", "name": "presort_deletes", "type": null}}, "presort_saves": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "uowcommit", "states"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.DependencyProcessor.presort_saves", "name": "presort_saves", "type": null}}, "process_deletes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "uowcommit", "states"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.DependencyProcessor.process_deletes", "name": "process_deletes", "type": null}}, "process_saves": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "uowcommit", "states"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.DependencyProcessor.process_saves", "name": "process_saves", "type": null}}, "prop": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.dependency.DependencyProcessor.prop", "name": "prop", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "prop_has_changes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "uowcommit", "states", "isdelete"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.DependencyProcessor.prop_has_changes", "name": "prop_has_changes", "type": null}}, "secondary": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.dependency.DependencyProcessor.secondary", "name": "secondary", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "sort_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.dependency.DependencyProcessor.sort_key", "name": "sort_key", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.dependency.DependencyProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.dependency.DependencyProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DetectKeySwitch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.dependency.DependencyProcessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.dependency.DetectKeySwitch", "name": "DetectKeySwitch", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.dependency.DetectKeySwitch", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.dependency", "mro": ["sqlalchemy.orm.dependency.DetectKeySwitch", "sqlalchemy.orm.dependency.DependencyProcessor", "builtins.object"], "names": {".class": "SymbolTable", "_key_switchers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "uow", "states"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.DetectKeySwitch._key_switchers", "name": "_key_switchers", "type": null}}, "_pks_changed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "uowcommit", "state"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.DetectKeySwitch._pks_changed", "name": "_pks_changed", "type": null}}, "_process_key_switches": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "deplist", "uowcommit"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.DetectKeySwitch._process_key_switches", "name": "_process_key_switches", "type": null}}, "per_property_flush_actions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "uow"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.DetectKeySwitch.per_property_flush_actions", "name": "per_property_flush_actions", "type": null}}, "per_property_preprocessors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "uow"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.DetectKeySwitch.per_property_preprocessors", "name": "per_property_preprocessors", "type": null}}, "per_state_flush_actions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "uow", "states", "isdelete"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.DetectKeySwitch.per_state_flush_actions", "name": "per_state_flush_actions", "type": null}}, "presort_deletes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "uowcommit", "states"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.DetectKeySwitch.presort_deletes", "name": "presort_deletes", "type": null}}, "presort_saves": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "uow", "states"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.DetectKeySwitch.presort_saves", "name": "presort_saves", "type": null}}, "process_deletes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "uowcommit", "states"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.DetectKeySwitch.process_deletes", "name": "process_deletes", "type": null}}, "process_saves": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "uowcommit", "states"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.DetectKeySwitch.process_saves", "name": "process_saves", "type": null}}, "prop_has_changes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "uow", "states", "isdelete"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.DetectKeySwitch.prop_has_changes", "name": "prop_has_changes", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.dependency.DetectKeySwitch.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.dependency.DetectKeySwitch", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MANYTOMANY": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.MANYTOMANY", "kind": "Gdef"}, "MANYTOONE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.MANYTOONE", "kind": "Gdef"}, "ManyToManyDP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.dependency.DependencyProcessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.dependency.ManyToManyDP", "name": "ManyToManyDP", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.dependency.ManyToManyDP", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.dependency", "mro": ["sqlalchemy.orm.dependency.ManyToManyDP", "sqlalchemy.orm.dependency.DependencyProcessor", "builtins.object"], "names": {".class": "SymbolTable", "_pks_changed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "uowcommit", "state"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.ManyToManyDP._pks_changed", "name": "_pks_changed", "type": null}}, "_run_crud": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "uowcommit", "secondary_insert", "secondary_update", "secondary_delete"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.ManyToManyDP._run_crud", "name": "_run_crud", "type": null}}, "_synchronize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "state", "child", "associationrow", "clearkeys", "uowcommit", "operation"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.ManyToManyDP._synchronize", "name": "_synchronize", "type": null}}, "per_property_dependencies": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "uow", "parent_saves", "child_saves", "parent_deletes", "child_deletes", "after_save", "before_delete"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.ManyToManyDP.per_property_dependencies", "name": "per_property_dependencies", "type": null}}, "per_state_dependencies": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "uow", "save_parent", "delete_parent", "child_action", "after_save", "before_delete", "isdelete", "<PERSON><PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.ManyToManyDP.per_state_dependencies", "name": "per_state_dependencies", "type": null}}, "presort_deletes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "uowcommit", "states"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.ManyToManyDP.presort_deletes", "name": "presort_deletes", "type": null}}, "presort_saves": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "uowcommit", "states"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.ManyToManyDP.presort_saves", "name": "presort_saves", "type": null}}, "process_deletes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "uowcommit", "states"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.ManyToManyDP.process_deletes", "name": "process_deletes", "type": null}}, "process_saves": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "uowcommit", "states"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.ManyToManyDP.process_saves", "name": "process_saves", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.dependency.ManyToManyDP.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.dependency.ManyToManyDP", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ManyToOneDP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.dependency.DependencyProcessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.dependency.ManyToOneDP", "name": "ManyToOneDP", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.dependency.ManyToOneDP", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.dependency", "mro": ["sqlalchemy.orm.dependency.ManyToOneDP", "sqlalchemy.orm.dependency.DependencyProcessor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "prop"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.ManyToOneDP.__init__", "name": "__init__", "type": null}}, "_synchronize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "state", "child", "associationrow", "clearkeys", "uowcommit", "operation"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.ManyToOneDP._synchronize", "name": "_synchronize", "type": null}}, "per_property_dependencies": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "uow", "parent_saves", "child_saves", "parent_deletes", "child_deletes", "after_save", "before_delete"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.ManyToOneDP.per_property_dependencies", "name": "per_property_dependencies", "type": null}}, "per_state_dependencies": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "uow", "save_parent", "delete_parent", "child_action", "after_save", "before_delete", "isdelete", "<PERSON><PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.ManyToOneDP.per_state_dependencies", "name": "per_state_dependencies", "type": null}}, "presort_deletes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "uowcommit", "states"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.ManyToOneDP.presort_deletes", "name": "presort_deletes", "type": null}}, "presort_saves": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "uowcommit", "states"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.ManyToOneDP.presort_saves", "name": "presort_saves", "type": null}}, "process_deletes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "uowcommit", "states"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.ManyToOneDP.process_deletes", "name": "process_deletes", "type": null}}, "process_saves": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "uowcommit", "states"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.ManyToOneDP.process_saves", "name": "process_saves", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.dependency.ManyToOneDP.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.dependency.ManyToOneDP", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ONETOMANY": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.ONETOMANY", "kind": "Gdef"}, "OneToManyDP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.dependency.DependencyProcessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.dependency.OneToManyDP", "name": "OneToManyDP", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.dependency.OneToManyDP", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.dependency", "mro": ["sqlalchemy.orm.dependency.OneToManyDP", "sqlalchemy.orm.dependency.DependencyProcessor", "builtins.object"], "names": {".class": "SymbolTable", "_pks_changed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "uowcommit", "state"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.OneToManyDP._pks_changed", "name": "_pks_changed", "type": null}}, "_synchronize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "state", "child", "associationrow", "clearkeys", "uowcommit", "pks_changed"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.OneToManyDP._synchronize", "name": "_synchronize", "type": null}}, "per_property_dependencies": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "uow", "parent_saves", "child_saves", "parent_deletes", "child_deletes", "after_save", "before_delete"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.OneToManyDP.per_property_dependencies", "name": "per_property_dependencies", "type": null}}, "per_state_dependencies": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "uow", "save_parent", "delete_parent", "child_action", "after_save", "before_delete", "isdelete", "<PERSON><PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.OneToManyDP.per_state_dependencies", "name": "per_state_dependencies", "type": null}}, "presort_deletes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "uowcommit", "states"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.OneToManyDP.presort_deletes", "name": "presort_deletes", "type": null}}, "presort_saves": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "uowcommit", "states"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.OneToManyDP.presort_saves", "name": "presort_saves", "type": null}}, "process_deletes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "uowcommit", "states"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.OneToManyDP.process_deletes", "name": "process_deletes", "type": null}}, "process_saves": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "uowcommit", "states"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.dependency.OneToManyDP.process_saves", "name": "process_saves", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.dependency.OneToManyDP.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.dependency.OneToManyDP", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.dependency.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.dependency.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.dependency.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.dependency.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.dependency.__package__", "name": "__package__", "type": "builtins.str"}}, "_direction_to_processor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.dependency._direction_to_processor", "name": "_direction_to_processor", "type": {".class": "Instance", "args": ["sqlalchemy.orm.base.RelationshipDirection", {".class": "CallableType", "arg_kinds": [0], "arg_names": ["prop"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["sqlalchemy.orm.dependency.ManyToManyDP"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.orm.dependency.DependencyProcessor", "type_guard": null, "unpack_kwargs": false, "variables": []}], "type_ref": "builtins.dict"}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "attributes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.attributes", "kind": "Gdef"}, "exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.exc", "kind": "Gdef"}, "mapperutil": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.util", "kind": "Gdef"}, "sa_exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc", "kind": "Gdef"}, "sql": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql", "kind": "Gdef"}, "sync": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.sync", "kind": "Gdef"}, "unitofwork": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.unitofwork", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\dependency.py"}