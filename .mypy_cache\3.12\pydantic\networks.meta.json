{"data_mtime": 1759503836, "dep_lines": [26, 26, 9, 13, 24, 26, 27, 28, 29, 30, 3, 5, 6, 8, 10, 11, 13, 22, 1, 1, 1, 1, 1, 1, 1, 1, 1, 33], "dep_prios": [10, 10, 5, 10, 5, 20, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 20, 20, 30, 30, 30, 30, 30, 30, 25], "dependencies": ["pydantic._internal._repr", "pydantic._internal._schema_generation_shared", "importlib.metadata", "pydantic_core.core_schema", "pydantic.errors", "pydantic._internal", "pydantic._migration", "pydantic.annotated_handlers", "pydantic.json_schema", "pydantic.type_adapter", "__future__", "dataclasses", "re", "functools", "ipaddress", "typing", "pydantic_core", "typing_extensions", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "_typeshed", "abc", "pydantic.v1", "pydantic.v1.networks", "pydantic_core._pydantic_core"], "hash": "fd8a529c147690c7e8597efab1dab7e1c7c21fe316af9a2bd2f7b4b68c3b396a", "id": "pydantic.networks", "ignore_all": true, "interface_hash": "02de3280c90756659f73019d64d9a91f6ff23c59de81a6634c7fc2fd10535202", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\networks.py", "plugin_data": null, "size": 41446, "suppressed": ["email_validator"], "version_id": "1.8.0"}