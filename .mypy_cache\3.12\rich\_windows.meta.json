{"data_mtime": 1759503832, "dep_lines": [25, 1, 2, 16, 65, 68, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 10, 5, 5, 20, 20, 30, 30], "dependencies": ["rich._win32_console", "sys", "dataclasses", "ctypes", "platform", "rich", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "typing"], "hash": "8acdd6a5b1cc8fc59a4c7601d7585cea53f6b78865bede135624e51d29a3b22d", "id": "rich._windows", "ignore_all": true, "interface_hash": "281fd59c99d424a2b9018050f4713f02098db4d23b8de3166439bc58dc134d65", "mtime": 1757092238, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\rich\\_windows.py", "plugin_data": null, "size": 1901, "suppressed": [], "version_id": "1.8.0"}