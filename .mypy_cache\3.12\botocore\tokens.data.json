{".class": "MypyFile", "_fullname": "botocore.tokens", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DeferredRefreshableToken": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.tokens.DeferredRefreshableToken", "name": "DeferredRefreshableToken", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.tokens.DeferredRefreshableToken", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.tokens", "mro": ["botocore.tokens.DeferredRefreshableToken", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "method", "refresh_using", "time_fetcher"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.tokens.DeferredRefreshableToken.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "method", "refresh_using", "time_fetcher"], "arg_types": ["botocore.tokens.DeferredRefreshableToken", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.tokens.FrozenAuthToken"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "datetime.datetime", "type_guard": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DeferredRefreshableToken", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_frozen_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.tokens.DeferredRefreshableToken.get_frozen_token", "name": "get_frozen_token", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.tokens.DeferredRefreshableToken"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_frozen_token of DeferredRefreshableToken", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.tokens.FrozenAuthToken"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.tokens.DeferredRefreshableToken.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.tokens.DeferredRefreshableToken", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FrozenAuthToken": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "datetime.datetime"]}], "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.tokens.FrozenAuthToken", "name": "FrozenAuthToken", "type_vars": []}, "deletable_attributes": [], "flags": ["is_named_tuple"], "fullname": "botocore.tokens.FrozenAuthToken", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["token", "expiration"]}}, "module_name": "botocore.tokens", "mro": ["botocore.tokens.FrozenAuthToken", "builtins.tuple", "typing.Sequence", "typing.Collection", "typing.Reversible", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.tokens.FrozenAuthToken._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "datetime.datetime"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "botocore.tokens.FrozenAuthToken.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "botocore.tokens.FrozenAuthToken.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "botocore.tokens.FrozenAuthToken.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "token"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "expiration"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["_cls", "token", "expiration"], "dataclass_transform_spec": null, "flags": ["is_static"], "fullname": "botocore.tokens.FrozenAuthToken.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["_cls", "token", "expiration"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.tokens.FrozenAuthToken._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "datetime.datetime"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "builtins.str", {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of FrozenAuthToken", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.tokens.FrozenAuthToken._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "datetime.datetime"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.tokens.FrozenAuthToken._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "datetime.datetime"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.tokens.FrozenAuthToken._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.tokens.FrozenAuthToken._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "datetime.datetime"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of FrozenAuthToken", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.tokens.FrozenAuthToken._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "datetime.datetime"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "botocore.tokens.FrozenAuthToken._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "botocore.tokens.FrozenAuthToken._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "botocore.tokens.FrozenAuthToken._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "botocore.tokens.FrozenAuthToken._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.tokens.FrozenAuthToken._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "datetime.datetime"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of FrozenAuthToken", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.tokens.FrozenAuthToken._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "datetime.datetime"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.tokens.FrozenAuthToken._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "datetime.datetime"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "botocore.tokens.FrozenAuthToken._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.tokens.FrozenAuthToken._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "datetime.datetime"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of FrozenAuthToken", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.tokens.FrozenAuthToken._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "datetime.datetime"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.tokens.FrozenAuthToken._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "datetime.datetime"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["_self", "token", "expiration"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.tokens.FrozenAuthToken._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["_self", "token", "expiration"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.tokens.FrozenAuthToken._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "datetime.datetime"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "builtins.str", {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of FrozenAuthToken", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.tokens.FrozenAuthToken._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "datetime.datetime"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.tokens.FrozenAuthToken._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "datetime.datetime"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "botocore.tokens.FrozenAuthToken._source", "name": "_source", "type": "builtins.str"}}, "expiration": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "botocore.tokens.FrozenAuthToken.expiration", "name": "expiration", "type": {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}]}}}, "expiration-redefinition": {".class": "SymbolTableNode", "cross_ref": "botocore.tokens.FrozenAuthToken.expiration", "kind": "<PERSON><PERSON><PERSON>"}, "token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "botocore.tokens.FrozenAuthToken.token", "name": "token", "type": "builtins.str"}}, "token-redefinition": {".class": "SymbolTableNode", "cross_ref": "botocore.tokens.FrozenAuthToken.token", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.tokens.FrozenAuthToken.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}]}], "partial_fallback": "botocore.tokens.FrozenAuthToken"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "datetime.datetime"]}], "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "JSONFileCache": {".class": "SymbolTableNode", "cross_ref": "botocore.utils.JSONFileCache", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NamedTuple": {".class": "SymbolTableNode", "cross_ref": "typing.NamedTuple", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SSOTokenProvider": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.tokens.SSOTokenProvider", "name": "SSOTokenProvider", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.tokens.SSOTokenProvider", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.tokens", "mro": ["botocore.tokens.SSOTokenProvider", "builtins.object"], "names": {".class": "SymbolTable", "DEFAULT_CACHE_CLS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.tokens.SSOTokenProvider.DEFAULT_CACHE_CLS", "name": "DEFAULT_CACHE_CLS", "type": {".class": "TypeType", "item": "botocore.utils.JSONFileCache"}}}, "METHOD": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.tokens.SSOTokenProvider.METHOD", "name": "METHOD", "type": "builtins.str"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "session", "cache", "time_fetcher", "profile_name"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.tokens.SSOTokenProvider.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "session", "cache", "time_fetcher", "profile_name"], "arg_types": ["botocore.tokens.SSOTokenProvider", "botocore.session.Session", {".class": "UnionType", "items": ["botocore.utils.JSONFileCache", {".class": "NoneType"}]}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "datetime.datetime", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SSOTokenProvider", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "load_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.tokens.SSOTokenProvider.load_token", "name": "load_token", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.tokens.SSOTokenProvider"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_token of SSOTokenProvider", "ret_type": "botocore.tokens.DeferredRefreshableToken", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.tokens.SSOTokenProvider.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.tokens.SSOTokenProvider", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ScopedEnvTokenProvider": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.tokens.ScopedEnvTokenProvider", "name": "ScopedEnvTokenProvider", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.tokens.ScopedEnvTokenProvider", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.tokens", "mro": ["botocore.tokens.ScopedEnvTokenProvider", "builtins.object"], "names": {".class": "SymbolTable", "METHOD": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.tokens.ScopedEnvTokenProvider.METHOD", "name": "METHOD", "type": "builtins.str"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "session", "environ"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.tokens.ScopedEnvTokenProvider.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "session", "environ"], "arg_types": ["botocore.tokens.ScopedEnvTokenProvider", "botocore.session.Session", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ScopedEnvTokenProvider", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "load_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.tokens.ScopedEnvTokenProvider.load_token", "name": "load_token", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["botocore.tokens.ScopedEnvTokenProvider", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_token of ScopedEnvTokenProvider", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "botocore.tokens.FrozenAuthToken"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.tokens.ScopedEnvTokenProvider.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.tokens.ScopedEnvTokenProvider", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Session": {".class": "SymbolTableNode", "cross_ref": "botocore.session.Session", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TokenProviderChain": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.tokens.TokenProviderChain", "name": "TokenProviderChain", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.tokens.TokenProviderChain", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.tokens", "mro": ["botocore.tokens.TokenProviderChain", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "providers"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.tokens.TokenProviderChain.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "providers"], "arg_types": ["botocore.tokens.TokenProviderChain", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Iterable"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TokenProviderChain", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "load_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.tokens.TokenProviderChain.load_token", "name": "load_token", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.tokens.TokenProviderChain"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_token of TokenProviderChain", "ret_type": "botocore.tokens.DeferredRefreshableToken", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.tokens.TokenProviderChain.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.tokens.TokenProviderChain", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.tokens.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.tokens.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.tokens.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.tokens.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.tokens.__package__", "name": "__package__", "type": "builtins.str"}}, "create_token_resolver": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["session"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.tokens.create_token_resolver", "name": "create_token_resolver", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["session"], "arg_types": ["botocore.session.Session"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_token_resolver", "ret_type": "botocore.tokens.TokenProviderChain", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime", "kind": "Gdef", "module_hidden": true, "module_public": false}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.tokens.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\botocore-stubs\\tokens.pyi"}