{"data_mtime": 1759503833, "dep_lines": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 10, 10, 5, 20, 20, 30, 30, 30], "dependencies": ["dateutil.easter", "dateutil.parser", "dateutil.<PERSON>del<PERSON>", "dateutil.rrule", "dateutil.tz", "dateutil.utils", "dateutil.zoneinfo", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "typing", "zoneinfo"], "hash": "ac5c768ee7d6a62e7f853f2fb1f0d75710a37603d87976eb798adbaae54c83b1", "id": "dateutil", "ignore_all": true, "interface_hash": "583877f229efc3aca71a592a0039cc2b954c6b47ee7aa2e153b1c5e4f79b1649", "mtime": 1759341510, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\dateutil-stubs\\__init__.pyi", "plugin_data": null, "size": 197, "suppressed": [], "version_id": "1.8.0"}