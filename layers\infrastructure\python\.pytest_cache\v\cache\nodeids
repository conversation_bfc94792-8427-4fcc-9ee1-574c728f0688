["tests/test_database_connection.py::TestAsyncSessionManager::test_async_session_manager_context", "tests/test_database_connection.py::TestAsyncSessionManager::test_get_async_session_function", "tests/test_database_connection.py::TestDatabaseConnection::test_database_connection_close", "tests/test_database_connection.py::TestDatabaseConnection::test_database_connection_connect", "tests/test_database_connection.py::TestDatabaseConnection::test_database_connection_initialization", "tests/test_database_connection.py::TestDatabaseConnection::test_database_connection_test", "tests/test_database_connection.py::TestDatabaseConnection::test_engine_caching", "tests/test_database_connection.py::TestDatabaseConnection::test_generate_iam_token", "tests/test_database_connection.py::TestDatabaseConnection::test_get_async_engine_connection_pooling", "tests/test_database_connection.py::TestDatabaseConnection::test_get_async_engine_ssl_configuration", "tests/test_database_connection.py::TestDatabaseConnection::test_get_async_engine_with_iam_auth", "tests/test_database_connection.py::TestDatabaseConnection::test_get_async_engine_with_password_auth", "tests/test_database_connection.py::TestDatabaseConnection::test_get_database_connection_singleton", "tests/test_database_connection.py::TestDatabaseConnection::test_ssl_context_creation", "tests/test_database_connection.py::TestDatabaseConnection::test_test_connection_failure", "tests/test_database_connection.py::TestDatabaseConnection::test_test_connection_success", "tests/test_database_models.py::TestDatabaseModels::test_all_expected_tables_exist", "tests/test_database_models.py::TestDatabaseModels::test_dim_agent_event_model_creation", "tests/test_database_models.py::TestDatabaseModels::test_dim_agent_event_with_ring_group", "tests/test_database_models.py::TestDatabaseModels::test_dim_agent_model_creation", "tests/test_database_models.py::TestDatabaseModels::test_dim_ring_group_model_creation", "tests/test_database_models.py::TestDatabaseModels::test_dim_tenant_model_creation", "tests/test_database_models.py::TestDatabaseModels::test_dim_tenant_unique_constraint", "tests/test_database_models.py::TestDatabaseModels::test_metadata_schema_configuration", "tests/test_database_models.py::TestDatabaseModels::test_scd_type2_pattern_support", "tests/test_repositories.py::TestAgentEventRepository::test_create_batch_events", "tests/test_repositories.py::TestAgentEventRepository::test_create_event_with_ring_group", "tests/test_repositories.py::TestAgentEventRepository::test_create_event_without_ring_group", "tests/test_repositories.py::TestAgentEventRepository::test_get_by_hash_found", "tests/test_repositories.py::TestAgentEventRepository::test_get_by_hash_not_found", "tests/test_repositories.py::TestAgentRepository::test_create_agent", "tests/test_repositories.py::TestAgentRepository::test_get_by_name_and_tenant_found", "tests/test_repositories.py::TestAgentRepository::test_get_or_create_new_agent", "tests/test_repositories.py::TestRingGroupRepository::test_create_ring_group", "tests/test_repositories.py::TestRingGroupRepository::test_get_by_name_and_tenant_found", "tests/test_repositories.py::TestRingGroupRepository::test_get_or_create_with_uri_extraction", "tests/test_repositories.py::TestTenantRepository::test_create_tenant", "tests/test_repositories.py::TestTenantRepository::test_get_by_name_found", "tests/test_repositories.py::TestTenantRepository::test_get_by_name_not_found", "tests/test_repositories.py::TestTenantRepository::test_get_or_create_existing_tenant", "tests/test_repositories.py::TestTenantRepository::test_get_or_create_new_tenant", "tests/test_repositroy.py::TestRepositories::test_agent_event_repository_add", "tests/test_repositroy.py::TestRepositories::test_agent_repository_get_or_create", "tests/test_repositroy.py::TestRepositories::test_ring_group_repository_get_or_create", "tests/test_repositroy.py::TestRepositories::test_tenant_repository_get_or_create", "tests/test_unit_of_work.py::TestSqlAlchemyUnitOfWork::test_unit_of_work_context_manager_exception", "tests/test_unit_of_work.py::TestSqlAlchemyUnitOfWork::test_unit_of_work_context_manager_success", "tests/test_unit_of_work.py::TestSqlAlchemyUnitOfWork::test_unit_of_work_initialization"]