{"data_mtime": 1759504552, "dep_lines": [7, 6, 3, 5, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 10, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["smartanalytics_utilities.config.settings", "dateutil.parser", "datetime", "pytz", "dateutil", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "dateutil.parser._parser", "functools", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "pydantic_settings", "pydantic_settings.main", "pytz.exceptions", "pytz.tzinfo", "smartanalytics_utilities.config", "smartanalytics_utilities.config.base", "typing", "typing_extensions"], "hash": "cf02b13252f2a81b90aa8c9d88c2043870c5da717de4c21f9add193bace7ac27", "id": "smartanalytics_utilities.utils.datetime_helper", "ignore_all": false, "interface_hash": "1e6e5a37e031822f1408b16283d9f569b8662e99e02ec84deb7c5f5eccd6cfbe", "mtime": 1759502092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "layers\\utilities\\python\\smartanalytics_utilities\\utils\\datetime_helper.py", "plugin_data": null, "size": 5076, "suppressed": [], "version_id": "1.8.0"}