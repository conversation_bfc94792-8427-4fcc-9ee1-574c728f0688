{".class": "MypyFile", "_fullname": "mypy_boto3_rds.client", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "AccountAttributesMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.AccountAttributesMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "AddRoleToDBClusterMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.AddRoleToDBClusterMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "AddRoleToDBInstanceMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.AddRoleToDBInstanceMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "AddSourceIdentifierToSubscriptionMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.AddSourceIdentifierToSubscriptionMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "AddSourceIdentifierToSubscriptionResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.AddSourceIdentifierToSubscriptionResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "AddTagsToResourceMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.AddTagsToResourceMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ApplyPendingMaintenanceActionMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ApplyPendingMaintenanceActionMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ApplyPendingMaintenanceActionResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ApplyPendingMaintenanceActionResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "AuthorizeDBSecurityGroupIngressMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.AuthorizeDBSecurityGroupIngressMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "AuthorizeDBSecurityGroupIngressResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.AuthorizeDBSecurityGroupIngressResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BacktrackDBClusterMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.BacktrackDBClusterMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseClient": {".class": "SymbolTableNode", "cross_ref": "botocore.client.BaseClient", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseClientExceptions": {".class": "SymbolTableNode", "cross_ref": "botocore.errorfactory.BaseClientExceptions", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BotocoreClientError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.ClientError", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CancelExportTaskMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CancelExportTaskMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CertificateMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CertificateMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ClientMeta": {".class": "SymbolTableNode", "cross_ref": "botocore.client.ClientMeta", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CopyDBClusterParameterGroupMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CopyDBClusterParameterGroupMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CopyDBClusterParameterGroupResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CopyDBClusterParameterGroupResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CopyDBClusterSnapshotMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CopyDBClusterSnapshotMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CopyDBClusterSnapshotResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CopyDBClusterSnapshotResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CopyDBParameterGroupMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CopyDBParameterGroupMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CopyDBParameterGroupResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CopyDBParameterGroupResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CopyDBSnapshotMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CopyDBSnapshotMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CopyDBSnapshotResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CopyDBSnapshotResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CopyOptionGroupMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CopyOptionGroupMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CopyOptionGroupResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CopyOptionGroupResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CreateBlueGreenDeploymentRequestTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CreateBlueGreenDeploymentRequestTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CreateBlueGreenDeploymentResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CreateBlueGreenDeploymentResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CreateCustomDBEngineVersionMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CreateCustomDBEngineVersionMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CreateDBClusterEndpointMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CreateDBClusterEndpointMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CreateDBClusterMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CreateDBClusterMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CreateDBClusterParameterGroupMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CreateDBClusterParameterGroupMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CreateDBClusterParameterGroupResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CreateDBClusterParameterGroupResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CreateDBClusterResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CreateDBClusterResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CreateDBClusterSnapshotMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CreateDBClusterSnapshotMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CreateDBClusterSnapshotResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CreateDBClusterSnapshotResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CreateDBInstanceMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CreateDBInstanceMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CreateDBInstanceReadReplicaMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CreateDBInstanceReadReplicaMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CreateDBInstanceReadReplicaResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CreateDBInstanceReadReplicaResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CreateDBInstanceResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CreateDBInstanceResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CreateDBParameterGroupMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CreateDBParameterGroupMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CreateDBParameterGroupResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CreateDBParameterGroupResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CreateDBProxyEndpointRequestTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CreateDBProxyEndpointRequestTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CreateDBProxyEndpointResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CreateDBProxyEndpointResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CreateDBProxyRequestTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CreateDBProxyRequestTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CreateDBProxyResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CreateDBProxyResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CreateDBSecurityGroupMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CreateDBSecurityGroupMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CreateDBSecurityGroupResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CreateDBSecurityGroupResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CreateDBShardGroupMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CreateDBShardGroupMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CreateDBSnapshotMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CreateDBSnapshotMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CreateDBSnapshotResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CreateDBSnapshotResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CreateDBSubnetGroupMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CreateDBSubnetGroupMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CreateDBSubnetGroupResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CreateDBSubnetGroupResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CreateEventSubscriptionMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CreateEventSubscriptionMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CreateEventSubscriptionResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CreateEventSubscriptionResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CreateGlobalClusterMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CreateGlobalClusterMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CreateGlobalClusterResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CreateGlobalClusterResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CreateIntegrationMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CreateIntegrationMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CreateOptionGroupMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CreateOptionGroupMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CreateOptionGroupResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CreateOptionGroupResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CreateTenantDatabaseMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CreateTenantDatabaseMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CreateTenantDatabaseResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CreateTenantDatabaseResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBClusterAutomatedBackupMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DBClusterAutomatedBackupMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBClusterAvailableWaiter": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.waiter.DBClusterAvailableWaiter", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBClusterBacktrackMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DBClusterBacktrackMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBClusterBacktrackResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DBClusterBacktrackResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBClusterCapacityInfoTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DBClusterCapacityInfoTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBClusterDeletedWaiter": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.waiter.DBClusterDeletedWaiter", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBClusterEndpointMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DBClusterEndpointMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBClusterEndpointResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DBClusterEndpointResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBClusterMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DBClusterMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBClusterParameterGroupDetailsTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DBClusterParameterGroupDetailsTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBClusterParameterGroupNameMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DBClusterParameterGroupNameMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBClusterParameterGroupsMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DBClusterParameterGroupsMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBClusterSnapshotAvailableWaiter": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.waiter.DBClusterSnapshotAvailableWaiter", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBClusterSnapshotDeletedWaiter": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.waiter.DBClusterSnapshotDeletedWaiter", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBClusterSnapshotMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DBClusterSnapshotMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBEngineVersionMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DBEngineVersionMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBEngineVersionResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DBEngineVersionResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBInstanceAutomatedBackupMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DBInstanceAutomatedBackupMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBInstanceAvailableWaiter": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.waiter.DBInstanceAvailableWaiter", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBInstanceDeletedWaiter": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.waiter.DBInstanceDeletedWaiter", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBInstanceMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DBInstanceMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBParameterGroupDetailsTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DBParameterGroupDetailsTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBParameterGroupNameMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DBParameterGroupNameMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBParameterGroupsMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DBParameterGroupsMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBRecommendationMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DBRecommendationMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBRecommendationsMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DBRecommendationsMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBSecurityGroupMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DBSecurityGroupMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBShardGroupResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DBShardGroupResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBSnapshotAvailableWaiter": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.waiter.DBSnapshotAvailableWaiter", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBSnapshotCompletedWaiter": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.waiter.DBSnapshotCompletedWaiter", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBSnapshotDeletedWaiter": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.waiter.DBSnapshotDeletedWaiter", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBSnapshotMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DBSnapshotMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBSnapshotTenantDatabasesMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DBSnapshotTenantDatabasesMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBSubnetGroupMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DBSubnetGroupMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DeleteBlueGreenDeploymentRequestTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DeleteBlueGreenDeploymentRequestTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DeleteBlueGreenDeploymentResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DeleteBlueGreenDeploymentResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DeleteCustomDBEngineVersionMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DeleteCustomDBEngineVersionMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DeleteDBClusterAutomatedBackupMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DeleteDBClusterAutomatedBackupMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DeleteDBClusterAutomatedBackupResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DeleteDBClusterAutomatedBackupResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DeleteDBClusterEndpointMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DeleteDBClusterEndpointMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DeleteDBClusterMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DeleteDBClusterMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DeleteDBClusterParameterGroupMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DeleteDBClusterParameterGroupMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DeleteDBClusterResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DeleteDBClusterResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DeleteDBClusterSnapshotMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DeleteDBClusterSnapshotMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DeleteDBClusterSnapshotResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DeleteDBClusterSnapshotResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DeleteDBInstanceAutomatedBackupMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DeleteDBInstanceAutomatedBackupMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DeleteDBInstanceAutomatedBackupResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DeleteDBInstanceAutomatedBackupResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DeleteDBInstanceMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DeleteDBInstanceMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DeleteDBInstanceResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DeleteDBInstanceResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DeleteDBParameterGroupMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DeleteDBParameterGroupMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DeleteDBProxyEndpointRequestTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DeleteDBProxyEndpointRequestTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DeleteDBProxyEndpointResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DeleteDBProxyEndpointResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DeleteDBProxyRequestTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DeleteDBProxyRequestTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DeleteDBProxyResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DeleteDBProxyResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DeleteDBSecurityGroupMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DeleteDBSecurityGroupMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DeleteDBShardGroupMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DeleteDBShardGroupMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DeleteDBSnapshotMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DeleteDBSnapshotMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DeleteDBSnapshotResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DeleteDBSnapshotResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DeleteDBSubnetGroupMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DeleteDBSubnetGroupMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DeleteEventSubscriptionMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DeleteEventSubscriptionMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DeleteEventSubscriptionResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DeleteEventSubscriptionResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DeleteGlobalClusterMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DeleteGlobalClusterMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DeleteGlobalClusterResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DeleteGlobalClusterResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DeleteIntegrationMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DeleteIntegrationMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DeleteOptionGroupMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DeleteOptionGroupMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DeleteTenantDatabaseMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DeleteTenantDatabaseMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DeleteTenantDatabaseResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DeleteTenantDatabaseResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DeregisterDBProxyTargetsRequestTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DeregisterDBProxyTargetsRequestTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeBlueGreenDeploymentsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeBlueGreenDeploymentsPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeBlueGreenDeploymentsRequestTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeBlueGreenDeploymentsRequestTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeBlueGreenDeploymentsResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeBlueGreenDeploymentsResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeCertificatesMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeCertificatesMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeCertificatesPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeCertificatesPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBClusterAutomatedBackupsMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBClusterAutomatedBackupsMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBClusterAutomatedBackupsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBClusterAutomatedBackupsPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBClusterBacktracksMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBClusterBacktracksMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBClusterBacktracksPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBClusterBacktracksPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBClusterEndpointsMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBClusterEndpointsMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBClusterEndpointsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBClusterEndpointsPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBClusterParameterGroupsMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBClusterParameterGroupsMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBClusterParameterGroupsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBClusterParameterGroupsPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBClusterParametersMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBClusterParametersMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBClusterParametersPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBClusterParametersPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBClusterSnapshotAttributesMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBClusterSnapshotAttributesMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBClusterSnapshotAttributesResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBClusterSnapshotAttributesResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBClusterSnapshotsMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBClusterSnapshotsMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBClusterSnapshotsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBClusterSnapshotsPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBClustersMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBClustersMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBClustersPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBClustersPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBEngineVersionsMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBEngineVersionsMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBEngineVersionsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBEngineVersionsPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBInstanceAutomatedBackupsMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBInstanceAutomatedBackupsMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBInstanceAutomatedBackupsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBInstanceAutomatedBackupsPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBInstancesMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBInstancesMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBInstancesPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBInstancesPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBLogFilesMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBLogFilesMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBLogFilesPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBLogFilesPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBLogFilesResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBLogFilesResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBMajorEngineVersionsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBMajorEngineVersionsPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBMajorEngineVersionsRequestTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBMajorEngineVersionsRequestTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBMajorEngineVersionsResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBMajorEngineVersionsResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBParameterGroupsMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBParameterGroupsMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBParameterGroupsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBParameterGroupsPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBParametersMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBParametersMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBParametersPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBParametersPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBProxiesPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBProxiesPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBProxiesRequestTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBProxiesRequestTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBProxiesResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBProxiesResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBProxyEndpointsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBProxyEndpointsPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBProxyEndpointsRequestTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBProxyEndpointsRequestTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBProxyEndpointsResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBProxyEndpointsResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBProxyTargetGroupsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBProxyTargetGroupsPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBProxyTargetGroupsRequestTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBProxyTargetGroupsRequestTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBProxyTargetGroupsResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBProxyTargetGroupsResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBProxyTargetsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBProxyTargetsPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBProxyTargetsRequestTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBProxyTargetsRequestTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBProxyTargetsResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBProxyTargetsResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBRecommendationsMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBRecommendationsMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBRecommendationsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBRecommendationsPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBSecurityGroupsMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBSecurityGroupsMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBSecurityGroupsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBSecurityGroupsPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBShardGroupsMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBShardGroupsMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBShardGroupsResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBShardGroupsResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBSnapshotAttributesMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBSnapshotAttributesMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBSnapshotAttributesResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBSnapshotAttributesResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBSnapshotTenantDatabasesMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBSnapshotTenantDatabasesMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBSnapshotTenantDatabasesPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBSnapshotTenantDatabasesPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBSnapshotsMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBSnapshotsMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBSnapshotsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBSnapshotsPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBSubnetGroupsMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBSubnetGroupsMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBSubnetGroupsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeDBSubnetGroupsPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeEngineDefaultClusterParametersMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeEngineDefaultClusterParametersMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeEngineDefaultClusterParametersPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeEngineDefaultClusterParametersPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeEngineDefaultClusterParametersResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeEngineDefaultClusterParametersResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeEngineDefaultParametersMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeEngineDefaultParametersMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeEngineDefaultParametersPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeEngineDefaultParametersPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeEngineDefaultParametersResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeEngineDefaultParametersResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeEventCategoriesMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeEventCategoriesMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeEventSubscriptionsMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeEventSubscriptionsMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeEventSubscriptionsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeEventSubscriptionsPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeEventsMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeEventsMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeEventsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeEventsPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeExportTasksMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeExportTasksMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeExportTasksPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeExportTasksPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeGlobalClustersMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeGlobalClustersMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeGlobalClustersPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeGlobalClustersPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeIntegrationsMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeIntegrationsMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeIntegrationsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeIntegrationsPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeIntegrationsResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeIntegrationsResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeOptionGroupOptionsMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeOptionGroupOptionsMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeOptionGroupOptionsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeOptionGroupOptionsPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeOptionGroupsMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeOptionGroupsMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeOptionGroupsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeOptionGroupsPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeOrderableDBInstanceOptionsMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeOrderableDBInstanceOptionsMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeOrderableDBInstanceOptionsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeOrderableDBInstanceOptionsPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribePendingMaintenanceActionsMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribePendingMaintenanceActionsMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribePendingMaintenanceActionsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribePendingMaintenanceActionsPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeReservedDBInstancesMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeReservedDBInstancesMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeReservedDBInstancesOfferingsMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeReservedDBInstancesOfferingsMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeReservedDBInstancesOfferingsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeReservedDBInstancesOfferingsPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeReservedDBInstancesPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeReservedDBInstancesPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeSourceRegionsMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeSourceRegionsMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeSourceRegionsPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeSourceRegionsPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeTenantDatabasesMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeTenantDatabasesMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeTenantDatabasesPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DescribeTenantDatabasesPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeValidDBInstanceModificationsMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeValidDBInstanceModificationsMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeValidDBInstanceModificationsResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeValidDBInstanceModificationsResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "builtins.dict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DisableHttpEndpointRequestTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DisableHttpEndpointRequestTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DisableHttpEndpointResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DisableHttpEndpointResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DownloadDBLogFilePortionDetailsTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DownloadDBLogFilePortionDetailsTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DownloadDBLogFilePortionMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DownloadDBLogFilePortionMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DownloadDBLogFilePortionPaginator": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.paginator.DownloadDBLogFilePortionPaginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "EmptyResponseMetadataTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.EmptyResponseMetadataTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "EnableHttpEndpointRequestTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.EnableHttpEndpointRequestTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "EnableHttpEndpointResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.EnableHttpEndpointResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "EventCategoriesMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.EventCategoriesMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "EventSubscriptionsMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.EventSubscriptionsMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "EventsMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.EventsMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Exceptions": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.errorfactory.BaseClientExceptions"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.client.Exceptions", "name": "Exceptions", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.client.Exceptions", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.client", "mro": ["mypy_boto3_rds.client.Exceptions", "botocore.errorfactory.BaseClientExceptions", "builtins.object"], "names": {".class": "SymbolTable", "AuthorizationAlreadyExistsFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.AuthorizationAlreadyExistsFault", "name": "AuthorizationAlreadyExistsFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "AuthorizationNotFoundFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.AuthorizationNotFoundFault", "name": "AuthorizationNotFoundFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "AuthorizationQuotaExceededFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.AuthorizationQuotaExceededFault", "name": "AuthorizationQuotaExceededFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "BackupPolicyNotFoundFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.BackupPolicyNotFoundFault", "name": "BackupPolicyNotFoundFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "BlueGreenDeploymentAlreadyExistsFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.BlueGreenDeploymentAlreadyExistsFault", "name": "BlueGreenDeploymentAlreadyExistsFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "BlueGreenDeploymentNotFoundFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.BlueGreenDeploymentNotFoundFault", "name": "BlueGreenDeploymentNotFoundFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "CertificateNotFoundFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.CertificateNotFoundFault", "name": "CertificateNotFoundFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "ClientError": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.ClientError", "name": "ClientError", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "CreateCustomDBEngineVersionFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.CreateCustomDBEngineVersionFault", "name": "CreateCustomDBEngineVersionFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "CustomAvailabilityZoneNotFoundFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.CustomAvailabilityZoneNotFoundFault", "name": "CustomAvailabilityZoneNotFoundFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "CustomDBEngineVersionAlreadyExistsFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.CustomDBEngineVersionAlreadyExistsFault", "name": "CustomDBEngineVersionAlreadyExistsFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "CustomDBEngineVersionNotFoundFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.CustomDBEngineVersionNotFoundFault", "name": "CustomDBEngineVersionNotFoundFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "CustomDBEngineVersionQuotaExceededFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.CustomDBEngineVersionQuotaExceededFault", "name": "CustomDBEngineVersionQuotaExceededFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBClusterAlreadyExistsFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBClusterAlreadyExistsFault", "name": "DBClusterAlreadyExistsFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBClusterAutomatedBackupNotFoundFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBClusterAutomatedBackupNotFoundFault", "name": "DBClusterAutomatedBackupNotFoundFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBClusterAutomatedBackupQuotaExceededFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBClusterAutomatedBackupQuotaExceededFault", "name": "DBClusterAutomatedBackupQuotaExceededFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBClusterBacktrackNotFoundFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBClusterBacktrackNotFoundFault", "name": "DBClusterBacktrackNotFoundFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBClusterEndpointAlreadyExistsFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBClusterEndpointAlreadyExistsFault", "name": "DBClusterEndpointAlreadyExistsFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBClusterEndpointNotFoundFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBClusterEndpointNotFoundFault", "name": "DBClusterEndpointNotFoundFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBClusterEndpointQuotaExceededFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBClusterEndpointQuotaExceededFault", "name": "DBClusterEndpointQuotaExceededFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBClusterNotFoundFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBClusterNotFoundFault", "name": "DBClusterNotFoundFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBClusterParameterGroupNotFoundFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBClusterParameterGroupNotFoundFault", "name": "DBClusterParameterGroupNotFoundFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBClusterQuotaExceededFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBClusterQuotaExceededFault", "name": "DBClusterQuotaExceededFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBClusterRoleAlreadyExistsFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBClusterRoleAlreadyExistsFault", "name": "DBClusterRoleAlreadyExistsFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBClusterRoleNotFoundFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBClusterRoleNotFoundFault", "name": "DBClusterRoleNotFoundFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBClusterRoleQuotaExceededFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBClusterRoleQuotaExceededFault", "name": "DBClusterRoleQuotaExceededFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBClusterSnapshotAlreadyExistsFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBClusterSnapshotAlreadyExistsFault", "name": "DBClusterSnapshotAlreadyExistsFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBClusterSnapshotNotFoundFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBClusterSnapshotNotFoundFault", "name": "DBClusterSnapshotNotFoundFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBInstanceAlreadyExistsFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBInstanceAlreadyExistsFault", "name": "DBInstanceAlreadyExistsFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBInstanceAutomatedBackupNotFoundFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBInstanceAutomatedBackupNotFoundFault", "name": "DBInstanceAutomatedBackupNotFoundFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBInstanceAutomatedBackupQuotaExceededFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBInstanceAutomatedBackupQuotaExceededFault", "name": "DBInstanceAutomatedBackupQuotaExceededFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBInstanceNotFoundFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBInstanceNotFoundFault", "name": "DBInstanceNotFoundFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBInstanceNotReadyFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBInstanceNotReadyFault", "name": "DBInstanceNotReadyFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBInstanceRoleAlreadyExistsFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBInstanceRoleAlreadyExistsFault", "name": "DBInstanceRoleAlreadyExistsFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBInstanceRoleNotFoundFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBInstanceRoleNotFoundFault", "name": "DBInstanceRoleNotFoundFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBInstanceRoleQuotaExceededFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBInstanceRoleQuotaExceededFault", "name": "DBInstanceRoleQuotaExceededFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBLogFileNotFoundFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBLogFileNotFoundFault", "name": "DBLogFileNotFoundFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBParameterGroupAlreadyExistsFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBParameterGroupAlreadyExistsFault", "name": "DBParameterGroupAlreadyExistsFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBParameterGroupNotFoundFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBParameterGroupNotFoundFault", "name": "DBParameterGroupNotFoundFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBParameterGroupQuotaExceededFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBParameterGroupQuotaExceededFault", "name": "DBParameterGroupQuotaExceededFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBProxyAlreadyExistsFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBProxyAlreadyExistsFault", "name": "DBProxyAlreadyExistsFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBProxyEndpointAlreadyExistsFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBProxyEndpointAlreadyExistsFault", "name": "DBProxyEndpointAlreadyExistsFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBProxyEndpointNotFoundFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBProxyEndpointNotFoundFault", "name": "DBProxyEndpointNotFoundFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBProxyEndpointQuotaExceededFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBProxyEndpointQuotaExceededFault", "name": "DBProxyEndpointQuotaExceededFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBProxyNotFoundFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBProxyNotFoundFault", "name": "DBProxyNotFoundFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBProxyQuotaExceededFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBProxyQuotaExceededFault", "name": "DBProxyQuotaExceededFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBProxyTargetAlreadyRegisteredFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBProxyTargetAlreadyRegisteredFault", "name": "DBProxyTargetAlreadyRegisteredFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBProxyTargetGroupNotFoundFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBProxyTargetGroupNotFoundFault", "name": "DBProxyTargetGroupNotFoundFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBProxyTargetNotFoundFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBProxyTargetNotFoundFault", "name": "DBProxyTargetNotFoundFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBSecurityGroupAlreadyExistsFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBSecurityGroupAlreadyExistsFault", "name": "DBSecurityGroupAlreadyExistsFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBSecurityGroupNotFoundFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBSecurityGroupNotFoundFault", "name": "DBSecurityGroupNotFoundFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBSecurityGroupNotSupportedFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBSecurityGroupNotSupportedFault", "name": "DBSecurityGroupNotSupportedFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBSecurityGroupQuotaExceededFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBSecurityGroupQuotaExceededFault", "name": "DBSecurityGroupQuotaExceededFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBShardGroupAlreadyExistsFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBShardGroupAlreadyExistsFault", "name": "DBShardGroupAlreadyExistsFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBShardGroupNotFoundFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBShardGroupNotFoundFault", "name": "DBShardGroupNotFoundFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBSnapshotAlreadyExistsFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBSnapshotAlreadyExistsFault", "name": "DBSnapshotAlreadyExistsFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBSnapshotNotFoundFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBSnapshotNotFoundFault", "name": "DBSnapshotNotFoundFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBSnapshotTenantDatabaseNotFoundFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBSnapshotTenantDatabaseNotFoundFault", "name": "DBSnapshotTenantDatabaseNotFoundFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBSubnetGroupAlreadyExistsFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBSubnetGroupAlreadyExistsFault", "name": "DBSubnetGroupAlreadyExistsFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBSubnetGroupDoesNotCoverEnoughAZs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBSubnetGroupDoesNotCoverEnoughAZs", "name": "DBSubnetGroupDoesNotCoverEnoughAZs", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBSubnetGroupNotAllowedFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBSubnetGroupNotAllowedFault", "name": "DBSubnetGroupNotAllowedFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBSubnetGroupNotFoundFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBSubnetGroupNotFoundFault", "name": "DBSubnetGroupNotFoundFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBSubnetGroupQuotaExceededFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBSubnetGroupQuotaExceededFault", "name": "DBSubnetGroupQuotaExceededFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBSubnetQuotaExceededFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBSubnetQuotaExceededFault", "name": "DBSubnetQuotaExceededFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DBUpgradeDependencyFailureFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DBUpgradeDependencyFailureFault", "name": "DBUpgradeDependencyFailureFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "DomainNotFoundFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.DomainNotFoundFault", "name": "DomainNotFoundFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "Ec2ImagePropertiesNotSupportedFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.Ec2ImagePropertiesNotSupportedFault", "name": "Ec2ImagePropertiesNotSupportedFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "EventSubscriptionQuotaExceededFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.EventSubscriptionQuotaExceededFault", "name": "EventSubscriptionQuotaExceededFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "ExportTaskAlreadyExistsFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.ExportTaskAlreadyExistsFault", "name": "ExportTaskAlreadyExistsFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "ExportTaskNotFoundFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.ExportTaskNotFoundFault", "name": "ExportTaskNotFoundFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "GlobalClusterAlreadyExistsFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.GlobalClusterAlreadyExistsFault", "name": "GlobalClusterAlreadyExistsFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "GlobalClusterNotFoundFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.GlobalClusterNotFoundFault", "name": "GlobalClusterNotFoundFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "GlobalClusterQuotaExceededFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.GlobalClusterQuotaExceededFault", "name": "GlobalClusterQuotaExceededFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "IamRoleMissingPermissionsFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.IamRoleMissingPermissionsFault", "name": "IamRoleMissingPermissionsFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "IamRoleNotFoundFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.IamRoleNotFoundFault", "name": "IamRoleNotFoundFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "InstanceQuotaExceededFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.InstanceQuotaExceededFault", "name": "InstanceQuotaExceededFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "InsufficientAvailableIPsInSubnetFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.InsufficientAvailableIPsInSubnetFault", "name": "InsufficientAvailableIPsInSubnetFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "InsufficientDBClusterCapacityFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.InsufficientDBClusterCapacityFault", "name": "InsufficientDBClusterCapacityFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "InsufficientDBInstanceCapacityFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.InsufficientDBInstanceCapacityFault", "name": "InsufficientDBInstanceCapacityFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "InsufficientStorageClusterCapacityFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.InsufficientStorageClusterCapacityFault", "name": "InsufficientStorageClusterCapacityFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "IntegrationAlreadyExistsFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.IntegrationAlreadyExistsFault", "name": "IntegrationAlreadyExistsFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "IntegrationConflictOperationFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.IntegrationConflictOperationFault", "name": "IntegrationConflictOperationFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "IntegrationNotFoundFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.IntegrationNotFoundFault", "name": "IntegrationNotFoundFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "IntegrationQuotaExceededFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.IntegrationQuotaExceededFault", "name": "IntegrationQuotaExceededFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "InvalidBlueGreenDeploymentStateFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.InvalidBlueGreenDeploymentStateFault", "name": "InvalidBlueGreenDeploymentStateFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "InvalidCustomDBEngineVersionStateFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.InvalidCustomDBEngineVersionStateFault", "name": "InvalidCustomDBEngineVersionStateFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "InvalidDBClusterAutomatedBackupStateFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.InvalidDBClusterAutomatedBackupStateFault", "name": "InvalidDBClusterAutomatedBackupStateFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "InvalidDBClusterCapacityFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.InvalidDBClusterCapacityFault", "name": "InvalidDBClusterCapacityFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "InvalidDBClusterEndpointStateFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.InvalidDBClusterEndpointStateFault", "name": "InvalidDBClusterEndpointStateFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "InvalidDBClusterSnapshotStateFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.InvalidDBClusterSnapshotStateFault", "name": "InvalidDBClusterSnapshotStateFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "InvalidDBClusterStateFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.InvalidDBClusterStateFault", "name": "InvalidDBClusterStateFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "InvalidDBInstanceAutomatedBackupStateFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.InvalidDBInstanceAutomatedBackupStateFault", "name": "InvalidDBInstanceAutomatedBackupStateFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "InvalidDBInstanceStateFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.InvalidDBInstanceStateFault", "name": "InvalidDBInstanceStateFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "InvalidDBParameterGroupStateFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.InvalidDBParameterGroupStateFault", "name": "InvalidDBParameterGroupStateFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "InvalidDBProxyEndpointStateFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.InvalidDBProxyEndpointStateFault", "name": "InvalidDBProxyEndpointStateFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "InvalidDBProxyStateFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.InvalidDBProxyStateFault", "name": "InvalidDBProxyStateFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "InvalidDBSecurityGroupStateFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.InvalidDBSecurityGroupStateFault", "name": "InvalidDBSecurityGroupStateFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "InvalidDBShardGroupStateFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.InvalidDBShardGroupStateFault", "name": "InvalidDBShardGroupStateFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "InvalidDBSnapshotStateFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.InvalidDBSnapshotStateFault", "name": "InvalidDBSnapshotStateFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "InvalidDBSubnetGroupFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.InvalidDBSubnetGroupFault", "name": "InvalidDBSubnetGroupFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "InvalidDBSubnetGroupStateFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.InvalidDBSubnetGroupStateFault", "name": "InvalidDBSubnetGroupStateFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "InvalidDBSubnetStateFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.InvalidDBSubnetStateFault", "name": "InvalidDBSubnetStateFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "InvalidEventSubscriptionStateFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.InvalidEventSubscriptionStateFault", "name": "InvalidEventSubscriptionStateFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "InvalidExportOnlyFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.InvalidExportOnlyFault", "name": "InvalidExportOnlyFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "InvalidExportSourceStateFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.InvalidExportSourceStateFault", "name": "InvalidExportSourceStateFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "InvalidExportTaskStateFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.InvalidExportTaskStateFault", "name": "InvalidExportTaskStateFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "InvalidGlobalClusterStateFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.InvalidGlobalClusterStateFault", "name": "InvalidGlobalClusterStateFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "InvalidIntegrationStateFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.InvalidIntegrationStateFault", "name": "InvalidIntegrationStateFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "InvalidOptionGroupStateFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.InvalidOptionGroupStateFault", "name": "InvalidOptionGroupStateFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "InvalidResourceStateFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.InvalidResourceStateFault", "name": "InvalidResourceStateFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "InvalidRestoreFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.InvalidRestoreFault", "name": "InvalidRestoreFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "InvalidS3BucketFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.InvalidS3BucketFault", "name": "InvalidS3BucketFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "InvalidSubnet": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.InvalidSubnet", "name": "InvalidSubnet", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "InvalidVPCNetworkStateFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.InvalidVPCNetworkStateFault", "name": "InvalidVPCNetworkStateFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "KMSKeyNotAccessibleFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.KMSKeyNotAccessibleFault", "name": "KMSKeyNotAccessibleFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "MaxDBShardGroupLimitReached": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.MaxDBShardGroupLimitReached", "name": "MaxDBShardGroupLimitReached", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "NetworkTypeNotSupported": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.NetworkTypeNotSupported", "name": "NetworkTypeNotSupported", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "OptionGroupAlreadyExistsFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.OptionGroupAlreadyExistsFault", "name": "OptionGroupAlreadyExistsFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "OptionGroupNotFoundFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.OptionGroupNotFoundFault", "name": "OptionGroupNotFoundFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "OptionGroupQuotaExceededFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.OptionGroupQuotaExceededFault", "name": "OptionGroupQuotaExceededFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "PointInTimeRestoreNotEnabledFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.PointInTimeRestoreNotEnabledFault", "name": "PointInTimeRestoreNotEnabledFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "ProvisionedIopsNotAvailableInAZFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.ProvisionedIopsNotAvailableInAZFault", "name": "ProvisionedIopsNotAvailableInAZFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "ReservedDBInstanceAlreadyExistsFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.ReservedDBInstanceAlreadyExistsFault", "name": "ReservedDBInstanceAlreadyExistsFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "ReservedDBInstanceNotFoundFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.ReservedDBInstanceNotFoundFault", "name": "ReservedDBInstanceNotFoundFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "ReservedDBInstanceQuotaExceededFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.ReservedDBInstanceQuotaExceededFault", "name": "ReservedDBInstanceQuotaExceededFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "ReservedDBInstancesOfferingNotFoundFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.ReservedDBInstancesOfferingNotFoundFault", "name": "ReservedDBInstancesOfferingNotFoundFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "ResourceNotFoundFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.ResourceNotFoundFault", "name": "ResourceNotFoundFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "SNSInvalidTopicFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.SNSInvalidTopicFault", "name": "SNSInvalidTopicFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "SNSNoAuthorizationFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.SNSNoAuthorizationFault", "name": "SNSNoAuthorizationFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "SNSTopicArnNotFoundFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.SNSTopicArnNotFoundFault", "name": "SNSTopicArnNotFoundFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "SharedSnapshotQuotaExceededFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.SharedSnapshotQuotaExceededFault", "name": "SharedSnapshotQuotaExceededFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "SnapshotQuotaExceededFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.SnapshotQuotaExceededFault", "name": "SnapshotQuotaExceededFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "SourceClusterNotSupportedFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.SourceClusterNotSupportedFault", "name": "SourceClusterNotSupportedFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "SourceDatabaseNotSupportedFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.SourceDatabaseNotSupportedFault", "name": "SourceDatabaseNotSupportedFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "SourceNotFoundFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.SourceNotFoundFault", "name": "SourceNotFoundFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "StorageQuotaExceededFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.StorageQuotaExceededFault", "name": "StorageQuotaExceededFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "StorageTypeNotAvailableFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.StorageTypeNotAvailableFault", "name": "StorageTypeNotAvailableFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "StorageTypeNotSupportedFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.StorageTypeNotSupportedFault", "name": "StorageTypeNotSupportedFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "SubnetAlreadyInUse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.SubnetAlreadyInUse", "name": "SubnetAlreadyInUse", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "SubscriptionAlreadyExistFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.SubscriptionAlreadyExistFault", "name": "SubscriptionAlreadyExistFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "SubscriptionCategoryNotFoundFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.SubscriptionCategoryNotFoundFault", "name": "SubscriptionCategoryNotFoundFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "SubscriptionNotFoundFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.SubscriptionNotFoundFault", "name": "SubscriptionNotFoundFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "TenantDatabaseAlreadyExistsFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.TenantDatabaseAlreadyExistsFault", "name": "TenantDatabaseAlreadyExistsFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "TenantDatabaseNotFoundFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.TenantDatabaseNotFoundFault", "name": "TenantDatabaseNotFoundFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "TenantDatabaseQuotaExceededFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.TenantDatabaseQuotaExceededFault", "name": "TenantDatabaseQuotaExceededFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "UnsupportedDBEngineVersionFault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.Exceptions.UnsupportedDBEngineVersionFault", "name": "UnsupportedDBEngineVersionFault", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.client.Exceptions.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.client.Exceptions", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExportTaskResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ExportTaskResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ExportTasksMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ExportTasksMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "FailoverDBClusterMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.FailoverDBClusterMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "FailoverDBClusterResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.FailoverDBClusterResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "FailoverGlobalClusterMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.FailoverGlobalClusterMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "FailoverGlobalClusterResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.FailoverGlobalClusterResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "GlobalClustersMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.GlobalClustersMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "IntegrationResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.IntegrationResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ListTagsForResourceMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ListTagsForResourceMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModifyActivityStreamRequestTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ModifyActivityStreamRequestTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModifyActivityStreamResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ModifyActivityStreamResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModifyCertificatesMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ModifyCertificatesMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModifyCertificatesResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ModifyCertificatesResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModifyCurrentDBClusterCapacityMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ModifyCurrentDBClusterCapacityMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModifyCustomDBEngineVersionMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ModifyCustomDBEngineVersionMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModifyDBClusterEndpointMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ModifyDBClusterEndpointMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModifyDBClusterMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ModifyDBClusterMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModifyDBClusterParameterGroupMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ModifyDBClusterParameterGroupMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModifyDBClusterResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ModifyDBClusterResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModifyDBClusterSnapshotAttributeMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ModifyDBClusterSnapshotAttributeMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModifyDBClusterSnapshotAttributeResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ModifyDBClusterSnapshotAttributeResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModifyDBInstanceMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ModifyDBInstanceMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModifyDBInstanceResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ModifyDBInstanceResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModifyDBParameterGroupMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ModifyDBParameterGroupMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModifyDBProxyEndpointRequestTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ModifyDBProxyEndpointRequestTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModifyDBProxyEndpointResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ModifyDBProxyEndpointResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModifyDBProxyRequestTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ModifyDBProxyRequestTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModifyDBProxyResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ModifyDBProxyResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModifyDBProxyTargetGroupRequestTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ModifyDBProxyTargetGroupRequestTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModifyDBProxyTargetGroupResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ModifyDBProxyTargetGroupResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModifyDBRecommendationMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ModifyDBRecommendationMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModifyDBShardGroupMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ModifyDBShardGroupMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModifyDBSnapshotAttributeMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ModifyDBSnapshotAttributeMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModifyDBSnapshotAttributeResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ModifyDBSnapshotAttributeResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModifyDBSnapshotMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ModifyDBSnapshotMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModifyDBSnapshotResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ModifyDBSnapshotResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModifyDBSubnetGroupMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ModifyDBSubnetGroupMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModifyDBSubnetGroupResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ModifyDBSubnetGroupResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModifyEventSubscriptionMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ModifyEventSubscriptionMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModifyEventSubscriptionResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ModifyEventSubscriptionResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModifyGlobalClusterMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ModifyGlobalClusterMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModifyGlobalClusterResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ModifyGlobalClusterResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModifyIntegrationMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ModifyIntegrationMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModifyOptionGroupMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ModifyOptionGroupMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModifyOptionGroupResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ModifyOptionGroupResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModifyTenantDatabaseMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ModifyTenantDatabaseMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModifyTenantDatabaseResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ModifyTenantDatabaseResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "OptionGroupOptionsMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.OptionGroupOptionsMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "OptionGroupsTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.OptionGroupsTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "OrderableDBInstanceOptionsMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.OrderableDBInstanceOptionsMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "PendingMaintenanceActionsMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.PendingMaintenanceActionsMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "PromoteReadReplicaDBClusterMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.PromoteReadReplicaDBClusterMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "PromoteReadReplicaDBClusterResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.PromoteReadReplicaDBClusterResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "PromoteReadReplicaMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.PromoteReadReplicaMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "PromoteReadReplicaResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.PromoteReadReplicaResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "PurchaseReservedDBInstancesOfferingMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.PurchaseReservedDBInstancesOfferingMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "PurchaseReservedDBInstancesOfferingResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.PurchaseReservedDBInstancesOfferingResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RDSClient": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.client.BaseClient"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.client.RDSClient", "name": "RDSClient", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.client", "mro": ["mypy_boto3_rds.client.RDSClient", "botocore.client.BaseClient", "builtins.object"], "names": {".class": "SymbolTable", "add_role_to_db_cluster": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.add_role_to_db_cluster", "name": "add_role_to_db_cluster", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.AddRoleToDBClusterMessageTypeDef", "items": [["DBClusterIdentifier", "builtins.str"], ["RoleArn", "builtins.str"], ["FeatureName", "builtins.str"]], "required_keys": ["DBClusterIdentifier", "RoleArn"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_role_to_db_cluster of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.EmptyResponseMetadataTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "add_role_to_db_instance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.add_role_to_db_instance", "name": "add_role_to_db_instance", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.AddRoleToDBInstanceMessageTypeDef", "items": [["DBInstanceIdentifier", "builtins.str"], ["RoleArn", "builtins.str"], ["FeatureName", "builtins.str"]], "required_keys": ["DBInstanceIdentifier", "FeatureName", "RoleArn"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_role_to_db_instance of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.EmptyResponseMetadataTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "add_source_identifier_to_subscription": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.add_source_identifier_to_subscription", "name": "add_source_identifier_to_subscription", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.AddSourceIdentifierToSubscriptionMessageTypeDef", "items": [["SubscriptionName", "builtins.str"], ["SourceIdentifier", "builtins.str"]], "required_keys": ["SourceIdentifier", "SubscriptionName"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_source_identifier_to_subscription of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.AddSourceIdentifierToSubscriptionResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "add_tags_to_resource": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.add_tags_to_resource", "name": "add_tags_to_resource", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.AddTagsToResourceMessageTypeDef", "items": [["ResourceName", "builtins.str"], ["Tags", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TagTypeDef"}], "type_ref": "typing.Sequence"}]], "required_keys": ["ResourceName", "Tags"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_tags_to_resource of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.EmptyResponseMetadataTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "apply_pending_maintenance_action": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.apply_pending_maintenance_action", "name": "apply_pending_maintenance_action", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.ApplyPendingMaintenanceActionMessageTypeDef", "items": [["ResourceIdentifier", "builtins.str"], ["ApplyAction", "builtins.str"], ["OptInType", "builtins.str"]], "required_keys": ["ApplyAction", "OptInType", "ResourceIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply_pending_maintenance_action of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ApplyPendingMaintenanceActionResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "authorize_db_security_group_ingress": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.authorize_db_security_group_ingress", "name": "authorize_db_security_group_ingress", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.AuthorizeDBSecurityGroupIngressMessageTypeDef", "items": [["DBSecurityGroupName", "builtins.str"], ["CIDRIP", "builtins.str"], ["EC2SecurityGroupName", "builtins.str"], ["EC2SecurityGroupId", "builtins.str"], ["EC2SecurityGroupOwnerId", "builtins.str"]], "required_keys": ["DBSecurityGroupName"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "authorize_db_security_group_ingress of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.AuthorizeDBSecurityGroupIngressResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "backtrack_db_cluster": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.backtrack_db_cluster", "name": "backtrack_db_cluster", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.BacktrackDBClusterMessageTypeDef", "items": [["DBClusterIdentifier", "builtins.str"], ["BacktrackTo", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TimestampTypeDef"}], ["Force", "builtins.bool"], ["UseEarliestTimeOnPointInTimeUnavailable", "builtins.bool"]], "required_keys": ["BacktrackTo", "DBClusterIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "backtrack_db_cluster of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBClusterBacktrackResponseTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "can_paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.can_paginate", "name": "can_paginate", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "can_paginate of RDSClient", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "cancel_export_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.cancel_export_task", "name": "cancel_export_task", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.CancelExportTaskMessageTypeDef", "items": [["ExportTaskIdentifier", "builtins.str"]], "required_keys": ["ExportTaskIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cancel_export_task of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ExportTaskResponseTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "copy_db_cluster_parameter_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.copy_db_cluster_parameter_group", "name": "copy_db_cluster_parameter_group", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.CopyDBClusterParameterGroupMessageTypeDef", "items": [["SourceDBClusterParameterGroupIdentifier", "builtins.str"], ["TargetDBClusterParameterGroupIdentifier", "builtins.str"], ["TargetDBClusterParameterGroupDescription", "builtins.str"], ["Tags", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TagTypeDef"}], "type_ref": "typing.Sequence"}]], "required_keys": ["SourceDBClusterParameterGroupIdentifier", "TargetDBClusterParameterGroupDescription", "TargetDBClusterParameterGroupIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy_db_cluster_parameter_group of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.CopyDBClusterParameterGroupResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "copy_db_cluster_snapshot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.copy_db_cluster_snapshot", "name": "copy_db_cluster_snapshot", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.CopyDBClusterSnapshotMessageTypeDef", "items": [["SourceDBClusterSnapshotIdentifier", "builtins.str"], ["TargetDBClusterSnapshotIdentifier", "builtins.str"], ["KmsKeyId", "builtins.str"], ["PreSignedUrl", "builtins.str"], ["CopyTags", "builtins.bool"], ["Tags", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TagTypeDef"}], "type_ref": "typing.Sequence"}], ["SourceRegion", "builtins.str"]], "required_keys": ["SourceDBClusterSnapshotIdentifier", "TargetDBClusterSnapshotIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy_db_cluster_snapshot of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.CopyDBClusterSnapshotResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "copy_db_parameter_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.copy_db_parameter_group", "name": "copy_db_parameter_group", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.CopyDBParameterGroupMessageTypeDef", "items": [["SourceDBParameterGroupIdentifier", "builtins.str"], ["TargetDBParameterGroupIdentifier", "builtins.str"], ["TargetDBParameterGroupDescription", "builtins.str"], ["Tags", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TagTypeDef"}], "type_ref": "typing.Sequence"}]], "required_keys": ["SourceDBParameterGroupIdentifier", "TargetDBParameterGroupDescription", "TargetDBParameterGroupIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy_db_parameter_group of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.CopyDBParameterGroupResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "copy_db_snapshot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.copy_db_snapshot", "name": "copy_db_snapshot", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.CopyDBSnapshotMessageTypeDef", "items": [["SourceDBSnapshotIdentifier", "builtins.str"], ["TargetDBSnapshotIdentifier", "builtins.str"], ["KmsKeyId", "builtins.str"], ["Tags", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TagTypeDef"}], "type_ref": "typing.Sequence"}], ["CopyTags", "builtins.bool"], ["PreSignedUrl", "builtins.str"], ["OptionGroupName", "builtins.str"], ["TargetCustomAvailabilityZone", "builtins.str"], ["SnapshotTarget", "builtins.str"], ["CopyOptionGroup", "builtins.bool"], ["SnapshotAvailabilityZone", "builtins.str"], ["SourceRegion", "builtins.str"]], "required_keys": ["SourceDBSnapshotIdentifier", "TargetDBSnapshotIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy_db_snapshot of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.CopyDBSnapshotResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "copy_option_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.copy_option_group", "name": "copy_option_group", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.CopyOptionGroupMessageTypeDef", "items": [["SourceOptionGroupIdentifier", "builtins.str"], ["TargetOptionGroupIdentifier", "builtins.str"], ["TargetOptionGroupDescription", "builtins.str"], ["Tags", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TagTypeDef"}], "type_ref": "typing.Sequence"}]], "required_keys": ["SourceOptionGroupIdentifier", "TargetOptionGroupDescription", "TargetOptionGroupIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy_option_group of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.CopyOptionGroupResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "create_blue_green_deployment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.create_blue_green_deployment", "name": "create_blue_green_deployment", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.CreateBlueGreenDeploymentRequestTypeDef", "items": [["BlueGreenDeploymentName", "builtins.str"], ["Source", "builtins.str"], ["TargetEngineVersion", "builtins.str"], ["TargetDBParameterGroupName", "builtins.str"], ["TargetDBClusterParameterGroupName", "builtins.str"], ["Tags", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TagTypeDef"}], "type_ref": "typing.Sequence"}], ["TargetDBInstanceClass", "builtins.str"], ["UpgradeTargetStorageConfig", "builtins.bool"], ["TargetIops", "builtins.int"], ["TargetStorageType", "builtins.str"], ["TargetAllocatedStorage", "builtins.int"], ["TargetStorageThroughput", "builtins.int"]], "required_keys": ["BlueGreenDeploymentName", "Source"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_blue_green_deployment of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.CreateBlueGreenDeploymentResponseTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "create_custom_db_engine_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.create_custom_db_engine_version", "name": "create_custom_db_engine_version", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.CreateCustomDBEngineVersionMessageTypeDef", "items": [["Engine", "builtins.str"], ["EngineVersion", "builtins.str"], ["DatabaseInstallationFilesS3BucketName", "builtins.str"], ["DatabaseInstallationFilesS3Prefix", "builtins.str"], ["ImageId", "builtins.str"], ["KMSKeyId", "builtins.str"], ["SourceCustomDbEngineVersionIdentifier", "builtins.str"], ["UseAwsProvidedLatestImage", "builtins.bool"], ["Description", "builtins.str"], ["Manifest", "builtins.str"], ["Tags", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TagTypeDef"}], "type_ref": "typing.Sequence"}]], "required_keys": ["Engine", "EngineVersion"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_custom_db_engine_version of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBEngineVersionResponseTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "create_db_cluster": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.create_db_cluster", "name": "create_db_cluster", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.CreateDBClusterMessageTypeDef", "items": [["DBClusterIdentifier", "builtins.str"], ["Engine", "builtins.str"], ["AvailabilityZones", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["BackupRetentionPeriod", "builtins.int"], ["CharacterSetName", "builtins.str"], ["DatabaseName", "builtins.str"], ["DBClusterParameterGroupName", "builtins.str"], ["VpcSecurityGroupIds", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["DBSubnetGroupName", "builtins.str"], ["EngineVersion", "builtins.str"], ["Port", "builtins.int"], ["MasterUsername", "builtins.str"], ["MasterUserPassword", "builtins.str"], ["OptionGroupName", "builtins.str"], ["PreferredBackupWindow", "builtins.str"], ["PreferredMaintenanceWindow", "builtins.str"], ["ReplicationSourceIdentifier", "builtins.str"], ["Tags", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TagTypeDef"}], "type_ref": "typing.Sequence"}], ["StorageEncrypted", "builtins.bool"], ["KmsKeyId", "builtins.str"], ["PreSignedUrl", "builtins.str"], ["EnableIAMDatabaseAuthentication", "builtins.bool"], ["BacktrackWindow", "builtins.int"], ["EnableCloudwatchLogsExports", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["EngineMode", "builtins.str"], ["ScalingConfiguration", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ScalingConfigurationTypeDef"}], ["RdsCustomClusterConfiguration", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.RdsCustomClusterConfigurationTypeDef"}], ["DBClusterInstanceClass", "builtins.str"], ["AllocatedStorage", "builtins.int"], ["StorageType", "builtins.str"], ["Iops", "builtins.int"], ["PubliclyAccessible", "builtins.bool"], ["AutoMinorVersionUpgrade", "builtins.bool"], ["DeletionProtection", "builtins.bool"], ["GlobalClusterIdentifier", "builtins.str"], ["EnableHttpEndpoint", "builtins.bool"], ["CopyTagsToSnapshot", "builtins.bool"], ["Domain", "builtins.str"], ["DomainIAMRoleName", "builtins.str"], ["EnableGlobalWriteForwarding", "builtins.bool"], ["NetworkType", "builtins.str"], ["ServerlessV2ScalingConfiguration", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ServerlessV2ScalingConfigurationTypeDef"}], ["MonitoringInterval", "builtins.int"], ["MonitoringRoleArn", "builtins.str"], ["DatabaseInsightsMode", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.literals.DatabaseInsightsModeType"}], ["EnablePerformanceInsights", "builtins.bool"], ["PerformanceInsightsKMSKeyId", "builtins.str"], ["PerformanceInsightsRetentionPeriod", "builtins.int"], ["EnableLimitlessDatabase", "builtins.bool"], ["ClusterScalabilityType", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.literals.ClusterScalabilityTypeType"}], ["DBSystemId", "builtins.str"], ["ManageMasterUserPassword", "builtins.bool"], ["EnableLocalWriteForwarding", "builtins.bool"], ["MasterUserSecretKmsKeyId", "builtins.str"], ["CACertificateIdentifier", "builtins.str"], ["EngineLifecycleSupport", "builtins.str"], ["MasterUserAuthenticationType", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.literals.MasterUserAuthenticationTypeType"}], ["SourceRegion", "builtins.str"]], "required_keys": ["DBClusterIdentifier", "Engine"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_db_cluster of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.CreateDBClusterResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "create_db_cluster_endpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.create_db_cluster_endpoint", "name": "create_db_cluster_endpoint", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.CreateDBClusterEndpointMessageTypeDef", "items": [["DBClusterIdentifier", "builtins.str"], ["DBClusterEndpointIdentifier", "builtins.str"], ["EndpointType", "builtins.str"], ["StaticMembers", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["ExcludedMembers", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["Tags", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TagTypeDef"}], "type_ref": "typing.Sequence"}]], "required_keys": ["DBClusterEndpointIdentifier", "DBClusterIdentifier", "EndpointType"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_db_cluster_endpoint of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBClusterEndpointResponseTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "create_db_cluster_parameter_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.create_db_cluster_parameter_group", "name": "create_db_cluster_parameter_group", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.CreateDBClusterParameterGroupMessageTypeDef", "items": [["DBClusterParameterGroupName", "builtins.str"], ["DBParameterGroupFamily", "builtins.str"], ["Description", "builtins.str"], ["Tags", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TagTypeDef"}], "type_ref": "typing.Sequence"}]], "required_keys": ["DBClusterParameterGroupName", "DBParameterGroupFamily", "Description"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_db_cluster_parameter_group of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.CreateDBClusterParameterGroupResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "create_db_cluster_snapshot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.create_db_cluster_snapshot", "name": "create_db_cluster_snapshot", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.CreateDBClusterSnapshotMessageTypeDef", "items": [["DBClusterSnapshotIdentifier", "builtins.str"], ["DBClusterIdentifier", "builtins.str"], ["Tags", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TagTypeDef"}], "type_ref": "typing.Sequence"}]], "required_keys": ["DBClusterIdentifier", "DBClusterSnapshotIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_db_cluster_snapshot of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.CreateDBClusterSnapshotResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "create_db_instance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.create_db_instance", "name": "create_db_instance", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.CreateDBInstanceMessageTypeDef", "items": [["DBInstanceIdentifier", "builtins.str"], ["DBInstanceClass", "builtins.str"], ["Engine", "builtins.str"], ["DBName", "builtins.str"], ["AllocatedStorage", "builtins.int"], ["MasterUsername", "builtins.str"], ["MasterUserPassword", "builtins.str"], ["DBSecurityGroups", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["VpcSecurityGroupIds", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["AvailabilityZone", "builtins.str"], ["DBSubnetGroupName", "builtins.str"], ["PreferredMaintenanceWindow", "builtins.str"], ["DBParameterGroupName", "builtins.str"], ["BackupRetentionPeriod", "builtins.int"], ["PreferredBackupWindow", "builtins.str"], ["Port", "builtins.int"], ["MultiAZ", "builtins.bool"], ["EngineVersion", "builtins.str"], ["AutoMinorVersionUpgrade", "builtins.bool"], ["LicenseModel", "builtins.str"], ["Iops", "builtins.int"], ["StorageThroughput", "builtins.int"], ["OptionGroupName", "builtins.str"], ["CharacterSetName", "builtins.str"], ["NcharCharacterSetName", "builtins.str"], ["PubliclyAccessible", "builtins.bool"], ["Tags", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TagTypeDef"}], "type_ref": "typing.Sequence"}], ["DBClusterIdentifier", "builtins.str"], ["StorageType", "builtins.str"], ["TdeCredentialArn", "builtins.str"], ["TdeCredentialPassword", "builtins.str"], ["StorageEncrypted", "builtins.bool"], ["KmsKeyId", "builtins.str"], ["Domain", "builtins.str"], ["DomainFqdn", "builtins.str"], ["DomainOu", "builtins.str"], ["DomainAuthSecretArn", "builtins.str"], ["DomainDnsIps", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["CopyTagsToSnapshot", "builtins.bool"], ["MonitoringInterval", "builtins.int"], ["MonitoringRoleArn", "builtins.str"], ["DomainIAMRoleName", "builtins.str"], ["PromotionTier", "builtins.int"], ["Timezone", "builtins.str"], ["EnableIAMDatabaseAuthentication", "builtins.bool"], ["DatabaseInsightsMode", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.literals.DatabaseInsightsModeType"}], ["EnablePerformanceInsights", "builtins.bool"], ["PerformanceInsightsKMSKeyId", "builtins.str"], ["PerformanceInsightsRetentionPeriod", "builtins.int"], ["EnableCloudwatchLogsExports", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["ProcessorFeatures", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ProcessorFeatureTypeDef"}], "type_ref": "typing.Sequence"}], ["DeletionProtection", "builtins.bool"], ["MaxAllocatedStorage", "builtins.int"], ["EnableCustomerOwnedIp", "builtins.bool"], ["NetworkType", "builtins.str"], ["Backup<PERSON>arget", "builtins.str"], ["CustomIamInstanceProfile", "builtins.str"], ["DBSystemId", "builtins.str"], ["CACertificateIdentifier", "builtins.str"], ["ManageMasterUserPassword", "builtins.bool"], ["MasterUserSecretKmsKeyId", "builtins.str"], ["MultiTenant", "builtins.bool"], ["DedicatedLogVolume", "builtins.bool"], ["EngineLifecycleSupport", "builtins.str"], ["MasterUserAuthenticationType", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.literals.MasterUserAuthenticationTypeType"}]], "required_keys": ["DBInstanceClass", "DBInstanceIdentifier", "Engine"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_db_instance of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.CreateDBInstanceResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "create_db_instance_read_replica": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.create_db_instance_read_replica", "name": "create_db_instance_read_replica", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.CreateDBInstanceReadReplicaMessageTypeDef", "items": [["DBInstanceIdentifier", "builtins.str"], ["SourceDBInstanceIdentifier", "builtins.str"], ["DBInstanceClass", "builtins.str"], ["AvailabilityZone", "builtins.str"], ["Port", "builtins.int"], ["MultiAZ", "builtins.bool"], ["AutoMinorVersionUpgrade", "builtins.bool"], ["Iops", "builtins.int"], ["StorageThroughput", "builtins.int"], ["OptionGroupName", "builtins.str"], ["DBParameterGroupName", "builtins.str"], ["PubliclyAccessible", "builtins.bool"], ["Tags", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TagTypeDef"}], "type_ref": "typing.Sequence"}], ["DBSubnetGroupName", "builtins.str"], ["VpcSecurityGroupIds", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["StorageType", "builtins.str"], ["CopyTagsToSnapshot", "builtins.bool"], ["MonitoringInterval", "builtins.int"], ["MonitoringRoleArn", "builtins.str"], ["KmsKeyId", "builtins.str"], ["PreSignedUrl", "builtins.str"], ["EnableIAMDatabaseAuthentication", "builtins.bool"], ["DatabaseInsightsMode", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.literals.DatabaseInsightsModeType"}], ["EnablePerformanceInsights", "builtins.bool"], ["PerformanceInsightsKMSKeyId", "builtins.str"], ["PerformanceInsightsRetentionPeriod", "builtins.int"], ["EnableCloudwatchLogsExports", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["ProcessorFeatures", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ProcessorFeatureTypeDef"}], "type_ref": "typing.Sequence"}], ["UseDefaultProcessorFeatures", "builtins.bool"], ["DeletionProtection", "builtins.bool"], ["Domain", "builtins.str"], ["DomainIAMRoleName", "builtins.str"], ["DomainFqdn", "builtins.str"], ["DomainOu", "builtins.str"], ["DomainAuthSecretArn", "builtins.str"], ["DomainDnsIps", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["ReplicaMode", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.literals.ReplicaModeType"}], ["EnableCustomerOwnedIp", "builtins.bool"], ["NetworkType", "builtins.str"], ["MaxAllocatedStorage", "builtins.int"], ["Backup<PERSON>arget", "builtins.str"], ["CustomIamInstanceProfile", "builtins.str"], ["AllocatedStorage", "builtins.int"], ["SourceDBClusterIdentifier", "builtins.str"], ["DedicatedLogVolume", "builtins.bool"], ["UpgradeStorageConfig", "builtins.bool"], ["CACertificateIdentifier", "builtins.str"], ["SourceRegion", "builtins.str"]], "required_keys": ["DBInstanceIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_db_instance_read_replica of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.CreateDBInstanceReadReplicaResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "create_db_parameter_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.create_db_parameter_group", "name": "create_db_parameter_group", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.CreateDBParameterGroupMessageTypeDef", "items": [["DBParameterGroupName", "builtins.str"], ["DBParameterGroupFamily", "builtins.str"], ["Description", "builtins.str"], ["Tags", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TagTypeDef"}], "type_ref": "typing.Sequence"}]], "required_keys": ["DBParameterGroupFamily", "DBParameterGroupName", "Description"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_db_parameter_group of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.CreateDBParameterGroupResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "create_db_proxy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.create_db_proxy", "name": "create_db_proxy", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.CreateDBProxyRequestTypeDef", "items": [["DBProxyName", "builtins.str"], ["EngineFamily", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.literals.EngineFamilyType"}], ["RoleArn", "builtins.str"], ["VpcSubnetIds", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["DefaultAuthScheme", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.literals.DefaultAuthSchemeType"}], ["<PERSON><PERSON>", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.UserAuthConfigTypeDef"}], "type_ref": "typing.Sequence"}], ["VpcSecurityGroupIds", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["RequireTLS", "builtins.bool"], ["IdleClientTimeout", "builtins.int"], ["DebugLogging", "builtins.bool"], ["Tags", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TagTypeDef"}], "type_ref": "typing.Sequence"}], ["EndpointNetworkType", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.literals.EndpointNetworkTypeType"}], ["TargetConnectionNetworkType", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.literals.TargetConnectionNetworkTypeType"}]], "required_keys": ["DBProxyName", "EngineFamily", "RoleArn", "VpcSubnetIds"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_db_proxy of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.CreateDBProxyResponseTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "create_db_proxy_endpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.create_db_proxy_endpoint", "name": "create_db_proxy_endpoint", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.CreateDBProxyEndpointRequestTypeDef", "items": [["DBProxyName", "builtins.str"], ["DBProxyEndpointName", "builtins.str"], ["VpcSubnetIds", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["VpcSecurityGroupIds", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["TargetRole", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.literals.DBProxyEndpointTargetRoleType"}], ["Tags", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TagTypeDef"}], "type_ref": "typing.Sequence"}], ["EndpointNetworkType", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.literals.EndpointNetworkTypeType"}]], "required_keys": ["DBProxyEndpointName", "DBProxyName", "VpcSubnetIds"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_db_proxy_endpoint of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.CreateDBProxyEndpointResponseTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "create_db_security_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.create_db_security_group", "name": "create_db_security_group", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.CreateDBSecurityGroupMessageTypeDef", "items": [["DBSecurityGroupName", "builtins.str"], ["DBSecurityGroupDescription", "builtins.str"], ["Tags", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TagTypeDef"}], "type_ref": "typing.Sequence"}]], "required_keys": ["DBSecurityGroupDescription", "DBSecurityGroupName"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_db_security_group of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.CreateDBSecurityGroupResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "create_db_shard_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.create_db_shard_group", "name": "create_db_shard_group", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.CreateDBShardGroupMessageTypeDef", "items": [["DBShardGroupIdentifier", "builtins.str"], ["DBClusterIdentifier", "builtins.str"], ["MaxACU", "builtins.float"], ["ComputeRedundancy", "builtins.int"], ["MinACU", "builtins.float"], ["PubliclyAccessible", "builtins.bool"], ["Tags", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TagTypeDef"}], "type_ref": "typing.Sequence"}]], "required_keys": ["DBClusterIdentifier", "DBShardGroupIdentifier", "MaxACU"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_db_shard_group of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBShardGroupResponseTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "create_db_snapshot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.create_db_snapshot", "name": "create_db_snapshot", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.CreateDBSnapshotMessageTypeDef", "items": [["DBSnapshotIdentifier", "builtins.str"], ["DBInstanceIdentifier", "builtins.str"], ["Tags", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TagTypeDef"}], "type_ref": "typing.Sequence"}]], "required_keys": ["DBInstanceIdentifier", "DBSnapshotIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_db_snapshot of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.CreateDBSnapshotResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "create_db_subnet_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.create_db_subnet_group", "name": "create_db_subnet_group", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.CreateDBSubnetGroupMessageTypeDef", "items": [["DBSubnetGroupName", "builtins.str"], ["DBSubnetGroupDescription", "builtins.str"], ["SubnetIds", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["Tags", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TagTypeDef"}], "type_ref": "typing.Sequence"}]], "required_keys": ["DBSubnetGroupDescription", "DBSubnetGroupName", "SubnetIds"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_db_subnet_group of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.CreateDBSubnetGroupResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "create_event_subscription": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.create_event_subscription", "name": "create_event_subscription", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.CreateEventSubscriptionMessageTypeDef", "items": [["SubscriptionName", "builtins.str"], ["SnsTopicArn", "builtins.str"], ["SourceType", "builtins.str"], ["EventCategories", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["SourceIds", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["Enabled", "builtins.bool"], ["Tags", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TagTypeDef"}], "type_ref": "typing.Sequence"}]], "required_keys": ["SnsTopicArn", "SubscriptionName"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_event_subscription of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.CreateEventSubscriptionResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "create_global_cluster": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.create_global_cluster", "name": "create_global_cluster", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.CreateGlobalClusterMessageTypeDef", "items": [["GlobalClusterIdentifier", "builtins.str"], ["SourceDBClusterIdentifier", "builtins.str"], ["Engine", "builtins.str"], ["EngineVersion", "builtins.str"], ["EngineLifecycleSupport", "builtins.str"], ["DeletionProtection", "builtins.bool"], ["DatabaseName", "builtins.str"], ["StorageEncrypted", "builtins.bool"], ["Tags", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TagTypeDef"}], "type_ref": "typing.Sequence"}]], "required_keys": ["GlobalClusterIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_global_cluster of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.CreateGlobalClusterResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "create_integration": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.create_integration", "name": "create_integration", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.CreateIntegrationMessageTypeDef", "items": [["SourceArn", "builtins.str"], ["TargetArn", "builtins.str"], ["IntegrationName", "builtins.str"], ["KMSKeyId", "builtins.str"], ["AdditionalEncryptionContext", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "typing.Mapping"}], ["Tags", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TagTypeDef"}], "type_ref": "typing.Sequence"}], ["DataFilter", "builtins.str"], ["Description", "builtins.str"]], "required_keys": ["IntegrationName", "SourceArn", "TargetArn"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_integration of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.IntegrationResponseTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "create_option_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.create_option_group", "name": "create_option_group", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.CreateOptionGroupMessageTypeDef", "items": [["OptionGroupName", "builtins.str"], ["EngineName", "builtins.str"], ["MajorEngineVersion", "builtins.str"], ["OptionGroupDescription", "builtins.str"], ["Tags", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TagTypeDef"}], "type_ref": "typing.Sequence"}]], "required_keys": ["EngineName", "MajorEngineVersion", "OptionGroupDescription", "OptionGroupName"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_option_group of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.CreateOptionGroupResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "create_tenant_database": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.create_tenant_database", "name": "create_tenant_database", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.CreateTenantDatabaseMessageTypeDef", "items": [["DBInstanceIdentifier", "builtins.str"], ["TenantDBName", "builtins.str"], ["MasterUsername", "builtins.str"], ["MasterUserPassword", "builtins.str"], ["CharacterSetName", "builtins.str"], ["NcharCharacterSetName", "builtins.str"], ["ManageMasterUserPassword", "builtins.bool"], ["MasterUserSecretKmsKeyId", "builtins.str"], ["Tags", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TagTypeDef"}], "type_ref": "typing.Sequence"}]], "required_keys": ["DBInstanceIdentifier", "MasterUsername", "TenantDBName"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_tenant_database of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.CreateTenantDatabaseResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "delete_blue_green_deployment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.delete_blue_green_deployment", "name": "delete_blue_green_deployment", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DeleteBlueGreenDeploymentRequestTypeDef", "items": [["BlueGreenDeploymentIdentifier", "builtins.str"], ["DeleteTarget", "builtins.bool"]], "required_keys": ["BlueGreenDeploymentIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_blue_green_deployment of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DeleteBlueGreenDeploymentResponseTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "delete_custom_db_engine_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.delete_custom_db_engine_version", "name": "delete_custom_db_engine_version", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DeleteCustomDBEngineVersionMessageTypeDef", "items": [["Engine", "builtins.str"], ["EngineVersion", "builtins.str"]], "required_keys": ["Engine", "EngineVersion"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_custom_db_engine_version of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBEngineVersionResponseTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "delete_db_cluster": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.delete_db_cluster", "name": "delete_db_cluster", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DeleteDBClusterMessageTypeDef", "items": [["DBClusterIdentifier", "builtins.str"], ["SkipFinalSnapshot", "builtins.bool"], ["FinalDBSnapshotIdentifier", "builtins.str"], ["DeleteAutomatedBackups", "builtins.bool"]], "required_keys": ["DBClusterIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_db_cluster of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DeleteDBClusterResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "delete_db_cluster_automated_backup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.delete_db_cluster_automated_backup", "name": "delete_db_cluster_automated_backup", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DeleteDBClusterAutomatedBackupMessageTypeDef", "items": [["DbClusterResourceId", "builtins.str"]], "required_keys": ["DbClusterResourceId"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_db_cluster_automated_backup of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DeleteDBClusterAutomatedBackupResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "delete_db_cluster_endpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.delete_db_cluster_endpoint", "name": "delete_db_cluster_endpoint", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DeleteDBClusterEndpointMessageTypeDef", "items": [["DBClusterEndpointIdentifier", "builtins.str"]], "required_keys": ["DBClusterEndpointIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_db_cluster_endpoint of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBClusterEndpointResponseTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "delete_db_cluster_parameter_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.delete_db_cluster_parameter_group", "name": "delete_db_cluster_parameter_group", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DeleteDBClusterParameterGroupMessageTypeDef", "items": [["DBClusterParameterGroupName", "builtins.str"]], "required_keys": ["DBClusterParameterGroupName"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_db_cluster_parameter_group of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.EmptyResponseMetadataTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "delete_db_cluster_snapshot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.delete_db_cluster_snapshot", "name": "delete_db_cluster_snapshot", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DeleteDBClusterSnapshotMessageTypeDef", "items": [["DBClusterSnapshotIdentifier", "builtins.str"]], "required_keys": ["DBClusterSnapshotIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_db_cluster_snapshot of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DeleteDBClusterSnapshotResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "delete_db_instance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.delete_db_instance", "name": "delete_db_instance", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DeleteDBInstanceMessageTypeDef", "items": [["DBInstanceIdentifier", "builtins.str"], ["SkipFinalSnapshot", "builtins.bool"], ["FinalDBSnapshotIdentifier", "builtins.str"], ["DeleteAutomatedBackups", "builtins.bool"]], "required_keys": ["DBInstanceIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_db_instance of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DeleteDBInstanceResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "delete_db_instance_automated_backup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.delete_db_instance_automated_backup", "name": "delete_db_instance_automated_backup", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DeleteDBInstanceAutomatedBackupMessageTypeDef", "items": [["DbiResourceId", "builtins.str"], ["DBInstanceAutomatedBackupsArn", "builtins.str"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_db_instance_automated_backup of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DeleteDBInstanceAutomatedBackupResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "delete_db_parameter_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.delete_db_parameter_group", "name": "delete_db_parameter_group", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DeleteDBParameterGroupMessageTypeDef", "items": [["DBParameterGroupName", "builtins.str"]], "required_keys": ["DBParameterGroupName"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_db_parameter_group of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.EmptyResponseMetadataTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "delete_db_proxy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.delete_db_proxy", "name": "delete_db_proxy", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DeleteDBProxyRequestTypeDef", "items": [["DBProxyName", "builtins.str"]], "required_keys": ["DBProxyName"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_db_proxy of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DeleteDBProxyResponseTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "delete_db_proxy_endpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.delete_db_proxy_endpoint", "name": "delete_db_proxy_endpoint", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DeleteDBProxyEndpointRequestTypeDef", "items": [["DBProxyEndpointName", "builtins.str"]], "required_keys": ["DBProxyEndpointName"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_db_proxy_endpoint of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DeleteDBProxyEndpointResponseTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "delete_db_security_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.delete_db_security_group", "name": "delete_db_security_group", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DeleteDBSecurityGroupMessageTypeDef", "items": [["DBSecurityGroupName", "builtins.str"]], "required_keys": ["DBSecurityGroupName"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_db_security_group of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.EmptyResponseMetadataTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "delete_db_shard_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.delete_db_shard_group", "name": "delete_db_shard_group", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DeleteDBShardGroupMessageTypeDef", "items": [["DBShardGroupIdentifier", "builtins.str"]], "required_keys": ["DBShardGroupIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_db_shard_group of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBShardGroupResponseTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "delete_db_snapshot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.delete_db_snapshot", "name": "delete_db_snapshot", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DeleteDBSnapshotMessageTypeDef", "items": [["DBSnapshotIdentifier", "builtins.str"]], "required_keys": ["DBSnapshotIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_db_snapshot of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DeleteDBSnapshotResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "delete_db_subnet_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.delete_db_subnet_group", "name": "delete_db_subnet_group", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DeleteDBSubnetGroupMessageTypeDef", "items": [["DBSubnetGroupName", "builtins.str"]], "required_keys": ["DBSubnetGroupName"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_db_subnet_group of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.EmptyResponseMetadataTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "delete_event_subscription": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.delete_event_subscription", "name": "delete_event_subscription", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DeleteEventSubscriptionMessageTypeDef", "items": [["SubscriptionName", "builtins.str"]], "required_keys": ["SubscriptionName"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_event_subscription of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DeleteEventSubscriptionResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "delete_global_cluster": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.delete_global_cluster", "name": "delete_global_cluster", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DeleteGlobalClusterMessageTypeDef", "items": [["GlobalClusterIdentifier", "builtins.str"]], "required_keys": ["GlobalClusterIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_global_cluster of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DeleteGlobalClusterResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "delete_integration": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.delete_integration", "name": "delete_integration", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DeleteIntegrationMessageTypeDef", "items": [["IntegrationIdentifier", "builtins.str"]], "required_keys": ["IntegrationIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_integration of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.IntegrationResponseTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "delete_option_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.delete_option_group", "name": "delete_option_group", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DeleteOptionGroupMessageTypeDef", "items": [["OptionGroupName", "builtins.str"]], "required_keys": ["OptionGroupName"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_option_group of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.EmptyResponseMetadataTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "delete_tenant_database": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.delete_tenant_database", "name": "delete_tenant_database", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DeleteTenantDatabaseMessageTypeDef", "items": [["DBInstanceIdentifier", "builtins.str"], ["TenantDBName", "builtins.str"], ["SkipFinalSnapshot", "builtins.bool"], ["FinalDBSnapshotIdentifier", "builtins.str"]], "required_keys": ["DBInstanceIdentifier", "TenantDBName"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_tenant_database of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DeleteTenantDatabaseResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "deregister_db_proxy_targets": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.deregister_db_proxy_targets", "name": "deregister_db_proxy_targets", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DeregisterDBProxyTargetsRequestTypeDef", "items": [["DBProxyName", "builtins.str"], ["TargetGroupName", "builtins.str"], ["DBInstanceIdentifiers", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["DBClusterIdentifiers", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}]], "required_keys": ["DBProxyName"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "deregister_db_proxy_targets of RDSClient", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_account_attributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_account_attributes", "name": "describe_account_attributes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mypy_boto3_rds.client.RDSClient"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_account_attributes of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.AccountAttributesMessageTypeDef"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "describe_blue_green_deployments": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_blue_green_deployments", "name": "describe_blue_green_deployments", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeBlueGreenDeploymentsRequestTypeDef", "items": [["BlueGreenDeploymentIdentifier", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["<PERSON><PERSON>", "builtins.str"], ["MaxRecords", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_blue_green_deployments of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeBlueGreenDeploymentsResponseTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_certificates": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_certificates", "name": "describe_certificates", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeCertificatesMessageTypeDef", "items": [["CertificateIdentifier", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["MaxRecords", "builtins.int"], ["<PERSON><PERSON>", "builtins.str"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_certificates of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.CertificateMessageTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_db_cluster_automated_backups": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_db_cluster_automated_backups", "name": "describe_db_cluster_automated_backups", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBClusterAutomatedBackupsMessageTypeDef", "items": [["DbClusterResourceId", "builtins.str"], ["DBClusterIdentifier", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["MaxRecords", "builtins.int"], ["<PERSON><PERSON>", "builtins.str"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_db_cluster_automated_backups of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBClusterAutomatedBackupMessageTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_db_cluster_backtracks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_db_cluster_backtracks", "name": "describe_db_cluster_backtracks", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBClusterBacktracksMessageTypeDef", "items": [["DBClusterIdentifier", "builtins.str"], ["BacktrackIdentifier", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["MaxRecords", "builtins.int"], ["<PERSON><PERSON>", "builtins.str"]], "required_keys": ["DBClusterIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_db_cluster_backtracks of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBClusterBacktrackMessageTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_db_cluster_endpoints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_db_cluster_endpoints", "name": "describe_db_cluster_endpoints", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBClusterEndpointsMessageTypeDef", "items": [["DBClusterIdentifier", "builtins.str"], ["DBClusterEndpointIdentifier", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["MaxRecords", "builtins.int"], ["<PERSON><PERSON>", "builtins.str"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_db_cluster_endpoints of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBClusterEndpointMessageTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_db_cluster_parameter_groups": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_db_cluster_parameter_groups", "name": "describe_db_cluster_parameter_groups", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBClusterParameterGroupsMessageTypeDef", "items": [["DBClusterParameterGroupName", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["MaxRecords", "builtins.int"], ["<PERSON><PERSON>", "builtins.str"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_db_cluster_parameter_groups of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBClusterParameterGroupsMessageTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_db_cluster_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_db_cluster_parameters", "name": "describe_db_cluster_parameters", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBClusterParametersMessageTypeDef", "items": [["DBClusterParameterGroupName", "builtins.str"], ["Source", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["MaxRecords", "builtins.int"], ["<PERSON><PERSON>", "builtins.str"]], "required_keys": ["DBClusterParameterGroupName"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_db_cluster_parameters of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBClusterParameterGroupDetailsTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_db_cluster_snapshot_attributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_db_cluster_snapshot_attributes", "name": "describe_db_cluster_snapshot_attributes", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBClusterSnapshotAttributesMessageTypeDef", "items": [["DBClusterSnapshotIdentifier", "builtins.str"]], "required_keys": ["DBClusterSnapshotIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_db_cluster_snapshot_attributes of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeDBClusterSnapshotAttributesResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_db_cluster_snapshots": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_db_cluster_snapshots", "name": "describe_db_cluster_snapshots", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBClusterSnapshotsMessageTypeDef", "items": [["DBClusterIdentifier", "builtins.str"], ["DBClusterSnapshotIdentifier", "builtins.str"], ["SnapshotType", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["MaxRecords", "builtins.int"], ["<PERSON><PERSON>", "builtins.str"], ["IncludeShared", "builtins.bool"], ["IncludePublic", "builtins.bool"], ["DbClusterResourceId", "builtins.str"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_db_cluster_snapshots of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBClusterSnapshotMessageTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_db_clusters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_db_clusters", "name": "describe_db_clusters", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBClustersMessageTypeDef", "items": [["DBClusterIdentifier", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["MaxRecords", "builtins.int"], ["<PERSON><PERSON>", "builtins.str"], ["IncludeShared", "builtins.bool"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_db_clusters of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBClusterMessageTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_db_engine_versions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_db_engine_versions", "name": "describe_db_engine_versions", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBEngineVersionsMessageTypeDef", "items": [["Engine", "builtins.str"], ["EngineVersion", "builtins.str"], ["DBParameterGroupFamily", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["MaxRecords", "builtins.int"], ["<PERSON><PERSON>", "builtins.str"], ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "builtins.bool"], ["ListSupportedCharacterSets", "builtins.bool"], ["ListSupportedTimezones", "builtins.bool"], ["IncludeAll", "builtins.bool"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_db_engine_versions of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBEngineVersionMessageTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_db_instance_automated_backups": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_db_instance_automated_backups", "name": "describe_db_instance_automated_backups", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBInstanceAutomatedBackupsMessageTypeDef", "items": [["DbiResourceId", "builtins.str"], ["DBInstanceIdentifier", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["MaxRecords", "builtins.int"], ["<PERSON><PERSON>", "builtins.str"], ["DBInstanceAutomatedBackupsArn", "builtins.str"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_db_instance_automated_backups of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBInstanceAutomatedBackupMessageTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_db_instances": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_db_instances", "name": "describe_db_instances", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBInstancesMessageTypeDef", "items": [["DBInstanceIdentifier", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["MaxRecords", "builtins.int"], ["<PERSON><PERSON>", "builtins.str"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_db_instances of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBInstanceMessageTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_db_log_files": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_db_log_files", "name": "describe_db_log_files", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBLogFilesMessageTypeDef", "items": [["DBInstanceIdentifier", "builtins.str"], ["FilenameContains", "builtins.str"], ["FileLastWritten", "builtins.int"], ["FileSize", "builtins.int"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["MaxRecords", "builtins.int"], ["<PERSON><PERSON>", "builtins.str"]], "required_keys": ["DBInstanceIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_db_log_files of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeDBLogFilesResponseTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_db_major_engine_versions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_db_major_engine_versions", "name": "describe_db_major_engine_versions", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBMajorEngineVersionsRequestTypeDef", "items": [["Engine", "builtins.str"], ["MajorEngineVersion", "builtins.str"], ["<PERSON><PERSON>", "builtins.str"], ["MaxRecords", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_db_major_engine_versions of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeDBMajorEngineVersionsResponseTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_db_parameter_groups": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_db_parameter_groups", "name": "describe_db_parameter_groups", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBParameterGroupsMessageTypeDef", "items": [["DBParameterGroupName", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["MaxRecords", "builtins.int"], ["<PERSON><PERSON>", "builtins.str"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_db_parameter_groups of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBParameterGroupsMessageTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_db_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_db_parameters", "name": "describe_db_parameters", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBParametersMessageTypeDef", "items": [["DBParameterGroupName", "builtins.str"], ["Source", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["MaxRecords", "builtins.int"], ["<PERSON><PERSON>", "builtins.str"]], "required_keys": ["DBParameterGroupName"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_db_parameters of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBParameterGroupDetailsTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_db_proxies": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_db_proxies", "name": "describe_db_proxies", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBProxiesRequestTypeDef", "items": [["DBProxyName", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["<PERSON><PERSON>", "builtins.str"], ["MaxRecords", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_db_proxies of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeDBProxiesResponseTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_db_proxy_endpoints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_db_proxy_endpoints", "name": "describe_db_proxy_endpoints", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBProxyEndpointsRequestTypeDef", "items": [["DBProxyName", "builtins.str"], ["DBProxyEndpointName", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["<PERSON><PERSON>", "builtins.str"], ["MaxRecords", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_db_proxy_endpoints of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeDBProxyEndpointsResponseTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_db_proxy_target_groups": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_db_proxy_target_groups", "name": "describe_db_proxy_target_groups", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBProxyTargetGroupsRequestTypeDef", "items": [["DBProxyName", "builtins.str"], ["TargetGroupName", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["<PERSON><PERSON>", "builtins.str"], ["MaxRecords", "builtins.int"]], "required_keys": ["DBProxyName"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_db_proxy_target_groups of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeDBProxyTargetGroupsResponseTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_db_proxy_targets": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_db_proxy_targets", "name": "describe_db_proxy_targets", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBProxyTargetsRequestTypeDef", "items": [["DBProxyName", "builtins.str"], ["TargetGroupName", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["<PERSON><PERSON>", "builtins.str"], ["MaxRecords", "builtins.int"]], "required_keys": ["DBProxyName"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_db_proxy_targets of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeDBProxyTargetsResponseTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_db_recommendations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_db_recommendations", "name": "describe_db_recommendations", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBRecommendationsMessageTypeDef", "items": [["LastUpdatedAfter", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TimestampTypeDef"}], ["LastUpdatedBefore", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TimestampTypeDef"}], ["Locale", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["MaxRecords", "builtins.int"], ["<PERSON><PERSON>", "builtins.str"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_db_recommendations of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBRecommendationsMessageTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_db_security_groups": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_db_security_groups", "name": "describe_db_security_groups", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBSecurityGroupsMessageTypeDef", "items": [["DBSecurityGroupName", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["MaxRecords", "builtins.int"], ["<PERSON><PERSON>", "builtins.str"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_db_security_groups of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBSecurityGroupMessageTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_db_shard_groups": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_db_shard_groups", "name": "describe_db_shard_groups", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBShardGroupsMessageTypeDef", "items": [["DBShardGroupIdentifier", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["<PERSON><PERSON>", "builtins.str"], ["MaxRecords", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_db_shard_groups of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeDBShardGroupsResponseTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_db_snapshot_attributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_db_snapshot_attributes", "name": "describe_db_snapshot_attributes", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBSnapshotAttributesMessageTypeDef", "items": [["DBSnapshotIdentifier", "builtins.str"]], "required_keys": ["DBSnapshotIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_db_snapshot_attributes of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeDBSnapshotAttributesResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_db_snapshot_tenant_databases": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_db_snapshot_tenant_databases", "name": "describe_db_snapshot_tenant_databases", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBSnapshotTenantDatabasesMessageTypeDef", "items": [["DBInstanceIdentifier", "builtins.str"], ["DBSnapshotIdentifier", "builtins.str"], ["SnapshotType", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["MaxRecords", "builtins.int"], ["<PERSON><PERSON>", "builtins.str"], ["DbiResourceId", "builtins.str"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_db_snapshot_tenant_databases of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBSnapshotTenantDatabasesMessageTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_db_snapshots": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_db_snapshots", "name": "describe_db_snapshots", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBSnapshotsMessageTypeDef", "items": [["DBInstanceIdentifier", "builtins.str"], ["DBSnapshotIdentifier", "builtins.str"], ["SnapshotType", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["MaxRecords", "builtins.int"], ["<PERSON><PERSON>", "builtins.str"], ["IncludeShared", "builtins.bool"], ["IncludePublic", "builtins.bool"], ["DbiResourceId", "builtins.str"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_db_snapshots of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBSnapshotMessageTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_db_subnet_groups": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_db_subnet_groups", "name": "describe_db_subnet_groups", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBSubnetGroupsMessageTypeDef", "items": [["DBSubnetGroupName", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["MaxRecords", "builtins.int"], ["<PERSON><PERSON>", "builtins.str"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_db_subnet_groups of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBSubnetGroupMessageTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_engine_default_cluster_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_engine_default_cluster_parameters", "name": "describe_engine_default_cluster_parameters", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeEngineDefaultClusterParametersMessageTypeDef", "items": [["DBParameterGroupFamily", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["MaxRecords", "builtins.int"], ["<PERSON><PERSON>", "builtins.str"]], "required_keys": ["DBParameterGroupFamily"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_engine_default_cluster_parameters of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeEngineDefaultClusterParametersResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_engine_default_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_engine_default_parameters", "name": "describe_engine_default_parameters", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeEngineDefaultParametersMessageTypeDef", "items": [["DBParameterGroupFamily", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["MaxRecords", "builtins.int"], ["<PERSON><PERSON>", "builtins.str"]], "required_keys": ["DBParameterGroupFamily"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_engine_default_parameters of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeEngineDefaultParametersResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_event_categories": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_event_categories", "name": "describe_event_categories", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeEventCategoriesMessageTypeDef", "items": [["SourceType", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_event_categories of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.EventCategoriesMessageTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_event_subscriptions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_event_subscriptions", "name": "describe_event_subscriptions", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeEventSubscriptionsMessageTypeDef", "items": [["SubscriptionName", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["MaxRecords", "builtins.int"], ["<PERSON><PERSON>", "builtins.str"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_event_subscriptions of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.EventSubscriptionsMessageTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_events": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_events", "name": "describe_events", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeEventsMessageTypeDef", "items": [["SourceIdentifier", "builtins.str"], ["SourceType", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.literals.SourceTypeType"}], ["StartTime", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TimestampTypeDef"}], ["EndTime", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TimestampTypeDef"}], ["Duration", "builtins.int"], ["EventCategories", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["MaxRecords", "builtins.int"], ["<PERSON><PERSON>", "builtins.str"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_events of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.EventsMessageTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_export_tasks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_export_tasks", "name": "describe_export_tasks", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeExportTasksMessageTypeDef", "items": [["ExportTaskIdentifier", "builtins.str"], ["SourceArn", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["<PERSON><PERSON>", "builtins.str"], ["MaxRecords", "builtins.int"], ["SourceType", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.literals.ExportSourceTypeType"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_export_tasks of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ExportTasksMessageTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_global_clusters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_global_clusters", "name": "describe_global_clusters", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeGlobalClustersMessageTypeDef", "items": [["GlobalClusterIdentifier", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["MaxRecords", "builtins.int"], ["<PERSON><PERSON>", "builtins.str"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_global_clusters of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.GlobalClustersMessageTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_integrations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_integrations", "name": "describe_integrations", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeIntegrationsMessageTypeDef", "items": [["IntegrationIdentifier", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["MaxRecords", "builtins.int"], ["<PERSON><PERSON>", "builtins.str"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_integrations of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeIntegrationsResponseTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_option_group_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_option_group_options", "name": "describe_option_group_options", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeOptionGroupOptionsMessageTypeDef", "items": [["EngineName", "builtins.str"], ["MajorEngineVersion", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["MaxRecords", "builtins.int"], ["<PERSON><PERSON>", "builtins.str"]], "required_keys": ["EngineName"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_option_group_options of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.OptionGroupOptionsMessageTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_option_groups": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_option_groups", "name": "describe_option_groups", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeOptionGroupsMessageTypeDef", "items": [["OptionGroupName", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["<PERSON><PERSON>", "builtins.str"], ["MaxRecords", "builtins.int"], ["EngineName", "builtins.str"], ["MajorEngineVersion", "builtins.str"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_option_groups of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.OptionGroupsTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_orderable_db_instance_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_orderable_db_instance_options", "name": "describe_orderable_db_instance_options", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeOrderableDBInstanceOptionsMessageTypeDef", "items": [["Engine", "builtins.str"], ["EngineVersion", "builtins.str"], ["DBInstanceClass", "builtins.str"], ["LicenseModel", "builtins.str"], ["AvailabilityZoneGroup", "builtins.str"], ["Vpc", "builtins.bool"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["MaxRecords", "builtins.int"], ["<PERSON><PERSON>", "builtins.str"]], "required_keys": ["Engine"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_orderable_db_instance_options of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.OrderableDBInstanceOptionsMessageTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_pending_maintenance_actions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_pending_maintenance_actions", "name": "describe_pending_maintenance_actions", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribePendingMaintenanceActionsMessageTypeDef", "items": [["ResourceIdentifier", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["<PERSON><PERSON>", "builtins.str"], ["MaxRecords", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_pending_maintenance_actions of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PendingMaintenanceActionsMessageTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_reserved_db_instances": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_reserved_db_instances", "name": "describe_reserved_db_instances", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeReservedDBInstancesMessageTypeDef", "items": [["ReservedDBInstanceId", "builtins.str"], ["ReservedDBInstancesOfferingId", "builtins.str"], ["DBInstanceClass", "builtins.str"], ["Duration", "builtins.str"], ["ProductDescription", "builtins.str"], ["OfferingType", "builtins.str"], ["MultiAZ", "builtins.bool"], ["LeaseId", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["MaxRecords", "builtins.int"], ["<PERSON><PERSON>", "builtins.str"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_reserved_db_instances of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ReservedDBInstanceMessageTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_reserved_db_instances_offerings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_reserved_db_instances_offerings", "name": "describe_reserved_db_instances_offerings", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeReservedDBInstancesOfferingsMessageTypeDef", "items": [["ReservedDBInstancesOfferingId", "builtins.str"], ["DBInstanceClass", "builtins.str"], ["Duration", "builtins.str"], ["ProductDescription", "builtins.str"], ["OfferingType", "builtins.str"], ["MultiAZ", "builtins.bool"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["MaxRecords", "builtins.int"], ["<PERSON><PERSON>", "builtins.str"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_reserved_db_instances_offerings of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ReservedDBInstancesOfferingMessageTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_source_regions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_source_regions", "name": "describe_source_regions", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeSourceRegionsMessageTypeDef", "items": [["RegionName", "builtins.str"], ["MaxRecords", "builtins.int"], ["<PERSON><PERSON>", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_source_regions of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.SourceRegionMessageTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_tenant_databases": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_tenant_databases", "name": "describe_tenant_databases", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeTenantDatabasesMessageTypeDef", "items": [["DBInstanceIdentifier", "builtins.str"], ["TenantDBName", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["<PERSON><PERSON>", "builtins.str"], ["MaxRecords", "builtins.int"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_tenant_databases of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TenantDatabasesMessageTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "describe_valid_db_instance_modifications": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.describe_valid_db_instance_modifications", "name": "describe_valid_db_instance_modifications", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeValidDBInstanceModificationsMessageTypeDef", "items": [["DBInstanceIdentifier", "builtins.str"]], "required_keys": ["DBInstanceIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_valid_db_instance_modifications of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeValidDBInstanceModificationsResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "disable_http_endpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.disable_http_endpoint", "name": "disable_http_endpoint", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DisableHttpEndpointRequestTypeDef", "items": [["ResourceArn", "builtins.str"]], "required_keys": ["ResourceArn"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "disable_http_endpoint of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DisableHttpEndpointResponseTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "download_db_log_file_portion": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.download_db_log_file_portion", "name": "download_db_log_file_portion", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DownloadDBLogFilePortionMessageTypeDef", "items": [["DBInstanceIdentifier", "builtins.str"], ["LogFileName", "builtins.str"], ["<PERSON><PERSON>", "builtins.str"], ["NumberOfLines", "builtins.int"]], "required_keys": ["DBInstanceIdentifier", "LogFileName"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "download_db_log_file_portion of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DownloadDBLogFilePortionDetailsTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "enable_http_endpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.enable_http_endpoint", "name": "enable_http_endpoint", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.EnableHttpEndpointRequestTypeDef", "items": [["ResourceArn", "builtins.str"]], "required_keys": ["ResourceArn"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "enable_http_endpoint of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.EnableHttpEndpointResponseTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "exceptions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.exceptions", "name": "exceptions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mypy_boto3_rds.client.RDSClient"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exceptions of RDSClient", "ret_type": "mypy_boto3_rds.client.Exceptions", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.exceptions", "name": "exceptions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mypy_boto3_rds.client.RDSClient"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exceptions of RDSClient", "ret_type": "mypy_boto3_rds.client.Exceptions", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "failover_db_cluster": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.failover_db_cluster", "name": "failover_db_cluster", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.FailoverDBClusterMessageTypeDef", "items": [["DBClusterIdentifier", "builtins.str"], ["TargetDBInstanceIdentifier", "builtins.str"]], "required_keys": ["DBClusterIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "failover_db_cluster of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FailoverDBClusterResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "failover_global_cluster": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.failover_global_cluster", "name": "failover_global_cluster", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.FailoverGlobalClusterMessageTypeDef", "items": [["GlobalClusterIdentifier", "builtins.str"], ["TargetDbClusterIdentifier", "builtins.str"], ["AllowDataLoss", "builtins.bool"], ["Switchover", "builtins.bool"]], "required_keys": ["GlobalClusterIdentifier", "TargetDbClusterIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "failover_global_cluster of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FailoverGlobalClusterResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "generate_db_auth_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "DBHostname", "Port", "DBUsername", "Region"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.generate_db_auth_token", "name": "generate_db_auth_token", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "DBHostname", "Port", "DBUsername", "Region"], "arg_types": ["mypy_boto3_rds.client.RDSClient", "builtins.str", "builtins.int", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_db_auth_token of RDSClient", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "generate_presigned_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "ClientMethod", "Params", "ExpiresIn", "HttpMethod"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.generate_presigned_url", "name": "generate_presigned_url", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "ClientMethod", "Params", "ExpiresIn", "HttpMethod"], "arg_types": ["mypy_boto3_rds.client.RDSClient", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_presigned_url of RDSClient", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_paginator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_blue_green_deployments"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeBlueGreenDeploymentsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_blue_green_deployments"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeBlueGreenDeploymentsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_certificates"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeCertificatesPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_certificates"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeCertificatesPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_cluster_automated_backups"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBClusterAutomatedBackupsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_cluster_automated_backups"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBClusterAutomatedBackupsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_cluster_backtracks"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBClusterBacktracksPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_cluster_backtracks"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBClusterBacktracksPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_cluster_endpoints"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBClusterEndpointsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_cluster_endpoints"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBClusterEndpointsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_cluster_parameter_groups"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBClusterParameterGroupsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_cluster_parameter_groups"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBClusterParameterGroupsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_cluster_parameters"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBClusterParametersPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_cluster_parameters"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBClusterParametersPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_cluster_snapshots"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBClusterSnapshotsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_cluster_snapshots"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBClusterSnapshotsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_clusters"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBClustersPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_clusters"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBClustersPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_engine_versions"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBEngineVersionsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_engine_versions"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBEngineVersionsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_instance_automated_backups"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBInstanceAutomatedBackupsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_instance_automated_backups"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBInstanceAutomatedBackupsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_instances"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBInstancesPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_instances"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBInstancesPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_log_files"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBLogFilesPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_log_files"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBLogFilesPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_major_engine_versions"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBMajorEngineVersionsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_major_engine_versions"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBMajorEngineVersionsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_parameter_groups"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBParameterGroupsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_parameter_groups"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBParameterGroupsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_parameters"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBParametersPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_parameters"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBParametersPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_proxies"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBProxiesPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_proxies"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBProxiesPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_proxy_endpoints"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBProxyEndpointsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_proxy_endpoints"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBProxyEndpointsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_proxy_target_groups"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBProxyTargetGroupsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_proxy_target_groups"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBProxyTargetGroupsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_proxy_targets"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBProxyTargetsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_proxy_targets"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBProxyTargetsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_recommendations"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBRecommendationsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_recommendations"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBRecommendationsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_security_groups"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBSecurityGroupsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_security_groups"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBSecurityGroupsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_snapshot_tenant_databases"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBSnapshotTenantDatabasesPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_snapshot_tenant_databases"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBSnapshotTenantDatabasesPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_snapshots"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBSnapshotsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_snapshots"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBSnapshotsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_subnet_groups"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBSubnetGroupsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_subnet_groups"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBSubnetGroupsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_engine_default_cluster_parameters"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeEngineDefaultClusterParametersPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_engine_default_cluster_parameters"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeEngineDefaultClusterParametersPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_engine_default_parameters"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeEngineDefaultParametersPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_engine_default_parameters"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeEngineDefaultParametersPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_event_subscriptions"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeEventSubscriptionsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_event_subscriptions"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeEventSubscriptionsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_events"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeEventsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_events"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeEventsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_export_tasks"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeExportTasksPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_export_tasks"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeExportTasksPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_global_clusters"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeGlobalClustersPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_global_clusters"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeGlobalClustersPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_integrations"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeIntegrationsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_integrations"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeIntegrationsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_option_group_options"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeOptionGroupOptionsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_option_group_options"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeOptionGroupOptionsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_option_groups"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeOptionGroupsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_option_groups"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeOptionGroupsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_orderable_db_instance_options"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeOrderableDBInstanceOptionsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_orderable_db_instance_options"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeOrderableDBInstanceOptionsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_pending_maintenance_actions"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribePendingMaintenanceActionsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_pending_maintenance_actions"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribePendingMaintenanceActionsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_reserved_db_instances_offerings"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeReservedDBInstancesOfferingsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_reserved_db_instances_offerings"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeReservedDBInstancesOfferingsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_reserved_db_instances"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeReservedDBInstancesPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_reserved_db_instances"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeReservedDBInstancesPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_source_regions"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeSourceRegionsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_source_regions"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeSourceRegionsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_tenant_databases"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeTenantDatabasesPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_tenant_databases"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeTenantDatabasesPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "download_db_log_file_portion"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DownloadDBLogFilePortionPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_paginator", "name": "get_paginator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "download_db_log_file_portion"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DownloadDBLogFilePortionPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_blue_green_deployments"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeBlueGreenDeploymentsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_certificates"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeCertificatesPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_cluster_automated_backups"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBClusterAutomatedBackupsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_cluster_backtracks"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBClusterBacktracksPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_cluster_endpoints"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBClusterEndpointsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_cluster_parameter_groups"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBClusterParameterGroupsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_cluster_parameters"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBClusterParametersPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_cluster_snapshots"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBClusterSnapshotsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_clusters"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBClustersPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_engine_versions"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBEngineVersionsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_instance_automated_backups"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBInstanceAutomatedBackupsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_instances"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBInstancesPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_log_files"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBLogFilesPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_major_engine_versions"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBMajorEngineVersionsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_parameter_groups"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBParameterGroupsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_parameters"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBParametersPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_proxies"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBProxiesPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_proxy_endpoints"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBProxyEndpointsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_proxy_target_groups"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBProxyTargetGroupsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_proxy_targets"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBProxyTargetsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_recommendations"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBRecommendationsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_security_groups"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBSecurityGroupsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_snapshot_tenant_databases"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBSnapshotTenantDatabasesPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_snapshots"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBSnapshotsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_db_subnet_groups"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeDBSubnetGroupsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_engine_default_cluster_parameters"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeEngineDefaultClusterParametersPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_engine_default_parameters"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeEngineDefaultParametersPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_event_subscriptions"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeEventSubscriptionsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_events"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeEventsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_export_tasks"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeExportTasksPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_global_clusters"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeGlobalClustersPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_integrations"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeIntegrationsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_option_group_options"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeOptionGroupOptionsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_option_groups"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeOptionGroupsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_orderable_db_instance_options"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeOrderableDBInstanceOptionsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_pending_maintenance_actions"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribePendingMaintenanceActionsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_reserved_db_instances_offerings"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeReservedDBInstancesOfferingsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_reserved_db_instances"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeReservedDBInstancesPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_source_regions"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeSourceRegionsPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "describe_tenant_databases"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DescribeTenantDatabasesPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "download_db_log_file_portion"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_paginator of RDSClient", "ret_type": "mypy_boto3_rds.paginator.DownloadDBLogFilePortionPaginator", "type_guard": null, "unpack_kwargs": false, "variables": []}]}}}, "get_waiter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.get_waiter", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_waiter", "name": "get_waiter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "db_cluster_available"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_waiter of RDSClient", "ret_type": "mypy_boto3_rds.waiter.DBClusterAvailableWaiter", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_waiter", "name": "get_waiter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "db_cluster_available"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_waiter of RDSClient", "ret_type": "mypy_boto3_rds.waiter.DBClusterAvailableWaiter", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_waiter", "name": "get_waiter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "db_cluster_deleted"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_waiter of RDSClient", "ret_type": "mypy_boto3_rds.waiter.DBClusterDeletedWaiter", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_waiter", "name": "get_waiter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "db_cluster_deleted"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_waiter of RDSClient", "ret_type": "mypy_boto3_rds.waiter.DBClusterDeletedWaiter", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_waiter", "name": "get_waiter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "db_cluster_snapshot_available"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_waiter of RDSClient", "ret_type": "mypy_boto3_rds.waiter.DBClusterSnapshotAvailableWaiter", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_waiter", "name": "get_waiter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "db_cluster_snapshot_available"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_waiter of RDSClient", "ret_type": "mypy_boto3_rds.waiter.DBClusterSnapshotAvailableWaiter", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_waiter", "name": "get_waiter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "db_cluster_snapshot_deleted"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_waiter of RDSClient", "ret_type": "mypy_boto3_rds.waiter.DBClusterSnapshotDeletedWaiter", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_waiter", "name": "get_waiter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "db_cluster_snapshot_deleted"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_waiter of RDSClient", "ret_type": "mypy_boto3_rds.waiter.DBClusterSnapshotDeletedWaiter", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_waiter", "name": "get_waiter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "db_instance_available"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_waiter of RDSClient", "ret_type": "mypy_boto3_rds.waiter.DBInstanceAvailableWaiter", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_waiter", "name": "get_waiter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "db_instance_available"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_waiter of RDSClient", "ret_type": "mypy_boto3_rds.waiter.DBInstanceAvailableWaiter", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_waiter", "name": "get_waiter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "db_instance_deleted"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_waiter of RDSClient", "ret_type": "mypy_boto3_rds.waiter.DBInstanceDeletedWaiter", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_waiter", "name": "get_waiter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "db_instance_deleted"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_waiter of RDSClient", "ret_type": "mypy_boto3_rds.waiter.DBInstanceDeletedWaiter", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_waiter", "name": "get_waiter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "db_snapshot_available"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_waiter of RDSClient", "ret_type": "mypy_boto3_rds.waiter.DBSnapshotAvailableWaiter", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_waiter", "name": "get_waiter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "db_snapshot_available"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_waiter of RDSClient", "ret_type": "mypy_boto3_rds.waiter.DBSnapshotAvailableWaiter", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_waiter", "name": "get_waiter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "db_snapshot_completed"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_waiter of RDSClient", "ret_type": "mypy_boto3_rds.waiter.DBSnapshotCompletedWaiter", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_waiter", "name": "get_waiter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "db_snapshot_completed"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_waiter of RDSClient", "ret_type": "mypy_boto3_rds.waiter.DBSnapshotCompletedWaiter", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_waiter", "name": "get_waiter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "db_snapshot_deleted"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_waiter of RDSClient", "ret_type": "mypy_boto3_rds.waiter.DBSnapshotDeletedWaiter", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_waiter", "name": "get_waiter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "db_snapshot_deleted"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_waiter of RDSClient", "ret_type": "mypy_boto3_rds.waiter.DBSnapshotDeletedWaiter", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_waiter", "name": "get_waiter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "tenant_database_available"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_waiter of RDSClient", "ret_type": "mypy_boto3_rds.waiter.TenantDatabaseAvailableWaiter", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_waiter", "name": "get_waiter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "tenant_database_available"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_waiter of RDSClient", "ret_type": "mypy_boto3_rds.waiter.TenantDatabaseAvailableWaiter", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "mypy_boto3_rds.client.RDSClient.get_waiter", "name": "get_waiter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "tenant_database_deleted"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_waiter of RDSClient", "ret_type": "mypy_boto3_rds.waiter.TenantDatabaseDeletedWaiter", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mypy_boto3_rds.client.RDSClient.get_waiter", "name": "get_waiter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "tenant_database_deleted"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_waiter of RDSClient", "ret_type": "mypy_boto3_rds.waiter.TenantDatabaseDeletedWaiter", "type_guard": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "db_cluster_available"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_waiter of RDSClient", "ret_type": "mypy_boto3_rds.waiter.DBClusterAvailableWaiter", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "db_cluster_deleted"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_waiter of RDSClient", "ret_type": "mypy_boto3_rds.waiter.DBClusterDeletedWaiter", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "db_cluster_snapshot_available"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_waiter of RDSClient", "ret_type": "mypy_boto3_rds.waiter.DBClusterSnapshotAvailableWaiter", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "db_cluster_snapshot_deleted"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_waiter of RDSClient", "ret_type": "mypy_boto3_rds.waiter.DBClusterSnapshotDeletedWaiter", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "db_instance_available"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_waiter of RDSClient", "ret_type": "mypy_boto3_rds.waiter.DBInstanceAvailableWaiter", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "db_instance_deleted"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_waiter of RDSClient", "ret_type": "mypy_boto3_rds.waiter.DBInstanceDeletedWaiter", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "db_snapshot_available"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_waiter of RDSClient", "ret_type": "mypy_boto3_rds.waiter.DBSnapshotAvailableWaiter", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "db_snapshot_completed"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_waiter of RDSClient", "ret_type": "mypy_boto3_rds.waiter.DBSnapshotCompletedWaiter", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "db_snapshot_deleted"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_waiter of RDSClient", "ret_type": "mypy_boto3_rds.waiter.DBSnapshotDeletedWaiter", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "tenant_database_available"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_waiter of RDSClient", "ret_type": "mypy_boto3_rds.waiter.TenantDatabaseAvailableWaiter", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "waiter_name"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "LiteralType", "fallback": "builtins.str", "value": "tenant_database_deleted"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_waiter of RDSClient", "ret_type": "mypy_boto3_rds.waiter.TenantDatabaseDeletedWaiter", "type_guard": null, "unpack_kwargs": false, "variables": []}]}}}, "list_tags_for_resource": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.list_tags_for_resource", "name": "list_tags_for_resource", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.ListTagsForResourceMessageTypeDef", "items": [["ResourceName", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}]], "required_keys": ["ResourceName"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_tags_for_resource of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TagListMessageTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mypy_boto3_rds.client.RDSClient.meta", "name": "meta", "type": "botocore.client.ClientMeta"}}, "modify_activity_stream": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.modify_activity_stream", "name": "modify_activity_stream", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.ModifyActivityStreamRequestTypeDef", "items": [["ResourceArn", "builtins.str"], ["AuditPolicyState", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.literals.AuditPolicyStateType"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "modify_activity_stream of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ModifyActivityStreamResponseTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "modify_certificates": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.modify_certificates", "name": "modify_certificates", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.ModifyCertificatesMessageTypeDef", "items": [["CertificateIdentifier", "builtins.str"], ["RemoveCustomerOverride", "builtins.bool"]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "modify_certificates of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ModifyCertificatesResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "modify_current_db_cluster_capacity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.modify_current_db_cluster_capacity", "name": "modify_current_db_cluster_capacity", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.ModifyCurrentDBClusterCapacityMessageTypeDef", "items": [["DBClusterIdentifier", "builtins.str"], ["Capacity", "builtins.int"], ["SecondsBeforeTimeout", "builtins.int"], ["TimeoutAction", "builtins.str"]], "required_keys": ["DBClusterIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "modify_current_db_cluster_capacity of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBClusterCapacityInfoTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "modify_custom_db_engine_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.modify_custom_db_engine_version", "name": "modify_custom_db_engine_version", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.ModifyCustomDBEngineVersionMessageTypeDef", "items": [["Engine", "builtins.str"], ["EngineVersion", "builtins.str"], ["Description", "builtins.str"], ["Status", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.literals.CustomEngineVersionStatusType"}]], "required_keys": ["Engine", "EngineVersion"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "modify_custom_db_engine_version of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBEngineVersionResponseTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "modify_db_cluster": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.modify_db_cluster", "name": "modify_db_cluster", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.ModifyDBClusterMessageTypeDef", "items": [["DBClusterIdentifier", "builtins.str"], ["NewDBClusterIdentifier", "builtins.str"], ["ApplyImmediately", "builtins.bool"], ["BackupRetentionPeriod", "builtins.int"], ["DBClusterParameterGroupName", "builtins.str"], ["VpcSecurityGroupIds", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["Port", "builtins.int"], ["MasterUserPassword", "builtins.str"], ["OptionGroupName", "builtins.str"], ["PreferredBackupWindow", "builtins.str"], ["PreferredMaintenanceWindow", "builtins.str"], ["EnableIAMDatabaseAuthentication", "builtins.bool"], ["BacktrackWindow", "builtins.int"], ["CloudwatchLogsExportConfiguration", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.CloudwatchLogsExportConfigurationTypeDef"}], ["EngineVersion", "builtins.str"], ["AllowMajorVersionUpgrade", "builtins.bool"], ["DBInstanceParameterGroupName", "builtins.str"], ["Domain", "builtins.str"], ["DomainIAMRoleName", "builtins.str"], ["ScalingConfiguration", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ScalingConfigurationTypeDef"}], ["DeletionProtection", "builtins.bool"], ["EnableHttpEndpoint", "builtins.bool"], ["CopyTagsToSnapshot", "builtins.bool"], ["EnableGlobalWriteForwarding", "builtins.bool"], ["DBClusterInstanceClass", "builtins.str"], ["AllocatedStorage", "builtins.int"], ["StorageType", "builtins.str"], ["Iops", "builtins.int"], ["AutoMinorVersionUpgrade", "builtins.bool"], ["NetworkType", "builtins.str"], ["ServerlessV2ScalingConfiguration", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ServerlessV2ScalingConfigurationTypeDef"}], ["MonitoringInterval", "builtins.int"], ["MonitoringRoleArn", "builtins.str"], ["DatabaseInsightsMode", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.literals.DatabaseInsightsModeType"}], ["EnablePerformanceInsights", "builtins.bool"], ["PerformanceInsightsKMSKeyId", "builtins.str"], ["PerformanceInsightsRetentionPeriod", "builtins.int"], ["ManageMasterUserPassword", "builtins.bool"], ["RotateMasterUserPassword", "builtins.bool"], ["EnableLocalWriteForwarding", "builtins.bool"], ["MasterUserSecretKmsKeyId", "builtins.str"], ["EngineMode", "builtins.str"], ["AllowEngineModeChange", "builtins.bool"], ["AwsBackupRecoveryPointArn", "builtins.str"], ["EnableLimitlessDatabase", "builtins.bool"], ["CACertificateIdentifier", "builtins.str"], ["MasterUserAuthenticationType", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.literals.MasterUserAuthenticationTypeType"}]], "required_keys": ["DBClusterIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "modify_db_cluster of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ModifyDBClusterResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "modify_db_cluster_endpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.modify_db_cluster_endpoint", "name": "modify_db_cluster_endpoint", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.ModifyDBClusterEndpointMessageTypeDef", "items": [["DBClusterEndpointIdentifier", "builtins.str"], ["EndpointType", "builtins.str"], ["StaticMembers", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["ExcludedMembers", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}]], "required_keys": ["DBClusterEndpointIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "modify_db_cluster_endpoint of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBClusterEndpointResponseTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "modify_db_cluster_parameter_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.modify_db_cluster_parameter_group", "name": "modify_db_cluster_parameter_group", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.ModifyDBClusterParameterGroupMessageTypeDef", "items": [["DBClusterParameterGroupName", "builtins.str"], ["Parameters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ParameterUnionTypeDef"}], "type_ref": "typing.Sequence"}]], "required_keys": ["DBClusterParameterGroupName", "Parameters"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "modify_db_cluster_parameter_group of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBClusterParameterGroupNameMessageTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "modify_db_cluster_snapshot_attribute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.modify_db_cluster_snapshot_attribute", "name": "modify_db_cluster_snapshot_attribute", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.ModifyDBClusterSnapshotAttributeMessageTypeDef", "items": [["DBClusterSnapshotIdentifier", "builtins.str"], ["AttributeName", "builtins.str"], ["ValuesToAdd", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["ValuesToRemove", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}]], "required_keys": ["AttributeName", "DBClusterSnapshotIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "modify_db_cluster_snapshot_attribute of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ModifyDBClusterSnapshotAttributeResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "modify_db_instance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.modify_db_instance", "name": "modify_db_instance", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.ModifyDBInstanceMessageTypeDef", "items": [["DBInstanceIdentifier", "builtins.str"], ["AllocatedStorage", "builtins.int"], ["DBInstanceClass", "builtins.str"], ["DBSubnetGroupName", "builtins.str"], ["DBSecurityGroups", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["VpcSecurityGroupIds", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["ApplyImmediately", "builtins.bool"], ["MasterUserPassword", "builtins.str"], ["DBParameterGroupName", "builtins.str"], ["BackupRetentionPeriod", "builtins.int"], ["PreferredBackupWindow", "builtins.str"], ["PreferredMaintenanceWindow", "builtins.str"], ["MultiAZ", "builtins.bool"], ["EngineVersion", "builtins.str"], ["AllowMajorVersionUpgrade", "builtins.bool"], ["AutoMinorVersionUpgrade", "builtins.bool"], ["LicenseModel", "builtins.str"], ["Iops", "builtins.int"], ["StorageThroughput", "builtins.int"], ["OptionGroupName", "builtins.str"], ["NewDBInstanceIdentifier", "builtins.str"], ["StorageType", "builtins.str"], ["TdeCredentialArn", "builtins.str"], ["TdeCredentialPassword", "builtins.str"], ["CACertificateIdentifier", "builtins.str"], ["Domain", "builtins.str"], ["DomainFqdn", "builtins.str"], ["DomainOu", "builtins.str"], ["DomainAuthSecretArn", "builtins.str"], ["DomainDnsIps", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["DisableDomain", "builtins.bool"], ["CopyTagsToSnapshot", "builtins.bool"], ["MonitoringInterval", "builtins.int"], ["DBPortNumber", "builtins.int"], ["PubliclyAccessible", "builtins.bool"], ["MonitoringRoleArn", "builtins.str"], ["DomainIAMRoleName", "builtins.str"], ["PromotionTier", "builtins.int"], ["EnableIAMDatabaseAuthentication", "builtins.bool"], ["DatabaseInsightsMode", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.literals.DatabaseInsightsModeType"}], ["EnablePerformanceInsights", "builtins.bool"], ["PerformanceInsightsKMSKeyId", "builtins.str"], ["PerformanceInsightsRetentionPeriod", "builtins.int"], ["CloudwatchLogsExportConfiguration", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.CloudwatchLogsExportConfigurationTypeDef"}], ["ProcessorFeatures", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ProcessorFeatureTypeDef"}], "type_ref": "typing.Sequence"}], ["UseDefaultProcessorFeatures", "builtins.bool"], ["DeletionProtection", "builtins.bool"], ["MaxAllocatedStorage", "builtins.int"], ["CertificateRotationRestart", "builtins.bool"], ["ReplicaMode", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.literals.ReplicaModeType"}], ["AutomationMode", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.literals.AutomationModeType"}], ["ResumeFullAutomationModeMinutes", "builtins.int"], ["EnableCustomerOwnedIp", "builtins.bool"], ["NetworkType", "builtins.str"], ["AwsBackupRecoveryPointArn", "builtins.str"], ["ManageMasterUserPassword", "builtins.bool"], ["RotateMasterUserPassword", "builtins.bool"], ["MasterUserSecretKmsKeyId", "builtins.str"], ["MultiTenant", "builtins.bool"], ["DedicatedLogVolume", "builtins.bool"], ["Engine", "builtins.str"], ["MasterUserAuthenticationType", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.literals.MasterUserAuthenticationTypeType"}]], "required_keys": ["DBInstanceIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "modify_db_instance of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ModifyDBInstanceResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "modify_db_parameter_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.modify_db_parameter_group", "name": "modify_db_parameter_group", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.ModifyDBParameterGroupMessageTypeDef", "items": [["DBParameterGroupName", "builtins.str"], ["Parameters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ParameterUnionTypeDef"}], "type_ref": "typing.Sequence"}]], "required_keys": ["DBParameterGroupName", "Parameters"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "modify_db_parameter_group of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBParameterGroupNameMessageTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "modify_db_proxy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.modify_db_proxy", "name": "modify_db_proxy", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.ModifyDBProxyRequestTypeDef", "items": [["DBProxyName", "builtins.str"], ["NewDBProxyName", "builtins.str"], ["DefaultAuthScheme", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.literals.DefaultAuthSchemeType"}], ["<PERSON><PERSON>", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.UserAuthConfigTypeDef"}], "type_ref": "typing.Sequence"}], ["RequireTLS", "builtins.bool"], ["IdleClientTimeout", "builtins.int"], ["DebugLogging", "builtins.bool"], ["RoleArn", "builtins.str"], ["SecurityGroups", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}]], "required_keys": ["DBProxyName"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "modify_db_proxy of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ModifyDBProxyResponseTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "modify_db_proxy_endpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.modify_db_proxy_endpoint", "name": "modify_db_proxy_endpoint", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.ModifyDBProxyEndpointRequestTypeDef", "items": [["DBProxyEndpointName", "builtins.str"], ["NewDBProxyEndpointName", "builtins.str"], ["VpcSecurityGroupIds", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}]], "required_keys": ["DBProxyEndpointName"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "modify_db_proxy_endpoint of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ModifyDBProxyEndpointResponseTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "modify_db_proxy_target_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.modify_db_proxy_target_group", "name": "modify_db_proxy_target_group", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.ModifyDBProxyTargetGroupRequestTypeDef", "items": [["TargetGroupName", "builtins.str"], ["DBProxyName", "builtins.str"], ["ConnectionPoolConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ConnectionPoolConfigurationTypeDef"}], ["NewName", "builtins.str"]], "required_keys": ["DBProxyName", "TargetGroupName"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "modify_db_proxy_target_group of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ModifyDBProxyTargetGroupResponseTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "modify_db_recommendation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.modify_db_recommendation", "name": "modify_db_recommendation", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.ModifyDBRecommendationMessageTypeDef", "items": [["RecommendationId", "builtins.str"], ["Locale", "builtins.str"], ["Status", "builtins.str"], ["RecommendedActionUpdates", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.RecommendedActionUpdateTypeDef"}], "type_ref": "typing.Sequence"}]], "required_keys": ["RecommendationId"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "modify_db_recommendation of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBRecommendationMessageTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "modify_db_shard_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.modify_db_shard_group", "name": "modify_db_shard_group", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.ModifyDBShardGroupMessageTypeDef", "items": [["DBShardGroupIdentifier", "builtins.str"], ["MaxACU", "builtins.float"], ["MinACU", "builtins.float"], ["ComputeRedundancy", "builtins.int"]], "required_keys": ["DBShardGroupIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "modify_db_shard_group of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBShardGroupResponseTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "modify_db_snapshot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.modify_db_snapshot", "name": "modify_db_snapshot", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.ModifyDBSnapshotMessageTypeDef", "items": [["DBSnapshotIdentifier", "builtins.str"], ["EngineVersion", "builtins.str"], ["OptionGroupName", "builtins.str"]], "required_keys": ["DBSnapshotIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "modify_db_snapshot of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ModifyDBSnapshotResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "modify_db_snapshot_attribute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.modify_db_snapshot_attribute", "name": "modify_db_snapshot_attribute", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.ModifyDBSnapshotAttributeMessageTypeDef", "items": [["DBSnapshotIdentifier", "builtins.str"], ["AttributeName", "builtins.str"], ["ValuesToAdd", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["ValuesToRemove", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}]], "required_keys": ["AttributeName", "DBSnapshotIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "modify_db_snapshot_attribute of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ModifyDBSnapshotAttributeResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "modify_db_subnet_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.modify_db_subnet_group", "name": "modify_db_subnet_group", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.ModifyDBSubnetGroupMessageTypeDef", "items": [["DBSubnetGroupName", "builtins.str"], ["SubnetIds", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["DBSubnetGroupDescription", "builtins.str"]], "required_keys": ["DBSubnetGroupName", "SubnetIds"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "modify_db_subnet_group of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ModifyDBSubnetGroupResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "modify_event_subscription": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.modify_event_subscription", "name": "modify_event_subscription", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.ModifyEventSubscriptionMessageTypeDef", "items": [["SubscriptionName", "builtins.str"], ["SnsTopicArn", "builtins.str"], ["SourceType", "builtins.str"], ["EventCategories", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["Enabled", "builtins.bool"]], "required_keys": ["SubscriptionName"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "modify_event_subscription of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ModifyEventSubscriptionResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "modify_global_cluster": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.modify_global_cluster", "name": "modify_global_cluster", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.ModifyGlobalClusterMessageTypeDef", "items": [["GlobalClusterIdentifier", "builtins.str"], ["NewGlobalClusterIdentifier", "builtins.str"], ["DeletionProtection", "builtins.bool"], ["EngineVersion", "builtins.str"], ["AllowMajorVersionUpgrade", "builtins.bool"]], "required_keys": ["GlobalClusterIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "modify_global_cluster of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ModifyGlobalClusterResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "modify_integration": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.modify_integration", "name": "modify_integration", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.ModifyIntegrationMessageTypeDef", "items": [["IntegrationIdentifier", "builtins.str"], ["IntegrationName", "builtins.str"], ["DataFilter", "builtins.str"], ["Description", "builtins.str"]], "required_keys": ["IntegrationIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "modify_integration of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.IntegrationResponseTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "modify_option_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.modify_option_group", "name": "modify_option_group", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.ModifyOptionGroupMessageTypeDef", "items": [["OptionGroupName", "builtins.str"], ["OptionsToInclude", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.OptionConfigurationTypeDef"}], "type_ref": "typing.Sequence"}], ["OptionsToRemove", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["ApplyImmediately", "builtins.bool"]], "required_keys": ["OptionGroupName"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "modify_option_group of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ModifyOptionGroupResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "modify_tenant_database": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.modify_tenant_database", "name": "modify_tenant_database", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.ModifyTenantDatabaseMessageTypeDef", "items": [["DBInstanceIdentifier", "builtins.str"], ["TenantDBName", "builtins.str"], ["MasterUserPassword", "builtins.str"], ["NewTenantDBName", "builtins.str"], ["ManageMasterUserPassword", "builtins.bool"], ["RotateMasterUserPassword", "builtins.bool"], ["MasterUserSecretKmsKeyId", "builtins.str"]], "required_keys": ["DBInstanceIdentifier", "TenantDBName"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "modify_tenant_database of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ModifyTenantDatabaseResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "promote_read_replica": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.promote_read_replica", "name": "promote_read_replica", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.PromoteReadReplicaMessageTypeDef", "items": [["DBInstanceIdentifier", "builtins.str"], ["BackupRetentionPeriod", "builtins.int"], ["PreferredBackupWindow", "builtins.str"]], "required_keys": ["DBInstanceIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "promote_read_replica of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PromoteReadReplicaResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "promote_read_replica_db_cluster": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.promote_read_replica_db_cluster", "name": "promote_read_replica_db_cluster", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.PromoteReadReplicaDBClusterMessageTypeDef", "items": [["DBClusterIdentifier", "builtins.str"]], "required_keys": ["DBClusterIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "promote_read_replica_db_cluster of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PromoteReadReplicaDBClusterResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "purchase_reserved_db_instances_offering": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.purchase_reserved_db_instances_offering", "name": "purchase_reserved_db_instances_offering", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.PurchaseReservedDBInstancesOfferingMessageTypeDef", "items": [["ReservedDBInstancesOfferingId", "builtins.str"], ["ReservedDBInstanceId", "builtins.str"], ["DBInstanceCount", "builtins.int"], ["Tags", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TagTypeDef"}], "type_ref": "typing.Sequence"}]], "required_keys": ["ReservedDBInstancesOfferingId"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "purchase_reserved_db_instances_offering of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PurchaseReservedDBInstancesOfferingResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "reboot_db_cluster": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.reboot_db_cluster", "name": "reboot_db_cluster", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.RebootDBClusterMessageTypeDef", "items": [["DBClusterIdentifier", "builtins.str"]], "required_keys": ["DBClusterIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reboot_db_cluster of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.RebootDBClusterResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "reboot_db_instance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.reboot_db_instance", "name": "reboot_db_instance", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.RebootDBInstanceMessageTypeDef", "items": [["DBInstanceIdentifier", "builtins.str"], ["ForceFailover", "builtins.bool"]], "required_keys": ["DBInstanceIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reboot_db_instance of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.RebootDBInstanceResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "reboot_db_shard_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.reboot_db_shard_group", "name": "reboot_db_shard_group", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.RebootDBShardGroupMessageTypeDef", "items": [["DBShardGroupIdentifier", "builtins.str"]], "required_keys": ["DBShardGroupIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reboot_db_shard_group of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBShardGroupResponseTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "register_db_proxy_targets": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.register_db_proxy_targets", "name": "register_db_proxy_targets", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.RegisterDBProxyTargetsRequestTypeDef", "items": [["DBProxyName", "builtins.str"], ["TargetGroupName", "builtins.str"], ["DBInstanceIdentifiers", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["DBClusterIdentifiers", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}]], "required_keys": ["DBProxyName"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_db_proxy_targets of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.RegisterDBProxyTargetsResponseTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "remove_from_global_cluster": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.remove_from_global_cluster", "name": "remove_from_global_cluster", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.RemoveFromGlobalClusterMessageTypeDef", "items": [["GlobalClusterIdentifier", "builtins.str"], ["DbClusterIdentifier", "builtins.str"]], "required_keys": ["DbClusterIdentifier", "GlobalClusterIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_from_global_cluster of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.RemoveFromGlobalClusterResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "remove_role_from_db_cluster": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.remove_role_from_db_cluster", "name": "remove_role_from_db_cluster", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.RemoveRoleFromDBClusterMessageTypeDef", "items": [["DBClusterIdentifier", "builtins.str"], ["RoleArn", "builtins.str"], ["FeatureName", "builtins.str"]], "required_keys": ["DBClusterIdentifier", "RoleArn"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_role_from_db_cluster of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.EmptyResponseMetadataTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "remove_role_from_db_instance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.remove_role_from_db_instance", "name": "remove_role_from_db_instance", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.RemoveRoleFromDBInstanceMessageTypeDef", "items": [["DBInstanceIdentifier", "builtins.str"], ["RoleArn", "builtins.str"], ["FeatureName", "builtins.str"]], "required_keys": ["DBInstanceIdentifier", "FeatureName", "RoleArn"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_role_from_db_instance of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.EmptyResponseMetadataTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "remove_source_identifier_from_subscription": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.remove_source_identifier_from_subscription", "name": "remove_source_identifier_from_subscription", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.RemoveSourceIdentifierFromSubscriptionMessageTypeDef", "items": [["SubscriptionName", "builtins.str"], ["SourceIdentifier", "builtins.str"]], "required_keys": ["SourceIdentifier", "SubscriptionName"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_source_identifier_from_subscription of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.RemoveSourceIdentifierFromSubscriptionResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "remove_tags_from_resource": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.remove_tags_from_resource", "name": "remove_tags_from_resource", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.RemoveTagsFromResourceMessageTypeDef", "items": [["ResourceName", "builtins.str"], ["TagKeys", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}]], "required_keys": ["ResourceName", "TagKeys"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_tags_from_resource of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.EmptyResponseMetadataTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "reset_db_cluster_parameter_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.reset_db_cluster_parameter_group", "name": "reset_db_cluster_parameter_group", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.ResetDBClusterParameterGroupMessageTypeDef", "items": [["DBClusterParameterGroupName", "builtins.str"], ["ResetAllParameters", "builtins.bool"], ["Parameters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ParameterUnionTypeDef"}], "type_ref": "typing.Sequence"}]], "required_keys": ["DBClusterParameterGroupName"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset_db_cluster_parameter_group of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBClusterParameterGroupNameMessageTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "reset_db_parameter_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.reset_db_parameter_group", "name": "reset_db_parameter_group", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.ResetDBParameterGroupMessageTypeDef", "items": [["DBParameterGroupName", "builtins.str"], ["ResetAllParameters", "builtins.bool"], ["Parameters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ParameterUnionTypeDef"}], "type_ref": "typing.Sequence"}]], "required_keys": ["DBParameterGroupName"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset_db_parameter_group of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBParameterGroupNameMessageTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "restore_db_cluster_from_s3": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.restore_db_cluster_from_s3", "name": "restore_db_cluster_from_s3", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.RestoreDBClusterFromS3MessageTypeDef", "items": [["DBClusterIdentifier", "builtins.str"], ["Engine", "builtins.str"], ["MasterUsername", "builtins.str"], ["SourceEngine", "builtins.str"], ["SourceEngineVersion", "builtins.str"], ["S3BucketName", "builtins.str"], ["S3IngestionRoleArn", "builtins.str"], ["AvailabilityZones", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["BackupRetentionPeriod", "builtins.int"], ["CharacterSetName", "builtins.str"], ["DatabaseName", "builtins.str"], ["DBClusterParameterGroupName", "builtins.str"], ["VpcSecurityGroupIds", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["DBSubnetGroupName", "builtins.str"], ["EngineVersion", "builtins.str"], ["Port", "builtins.int"], ["MasterUserPassword", "builtins.str"], ["OptionGroupName", "builtins.str"], ["PreferredBackupWindow", "builtins.str"], ["PreferredMaintenanceWindow", "builtins.str"], ["Tags", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TagTypeDef"}], "type_ref": "typing.Sequence"}], ["StorageEncrypted", "builtins.bool"], ["KmsKeyId", "builtins.str"], ["EnableIAMDatabaseAuthentication", "builtins.bool"], ["S3Prefix", "builtins.str"], ["BacktrackWindow", "builtins.int"], ["EnableCloudwatchLogsExports", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["DeletionProtection", "builtins.bool"], ["CopyTagsToSnapshot", "builtins.bool"], ["Domain", "builtins.str"], ["DomainIAMRoleName", "builtins.str"], ["StorageType", "builtins.str"], ["NetworkType", "builtins.str"], ["ServerlessV2ScalingConfiguration", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ServerlessV2ScalingConfigurationTypeDef"}], ["ManageMasterUserPassword", "builtins.bool"], ["MasterUserSecretKmsKeyId", "builtins.str"], ["EngineLifecycleSupport", "builtins.str"]], "required_keys": ["DBClusterIdentifier", "Engine", "MasterUsername", "S3BucketName", "S3IngestionRoleArn", "SourceEngine", "SourceEngineVersion"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "restore_db_cluster_from_s3 of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.RestoreDBClusterFromS3ResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "restore_db_cluster_from_snapshot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.restore_db_cluster_from_snapshot", "name": "restore_db_cluster_from_snapshot", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.RestoreDBClusterFromSnapshotMessageTypeDef", "items": [["DBClusterIdentifier", "builtins.str"], ["SnapshotIdentifier", "builtins.str"], ["Engine", "builtins.str"], ["AvailabilityZones", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["EngineVersion", "builtins.str"], ["Port", "builtins.int"], ["DBSubnetGroupName", "builtins.str"], ["DatabaseName", "builtins.str"], ["OptionGroupName", "builtins.str"], ["VpcSecurityGroupIds", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["Tags", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TagTypeDef"}], "type_ref": "typing.Sequence"}], ["KmsKeyId", "builtins.str"], ["EnableIAMDatabaseAuthentication", "builtins.bool"], ["BacktrackWindow", "builtins.int"], ["EnableCloudwatchLogsExports", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["EngineMode", "builtins.str"], ["ScalingConfiguration", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ScalingConfigurationTypeDef"}], ["DBClusterParameterGroupName", "builtins.str"], ["DeletionProtection", "builtins.bool"], ["CopyTagsToSnapshot", "builtins.bool"], ["Domain", "builtins.str"], ["DomainIAMRoleName", "builtins.str"], ["DBClusterInstanceClass", "builtins.str"], ["StorageType", "builtins.str"], ["Iops", "builtins.int"], ["PubliclyAccessible", "builtins.bool"], ["NetworkType", "builtins.str"], ["ServerlessV2ScalingConfiguration", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ServerlessV2ScalingConfigurationTypeDef"}], ["RdsCustomClusterConfiguration", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.RdsCustomClusterConfigurationTypeDef"}], ["MonitoringInterval", "builtins.int"], ["MonitoringRoleArn", "builtins.str"], ["EnablePerformanceInsights", "builtins.bool"], ["PerformanceInsightsKMSKeyId", "builtins.str"], ["PerformanceInsightsRetentionPeriod", "builtins.int"], ["EngineLifecycleSupport", "builtins.str"]], "required_keys": ["DBClusterIdentifier", "Engine", "SnapshotIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "restore_db_cluster_from_snapshot of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.RestoreDBClusterFromSnapshotResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "restore_db_cluster_to_point_in_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.restore_db_cluster_to_point_in_time", "name": "restore_db_cluster_to_point_in_time", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.RestoreDBClusterToPointInTimeMessageTypeDef", "items": [["DBClusterIdentifier", "builtins.str"], ["RestoreType", "builtins.str"], ["SourceDBClusterIdentifier", "builtins.str"], ["RestoreToTime", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TimestampTypeDef"}], ["UseLatestRestorableTime", "builtins.bool"], ["Port", "builtins.int"], ["DBSubnetGroupName", "builtins.str"], ["OptionGroupName", "builtins.str"], ["VpcSecurityGroupIds", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["Tags", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TagTypeDef"}], "type_ref": "typing.Sequence"}], ["KmsKeyId", "builtins.str"], ["EnableIAMDatabaseAuthentication", "builtins.bool"], ["BacktrackWindow", "builtins.int"], ["EnableCloudwatchLogsExports", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["DBClusterParameterGroupName", "builtins.str"], ["DeletionProtection", "builtins.bool"], ["CopyTagsToSnapshot", "builtins.bool"], ["Domain", "builtins.str"], ["DomainIAMRoleName", "builtins.str"], ["DBClusterInstanceClass", "builtins.str"], ["StorageType", "builtins.str"], ["PubliclyAccessible", "builtins.bool"], ["Iops", "builtins.int"], ["NetworkType", "builtins.str"], ["SourceDbClusterResourceId", "builtins.str"], ["ServerlessV2ScalingConfiguration", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ServerlessV2ScalingConfigurationTypeDef"}], ["ScalingConfiguration", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ScalingConfigurationTypeDef"}], ["EngineMode", "builtins.str"], ["RdsCustomClusterConfiguration", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.RdsCustomClusterConfigurationTypeDef"}], ["MonitoringInterval", "builtins.int"], ["MonitoringRoleArn", "builtins.str"], ["EnablePerformanceInsights", "builtins.bool"], ["PerformanceInsightsKMSKeyId", "builtins.str"], ["PerformanceInsightsRetentionPeriod", "builtins.int"], ["EngineLifecycleSupport", "builtins.str"]], "required_keys": ["DBClusterIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "restore_db_cluster_to_point_in_time of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.RestoreDBClusterToPointInTimeResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "restore_db_instance_from_db_snapshot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.restore_db_instance_from_db_snapshot", "name": "restore_db_instance_from_db_snapshot", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.RestoreDBInstanceFromDBSnapshotMessageTypeDef", "items": [["DBInstanceIdentifier", "builtins.str"], ["DBSnapshotIdentifier", "builtins.str"], ["DBInstanceClass", "builtins.str"], ["Port", "builtins.int"], ["AvailabilityZone", "builtins.str"], ["DBSubnetGroupName", "builtins.str"], ["MultiAZ", "builtins.bool"], ["PubliclyAccessible", "builtins.bool"], ["AutoMinorVersionUpgrade", "builtins.bool"], ["LicenseModel", "builtins.str"], ["DBName", "builtins.str"], ["Engine", "builtins.str"], ["Iops", "builtins.int"], ["StorageThroughput", "builtins.int"], ["OptionGroupName", "builtins.str"], ["Tags", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TagTypeDef"}], "type_ref": "typing.Sequence"}], ["StorageType", "builtins.str"], ["TdeCredentialArn", "builtins.str"], ["TdeCredentialPassword", "builtins.str"], ["VpcSecurityGroupIds", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["Domain", "builtins.str"], ["DomainFqdn", "builtins.str"], ["DomainOu", "builtins.str"], ["DomainAuthSecretArn", "builtins.str"], ["DomainDnsIps", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["CopyTagsToSnapshot", "builtins.bool"], ["DomainIAMRoleName", "builtins.str"], ["EnableIAMDatabaseAuthentication", "builtins.bool"], ["EnableCloudwatchLogsExports", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["ProcessorFeatures", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ProcessorFeatureTypeDef"}], "type_ref": "typing.Sequence"}], ["UseDefaultProcessorFeatures", "builtins.bool"], ["DBParameterGroupName", "builtins.str"], ["DeletionProtection", "builtins.bool"], ["EnableCustomerOwnedIp", "builtins.bool"], ["NetworkType", "builtins.str"], ["Backup<PERSON>arget", "builtins.str"], ["CustomIamInstanceProfile", "builtins.str"], ["AllocatedStorage", "builtins.int"], ["DBClusterSnapshotIdentifier", "builtins.str"], ["DedicatedLogVolume", "builtins.bool"], ["CACertificateIdentifier", "builtins.str"], ["EngineLifecycleSupport", "builtins.str"], ["ManageMasterUserPassword", "builtins.bool"], ["MasterUserSecretKmsKeyId", "builtins.str"]], "required_keys": ["DBInstanceIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "restore_db_instance_from_db_snapshot of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.RestoreDBInstanceFromDBSnapshotResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "restore_db_instance_from_s3": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.restore_db_instance_from_s3", "name": "restore_db_instance_from_s3", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.RestoreDBInstanceFromS3MessageTypeDef", "items": [["DBInstanceIdentifier", "builtins.str"], ["DBInstanceClass", "builtins.str"], ["Engine", "builtins.str"], ["SourceEngine", "builtins.str"], ["SourceEngineVersion", "builtins.str"], ["S3BucketName", "builtins.str"], ["S3IngestionRoleArn", "builtins.str"], ["DBName", "builtins.str"], ["AllocatedStorage", "builtins.int"], ["MasterUsername", "builtins.str"], ["MasterUserPassword", "builtins.str"], ["DBSecurityGroups", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["VpcSecurityGroupIds", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["AvailabilityZone", "builtins.str"], ["DBSubnetGroupName", "builtins.str"], ["PreferredMaintenanceWindow", "builtins.str"], ["DBParameterGroupName", "builtins.str"], ["BackupRetentionPeriod", "builtins.int"], ["PreferredBackupWindow", "builtins.str"], ["Port", "builtins.int"], ["MultiAZ", "builtins.bool"], ["EngineVersion", "builtins.str"], ["AutoMinorVersionUpgrade", "builtins.bool"], ["LicenseModel", "builtins.str"], ["Iops", "builtins.int"], ["StorageThroughput", "builtins.int"], ["OptionGroupName", "builtins.str"], ["PubliclyAccessible", "builtins.bool"], ["Tags", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TagTypeDef"}], "type_ref": "typing.Sequence"}], ["StorageType", "builtins.str"], ["StorageEncrypted", "builtins.bool"], ["KmsKeyId", "builtins.str"], ["CopyTagsToSnapshot", "builtins.bool"], ["MonitoringInterval", "builtins.int"], ["MonitoringRoleArn", "builtins.str"], ["EnableIAMDatabaseAuthentication", "builtins.bool"], ["S3Prefix", "builtins.str"], ["DatabaseInsightsMode", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.literals.DatabaseInsightsModeType"}], ["EnablePerformanceInsights", "builtins.bool"], ["PerformanceInsightsKMSKeyId", "builtins.str"], ["PerformanceInsightsRetentionPeriod", "builtins.int"], ["EnableCloudwatchLogsExports", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["ProcessorFeatures", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ProcessorFeatureTypeDef"}], "type_ref": "typing.Sequence"}], ["UseDefaultProcessorFeatures", "builtins.bool"], ["DeletionProtection", "builtins.bool"], ["MaxAllocatedStorage", "builtins.int"], ["NetworkType", "builtins.str"], ["ManageMasterUserPassword", "builtins.bool"], ["MasterUserSecretKmsKeyId", "builtins.str"], ["DedicatedLogVolume", "builtins.bool"], ["CACertificateIdentifier", "builtins.str"], ["EngineLifecycleSupport", "builtins.str"]], "required_keys": ["DBInstanceClass", "DBInstanceIdentifier", "Engine", "S3BucketName", "S3IngestionRoleArn", "SourceEngine", "SourceEngineVersion"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "restore_db_instance_from_s3 of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.RestoreDBInstanceFromS3ResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "restore_db_instance_to_point_in_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.restore_db_instance_to_point_in_time", "name": "restore_db_instance_to_point_in_time", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.RestoreDBInstanceToPointInTimeMessageTypeDef", "items": [["TargetDBInstanceIdentifier", "builtins.str"], ["SourceDBInstanceIdentifier", "builtins.str"], ["RestoreTime", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TimestampTypeDef"}], ["UseLatestRestorableTime", "builtins.bool"], ["DBInstanceClass", "builtins.str"], ["Port", "builtins.int"], ["AvailabilityZone", "builtins.str"], ["DBSubnetGroupName", "builtins.str"], ["MultiAZ", "builtins.bool"], ["PubliclyAccessible", "builtins.bool"], ["AutoMinorVersionUpgrade", "builtins.bool"], ["LicenseModel", "builtins.str"], ["DBName", "builtins.str"], ["Engine", "builtins.str"], ["Iops", "builtins.int"], ["StorageThroughput", "builtins.int"], ["OptionGroupName", "builtins.str"], ["CopyTagsToSnapshot", "builtins.bool"], ["Tags", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TagTypeDef"}], "type_ref": "typing.Sequence"}], ["StorageType", "builtins.str"], ["TdeCredentialArn", "builtins.str"], ["TdeCredentialPassword", "builtins.str"], ["VpcSecurityGroupIds", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["Domain", "builtins.str"], ["DomainIAMRoleName", "builtins.str"], ["DomainFqdn", "builtins.str"], ["DomainOu", "builtins.str"], ["DomainAuthSecretArn", "builtins.str"], ["DomainDnsIps", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["EnableIAMDatabaseAuthentication", "builtins.bool"], ["EnableCloudwatchLogsExports", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["ProcessorFeatures", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ProcessorFeatureTypeDef"}], "type_ref": "typing.Sequence"}], ["UseDefaultProcessorFeatures", "builtins.bool"], ["DBParameterGroupName", "builtins.str"], ["DeletionProtection", "builtins.bool"], ["SourceDbiResourceId", "builtins.str"], ["MaxAllocatedStorage", "builtins.int"], ["EnableCustomerOwnedIp", "builtins.bool"], ["NetworkType", "builtins.str"], ["SourceDBInstanceAutomatedBackupsArn", "builtins.str"], ["Backup<PERSON>arget", "builtins.str"], ["CustomIamInstanceProfile", "builtins.str"], ["AllocatedStorage", "builtins.int"], ["DedicatedLogVolume", "builtins.bool"], ["CACertificateIdentifier", "builtins.str"], ["EngineLifecycleSupport", "builtins.str"], ["ManageMasterUserPassword", "builtins.bool"], ["MasterUserSecretKmsKeyId", "builtins.str"]], "required_keys": ["TargetDBInstanceIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "restore_db_instance_to_point_in_time of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.RestoreDBInstanceToPointInTimeResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "revoke_db_security_group_ingress": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.revoke_db_security_group_ingress", "name": "revoke_db_security_group_ingress", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.RevokeDBSecurityGroupIngressMessageTypeDef", "items": [["DBSecurityGroupName", "builtins.str"], ["CIDRIP", "builtins.str"], ["EC2SecurityGroupName", "builtins.str"], ["EC2SecurityGroupId", "builtins.str"], ["EC2SecurityGroupOwnerId", "builtins.str"]], "required_keys": ["DBSecurityGroupName"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "revoke_db_security_group_ingress of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.RevokeDBSecurityGroupIngressResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "start_activity_stream": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.start_activity_stream", "name": "start_activity_stream", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.StartActivityStreamRequestTypeDef", "items": [["ResourceArn", "builtins.str"], ["Mode", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.literals.ActivityStreamModeType"}], ["KmsKeyId", "builtins.str"], ["ApplyImmediately", "builtins.bool"], ["EngineNativeAuditFieldsIncluded", "builtins.bool"]], "required_keys": ["KmsKeyId", "Mode", "ResourceArn"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_activity_stream of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.StartActivityStreamResponseTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "start_db_cluster": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.start_db_cluster", "name": "start_db_cluster", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.StartDBClusterMessageTypeDef", "items": [["DBClusterIdentifier", "builtins.str"]], "required_keys": ["DBClusterIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_db_cluster of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.StartDBClusterResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "start_db_instance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.start_db_instance", "name": "start_db_instance", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.StartDBInstanceMessageTypeDef", "items": [["DBInstanceIdentifier", "builtins.str"]], "required_keys": ["DBInstanceIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_db_instance of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.StartDBInstanceResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "start_db_instance_automated_backups_replication": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.start_db_instance_automated_backups_replication", "name": "start_db_instance_automated_backups_replication", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.StartDBInstanceAutomatedBackupsReplicationMessageTypeDef", "items": [["SourceDBInstanceArn", "builtins.str"], ["BackupRetentionPeriod", "builtins.int"], ["KmsKeyId", "builtins.str"], ["PreSignedUrl", "builtins.str"], ["SourceRegion", "builtins.str"]], "required_keys": ["SourceDBInstanceArn"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_db_instance_automated_backups_replication of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.StartDBInstanceAutomatedBackupsReplicationResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "start_export_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.start_export_task", "name": "start_export_task", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.StartExportTaskMessageTypeDef", "items": [["ExportTaskIdentifier", "builtins.str"], ["SourceArn", "builtins.str"], ["S3BucketName", "builtins.str"], ["IamRoleArn", "builtins.str"], ["KmsKeyId", "builtins.str"], ["S3Prefix", "builtins.str"], ["ExportOnly", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}]], "required_keys": ["ExportTaskIdentifier", "IamRoleArn", "KmsKeyId", "S3BucketName", "SourceArn"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_export_task of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ExportTaskResponseTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "stop_activity_stream": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.stop_activity_stream", "name": "stop_activity_stream", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.StopActivityStreamRequestTypeDef", "items": [["ResourceArn", "builtins.str"], ["ApplyImmediately", "builtins.bool"]], "required_keys": ["ResourceArn"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stop_activity_stream of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.StopActivityStreamResponseTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "stop_db_cluster": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.stop_db_cluster", "name": "stop_db_cluster", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.StopDBClusterMessageTypeDef", "items": [["DBClusterIdentifier", "builtins.str"]], "required_keys": ["DBClusterIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stop_db_cluster of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.StopDBClusterResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "stop_db_instance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.stop_db_instance", "name": "stop_db_instance", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.StopDBInstanceMessageTypeDef", "items": [["DBInstanceIdentifier", "builtins.str"], ["DBSnapshotIdentifier", "builtins.str"]], "required_keys": ["DBInstanceIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stop_db_instance of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.StopDBInstanceResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "stop_db_instance_automated_backups_replication": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.stop_db_instance_automated_backups_replication", "name": "stop_db_instance_automated_backups_replication", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.StopDBInstanceAutomatedBackupsReplicationMessageTypeDef", "items": [["SourceDBInstanceArn", "builtins.str"]], "required_keys": ["SourceDBInstanceArn"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stop_db_instance_automated_backups_replication of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.StopDBInstanceAutomatedBackupsReplicationResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "switchover_blue_green_deployment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.switchover_blue_green_deployment", "name": "switchover_blue_green_deployment", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.SwitchoverBlueGreenDeploymentRequestTypeDef", "items": [["BlueGreenDeploymentIdentifier", "builtins.str"], ["SwitchoverTimeout", "builtins.int"]], "required_keys": ["BlueGreenDeploymentIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "switchover_blue_green_deployment of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.SwitchoverBlueGreenDeploymentResponseTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "switchover_global_cluster": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.switchover_global_cluster", "name": "switchover_global_cluster", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.SwitchoverGlobalClusterMessageTypeDef", "items": [["GlobalClusterIdentifier", "builtins.str"], ["TargetDbClusterIdentifier", "builtins.str"]], "required_keys": ["GlobalClusterIdentifier", "TargetDbClusterIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "switchover_global_cluster of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.SwitchoverGlobalClusterResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}, "switchover_read_replica": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.client.RDSClient.switchover_read_replica", "name": "switchover_read_replica", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.client.RDSClient", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.SwitchoverReadReplicaMessageTypeDef", "items": [["DBInstanceIdentifier", "builtins.str"]], "required_keys": ["DBInstanceIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "switchover_read_replica of RDSClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.SwitchoverReadReplicaResultTypeDef"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.client.RDSClient.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.client.RDSClient", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RebootDBClusterMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.RebootDBClusterMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RebootDBClusterResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.RebootDBClusterResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RebootDBInstanceMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.RebootDBInstanceMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RebootDBInstanceResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.RebootDBInstanceResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RebootDBShardGroupMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.RebootDBShardGroupMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RegisterDBProxyTargetsRequestTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.RegisterDBProxyTargetsRequestTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RegisterDBProxyTargetsResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.RegisterDBProxyTargetsResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RemoveFromGlobalClusterMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.RemoveFromGlobalClusterMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RemoveFromGlobalClusterResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.RemoveFromGlobalClusterResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RemoveRoleFromDBClusterMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.RemoveRoleFromDBClusterMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RemoveRoleFromDBInstanceMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.RemoveRoleFromDBInstanceMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RemoveSourceIdentifierFromSubscriptionMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.RemoveSourceIdentifierFromSubscriptionMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RemoveSourceIdentifierFromSubscriptionResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.RemoveSourceIdentifierFromSubscriptionResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RemoveTagsFromResourceMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.RemoveTagsFromResourceMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ReservedDBInstanceMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ReservedDBInstanceMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ReservedDBInstancesOfferingMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ReservedDBInstancesOfferingMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ResetDBClusterParameterGroupMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ResetDBClusterParameterGroupMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ResetDBParameterGroupMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ResetDBParameterGroupMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RestoreDBClusterFromS3MessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.RestoreDBClusterFromS3MessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RestoreDBClusterFromS3ResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.RestoreDBClusterFromS3ResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RestoreDBClusterFromSnapshotMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.RestoreDBClusterFromSnapshotMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RestoreDBClusterFromSnapshotResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.RestoreDBClusterFromSnapshotResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RestoreDBClusterToPointInTimeMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.RestoreDBClusterToPointInTimeMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RestoreDBClusterToPointInTimeResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.RestoreDBClusterToPointInTimeResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RestoreDBInstanceFromDBSnapshotMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.RestoreDBInstanceFromDBSnapshotMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RestoreDBInstanceFromDBSnapshotResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.RestoreDBInstanceFromDBSnapshotResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RestoreDBInstanceFromS3MessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.RestoreDBInstanceFromS3MessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RestoreDBInstanceFromS3ResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.RestoreDBInstanceFromS3ResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RestoreDBInstanceToPointInTimeMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.RestoreDBInstanceToPointInTimeMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RestoreDBInstanceToPointInTimeResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.RestoreDBInstanceToPointInTimeResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RevokeDBSecurityGroupIngressMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.RevokeDBSecurityGroupIngressMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RevokeDBSecurityGroupIngressResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.RevokeDBSecurityGroupIngressResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SourceRegionMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.SourceRegionMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "StartActivityStreamRequestTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.StartActivityStreamRequestTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "StartActivityStreamResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.StartActivityStreamResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "StartDBClusterMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.StartDBClusterMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "StartDBClusterResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.StartDBClusterResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "StartDBInstanceAutomatedBackupsReplicationMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.StartDBInstanceAutomatedBackupsReplicationMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "StartDBInstanceAutomatedBackupsReplicationResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.StartDBInstanceAutomatedBackupsReplicationResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "StartDBInstanceMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.StartDBInstanceMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "StartDBInstanceResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.StartDBInstanceResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "StartExportTaskMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.StartExportTaskMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "StopActivityStreamRequestTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.StopActivityStreamRequestTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "StopActivityStreamResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.StopActivityStreamResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "StopDBClusterMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.StopDBClusterMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "StopDBClusterResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.StopDBClusterResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "StopDBInstanceAutomatedBackupsReplicationMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.StopDBInstanceAutomatedBackupsReplicationMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "StopDBInstanceAutomatedBackupsReplicationResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.StopDBInstanceAutomatedBackupsReplicationResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "StopDBInstanceMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.StopDBInstanceMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "StopDBInstanceResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.StopDBInstanceResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SwitchoverBlueGreenDeploymentRequestTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.SwitchoverBlueGreenDeploymentRequestTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SwitchoverBlueGreenDeploymentResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.SwitchoverBlueGreenDeploymentResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SwitchoverGlobalClusterMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.SwitchoverGlobalClusterMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SwitchoverGlobalClusterResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.SwitchoverGlobalClusterResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SwitchoverReadReplicaMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.SwitchoverReadReplicaMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SwitchoverReadReplicaResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.SwitchoverReadReplicaResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TagListMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.TagListMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TenantDatabaseAvailableWaiter": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.waiter.TenantDatabaseAvailableWaiter", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TenantDatabaseDeletedWaiter": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.waiter.TenantDatabaseDeletedWaiter", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TenantDatabasesMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.TenantDatabasesMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Type": {".class": "SymbolTableNode", "cross_ref": "builtins.type", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Unpack": {".class": "SymbolTableNode", "cross_ref": "typing.Unpack", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "mypy_boto3_rds.client.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mypy_boto3_rds.client.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mypy_boto3_rds.client.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mypy_boto3_rds.client.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mypy_boto3_rds.client.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mypy_boto3_rds.client.__package__", "name": "__package__", "type": "builtins.str"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_hidden": true, "module_public": false}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\mypy_boto3_rds\\client.pyi"}