{"data_mtime": 1759504547, "dep_lines": [25, 26, 30, 25, 40, 43, 44, 48, 49, 58, 59, 62, 63, 64, 33, 34, 35, 36, 60, 7, 9, 10, 11, 33, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 20, 5, 5, 5, 25, 25, 25, 25, 25, 25, 25, 5, 10, 10, 5, 25, 5, 10, 10, 5, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.ext.asyncio.exc", "sqlalchemy.ext.asyncio.base", "sqlalchemy.ext.asyncio.result", "sqlalchemy.ext.asyncio", "sqlalchemy.engine.base", "sqlalchemy.util.concurrency", "sqlalchemy.util.typing", "sqlalchemy.engine.cursor", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.result", "sqlalchemy.engine.url", "sqlalchemy.sql._typing", "sqlalchemy.sql.base", "sqlalchemy.sql.selectable", "sqlalchemy.exc", "sqlalchemy.inspection", "sqlalchemy.util", "sqlalchemy.engine", "sqlalchemy.pool", "__future__", "asyncio", "contextlib", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "asyncio.futures", "asyncio.tasks", "<PERSON><PERSON><PERSON>", "sqlalchemy.engine.default", "sqlalchemy.engine.util", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.log", "sqlalchemy.pool.base", "sqlalchemy.sql", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.compiler", "sqlalchemy.sql.elements", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util._concurrency_py3k", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "types"], "hash": "88373b9114e89930d0c9968af8c8d2a1fa0dde3209f88f0c97019bc21391544d", "id": "sqlalchemy.ext.asyncio.engine", "ignore_all": true, "interface_hash": "72675e37fb8ad64bd4ed3501234ff3173d040313f5f3b93bfcfc12c941906add", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\engine.py", "plugin_data": null, "size": 49790, "suppressed": [], "version_id": "1.8.0"}