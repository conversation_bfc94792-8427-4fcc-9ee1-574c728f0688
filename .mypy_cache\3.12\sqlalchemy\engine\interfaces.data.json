{".class": "MypyFile", "_fullname": "sqlalchemy.engine.interfaces", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AdaptedConnection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.interfaces.AdaptedConnection", "name": "AdaptedConnection", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.engine.interfaces.AdaptedConnection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.engine.interfaces", "mro": ["sqlalchemy.engine.interfaces.AdaptedConnection", "builtins.object"], "names": {".class": "SymbolTable", "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.AdaptedConnection.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["sqlalchemy.engine.interfaces.AdaptedConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of AdaptedConnection", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.engine.interfaces.AdaptedConnection.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.AdaptedConnection._connection", "name": "_connection", "type": "sqlalchemy.connectors.asyncio.AsyncIODBAPIConnection"}}, "driver_connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.engine.interfaces.AdaptedConnection.driver_connection", "name": "driver_connection", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.engine.interfaces.AdaptedConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "driver_connection of AdaptedConnection", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.interfaces.AdaptedConnection.driver_connection", "name": "driver_connection", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.engine.interfaces.AdaptedConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "driver_connection of AdaptedConnection", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "run_async": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fn"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.AdaptedConnection.run_async", "name": "run_async", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fn"], "arg_types": ["sqlalchemy.engine.interfaces.AdaptedConnection", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.interfaces._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "typing.Awaitable"}, "type_guard": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_async of AdaptedConnection", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.interfaces._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.interfaces._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.interfaces.AdaptedConnection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.engine.interfaces.AdaptedConnection", "values": [], "variance": 0}, "slots": ["_connection"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AsyncIODBAPIConnection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.connectors.asyncio.AsyncIODBAPIConnection", "kind": "Gdef"}, "Awaitable": {".class": "SymbolTableNode", "cross_ref": "typing.Awaitable", "kind": "Gdef"}, "BindParameter": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.BindParameter", "kind": "Gdef"}, "BindTyping": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.interfaces.BindTyping", "name": "BindTyping", "type_vars": []}, "deletable_attributes": [], "flags": ["is_enum"], "fullname": "sqlalchemy.engine.interfaces.BindTyping", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "sqlalchemy.engine.interfaces", "mro": ["sqlalchemy.engine.interfaces.BindTyping", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "NONE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.interfaces.BindTyping.NONE", "name": "NONE", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "RENDER_CASTS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.interfaces.BindTyping.RENDER_CASTS", "name": "RENDER_CASTS", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_ref": "builtins.int"}}}, "SETINPUTSIZES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.interfaces.BindTyping.SETINPUTSIZES", "name": "SETINPUTSIZES", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.interfaces.BindTyping.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.engine.interfaces.BindTyping", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CacheStats": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.interfaces.CacheStats", "name": "CacheStats", "type_vars": []}, "deletable_attributes": [], "flags": ["is_enum"], "fullname": "sqlalchemy.engine.interfaces.CacheStats", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "sqlalchemy.engine.interfaces", "mro": ["sqlalchemy.engine.interfaces.CacheStats", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "CACHE_HIT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.interfaces.CacheStats.CACHE_HIT", "name": "CACHE_HIT", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "CACHE_MISS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.interfaces.CacheStats.CACHE_MISS", "name": "CACHE_MISS", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "CACHING_DISABLED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.interfaces.CacheStats.CACHING_DISABLED", "name": "CACHING_DISABLED", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "NO_CACHE_KEY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.interfaces.CacheStats.NO_CACHE_KEY", "name": "NO_CACHE_KEY", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_ref": "builtins.int"}}}, "NO_DIALECT_SUPPORT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.interfaces.CacheStats.NO_DIALECT_SUPPORT", "name": "NO_DIALECT_SUPPORT", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.interfaces.CacheStats.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.engine.interfaces.CacheStats", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef"}, "ClauseElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ClauseElement", "kind": "Gdef"}, "Collection": {".class": "SymbolTableNode", "cross_ref": "typing.Collection", "kind": "Gdef"}, "Column": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Column", "kind": "Gdef"}, "Compiled": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.Compiled", "kind": "Gdef"}, "CompiledCacheType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.engine.interfaces.CompiledCacheType", "line": 262, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "sqlalchemy.sql.compiler.Compiled"], "type_ref": "typing.MutableMapping"}}}, "ConnectArgsType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.engine.interfaces.ConnectArgsType", "line": 75, "no_args": false, "normalized": false, "target": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.MutableMapping"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "Connectable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.engine.interfaces.Connectable", "line": 3243, "no_args": true, "normalized": false, "target": "sqlalchemy.engine.interfaces.ConnectionEventsTarget"}}, "Connection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.base.Connection", "kind": "Gdef"}, "ConnectionEventsTarget": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.event.registry.EventTarget"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.interfaces.ConnectionEventsTarget", "name": "ConnectionEventsTarget", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.engine.interfaces.ConnectionEventsTarget", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.engine.interfaces", "mro": ["sqlalchemy.engine.interfaces.ConnectionEventsTarget", "sqlalchemy.event.registry.EventTarget", "builtins.object"], "names": {".class": "SymbolTable", "dispatch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.ConnectionEventsTarget.dispatch", "name": "dispatch", "type": {".class": "Instance", "args": ["sqlalchemy.engine.interfaces.ConnectionEventsTarget"], "type_ref": "sqlalchemy.event.base.dispatcher"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.interfaces.ConnectionEventsTarget.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.engine.interfaces.ConnectionEventsTarget", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CoreExecuteOptionsParameter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.engine.interfaces.CoreExecuteOptionsParameter", "line": 296, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreKnownExecutionOptions"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.Mapping"}]}}}, "CreateEnginePlugin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.interfaces.CreateEnginePlugin", "name": "CreateEnginePlugin", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.engine.interfaces.CreateEnginePlugin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.engine.interfaces", "mro": ["sqlalchemy.engine.interfaces.CreateEnginePlugin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "url", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.CreateEnginePlugin.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "url", "kwargs"], "arg_types": ["sqlalchemy.engine.interfaces.CreateEnginePlugin", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.url.URL"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CreateEnginePlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "engine_created": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "engine"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.CreateEnginePlugin.engine_created", "name": "engine_created", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "engine"], "arg_types": ["sqlalchemy.engine.interfaces.CreateEnginePlugin", "sqlalchemy.engine.base.Engine"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "engine_created of CreateEnginePlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "handle_dialect_kwargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dialect_cls", "dialect_args"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.CreateEnginePlugin.handle_dialect_kwargs", "name": "handle_dialect_kwargs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "dialect_cls", "dialect_args"], "arg_types": ["sqlalchemy.engine.interfaces.CreateEnginePlugin", {".class": "TypeType", "item": "sqlalchemy.engine.interfaces.Dialect"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_dialect_kwargs of CreateEnginePlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "handle_pool_kwargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "pool_cls", "pool_args"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.CreateEnginePlugin.handle_pool_kwargs", "name": "handle_pool_kwargs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "pool_cls", "pool_args"], "arg_types": ["sqlalchemy.engine.interfaces.CreateEnginePlugin", {".class": "TypeType", "item": "sqlalchemy.pool.base.Pool"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_pool_kwargs of CreateEnginePlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "update_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "url"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.CreateEnginePlugin.update_url", "name": "update_url", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "url"], "arg_types": ["sqlalchemy.engine.interfaces.CreateEnginePlugin", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.url.URL"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_url of CreateEnginePlugin", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.url.URL"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "url": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.engine.interfaces.CreateEnginePlugin.url", "name": "url", "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.url.URL"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.interfaces.CreateEnginePlugin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.engine.interfaces.CreateEnginePlugin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CursorResult": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.cursor.CursorResult", "kind": "Gdef"}, "DBAPIConnection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__getattr__", 2], ["__setattr__", 2], ["close", 2], ["commit", 2], ["cursor", 2], ["rollback", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.interfaces.DBAPIConnection", "name": "DBAPIConnection", "type_vars": []}, "deletable_attributes": [], "flags": ["is_abstract", "is_protocol"], "fullname": "sqlalchemy.engine.interfaces.DBAPIConnection", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.engine.interfaces", "mro": ["sqlalchemy.engine.interfaces.DBAPIConnection", "builtins.object"], "names": {".class": "SymbolTable", "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": ["is_trivial_body"], "fullname": "sqlalchemy.engine.interfaces.DBAPIConnection.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.engine.interfaces.DBAPIConnection", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattr__ of DBAPIConnection", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__setattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "value"], "dataclass_transform_spec": null, "flags": ["is_trivial_body"], "fullname": "sqlalchemy.engine.interfaces.DBAPIConnection.__setattr__", "name": "__setattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "value"], "arg_types": ["sqlalchemy.engine.interfaces.DBAPIConnection", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setattr__ of DBAPIConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_trivial_body"], "fullname": "sqlalchemy.engine.interfaces.DBAPIConnection.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.engine.interfaces.DBAPIConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of DBAPIConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "commit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_trivial_body"], "fullname": "sqlalchemy.engine.interfaces.DBAPIConnection.commit", "name": "commit", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.engine.interfaces.DBAPIConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "commit of DBAPIConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "cursor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "flags": ["is_trivial_body"], "fullname": "sqlalchemy.engine.interfaces.DBAPIConnection.cursor", "name": "cursor", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["sqlalchemy.engine.interfaces.DBAPIConnection", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cursor of DBAPIConnection", "ret_type": "sqlalchemy.engine.interfaces.DBAPICursor", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "rollback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_trivial_body"], "fullname": "sqlalchemy.engine.interfaces.DBAPIConnection.rollback", "name": "rollback", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.engine.interfaces.DBAPIConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rollback of DBAPIConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.interfaces.DBAPIConnection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.engine.interfaces.DBAPIConnection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DBAPICursor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__getattr__", 2], ["arraysize", 1], ["callproc", 2], ["close", 2], ["description", 2], ["execute", 2], ["executemany", 2], ["fetchall", 2], ["fetchmany", 2], ["fetchone", 2], ["lastrowid", 1], ["nextset", 2], ["rowcount", 2], ["setinputsizes", 2], ["setoutputsize", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.interfaces.DBAPICursor", "name": "DBAPICursor", "type_vars": []}, "deletable_attributes": [], "flags": ["is_abstract", "is_protocol"], "fullname": "sqlalchemy.engine.interfaces.DBAPICursor", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.engine.interfaces", "mro": ["sqlalchemy.engine.interfaces.DBAPICursor", "builtins.object"], "names": {".class": "SymbolTable", "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": ["is_trivial_body"], "fullname": "sqlalchemy.engine.interfaces.DBAPICursor.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.engine.interfaces.DBAPICursor", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattr__ of DBAPICursor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "arraysize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_abstract_var", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.DBAPICursor.arraysize", "name": "arraysize", "type": "builtins.int"}}, "callproc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 1], "arg_names": ["self", "procname", "parameters"], "dataclass_transform_spec": null, "flags": ["is_trivial_body"], "fullname": "sqlalchemy.engine.interfaces.DBAPICursor.callproc", "name": "callproc", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "procname", "parameters"], "arg_types": ["sqlalchemy.engine.interfaces.DBAPICursor", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "callproc of DBAPICursor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_trivial_body"], "fullname": "sqlalchemy.engine.interfaces.DBAPICursor.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.engine.interfaces.DBAPICursor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of DBAPICursor", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "description": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "sqlalchemy.engine.interfaces.DBAPICursor.description", "name": "description", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.engine.interfaces.DBAPICursor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "description of DBAPICursor", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPICursorDescription"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.interfaces.DBAPICursor.description", "name": "description", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.engine.interfaces.DBAPICursor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "description of DBAPICursor", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPICursorDescription"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 1], "arg_names": ["self", "operation", "parameters"], "dataclass_transform_spec": null, "flags": ["is_trivial_body"], "fullname": "sqlalchemy.engine.interfaces.DBAPICursor.execute", "name": "execute", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "operation", "parameters"], "arg_types": ["sqlalchemy.engine.interfaces.DBAPICursor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute of DBAPICursor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "executemany": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0], "arg_names": ["self", "operation", "parameters"], "dataclass_transform_spec": null, "flags": ["is_trivial_body"], "fullname": "sqlalchemy.engine.interfaces.DBAPICursor.executemany", "name": "executemany", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "operation", "parameters"], "arg_types": ["sqlalchemy.engine.interfaces.DBAPICursor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPIMultiExecuteParams"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "executemany of DBAPICursor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "fetchall": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_trivial_body"], "fullname": "sqlalchemy.engine.interfaces.DBAPICursor.fetchall", "name": "fetchall", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.engine.interfaces.DBAPICursor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fetchall of DBAPICursor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Sequence"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "fetchmany": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 1], "arg_names": ["self", "size"], "dataclass_transform_spec": null, "flags": ["is_trivial_body"], "fullname": "sqlalchemy.engine.interfaces.DBAPICursor.fetchmany", "name": "fetchmany", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "size"], "arg_types": ["sqlalchemy.engine.interfaces.DBAPICursor", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fetchmany of DBAPICursor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Sequence"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "fetchone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_trivial_body"], "fullname": "sqlalchemy.engine.interfaces.DBAPICursor.fetchone", "name": "fetchone", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.engine.interfaces.DBAPICursor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fetchone of DBAPICursor", "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "lastrowid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_abstract_var", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.DBAPICursor.lastrowid", "name": "lastrowid", "type": "builtins.int"}}, "nextset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_trivial_body"], "fullname": "sqlalchemy.engine.interfaces.DBAPICursor.nextset", "name": "nextset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.engine.interfaces.DBAPICursor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nextset of DBAPICursor", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "rowcount": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "sqlalchemy.engine.interfaces.DBAPICursor.rowcount", "name": "rowcount", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.engine.interfaces.DBAPICursor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rowcount of DBAPICursor", "ret_type": "builtins.int", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.interfaces.DBAPICursor.rowcount", "name": "rowcount", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.engine.interfaces.DBAPICursor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rowcount of DBAPICursor", "ret_type": "builtins.int", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "setinputsizes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", "sizes"], "dataclass_transform_spec": null, "flags": ["is_trivial_body"], "fullname": "sqlalchemy.engine.interfaces.DBAPICursor.setinputsizes", "name": "setinputsizes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "sizes"], "arg_types": ["sqlalchemy.engine.interfaces.DBAPICursor", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setinputsizes of DBAPICursor", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "setoutputsize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0], "arg_names": ["self", "size", "column"], "dataclass_transform_spec": null, "flags": ["is_trivial_body"], "fullname": "sqlalchemy.engine.interfaces.DBAPICursor.setoutputsize", "name": "setoutputsize", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "size", "column"], "arg_types": ["sqlalchemy.engine.interfaces.DBAPICursor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setoutputsize of DBAPICursor", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.interfaces.DBAPICursor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.engine.interfaces.DBAPICursor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DBAPIModule": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__getattr__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.interfaces.DBAPIModule", "name": "DBAPIModule", "type_vars": []}, "deletable_attributes": [], "flags": ["is_abstract", "is_protocol"], "fullname": "sqlalchemy.engine.interfaces.DBAPIModule", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.engine.interfaces", "mro": ["sqlalchemy.engine.interfaces.DBAPIModule", "builtins.object"], "names": {".class": "SymbolTable", "Error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.interfaces.DBAPIModule.Error", "name": "Error", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.engine.interfaces.DBAPIModule.Error", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.engine.interfaces", "mro": ["sqlalchemy.engine.interfaces.DBAPIModule.Error", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.DBAPIModule.Error.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.engine.interfaces.DBAPIModule.Error", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattr__ of Error", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.interfaces.DBAPIModule.Error.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.engine.interfaces.DBAPIModule.Error", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IntegrityError": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.engine.interfaces.DBAPIModule.Error"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.interfaces.DBAPIModule.IntegrityError", "name": "IntegrityError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.engine.interfaces.DBAPIModule.IntegrityError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.engine.interfaces", "mro": ["sqlalchemy.engine.interfaces.DBAPIModule.IntegrityError", "sqlalchemy.engine.interfaces.DBAPIModule.Error", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.interfaces.DBAPIModule.IntegrityError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.engine.interfaces.DBAPIModule.IntegrityError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InterfaceError": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.engine.interfaces.DBAPIModule.Error"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.interfaces.DBAPIModule.InterfaceError", "name": "InterfaceError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.engine.interfaces.DBAPIModule.InterfaceError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.engine.interfaces", "mro": ["sqlalchemy.engine.interfaces.DBAPIModule.InterfaceError", "sqlalchemy.engine.interfaces.DBAPIModule.Error", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.interfaces.DBAPIModule.InterfaceError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.engine.interfaces.DBAPIModule.InterfaceError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OperationalError": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.engine.interfaces.DBAPIModule.Error"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.interfaces.DBAPIModule.OperationalError", "name": "OperationalError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.engine.interfaces.DBAPIModule.OperationalError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.engine.interfaces", "mro": ["sqlalchemy.engine.interfaces.DBAPIModule.OperationalError", "sqlalchemy.engine.interfaces.DBAPIModule.Error", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.interfaces.DBAPIModule.OperationalError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.engine.interfaces.DBAPIModule.OperationalError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": ["is_trivial_body"], "fullname": "sqlalchemy.engine.interfaces.DBAPIModule.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.engine.interfaces.DBAPIModule", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattr__ of DBAPIModule", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.interfaces.DBAPIModule.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.engine.interfaces.DBAPIModule", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DBAPIType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.interfaces.DBAPIType", "name": "DBAPIType", "type_vars": []}, "deletable_attributes": [], "flags": ["is_protocol"], "fullname": "sqlalchemy.engine.interfaces.DBAPIType", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.engine.interfaces", "mro": ["sqlalchemy.engine.interfaces.DBAPIType", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.interfaces.DBAPIType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.engine.interfaces.DBAPIType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DDLCompiler": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.DDLCompiler", "kind": "Gdef"}, "DefaultGenerator": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.DefaultGenerator", "kind": "Gdef"}, "Dialect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.event.registry.EventTarget"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.interfaces.Dialect", "name": "Dialect", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.engine.interfaces", "mro": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.event.registry.EventTarget", "builtins.object"], "names": {".class": "SymbolTable", "CACHE_HIT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.interfaces.Dialect.CACHE_HIT", "name": "CACHE_HIT", "type": "sqlalchemy.engine.interfaces.CacheStats"}}, "CACHE_MISS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.interfaces.Dialect.CACHE_MISS", "name": "CACHE_MISS", "type": "sqlalchemy.engine.interfaces.CacheStats"}}, "CACHING_DISABLED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.interfaces.Dialect.CACHING_DISABLED", "name": "CACHING_DISABLED", "type": "sqlalchemy.engine.interfaces.CacheStats"}}, "NO_CACHE_KEY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.interfaces.Dialect.NO_CACHE_KEY", "name": "NO_CACHE_KEY", "type": "sqlalchemy.engine.interfaces.CacheStats"}}, "NO_DIALECT_SUPPORT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.interfaces.Dialect.NO_DIALECT_SUPPORT", "name": "NO_DIALECT_SUPPORT", "type": "sqlalchemy.engine.interfaces.CacheStats"}}, "_assert_and_set_isolation_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dbapi_conn", "level"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect._assert_and_set_isolation_level", "name": "_assert_and_set_isolation_level", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "dbapi_conn", "level"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.interfaces.DBAPIConnection", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.IsolationLevel"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_assert_and_set_isolation_level of Dialect", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_bind_typing_render_casts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect._bind_typing_render_casts", "name": "_bind_typing_render_casts", "type": "builtins.bool"}}, "_builtin_onconnect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect._builtin_onconnect", "name": "_builtin_onconnect", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_builtin_onconnect of Dialect", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_deliver_insertmanyvalues_batches": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "connection", "cursor", "statement", "parameters", "generic_setinputsizes", "context"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect._deliver_insertmanyvalues_batches", "name": "_deliver_insertmanyvalues_batches", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "connection", "cursor", "statement", "parameters", "generic_setinputsizes", "context"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", "sqlalchemy.engine.interfaces.DBAPICursor", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPIMultiExecuteParams"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._GenericSetInputSizesType"}, {".class": "NoneType"}]}, "sqlalchemy.engine.interfaces.ExecutionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deliver_insertmanyvalues_batches of Dialect", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.compiler._InsertManyValuesBatch"}], "type_ref": "typing.Iterator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_do_ping_w_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect._do_ping_w_event", "name": "_do_ping_w_event", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_connection"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.interfaces.DBAPIConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_do_ping_w_event of <PERSON><PERSON>ct", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_get_default_schema_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect._get_default_schema_name", "name": "_get_default_schema_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_default_schema_name of Dialect", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_get_server_version_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect._get_server_version_info", "name": "_get_server_version_info", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_server_version_info of Dialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_has_events": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.interfaces.Dialect._has_events", "name": "_has_events", "type": "builtins.bool"}}, "_on_connect_isolation_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect._on_connect_isolation_level", "name": "_on_connect_isolation_level", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "SERIALIZABLE"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "REPEATABLE READ"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "READ COMMITTED"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "READ UNCOMMITTED"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "AUTOCOMMIT"}, {".class": "NoneType"}]}}}, "_overrides_default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "method_name"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.engine.interfaces.Dialect._overrides_default", "name": "_overrides_default", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "method_name"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_overrides_default of Dialect", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_supports_statement_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect._supports_statement_cache", "name": "_supports_statement_cache", "type": "builtins.bool"}}, "_type_memos": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect._type_memos", "name": "_type_memos", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.type_api._TypeMemoDict"}], "type_ref": "typing.MutableMapping"}}}, "bind_typing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.interfaces.Dialect.bind_typing", "name": "bind_typing", "type": "sqlalchemy.engine.interfaces.BindTyping"}}, "colspecs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.colspecs", "name": "colspecs", "type": {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}}], "type_ref": "typing.MutableMapping"}}}, "compiler_linting": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.compiler_linting", "name": "compiler_linting", "type": "sqlalchemy.sql.compiler.<PERSON>"}}, "connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "cargs", "cparams"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.connect", "name": "connect", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "cargs", "cparams"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect of Dialect", "ret_type": "sqlalchemy.engine.interfaces.DBAPIConnection", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "construct_arguments": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.engine.interfaces.Dialect.construct_arguments", "name": "construct_arguments", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeType", "item": "sqlalchemy.sql.schema.SchemaItem"}, {".class": "TypeType", "item": "sqlalchemy.sql.elements.ClauseElement"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}, {".class": "NoneType"}]}}}, "create_connect_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "url"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.create_connect_args", "name": "create_connect_args", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "url"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.url.URL"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_connect_args of Dialect", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.ConnectArgsType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "create_xid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.create_xid", "name": "create_xid", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_xid of Dialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "cte_follows_insert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.cte_follows_insert", "name": "cte_follows_insert", "type": "builtins.bool"}}, "dbapi": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.dbapi", "name": "dbapi", "type": {".class": "UnionType", "items": ["sqlalchemy.engine.interfaces.DBAPIModule", {".class": "NoneType"}]}}}, "dbapi_exception_translation_map": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.engine.interfaces.Dialect.dbapi_exception_translation_map", "name": "dbapi_exception_translation_map", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "typing.Mapping"}}}, "ddl_compiler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.ddl_compiler", "name": "ddl_compiler", "type": {".class": "TypeType", "item": "sqlalchemy.sql.compiler.DDLCompiler"}}}, "default_isolation_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.default_isolation_level", "name": "default_isolation_level", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "SERIALIZABLE"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "REPEATABLE READ"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "READ COMMITTED"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "READ UNCOMMITTED"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "AUTOCOMMIT"}, {".class": "NoneType"}]}}}, "default_metavalue_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.engine.interfaces.Dialect.default_metavalue_token", "name": "default_metavalue_token", "type": "builtins.str"}}, "default_schema_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.default_schema_name", "name": "default_schema_name", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "default_sequence_base": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.default_sequence_base", "name": "default_sequence_base", "type": "builtins.int"}}, "delete_executemany_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.delete_executemany_returning", "name": "delete_executemany_returning", "type": "builtins.bool"}}, "delete_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.delete_returning", "name": "delete_returning", "type": "builtins.bool"}}, "delete_returning_multifrom": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.delete_returning_multifrom", "name": "delete_returning_multifrom", "type": "builtins.bool"}}, "denormalize_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.denormalize_name", "name": "denormalize_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "denormalize_name of Dialect", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "detect_autocommit_setting": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_conn"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.detect_autocommit_setting", "name": "detect_autocommit_setting", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_conn"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.interfaces.DBAPIConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "detect_autocommit_setting of Dialect", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "dialect_description": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.dialect_description", "name": "dialect_description", "type": "builtins.str"}}, "dispatch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.dispatch", "name": "dispatch", "type": {".class": "Instance", "args": ["sqlalchemy.engine.interfaces.Dialect"], "type_ref": "sqlalchemy.event.base.dispatcher"}}}, "div_is_floordiv": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.div_is_floordiv", "name": "div_is_floordiv", "type": "builtins.bool"}}, "do_begin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.do_begin", "name": "do_begin", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_connection"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.pool.base.PoolProxiedConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "do_begin of Dialect", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "do_begin_twophase": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "xid"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.do_begin_twophase", "name": "do_begin_twophase", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "xid"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "do_begin_twophase of Dialect", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "do_close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.do_close", "name": "do_close", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_connection"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.interfaces.DBAPIConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "do_close of Dialect", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "do_commit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.do_commit", "name": "do_commit", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_connection"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.pool.base.PoolProxiedConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "do_commit of Dialect", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "do_commit_twophase": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "connection", "xid", "is_prepared", "recover"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.do_commit_twophase", "name": "do_commit_twophase", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "connection", "xid", "is_prepared", "recover"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "do_commit_twophase of Dialect", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "do_execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "cursor", "statement", "parameters", "context"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.do_execute", "name": "do_execute", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "cursor", "statement", "parameters", "context"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.interfaces.DBAPICursor", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["sqlalchemy.engine.interfaces.ExecutionContext", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "do_execute of Dialect", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "do_execute_no_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "cursor", "statement", "context"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.do_execute_no_params", "name": "do_execute_no_params", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "cursor", "statement", "context"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.interfaces.DBAPICursor", "builtins.str", {".class": "UnionType", "items": ["sqlalchemy.engine.interfaces.ExecutionContext", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "do_execute_no_params of Dialect", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "do_executemany": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "cursor", "statement", "parameters", "context"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.do_executemany", "name": "do_executemany", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "cursor", "statement", "parameters", "context"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.interfaces.DBAPICursor", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPIMultiExecuteParams"}, {".class": "UnionType", "items": ["sqlalchemy.engine.interfaces.ExecutionContext", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "do_executemany of Dialect", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "do_ping": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.do_ping", "name": "do_ping", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_connection"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.interfaces.DBAPIConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "do_ping of Dialect", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "do_prepare_twophase": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "xid"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.do_prepare_twophase", "name": "do_prepare_twophase", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "xid"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "do_prepare_twophase of Dialect", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "do_recover_twophase": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.do_recover_twophase", "name": "do_recover_twophase", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "do_recover_twophase of Dialect", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "do_release_savepoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "name"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.do_release_savepoint", "name": "do_release_savepoint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "name"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "do_release_savepoint of Dialect", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "do_rollback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.do_rollback", "name": "do_rollback", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_connection"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.pool.base.PoolProxiedConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "do_rollback of Dialect", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "do_rollback_to_savepoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "name"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.do_rollback_to_savepoint", "name": "do_rollback_to_savepoint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "name"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "do_rollback_to_savepoint of Dialect", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "do_rollback_twophase": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "connection", "xid", "is_prepared", "recover"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.do_rollback_twophase", "name": "do_rollback_twophase", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "connection", "xid", "is_prepared", "recover"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "do_rollback_twophase of Dialect", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "do_savepoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "name"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.do_savepoint", "name": "do_savepoint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "name"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "do_savepoint of Dialect", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "do_set_input_sizes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "cursor", "list_of_tuples", "context"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.do_set_input_sizes", "name": "do_set_input_sizes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "cursor", "list_of_tuples", "context"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.interfaces.DBAPICursor", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._GenericSetInputSizesType"}, "sqlalchemy.engine.interfaces.ExecutionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "do_set_input_sizes of Dialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "do_terminate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.do_terminate", "name": "do_terminate", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_connection"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.interfaces.DBAPIConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "do_terminate of Dialect", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "driver": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.driver", "name": "driver", "type": "builtins.str"}}, "engine_config_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.engine_config_types", "name": "engine_config_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}}}, "engine_created": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "engine"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.engine.interfaces.Dialect.engine_created", "name": "engine_created", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "engine"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.engine.interfaces.Dialect"}, "sqlalchemy.engine.base.Engine"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "engine_created of Dialect", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.interfaces.Dialect.engine_created", "name": "engine_created", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "engine"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.engine.interfaces.Dialect"}, "sqlalchemy.engine.base.Engine"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "engine_created of Dialect", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "exclude_set_input_sizes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.exclude_set_input_sizes", "name": "exclude_set_input_sizes", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.set"}, {".class": "NoneType"}]}}}, "execute_sequence_format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.execute_sequence_format", "name": "execute_sequence_format", "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}}, {".class": "TypeType", "item": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}]}}}, "execution_ctx_cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.execution_ctx_cls", "name": "execution_ctx_cls", "type": {".class": "TypeType", "item": "sqlalchemy.engine.interfaces.ExecutionContext"}}}, "favor_returning_over_lastrowid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.favor_returning_over_lastrowid", "name": "favor_returning_over_lastrowid", "type": "builtins.bool"}}, "get_async_dialect_cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "url"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.engine.interfaces.Dialect.get_async_dialect_cls", "name": "get_async_dialect_cls", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "url"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.engine.interfaces.Dialect"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.url.URL"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_async_dialect_cls of Dialect", "ret_type": {".class": "TypeType", "item": "sqlalchemy.engine.interfaces.Dialect"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.interfaces.Dialect.get_async_dialect_cls", "name": "get_async_dialect_cls", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "url"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.engine.interfaces.Dialect"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.url.URL"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_async_dialect_cls of Dialect", "ret_type": {".class": "TypeType", "item": "sqlalchemy.engine.interfaces.Dialect"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "get_check_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.get_check_constraints", "name": "get_check_constraints", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_check_constraints of Dialect", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.ReflectedCheckConstraint"}], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.get_columns", "name": "get_columns", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_columns of Dialect", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.ReflectedColumn"}], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_default_isolation_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_conn"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.get_default_isolation_level", "name": "get_default_isolation_level", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_conn"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.interfaces.DBAPIConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_default_isolation_level of Dialect", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.IsolationLevel"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_dialect_cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "url"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.engine.interfaces.Dialect.get_dialect_cls", "name": "get_dialect_cls", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "url"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.engine.interfaces.Dialect"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.url.URL"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_dialect_cls of Dialect", "ret_type": {".class": "TypeType", "item": "sqlalchemy.engine.interfaces.Dialect"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.interfaces.Dialect.get_dialect_cls", "name": "get_dialect_cls", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "url"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.engine.interfaces.Dialect"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.url.URL"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_dialect_cls of Dialect", "ret_type": {".class": "TypeType", "item": "sqlalchemy.engine.interfaces.Dialect"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "get_dialect_pool_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "url"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.get_dialect_pool_class", "name": "get_dialect_pool_class", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "url"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.url.URL"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_dialect_pool_class of Dialect", "ret_type": {".class": "TypeType", "item": "sqlalchemy.pool.base.Pool"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_driver_connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.get_driver_connection", "name": "get_driver_connection", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.interfaces.DBAPIConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_driver_connection of Dialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_foreign_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.get_foreign_keys", "name": "get_foreign_keys", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_foreign_keys of Dialect", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.ReflectedForeignKeyConstraint"}], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_indexes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.get_indexes", "name": "get_indexes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_indexes of Dialect", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.ReflectedIndex"}], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_isolation_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.get_isolation_level", "name": "get_isolation_level", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_connection"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.interfaces.DBAPIConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_isolation_level of Dialect", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.IsolationLevel"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_isolation_level_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_conn"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.get_isolation_level_values", "name": "get_isolation_level_values", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_conn"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.interfaces.DBAPIConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_isolation_level_values of Dialect", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.IsolationLevel"}], "type_ref": "typing.Sequence"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_materialized_view_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "connection", "schema", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.get_materialized_view_names", "name": "get_materialized_view_names", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "connection", "schema", "kw"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_materialized_view_names of Di<PERSON><PERSON>", "ret_type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_multi_check_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 4], "arg_names": ["self", "connection", "schema", "filter_names", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.get_multi_check_constraints", "name": "get_multi_check_constraints", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 4], "arg_names": ["self", "connection", "schema", "filter_names", "kw"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Collection"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_multi_check_constraints of Dialect", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.TableKey"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.ReflectedCheckConstraint"}], "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "typing.Iterable"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_multi_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 4], "arg_names": ["self", "connection", "schema", "filter_names", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.get_multi_columns", "name": "get_multi_columns", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 4], "arg_names": ["self", "connection", "schema", "filter_names", "kw"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Collection"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_multi_columns of Dialect", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.TableKey"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.ReflectedColumn"}], "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "typing.Iterable"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_multi_foreign_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 4], "arg_names": ["self", "connection", "schema", "filter_names", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.get_multi_foreign_keys", "name": "get_multi_foreign_keys", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 4], "arg_names": ["self", "connection", "schema", "filter_names", "kw"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Collection"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_multi_foreign_keys of Dialect", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.TableKey"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.ReflectedForeignKeyConstraint"}], "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "typing.Iterable"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_multi_indexes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 4], "arg_names": ["self", "connection", "schema", "filter_names", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.get_multi_indexes", "name": "get_multi_indexes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 4], "arg_names": ["self", "connection", "schema", "filter_names", "kw"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Collection"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_multi_indexes of Dialect", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.TableKey"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.ReflectedIndex"}], "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "typing.Iterable"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_multi_pk_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 4], "arg_names": ["self", "connection", "schema", "filter_names", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.get_multi_pk_constraint", "name": "get_multi_pk_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 4], "arg_names": ["self", "connection", "schema", "filter_names", "kw"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Collection"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_multi_pk_constraint of Dialect", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.TableKey"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.ReflectedPrimaryKeyConstraint"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "typing.Iterable"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_multi_table_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 4], "arg_names": ["self", "connection", "schema", "filter_names", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.get_multi_table_comment", "name": "get_multi_table_comment", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 4], "arg_names": ["self", "connection", "schema", "filter_names", "kw"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Collection"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_multi_table_comment of <PERSON><PERSON><PERSON>", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.TableKey"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.ReflectedTableComment"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "typing.Iterable"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_multi_table_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 4], "arg_names": ["self", "connection", "schema", "filter_names", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.get_multi_table_options", "name": "get_multi_table_options", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 4], "arg_names": ["self", "connection", "schema", "filter_names", "kw"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Collection"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_multi_table_options of Dialect", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.TableKey"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "typing.Iterable"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_multi_unique_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 4], "arg_names": ["self", "connection", "schema", "filter_names", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.get_multi_unique_constraints", "name": "get_multi_unique_constraints", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 4], "arg_names": ["self", "connection", "schema", "filter_names", "kw"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Collection"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_multi_unique_constraints of Dialect", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.TableKey"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.ReflectedUniqueConstraint"}], "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "typing.Iterable"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_pk_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.get_pk_constraint", "name": "get_pk_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_pk_constraint of Dialect", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.ReflectedPrimaryKeyConstraint"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_schema_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "connection", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.get_schema_names", "name": "get_schema_names", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "connection", "kw"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_schema_names of Dialect", "ret_type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_sequence_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "connection", "schema", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.get_sequence_names", "name": "get_sequence_names", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "connection", "schema", "kw"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_sequence_names of Di<PERSON><PERSON>", "ret_type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_table_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.get_table_comment", "name": "get_table_comment", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_table_comment of <PERSON><PERSON><PERSON>", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.ReflectedTableComment"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_table_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "connection", "schema", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.get_table_names", "name": "get_table_names", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "connection", "schema", "kw"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_table_names of Diale<PERSON>", "ret_type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_table_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.get_table_options", "name": "get_table_options", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_table_options of Dialect", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_temp_table_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "connection", "schema", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.get_temp_table_names", "name": "get_temp_table_names", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "connection", "schema", "kw"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_temp_table_names of Di<PERSON><PERSON>", "ret_type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_temp_view_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "connection", "schema", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.get_temp_view_names", "name": "get_temp_view_names", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "connection", "schema", "kw"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_temp_view_names of Di<PERSON><PERSON>", "ret_type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_unique_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.get_unique_constraints", "name": "get_unique_constraints", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_unique_constraints of Dialect", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.ReflectedUniqueConstraint"}], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_view_definition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "view_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.get_view_definition", "name": "get_view_definition", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "view_name", "schema", "kw"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_view_definition of Dialect", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_view_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "connection", "schema", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.get_view_names", "name": "get_view_names", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "connection", "schema", "kw"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_view_names of Dialect", "ret_type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "has_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "index_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.has_index", "name": "has_index", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "index_name", "schema", "kw"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_index of Dialect", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "has_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "connection", "schema_name", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.has_schema", "name": "has_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "connection", "schema_name", "kw"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_schema of Dialect", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "has_sequence": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "sequence_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.has_sequence", "name": "has_sequence", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "sequence_name", "schema", "kw"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_sequence of Dialect", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "has_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.has_table", "name": "has_table", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_table of Dialect", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "has_terminate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.has_terminate", "name": "has_terminate", "type": "builtins.bool"}}, "identifier_preparer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.identifier_preparer", "name": "identifier_preparer", "type": "sqlalchemy.sql.compiler.IdentifierPreparer"}}, "import_dbapi": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.engine.interfaces.Dialect.import_dbapi", "name": "import_dbapi", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.engine.interfaces.Dialect"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "import_dbapi of Dialect", "ret_type": "sqlalchemy.engine.interfaces.DBAPIModule", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.interfaces.Dialect.import_dbapi", "name": "import_dbapi", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.engine.interfaces.Dialect"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "import_dbapi of Dialect", "ret_type": "sqlalchemy.engine.interfaces.DBAPIModule", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "include_set_input_sizes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.include_set_input_sizes", "name": "include_set_input_sizes", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.set"}, {".class": "NoneType"}]}}}, "initialize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.initialize", "name": "initialize", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "initialize of Dialect", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "inline_comments": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.inline_comments", "name": "inline_comments", "type": "builtins.bool"}}, "insert_executemany_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.insert_executemany_returning", "name": "insert_executemany_returning", "type": "builtins.bool"}}, "insert_executemany_returning_sort_by_parameter_order": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.insert_executemany_returning_sort_by_parameter_order", "name": "insert_executemany_returning_sort_by_parameter_order", "type": "builtins.bool"}}, "insert_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.insert_returning", "name": "insert_returning", "type": "builtins.bool"}}, "insertmanyvalues_implicit_sentinel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.insertmanyvalues_implicit_sentinel", "name": "insertmanyvalues_implicit_sentinel", "type": "sqlalchemy.sql.compiler.InsertmanyvaluesSentinelOpts"}}, "insertmanyvalues_max_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.insertmanyvalues_max_parameters", "name": "insertmanyvalues_max_parameters", "type": "builtins.int"}}, "insertmanyvalues_page_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.insertmanyvalues_page_size", "name": "insertmanyvalues_page_size", "type": "builtins.int"}}, "is_async": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.is_async", "name": "is_async", "type": "builtins.bool"}}, "is_disconnect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "e", "connection", "cursor"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.is_disconnect", "name": "is_disconnect", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "e", "connection", "cursor"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.interfaces.DBAPIModule.Error", {".class": "UnionType", "items": ["sqlalchemy.pool.base.PoolProxiedConnection", "sqlalchemy.engine.interfaces.DBAPIConnection", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["sqlalchemy.engine.interfaces.DBAPICursor", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_disconnect of Dialect", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "label_length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.label_length", "name": "label_length", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}}}, "load_provisioning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.engine.interfaces.Dialect.load_provisioning", "name": "load_provisioning", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.engine.interfaces.Dialect"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_provisioning of Dialect", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.interfaces.Dialect.load_provisioning", "name": "load_provisioning", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.engine.interfaces.Dialect"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_provisioning of Dialect", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "loaded_dbapi": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.engine.interfaces.Dialect.loaded_dbapi", "name": "loaded_dbapi", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "loaded_dbapi of Dialect", "ret_type": "sqlalchemy.engine.interfaces.DBAPIModule", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.interfaces.Dialect.loaded_dbapi", "name": "loaded_dbapi", "type": {".class": "Instance", "args": ["sqlalchemy.engine.interfaces.DBAPIModule"], "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "max_constraint_name_length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.max_constraint_name_length", "name": "max_constraint_name_length", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}}}, "max_identifier_length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.max_identifier_length", "name": "max_identifier_length", "type": "builtins.int"}}, "max_index_name_length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.max_index_name_length", "name": "max_index_name_length", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.name", "name": "name", "type": "builtins.str"}}, "normalize_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.normalize_name", "name": "normalize_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "normalize_name of Dialect", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "on_connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.on_connect", "name": "on_connect", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_connect of Dialect", "ret_type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "on_connect_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "url"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.on_connect_url", "name": "on_connect_url", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "url"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.url.URL"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_connect_url of Dialect", "ret_type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "paramstyle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.paramstyle", "name": "paramstyle", "type": "builtins.str"}}, "positional": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.positional", "name": "positional", "type": "builtins.bool"}}, "preexecute_autoincrement_sequences": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.preexecute_autoincrement_sequences", "name": "preexecute_autoincrement_sequences", "type": "builtins.bool"}}, "preparer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.preparer", "name": "preparer", "type": {".class": "TypeType", "item": "sqlalchemy.sql.compiler.IdentifierPreparer"}}}, "reflection_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.engine.interfaces.Dialect.reflection_options", "name": "reflection_options", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}}}, "reset_isolation_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.reset_isolation_level", "name": "reset_isolation_level", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_connection"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.interfaces.DBAPIConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset_isolation_level of Dialect", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "returns_native_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.returns_native_bytes", "name": "returns_native_bytes", "type": "builtins.bool"}}, "sequences_optional": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.sequences_optional", "name": "sequences_optional", "type": "builtins.bool"}}, "server_side_cursors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.server_side_cursors", "name": "server_side_cursors", "type": "builtins.bool"}}, "server_version_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.server_version_info", "name": "server_version_info", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}, {".class": "NoneType"}]}}}, "set_connection_execution_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "opts"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.set_connection_execution_options", "name": "set_connection_execution_options", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "opts"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.CoreExecuteOptionsParameter"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_connection_execution_options of Dialect", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "set_engine_execution_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "engine", "opts"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.set_engine_execution_options", "name": "set_engine_execution_options", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "engine", "opts"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Engine", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.CoreExecuteOptionsParameter"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_engine_execution_options of Dialect", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "set_isolation_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dbapi_connection", "level"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.set_isolation_level", "name": "set_isolation_level", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "dbapi_connection", "level"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.interfaces.DBAPIConnection", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.IsolationLevel"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_isolation_level of Dialect", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "skip_autocommit_rollback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.skip_autocommit_rollback", "name": "skip_autocommit_rollback", "type": "builtins.bool"}}, "statement_compiler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.statement_compiler", "name": "statement_compiler", "type": {".class": "TypeType", "item": "sqlalchemy.sql.compiler.SQLCompiler"}}}, "supports_alter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.supports_alter", "name": "supports_alter", "type": "builtins.bool"}}, "supports_comments": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.supports_comments", "name": "supports_comments", "type": "builtins.bool"}}, "supports_constraint_comments": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.supports_constraint_comments", "name": "supports_constraint_comments", "type": "builtins.bool"}}, "supports_default_metavalue": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.supports_default_metavalue", "name": "supports_default_metavalue", "type": "builtins.bool"}}, "supports_default_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.supports_default_values", "name": "supports_default_values", "type": "builtins.bool"}}, "supports_empty_insert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.supports_empty_insert", "name": "supports_empty_insert", "type": "builtins.bool"}}, "supports_identity_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.supports_identity_columns", "name": "supports_identity_columns", "type": "builtins.bool"}}, "supports_multivalues_insert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.supports_multivalues_insert", "name": "supports_multivalues_insert", "type": "builtins.bool"}}, "supports_native_boolean": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.supports_native_boolean", "name": "supports_native_boolean", "type": "builtins.bool"}}, "supports_native_decimal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.supports_native_decimal", "name": "supports_native_decimal", "type": "builtins.bool"}}, "supports_native_enum": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.supports_native_enum", "name": "supports_native_enum", "type": "builtins.bool"}}, "supports_native_uuid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.supports_native_uuid", "name": "supports_native_uuid", "type": "builtins.bool"}}, "supports_sane_multi_rowcount": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.supports_sane_multi_rowcount", "name": "supports_sane_multi_rowcount", "type": "builtins.bool"}}, "supports_sane_rowcount": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.supports_sane_rowcount", "name": "supports_sane_rowcount", "type": "builtins.bool"}}, "supports_sequences": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.supports_sequences", "name": "supports_sequences", "type": "builtins.bool"}}, "supports_server_side_cursors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.supports_server_side_cursors", "name": "supports_server_side_cursors", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bool"], "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}, "builtins.bool"]}}}, "supports_simple_order_by_label": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.supports_simple_order_by_label", "name": "supports_simple_order_by_label", "type": "builtins.bool"}}, "supports_statement_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.engine.interfaces.Dialect.supports_statement_cache", "name": "supports_statement_cache", "type": "builtins.bool"}}, "tuple_in_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.tuple_in_values", "name": "tuple_in_values", "type": "builtins.bool"}}, "type_compiler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.type_compiler", "name": "type_compiler", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "type_compiler_cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.type_compiler_cls", "name": "type_compiler_cls", "type": {".class": "TypeType", "item": "sqlalchemy.sql.compiler.TypeCompiler"}}}, "type_compiler_instance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.type_compiler_instance", "name": "type_compiler_instance", "type": "sqlalchemy.sql.compiler.TypeCompiler"}}, "type_descriptor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "<PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.type_descriptor", "name": "type_descriptor", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "<PERSON><PERSON><PERSON>"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.interfaces._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "type_descriptor of Dialect", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.interfaces._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.interfaces._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}, "update_executemany_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.update_executemany_returning", "name": "update_executemany_returning", "type": "builtins.bool"}}, "update_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.update_returning", "name": "update_returning", "type": "builtins.bool"}}, "update_returning_multifrom": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.update_returning_multifrom", "name": "update_returning_multifrom", "type": "builtins.bool"}}, "use_insertmanyvalues": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.use_insertmanyvalues", "name": "use_insertmanyvalues", "type": "builtins.bool"}}, "use_insertmanyvalues_wo_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.Dialect.use_insertmanyvalues_wo_returning", "name": "use_insertmanyvalues_wo_returning", "type": "builtins.bool"}}, "validate_identifier": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ident"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.Dialect.validate_identifier", "name": "validate_identifier", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ident"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate_identifier of Dialect", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.interfaces.Dialect.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.engine.interfaces.Dialect", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Engine": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.base.Engine", "kind": "Gdef"}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "EventTarget": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.registry.EventTarget", "kind": "Gdef"}, "ExceptionContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.interfaces.ExceptionContext", "name": "ExceptionContext", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.engine.interfaces.ExceptionContext", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.engine.interfaces", "mro": ["sqlalchemy.engine.interfaces.ExceptionContext", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.engine.interfaces.ExceptionContext.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "chained_exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.ExceptionContext.chained_exception", "name": "chained_exception", "type": {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}]}}}, "connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.ExceptionContext.connection", "name": "connection", "type": {".class": "UnionType", "items": ["sqlalchemy.engine.base.Connection", {".class": "NoneType"}]}}}, "cursor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.ExceptionContext.cursor", "name": "cursor", "type": {".class": "UnionType", "items": ["sqlalchemy.engine.interfaces.DBAPICursor", {".class": "NoneType"}]}}}, "dialect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.ExceptionContext.dialect", "name": "dialect", "type": "sqlalchemy.engine.interfaces.Dialect"}}, "engine": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.ExceptionContext.engine", "name": "engine", "type": {".class": "UnionType", "items": ["sqlalchemy.engine.base.Engine", {".class": "NoneType"}]}}}, "execution_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.ExceptionContext.execution_context", "name": "execution_context", "type": {".class": "UnionType", "items": ["sqlalchemy.engine.interfaces.ExecutionContext", {".class": "NoneType"}]}}}, "invalidate_pool_on_disconnect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.ExceptionContext.invalidate_pool_on_disconnect", "name": "invalidate_pool_on_disconnect", "type": "builtins.bool"}}, "is_disconnect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.ExceptionContext.is_disconnect", "name": "is_disconnect", "type": "builtins.bool"}}, "is_pre_ping": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.ExceptionContext.is_pre_ping", "name": "is_pre_ping", "type": "builtins.bool"}}, "original_exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.ExceptionContext.original_exception", "name": "original_exception", "type": "builtins.BaseException"}}, "parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.ExceptionContext.parameters", "name": "parameters", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPIMultiExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams"}, {".class": "NoneType"}]}}}, "sqlalchemy_exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.ExceptionContext.sqlalchemy_exception", "name": "sqlalchemy_exception", "type": {".class": "UnionType", "items": ["sqlalchemy.exc.StatementError", {".class": "NoneType"}]}}}, "statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.ExceptionContext.statement", "name": "statement", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.interfaces.ExceptionContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.engine.interfaces.ExceptionContext", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Executable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.Executable", "kind": "Gdef"}, "ExecuteStyle": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.interfaces.ExecuteStyle", "name": "ExecuteStyle", "type_vars": []}, "deletable_attributes": [], "flags": ["is_enum"], "fullname": "sqlalchemy.engine.interfaces.ExecuteStyle", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "sqlalchemy.engine.interfaces", "mro": ["sqlalchemy.engine.interfaces.ExecuteStyle", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "EXECUTE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.interfaces.ExecuteStyle.EXECUTE", "name": "EXECUTE", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "EXECUTEMANY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.interfaces.ExecuteStyle.EXECUTEMANY", "name": "EXECUTEMANY", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "INSERTMANYVALUES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.interfaces.ExecuteStyle.INSERTMANYVALUES", "name": "INSERTMANYVALUES", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.interfaces.ExecuteStyle.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.engine.interfaces.ExecuteStyle", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExecutionContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.interfaces.ExecutionContext", "name": "ExecutionContext", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.engine.interfaces.ExecutionContext", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.engine.interfaces", "mro": ["sqlalchemy.engine.interfaces.ExecutionContext", "builtins.object"], "names": {".class": "SymbolTable", "_exec_default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "column", "default", "type_"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.ExecutionContext._exec_default", "name": "_exec_default", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "column", "default", "type_"], "arg_types": ["sqlalchemy.engine.interfaces.ExecutionContext", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "NoneType"}]}, "sqlalchemy.sql.schema.DefaultGenerator", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_exec_default of ExecutionContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_get_cache_stats": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.ExecutionContext._get_cache_stats", "name": "_get_cache_stats", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.engine.interfaces.ExecutionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_cache_stats of ExecutionContext", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_init_compiled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 1], "arg_names": ["cls", "dialect", "connection", "dbapi_connection", "execution_options", "compiled", "parameters", "invoked_statement", "extracted_parameters", "cache_hit"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.engine.interfaces.ExecutionContext._init_compiled", "name": "_init_compiled", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 1], "arg_names": ["cls", "dialect", "connection", "dbapi_connection", "execution_options", "compiled", "parameters", "invoked_statement", "extracted_parameters", "cache_hit"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.engine.interfaces.ExecutionContext"}, "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", "sqlalchemy.pool.base.PoolProxiedConnection", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._ExecuteOptions"}, "sqlalchemy.sql.compiler.SQLCompiler", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreMultiExecuteParams"}, "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.elements.BindParameter"}], "type_ref": "typing.Sequence"}, {".class": "NoneType"}]}, "sqlalchemy.engine.interfaces.CacheStats"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_init_compiled of ExecutionContext", "ret_type": "sqlalchemy.engine.interfaces.ExecutionContext", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.interfaces.ExecutionContext._init_compiled", "name": "_init_compiled", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 1], "arg_names": ["cls", "dialect", "connection", "dbapi_connection", "execution_options", "compiled", "parameters", "invoked_statement", "extracted_parameters", "cache_hit"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.engine.interfaces.ExecutionContext"}, "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", "sqlalchemy.pool.base.PoolProxiedConnection", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._ExecuteOptions"}, "sqlalchemy.sql.compiler.SQLCompiler", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreMultiExecuteParams"}, "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.elements.BindParameter"}], "type_ref": "typing.Sequence"}, {".class": "NoneType"}]}, "sqlalchemy.engine.interfaces.CacheStats"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_init_compiled of ExecutionContext", "ret_type": "sqlalchemy.engine.interfaces.ExecutionContext", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_init_ddl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["cls", "dialect", "connection", "dbapi_connection", "execution_options", "compiled_ddl"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.engine.interfaces.ExecutionContext._init_ddl", "name": "_init_ddl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["cls", "dialect", "connection", "dbapi_connection", "execution_options", "compiled_ddl"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.engine.interfaces.ExecutionContext"}, "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", "sqlalchemy.pool.base.PoolProxiedConnection", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._ExecuteOptions"}, "sqlalchemy.sql.compiler.DDLCompiler"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_init_ddl of ExecutionContext", "ret_type": "sqlalchemy.engine.interfaces.ExecutionContext", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.interfaces.ExecutionContext._init_ddl", "name": "_init_ddl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["cls", "dialect", "connection", "dbapi_connection", "execution_options", "compiled_ddl"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.engine.interfaces.ExecutionContext"}, "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", "sqlalchemy.pool.base.PoolProxiedConnection", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._ExecuteOptions"}, "sqlalchemy.sql.compiler.DDLCompiler"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_init_ddl of ExecutionContext", "ret_type": "sqlalchemy.engine.interfaces.ExecutionContext", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_init_default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "dialect", "connection", "dbapi_connection", "execution_options"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.engine.interfaces.ExecutionContext._init_default", "name": "_init_default", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "dialect", "connection", "dbapi_connection", "execution_options"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.engine.interfaces.ExecutionContext"}, "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", "sqlalchemy.pool.base.PoolProxiedConnection", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._ExecuteOptions"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_init_default of ExecutionContext", "ret_type": "sqlalchemy.engine.interfaces.ExecutionContext", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.interfaces.ExecutionContext._init_default", "name": "_init_default", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "dialect", "connection", "dbapi_connection", "execution_options"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.engine.interfaces.ExecutionContext"}, "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", "sqlalchemy.pool.base.PoolProxiedConnection", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._ExecuteOptions"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_init_default of ExecutionContext", "ret_type": "sqlalchemy.engine.interfaces.ExecutionContext", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_init_statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "dialect", "connection", "dbapi_connection", "execution_options", "statement", "parameters"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.engine.interfaces.ExecutionContext._init_statement", "name": "_init_statement", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "dialect", "connection", "dbapi_connection", "execution_options", "statement", "parameters"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.engine.interfaces.ExecutionContext"}, "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", "sqlalchemy.pool.base.PoolProxiedConnection", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._ExecuteOptions"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPIMultiExecuteParams"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_init_statement of ExecutionContext", "ret_type": "sqlalchemy.engine.interfaces.ExecutionContext", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.interfaces.ExecutionContext._init_statement", "name": "_init_statement", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "dialect", "connection", "dbapi_connection", "execution_options", "statement", "parameters"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.engine.interfaces.ExecutionContext"}, "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", "sqlalchemy.pool.base.PoolProxiedConnection", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._ExecuteOptions"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPIMultiExecuteParams"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_init_statement of ExecutionContext", "ret_type": "sqlalchemy.engine.interfaces.ExecutionContext", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_prepare_set_input_sizes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.ExecutionContext._prepare_set_input_sizes", "name": "_prepare_set_input_sizes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.engine.interfaces.ExecutionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prepare_set_input_sizes of ExecutionContext", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_setup_result_proxy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.ExecutionContext._setup_result_proxy", "name": "_setup_result_proxy", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.engine.interfaces.ExecutionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_setup_result_proxy of ExecutionContext", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.engine.cursor.CursorResult"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "compiled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.ExecutionContext.compiled", "name": "compiled", "type": {".class": "UnionType", "items": ["sqlalchemy.sql.compiler.Compiled", {".class": "NoneType"}]}}}, "connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.ExecutionContext.connection", "name": "connection", "type": "sqlalchemy.engine.base.Connection"}}, "create_cursor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.ExecutionContext.create_cursor", "name": "create_cursor", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.engine.interfaces.ExecutionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_cursor of ExecutionContext", "ret_type": "sqlalchemy.engine.interfaces.DBAPICursor", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "cursor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.ExecutionContext.cursor", "name": "cursor", "type": "sqlalchemy.engine.interfaces.DBAPICursor"}}, "dialect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.ExecutionContext.dialect", "name": "dialect", "type": "sqlalchemy.engine.interfaces.Dialect"}}, "engine": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.ExecutionContext.engine", "name": "engine", "type": "sqlalchemy.engine.base.Engine"}}, "execute_style": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.ExecutionContext.execute_style", "name": "execute_style", "type": "sqlalchemy.engine.interfaces.ExecuteStyle"}}, "executemany": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.ExecutionContext.executemany", "name": "executemany", "type": "builtins.bool"}}, "execution_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.ExecutionContext.execution_options", "name": "execution_options", "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._ExecuteOptions"}}}, "fetchall_for_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cursor"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.ExecutionContext.fetchall_for_returning", "name": "fetchall_for_returning", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "cursor"], "arg_types": ["sqlalchemy.engine.interfaces.ExecutionContext", "sqlalchemy.engine.interfaces.DBAPICursor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fetchall_for_returning of ExecutionContext", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Sequence"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "fire_sequence": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "seq", "type_"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.ExecutionContext.fire_sequence", "name": "fire_sequence", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "seq", "type_"], "arg_types": ["sqlalchemy.engine.interfaces.ExecutionContext", "sqlalchemy.sql.schema.Sequence", "sqlalchemy.sql.sqltypes.Integer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fire_sequence of ExecutionContext", "ret_type": "builtins.int", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_out_parameter_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "out_param_names"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.ExecutionContext.get_out_parameter_values", "name": "get_out_parameter_values", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "out_param_names"], "arg_types": ["sqlalchemy.engine.interfaces.ExecutionContext", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_out_parameter_values of ExecutionContext", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Sequence"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_rowcount": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.ExecutionContext.get_rowcount", "name": "get_rowcount", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.engine.interfaces.ExecutionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_rowcount of ExecutionContext", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "handle_dbapi_exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "e"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.ExecutionContext.handle_dbapi_exception", "name": "handle_dbapi_exception", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "e"], "arg_types": ["sqlalchemy.engine.interfaces.ExecutionContext", "builtins.BaseException"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_dbapi_exception of ExecutionContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "invoked_statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.ExecutionContext.invoked_statement", "name": "invoked_statement", "type": {".class": "UnionType", "items": ["sqlalchemy.sql.base.Executable", {".class": "NoneType"}]}}}, "isinsert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.ExecutionContext.isinsert", "name": "<PERSON><PERSON><PERSON>", "type": "builtins.bool"}}, "isupdate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.ExecutionContext.isupdate", "name": "isupdate", "type": "builtins.bool"}}, "lastrow_has_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.ExecutionContext.lastrow_has_defaults", "name": "lastrow_has_defaults", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.engine.interfaces.ExecutionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lastrow_has_defaults of ExecutionContext", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "no_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.ExecutionContext.no_parameters", "name": "no_parameters", "type": "builtins.bool"}}, "parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.ExecutionContext.parameters", "name": "parameters", "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._AnyMultiExecuteParams"}}}, "post_exec": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.ExecutionContext.post_exec", "name": "post_exec", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.engine.interfaces.ExecutionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "post_exec of ExecutionContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "postfetch_cols": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.ExecutionContext.postfetch_cols", "name": "postfetch_cols", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.schema.Column"}], "type_ref": "typing.Sequence"}, {".class": "NoneType"}]}], "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}, "pre_exec": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.interfaces.ExecutionContext.pre_exec", "name": "pre_exec", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.engine.interfaces.ExecutionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pre_exec of ExecutionContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "prefetch_cols": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.ExecutionContext.prefetch_cols", "name": "prefetch_cols", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.schema.Column"}], "type_ref": "typing.Sequence"}, {".class": "NoneType"}]}], "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}, "root_connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.ExecutionContext.root_connection", "name": "root_connection", "type": "sqlalchemy.engine.base.Connection"}}, "statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.interfaces.ExecutionContext.statement", "name": "statement", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.interfaces.ExecutionContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.engine.interfaces.ExecutionContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IdentifierPreparer": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.IdentifierPreparer", "kind": "Gdef"}, "InsertmanyvaluesSentinelOpts": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.InsertmanyvaluesSentinelOpts", "kind": "Gdef"}, "Integer": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.Integer", "kind": "Gdef"}, "IsolationLevel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.engine.interfaces.IsolationLevel", "line": 273, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "SERIALIZABLE"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "REPEATABLE READ"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "READ COMMITTED"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "READ UNCOMMITTED"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "AUTOCOMMIT"}]}}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "Linting": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.<PERSON>", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "MutableMapping": {".class": "SymbolTableNode", "cross_ref": "typing.MutableMapping", "kind": "Gdef"}, "NotRequired": {".class": "SymbolTableNode", "cross_ref": "typing.NotRequired", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Pool": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool.base.Pool", "kind": "Gdef"}, "PoolProxiedConnection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool.base.PoolProxiedConnection", "kind": "Gdef"}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Protocol", "kind": "Gdef"}, "ReflectedCheckConstraint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.interfaces.ReflectedCheckConstraint", "name": "ReflectedCheckConstraint", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.engine.interfaces.ReflectedCheckConstraint", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.engine.interfaces", "mro": ["sqlalchemy.engine.interfaces.ReflectedCheckConstraint", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["name", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], ["comment", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], ["sqltext", "builtins.str"], ["dialect_options", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}]], "required_keys": ["name", "sqltext"]}}}, "ReflectedColumn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.interfaces.ReflectedColumn", "name": "ReflectedColumn", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.engine.interfaces.ReflectedColumn", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.engine.interfaces", "mro": ["sqlalchemy.engine.interfaces.ReflectedColumn", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["name", "builtins.str"], ["type", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], ["nullable", "builtins.bool"], ["default", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], ["autoincrement", "builtins.bool"], ["comment", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], ["computed", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.ReflectedComputed"}], ["identity", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.ReflectedIdentity"}], ["dialect_options", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}]], "required_keys": ["default", "name", "nullable", "type"]}}}, "ReflectedComputed": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.interfaces.ReflectedComputed", "name": "ReflectedComputed", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.engine.interfaces.ReflectedComputed", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.engine.interfaces", "mro": ["sqlalchemy.engine.interfaces.ReflectedComputed", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["sqltext", "builtins.str"], ["persisted", "builtins.bool"]], "required_keys": ["sqltext"]}}}, "ReflectedConstraint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.interfaces.ReflectedConstraint", "name": "ReflectedConstraint", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.engine.interfaces.ReflectedConstraint", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.engine.interfaces", "mro": ["sqlalchemy.engine.interfaces.ReflectedConstraint", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["name", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], ["comment", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}]], "required_keys": ["name"]}}}, "ReflectedForeignKeyConstraint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.interfaces.ReflectedForeignKeyConstraint", "name": "ReflectedForeignKeyConstraint", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.engine.interfaces.ReflectedForeignKeyConstraint", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.engine.interfaces", "mro": ["sqlalchemy.engine.interfaces.ReflectedForeignKeyConstraint", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["name", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], ["comment", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], ["constrained_columns", {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}], ["referred_schema", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], ["referred_table", "builtins.str"], ["referred_columns", {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}], ["options", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}]], "required_keys": ["constrained_columns", "name", "referred_columns", "referred_schema", "referred_table"]}}}, "ReflectedIdentity": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.interfaces.ReflectedIdentity", "name": "ReflectedIdentity", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.engine.interfaces.ReflectedIdentity", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.engine.interfaces", "mro": ["sqlalchemy.engine.interfaces.ReflectedIdentity", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["always", "builtins.bool"], ["on_null", "builtins.bool"], ["start", "builtins.int"], ["increment", "builtins.int"], ["minvalue", "builtins.int"], ["maxvalue", "builtins.int"], ["nominvalue", "builtins.bool"], ["nomaxvalue", "builtins.bool"], ["cycle", "builtins.bool"], ["cache", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], ["order", "builtins.bool"]], "required_keys": ["always", "cache", "cycle", "increment", "maxvalue", "minvalue", "nomaxvalue", "nominvalue", "on_null", "order", "start"]}}}, "ReflectedIndex": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.interfaces.ReflectedIndex", "name": "ReflectedIndex", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.engine.interfaces.ReflectedIndex", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.engine.interfaces", "mro": ["sqlalchemy.engine.interfaces.ReflectedIndex", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["name", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], ["column_names", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "type_ref": "builtins.list"}], ["expressions", {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}], ["unique", "builtins.bool"], ["duplicates_constraint", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], ["include_columns", {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}], ["column_sorting", {".class": "Instance", "args": ["builtins.str", {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.dict"}], ["dialect_options", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}]], "required_keys": ["column_names", "name", "unique"]}}}, "ReflectedPrimaryKeyConstraint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.interfaces.ReflectedPrimaryKeyConstraint", "name": "ReflectedPrimaryKeyConstraint", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.engine.interfaces.ReflectedPrimaryKeyConstraint", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.engine.interfaces", "mro": ["sqlalchemy.engine.interfaces.ReflectedPrimaryKeyConstraint", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["name", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], ["comment", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], ["constrained_columns", {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}], ["dialect_options", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}]], "required_keys": ["constrained_columns", "name"]}}}, "ReflectedTableComment": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.interfaces.ReflectedTableComment", "name": "ReflectedTableComment", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.engine.interfaces.ReflectedTableComment", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.engine.interfaces", "mro": ["sqlalchemy.engine.interfaces.ReflectedTableComment", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["text", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}]], "required_keys": ["text"]}}}, "ReflectedUniqueConstraint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.interfaces.ReflectedUniqueConstraint", "name": "ReflectedUniqueConstraint", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.engine.interfaces.ReflectedUniqueConstraint", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.engine.interfaces", "mro": ["sqlalchemy.engine.interfaces.ReflectedUniqueConstraint", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["name", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], ["comment", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], ["column_names", {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}], ["duplicates_index", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], ["dialect_options", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}]], "required_keys": ["column_names", "name"]}}}, "SQLCompiler": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.SQLCompiler", "kind": "Gdef"}, "SchemaItem": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.SchemaItem", "kind": "Gdef"}, "SchemaTranslateMapType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.engine.interfaces.SchemaTranslateMapType", "line": 263, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "type_ref": "typing.Mapping"}}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "Sequence_SchemaItem": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Sequence", "kind": "Gdef"}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef"}, "StatementError": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc.StatementError", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TableKey": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.engine.interfaces.TableKey", "line": 643, "no_args": false, "normalized": false, "target": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypeCompiler": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.TypeCompiler", "kind": "Gdef"}, "TypeEngine": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.type_api.TypeEngine", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypedDict", "kind": "Gdef"}, "URL": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.url.URL", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "VersionInfoType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.engine.interfaces.VersionInfoType", "line": 642, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"]}], "type_ref": "builtins.tuple"}}}, "_AnyExecuteParams": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.engine.interfaces._AnyExecuteParams", "line": 260, "no_args": false, "normalized": false, "target": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPIAnyExecuteParams"}}}, "_AnyMultiExecuteParams": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.engine.interfaces._AnyMultiExecuteParams", "line": 259, "no_args": false, "normalized": false, "target": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPIMultiExecuteParams"}}}, "_AnySingleExecuteParams": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.engine.interfaces._AnySingleExecuteParams", "line": 258, "no_args": false, "normalized": false, "target": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams"}}}, "_CoreAnyExecuteParams": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams", "line": 234, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreMultiExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}]}}}, "_CoreKnownExecutionOptions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.interfaces._CoreKnownExecutionOptions", "name": "_CoreKnownExecutionOptions", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.engine.interfaces._CoreKnownExecutionOptions", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.engine.interfaces", "mro": ["sqlalchemy.engine.interfaces._CoreKnownExecutionOptions", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["compiled_cache", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.CompiledCacheType"}, {".class": "NoneType"}]}], ["logging_token", "builtins.str"], ["isolation_level", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.IsolationLevel"}], ["no_parameters", "builtins.bool"], ["stream_results", "builtins.bool"], ["max_row_buffer", "builtins.int"], ["yield_per", "builtins.int"], ["insertmanyvalues_page_size", "builtins.int"], ["schema_translate_map", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.SchemaTranslateMapType"}, {".class": "NoneType"}]}], ["preserve_rowcount", "builtins.bool"]], "required_keys": []}}}, "_CoreMultiExecuteParams": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.engine.interfaces._CoreMultiExecuteParams", "line": 233, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}], "type_ref": "typing.Sequence"}}}, "_CoreSingleExecuteParams": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams", "line": 231, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.Mapping"}}}, "_DBAPIAnyExecuteParams": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.engine.interfaces._DBAPIAnyExecuteParams", "line": 243, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPIMultiExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams"}]}}}, "_DBAPICursorDescription": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.engine.interfaces._DBAPICursorDescription", "line": 246, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "sqlalchemy.engine.interfaces.DBAPIType", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "typing.Sequence"}}}, "_DBAPIMultiExecuteParams": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.engine.interfaces._DBAPIMultiExecuteParams", "line": 240, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.Sequence"}], "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreMultiExecuteParams"}]}}}, "_DBAPISingleExecuteParams": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams", "line": 238, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}]}}}, "_ExecuteOptions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.engine.interfaces._ExecuteOptions", "line": 295, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}}}, "_GenericSetInputSizesType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.engine.interfaces._GenericSetInputSizesType", "line": 271, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}}}, "_ImmutableExecuteOptions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.engine.interfaces._ImmutableExecuteOptions", "line": 265, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}}}, "_InsertManyValuesBatch": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler._InsertManyValuesBatch", "kind": "Gdef"}, "_ListenerFnType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.registry._ListenerFnType", "kind": "Gdef"}, "_MutableCoreSingleExecuteParams": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.engine.interfaces._MutableCoreSingleExecuteParams", "line": 232, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.MutableMapping"}}}, "_ParamStyle": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.engine.interfaces._ParamStyle", "line": 267, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "qmark"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "numeric"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "named"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "format"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "pyformat"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "numeric_dollar"}]}}}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.interfaces._T", "name": "_T", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, "_TypeMemoDict": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.type_api._TypeMemoDict", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.interfaces.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.interfaces.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.interfaces.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.interfaces.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.interfaces.__package__", "name": "__package__", "type": "builtins.str"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "await_only": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._concurrency_py3k.await_only", "kind": "Gdef"}, "dispatcher": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.base.dispatcher", "kind": "Gdef"}, "generic_fn_descriptor": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor", "kind": "Gdef"}, "immutabledict": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._py_collections.immutabledict", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\interfaces.py"}