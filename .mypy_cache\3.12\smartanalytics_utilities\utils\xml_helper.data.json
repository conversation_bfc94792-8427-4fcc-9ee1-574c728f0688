{".class": "MypyFile", "_fullname": "smartanalytics_utilities.utils.xml_helper", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ACDEventXMLParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["smartanalytics_utilities.utils.xml_helper.XMLParser"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "smartanalytics_utilities.utils.xml_helper.ACDEventXMLParser", "name": "ACDEventXMLParser", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "smartanalytics_utilities.utils.xml_helper.ACDEventXMLParser", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "smartanalytics_utilities.utils.xml_helper", "mro": ["smartanalytics_utilities.utils.xml_helper.ACDEventXMLParser", "smartanalytics_utilities.utils.xml_helper.XMLParser", "builtins.object"], "names": {".class": "SymbolTable", "REQUIRED_FIELDS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "smartanalytics_utilities.utils.xml_helper.ACDEventXMLParser.REQUIRED_FIELDS", "name": "REQUIRED_FIELDS", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}, "xml_to_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "xml_content"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "smartanalytics_utilities.utils.xml_helper.ACDEventXMLParser.xml_to_json", "name": "xml_to_json", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "xml_content"], "arg_types": [{".class": "TypeType", "item": "smartanalytics_utilities.utils.xml_helper.ACDEventXMLParser"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "xml_to_json of ACDEventXMLParser", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "smartanalytics_utilities.utils.xml_helper.ACDEventXMLParser.xml_to_json", "name": "xml_to_json", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "xml_content"], "arg_types": [{".class": "TypeType", "item": "smartanalytics_utilities.utils.xml_helper.ACDEventXMLParser"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "xml_to_json of ACDEventXMLParser", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "smartanalytics_utilities.utils.xml_helper.ACDEventXMLParser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "smartanalytics_utilities.utils.xml_helper.ACDEventXMLParser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AgentEventXMLParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["smartanalytics_utilities.utils.xml_helper.XMLParser"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "smartanalytics_utilities.utils.xml_helper.AgentEventXMLParser", "name": "AgentEventXMLParser", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "smartanalytics_utilities.utils.xml_helper.AgentEventXMLParser", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "smartanalytics_utilities.utils.xml_helper", "mro": ["smartanalytics_utilities.utils.xml_helper.AgentEventXMLParser", "smartanalytics_utilities.utils.xml_helper.XMLParser", "builtins.object"], "names": {".class": "SymbolTable", "REQUIRED_FIELDS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "smartanalytics_utilities.utils.xml_helper.AgentEventXMLParser.REQUIRED_FIELDS", "name": "REQUIRED_FIELDS", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}, "xml_to_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "xml_content"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "smartanalytics_utilities.utils.xml_helper.AgentEventXMLParser.xml_to_json", "name": "xml_to_json", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "xml_content"], "arg_types": [{".class": "TypeType", "item": "smartanalytics_utilities.utils.xml_helper.AgentEventXMLParser"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "xml_to_json of AgentEventXMLParser", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "smartanalytics_utilities.utils.xml_helper.AgentEventXMLParser.xml_to_json", "name": "xml_to_json", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "xml_content"], "arg_types": [{".class": "TypeType", "item": "smartanalytics_utilities.utils.xml_helper.AgentEventXMLParser"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "xml_to_json of AgentEventXMLParser", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "smartanalytics_utilities.utils.xml_helper.AgentEventXMLParser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "smartanalytics_utilities.utils.xml_helper.AgentEventXMLParser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef"}, "Logger": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.logging.logger.Logger", "kind": "Gdef"}, "XMLParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "smartanalytics_utilities.utils.xml_helper.XMLParser", "name": "XMLParser", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "smartanalytics_utilities.utils.xml_helper.XMLParser", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "smartanalytics_utilities.utils.xml_helper", "mro": ["smartanalytics_utilities.utils.xml_helper.XMLParser", "builtins.object"], "names": {".class": "SymbolTable", "extract_xml_from_sqs_message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "sqs_body"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "smartanalytics_utilities.utils.xml_helper.XMLParser.extract_xml_from_sqs_message", "name": "extract_xml_from_sqs_message", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "sqs_body"], "arg_types": [{".class": "TypeType", "item": "smartanalytics_utilities.utils.xml_helper.XMLParser"}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}]}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extract_xml_from_sqs_message of XMLParser", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "smartanalytics_utilities.utils.xml_helper.XMLParser.extract_xml_from_sqs_message", "name": "extract_xml_from_sqs_message", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "sqs_body"], "arg_types": [{".class": "TypeType", "item": "smartanalytics_utilities.utils.xml_helper.XMLParser"}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}]}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extract_xml_from_sqs_message of XMLParser", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "validate_xml_structure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "json_data", "required_fields"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "smartanalytics_utilities.utils.xml_helper.XMLParser.validate_xml_structure", "name": "validate_xml_structure", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "json_data", "required_fields"], "arg_types": [{".class": "TypeType", "item": "smartanalytics_utilities.utils.xml_helper.XMLParser"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate_xml_structure of XMLParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "smartanalytics_utilities.utils.xml_helper.XMLParser.validate_xml_structure", "name": "validate_xml_structure", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "json_data", "required_fields"], "arg_types": [{".class": "TypeType", "item": "smartanalytics_utilities.utils.xml_helper.XMLParser"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate_xml_structure of XMLParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "xml_to_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "xml_content"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "smartanalytics_utilities.utils.xml_helper.XMLParser.xml_to_json", "name": "xml_to_json", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "xml_content"], "arg_types": [{".class": "TypeType", "item": "smartanalytics_utilities.utils.xml_helper.XMLParser"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "xml_to_json of XMLParser", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "smartanalytics_utilities.utils.xml_helper.XMLParser.xml_to_json", "name": "xml_to_json", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "xml_content"], "arg_types": [{".class": "TypeType", "item": "smartanalytics_utilities.utils.xml_helper.XMLParser"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "xml_to_json of XMLParser", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "smartanalytics_utilities.utils.xml_helper.XMLParser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "smartanalytics_utilities.utils.xml_helper.XMLParser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "smartanalytics_utilities.utils.xml_helper.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "smartanalytics_utilities.utils.xml_helper.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "smartanalytics_utilities.utils.xml_helper.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "smartanalytics_utilities.utils.xml_helper.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "smartanalytics_utilities.utils.xml_helper.__package__", "name": "__package__", "type": "builtins.str"}}, "expat_parser": {".class": "SymbolTableNode", "cross_ref": "xml.parsers.expat", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "smartanalytics_utilities.utils.xml_helper.logger", "name": "logger", "type": "aws_lambda_powertools.logging.logger.Logger"}}, "xmltodict": {".class": "SymbolTableNode", "cross_ref": "xmltodict", "kind": "Gdef"}}, "path": "layers\\utilities\\python\\smartanalytics_utilities\\utils\\xml_helper.py"}