{".class": "MypyFile", "_fullname": "boto3.resources.model", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Action": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "boto3.resources.model.Action", "name": "Action", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "boto3.resources.model.Action", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "boto3.resources.model", "mro": ["boto3.resources.model.Action", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "definition", "resource_defs"], "dataclass_transform_spec": null, "flags": [], "fullname": "boto3.resources.model.Action.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "definition", "resource_defs"], "arg_types": ["boto3.resources.model.Action", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "boto3.resources.model._ActionDefinition"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Action", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "boto3.resources.model.Action.name", "name": "name", "type": "builtins.str"}}, "path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "boto3.resources.model.Action.path", "name": "path", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "request": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "boto3.resources.model.Action.request", "name": "request", "type": {".class": "UnionType", "items": ["boto3.resources.model.Request", {".class": "NoneType"}]}}}, "resource": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "boto3.resources.model.Action.resource", "name": "resource", "type": {".class": "UnionType", "items": ["boto3.resources.model.ResponseResource", {".class": "NoneType"}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "boto3.resources.model.Action.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "boto3.resources.model.Action", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Collection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["boto3.resources.model.Action"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "boto3.resources.model.Collection", "name": "Collection", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "boto3.resources.model.Collection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "boto3.resources.model", "mro": ["boto3.resources.model.Collection", "boto3.resources.model.Action", "builtins.object"], "names": {".class": "SymbolTable", "batch_actions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "boto3.resources.model.Collection.batch_actions", "name": "batch_actions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["boto3.resources.model.Collection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "batch_actions of Collection", "ret_type": {".class": "Instance", "args": ["boto3.resources.model.Action"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "boto3.resources.model.Collection.batch_actions", "name": "batch_actions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["boto3.resources.model.Collection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "batch_actions of Collection", "ret_type": {".class": "Instance", "args": ["boto3.resources.model.Action"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "boto3.resources.model.Collection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "boto3.resources.model.Collection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DefinitionWithParams": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "boto3.resources.model.DefinitionWithParams", "name": "DefinitionWithParams", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "boto3.resources.model.DefinitionWithParams", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "boto3.resources.model", "mro": ["boto3.resources.model.DefinitionWithParams", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "definition"], "dataclass_transform_spec": null, "flags": [], "fullname": "boto3.resources.model.DefinitionWithParams.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "definition"], "arg_types": ["boto3.resources.model.DefinitionWithParams", {".class": "TypeAliasType", "args": [], "type_ref": "boto3.resources.model._DefinitionWithParamsDefinition"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DefinitionWithParams", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "boto3.resources.model.DefinitionWithParams.params", "name": "params", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["boto3.resources.model.DefinitionWithParams"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "params of DefinitionWithParams", "ret_type": {".class": "Instance", "args": ["boto3.resources.model.Parameter"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "boto3.resources.model.DefinitionWithParams.params", "name": "params", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["boto3.resources.model.DefinitionWithParams"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "params of DefinitionWithParams", "ret_type": {".class": "Instance", "args": ["boto3.resources.model.Parameter"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "boto3.resources.model.DefinitionWithParams.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "boto3.resources.model.DefinitionWithParams", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Identifier": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "boto3.resources.model.Identifier", "name": "Identifier", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "boto3.resources.model.Identifier", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "boto3.resources.model", "mro": ["boto3.resources.model.Identifier", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "member_name"], "dataclass_transform_spec": null, "flags": [], "fullname": "boto3.resources.model.Identifier.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "member_name"], "arg_types": ["boto3.resources.model.Identifier", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Identifier", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "member_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "boto3.resources.model.Identifier.member_name", "name": "member_name", "type": "builtins.str"}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "boto3.resources.model.Identifier.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "boto3.resources.model.Identifier.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "boto3.resources.model.Identifier", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Parameter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "boto3.resources.model.Parameter", "name": "Parameter", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "boto3.resources.model.Parameter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "boto3.resources.model", "mro": ["boto3.resources.model.Parameter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 4], "arg_names": ["self", "target", "source", "name", "path", "value", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "boto3.resources.model.Parameter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 4], "arg_names": ["self", "target", "source", "name", "path", "value", "kwargs"], "arg_types": ["boto3.resources.model.Parameter", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", "builtins.float", "builtins.bool", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Parameter", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "boto3.resources.model.Parameter.name", "name": "name", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "boto3.resources.model.Parameter.path", "name": "path", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "source": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "boto3.resources.model.Parameter.source", "name": "source", "type": "builtins.str"}}, "target": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "boto3.resources.model.Parameter.target", "name": "target", "type": "builtins.str"}}, "value": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "boto3.resources.model.Parameter.value", "name": "value", "type": {".class": "UnionType", "items": ["builtins.str", "builtins.int", "builtins.float", "builtins.bool", {".class": "NoneType"}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "boto3.resources.model.Parameter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "boto3.resources.model.Parameter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Request": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["boto3.resources.model.DefinitionWithParams"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "boto3.resources.model.Request", "name": "Request", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "boto3.resources.model.Request", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "boto3.resources.model", "mro": ["boto3.resources.model.Request", "boto3.resources.model.DefinitionWithParams", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "definition"], "dataclass_transform_spec": null, "flags": [], "fullname": "boto3.resources.model.Request.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "definition"], "arg_types": ["boto3.resources.model.Request", {".class": "TypeAliasType", "args": [], "type_ref": "boto3.resources.model._RequestDefinition"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Request", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "operation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "boto3.resources.model.Request.operation", "name": "operation", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "boto3.resources.model.Request.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "boto3.resources.model.Request", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ResourceModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "boto3.resources.model.ResourceModel", "name": "ResourceModel", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "boto3.resources.model.ResourceModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "boto3.resources.model", "mro": ["boto3.resources.model.ResourceModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "definition", "resource_defs"], "dataclass_transform_spec": null, "flags": [], "fullname": "boto3.resources.model.ResourceModel.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "definition", "resource_defs"], "arg_types": ["boto3.resources.model.ResourceModel", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "boto3.resources.model._ResourceModelDefinition"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ResourceModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "actions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "boto3.resources.model.ResourceModel.actions", "name": "actions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["boto3.resources.model.ResourceModel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "actions of ResourceModel", "ret_type": {".class": "Instance", "args": ["boto3.resources.model.Action"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "boto3.resources.model.ResourceModel.actions", "name": "actions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["boto3.resources.model.ResourceModel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "actions of ResourceModel", "ret_type": {".class": "Instance", "args": ["boto3.resources.model.Action"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "batch_actions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "boto3.resources.model.ResourceModel.batch_actions", "name": "batch_actions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["boto3.resources.model.ResourceModel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "batch_actions of ResourceModel", "ret_type": {".class": "Instance", "args": ["boto3.resources.model.Action"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "boto3.resources.model.ResourceModel.batch_actions", "name": "batch_actions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["boto3.resources.model.ResourceModel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "batch_actions of ResourceModel", "ret_type": {".class": "Instance", "args": ["boto3.resources.model.Action"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "collections": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "boto3.resources.model.ResourceModel.collections", "name": "collections", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["boto3.resources.model.ResourceModel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "collections of ResourceModel", "ret_type": {".class": "Instance", "args": ["boto3.resources.model.Collection"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "boto3.resources.model.ResourceModel.collections", "name": "collections", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["boto3.resources.model.ResourceModel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "collections of ResourceModel", "ret_type": {".class": "Instance", "args": ["boto3.resources.model.Collection"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "get_attributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "shape"], "dataclass_transform_spec": null, "flags": [], "fullname": "boto3.resources.model.ResourceModel.get_attributes", "name": "get_attributes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "shape"], "arg_types": ["boto3.resources.model.ResourceModel", "botocore.model.Shape"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_attributes of ResourceModel", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "TupleType", "implicit": false, "items": ["builtins.str", "botocore.model.Shape"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "identifiers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "boto3.resources.model.ResourceModel.identifiers", "name": "identifiers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["boto3.resources.model.ResourceModel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "identifiers of ResourceModel", "ret_type": {".class": "Instance", "args": ["boto3.resources.model.Identifier"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "boto3.resources.model.ResourceModel.identifiers", "name": "identifiers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["boto3.resources.model.ResourceModel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "identifiers of ResourceModel", "ret_type": {".class": "Instance", "args": ["boto3.resources.model.Identifier"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "load": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "boto3.resources.model.ResourceModel.load", "name": "load", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["boto3.resources.model.ResourceModel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load of ResourceModel", "ret_type": {".class": "UnionType", "items": ["boto3.resources.model.Action", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "boto3.resources.model.ResourceModel.load", "name": "load", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["boto3.resources.model.ResourceModel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load of ResourceModel", "ret_type": {".class": "UnionType", "items": ["boto3.resources.model.Action", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "load_rename_map": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "shape"], "dataclass_transform_spec": null, "flags": [], "fullname": "boto3.resources.model.ResourceModel.load_rename_map", "name": "load_rename_map", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "shape"], "arg_types": ["boto3.resources.model.ResourceModel", {".class": "UnionType", "items": ["botocore.model.Shape", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_rename_map of ResourceModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "boto3.resources.model.ResourceModel.name", "name": "name", "type": "builtins.str"}}, "references": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "boto3.resources.model.ResourceModel.references", "name": "references", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["boto3.resources.model.ResourceModel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "references of ResourceModel", "ret_type": {".class": "Instance", "args": ["boto3.resources.model.Action"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "boto3.resources.model.ResourceModel.references", "name": "references", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["boto3.resources.model.ResourceModel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "references of ResourceModel", "ret_type": {".class": "Instance", "args": ["boto3.resources.model.Action"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "shape": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "boto3.resources.model.ResourceModel.shape", "name": "shape", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "subresources": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "boto3.resources.model.ResourceModel.subresources", "name": "subresources", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["boto3.resources.model.ResourceModel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "subresources of ResourceModel", "ret_type": {".class": "Instance", "args": ["boto3.resources.model.ResponseResource"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "boto3.resources.model.ResourceModel.subresources", "name": "subresources", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["boto3.resources.model.ResourceModel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "subresources of ResourceModel", "ret_type": {".class": "Instance", "args": ["boto3.resources.model.ResponseResource"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "waiters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "boto3.resources.model.ResourceModel.waiters", "name": "waiters", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["boto3.resources.model.ResourceModel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "waiters of ResourceModel", "ret_type": {".class": "Instance", "args": ["boto3.resources.model.Waiter"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "boto3.resources.model.ResourceModel.waiters", "name": "waiters", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["boto3.resources.model.ResourceModel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "waiters of ResourceModel", "ret_type": {".class": "Instance", "args": ["boto3.resources.model.Waiter"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "boto3.resources.model.ResourceModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "boto3.resources.model.ResourceModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ResponseResource": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "boto3.resources.model.ResponseResource", "name": "ResponseResource", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "boto3.resources.model.ResponseResource", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "boto3.resources.model", "mro": ["boto3.resources.model.ResponseResource", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "definition", "resource_defs"], "dataclass_transform_spec": null, "flags": [], "fullname": "boto3.resources.model.ResponseResource.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "definition", "resource_defs"], "arg_types": ["boto3.resources.model.ResponseResource", {".class": "TypeAliasType", "args": [], "type_ref": "boto3.resources.model._ResponseResourceDefinition"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ResponseResource", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "identifiers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "boto3.resources.model.ResponseResource.identifiers", "name": "identifiers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["boto3.resources.model.ResponseResource"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "identifiers of ResponseResource", "ret_type": {".class": "Instance", "args": ["boto3.resources.model.Identifier"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "boto3.resources.model.ResponseResource.identifiers", "name": "identifiers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["boto3.resources.model.ResponseResource"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "identifiers of ResponseResource", "ret_type": {".class": "Instance", "args": ["boto3.resources.model.Identifier"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "boto3.resources.model.ResponseResource.model", "name": "model", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["boto3.resources.model.ResponseResource"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "model of ResponseResource", "ret_type": "boto3.resources.model.ResourceModel", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "boto3.resources.model.ResponseResource.model", "name": "model", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["boto3.resources.model.ResponseResource"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "model of ResponseResource", "ret_type": "boto3.resources.model.ResourceModel", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "boto3.resources.model.ResponseResource.path", "name": "path", "type": "builtins.str"}}, "type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "boto3.resources.model.ResponseResource.type", "name": "type", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "boto3.resources.model.ResponseResource.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "boto3.resources.model.ResponseResource", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Shape": {".class": "SymbolTableNode", "cross_ref": "botocore.model.Shape", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Waiter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["boto3.resources.model.DefinitionWithParams"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "boto3.resources.model.Waiter", "name": "Waiter", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "boto3.resources.model.Waiter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "boto3.resources.model", "mro": ["boto3.resources.model.Waiter", "boto3.resources.model.DefinitionWithParams", "builtins.object"], "names": {".class": "SymbolTable", "PREFIX": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "boto3.resources.model.Waiter.PREFIX", "name": "PREFIX", "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "WaitUntil"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "definition"], "dataclass_transform_spec": null, "flags": [], "fullname": "boto3.resources.model.Waiter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "definition"], "arg_types": ["boto3.resources.model.Waiter", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "boto3.resources.model._WaiterDefinition"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Waiter", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "boto3.resources.model.Waiter.name", "name": "name", "type": "builtins.str"}}, "waiter_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "boto3.resources.model.Waiter.waiter_name", "name": "waiter_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "boto3.resources.model.Waiter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "boto3.resources.model.Waiter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ActionDefinition": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "boto3.resources.model._ActionDefinition", "name": "_ActionDefinition", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "boto3.resources.model._ActionDefinition", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "boto3.resources.model", "mro": ["boto3.resources.model._ActionDefinition", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["request", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], ["resource", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], ["path", "builtins.str"]], "required_keys": []}}}, "_DefinitionWithParamsDefinition": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "boto3.resources.model._DefinitionWithParamsDefinition", "name": "_DefinitionWithParamsDefinition", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "boto3.resources.model._DefinitionWithParamsDefinition", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "boto3.resources.model", "mro": ["boto3.resources.model._DefinitionWithParamsDefinition", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["params", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "type_ref": "builtins.list"}]], "required_keys": []}}}, "_RequestDefinition": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "boto3.resources.model._RequestDefinition", "name": "_RequestDefinition", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "boto3.resources.model._RequestDefinition", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "boto3.resources.model", "mro": ["boto3.resources.model._RequestDefinition", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["operation", "builtins.str"]], "required_keys": []}}}, "_ResourceModelDefinition": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "boto3.resources.model._ResourceModelDefinition", "name": "_ResourceModelDefinition", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "boto3.resources.model._ResourceModelDefinition", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "boto3.resources.model", "mro": ["boto3.resources.model._ResourceModelDefinition", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["shape", "builtins.str"]], "required_keys": []}}}, "_ResponseResourceDefinition": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "boto3.resources.model._ResponseResourceDefinition", "name": "_ResponseResourceDefinition", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "boto3.resources.model._ResponseResourceDefinition", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "boto3.resources.model", "mro": ["boto3.resources.model._ResponseResourceDefinition", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", "builtins.str"], ["path", "builtins.str"]], "required_keys": []}}}, "_WaiterDefinition": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "boto3.resources.model._WaiterDefinition", "name": "_WaiterDefinition", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "boto3.resources.model._WaiterDefinition", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "boto3.resources.model", "mro": ["boto3.resources.model._WaiterDefinition", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["<PERSON><PERSON><PERSON>", "builtins.str"]], "required_keys": []}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "boto3.resources.model.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "boto3.resources.model.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "boto3.resources.model.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "boto3.resources.model.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "boto3.resources.model.__package__", "name": "__package__", "type": "builtins.str"}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "boto3.resources.model.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\boto3-stubs\\resources\\model.pyi"}