{"data_mtime": 1759504547, "dep_lines": [28, 35, 42, 43, 60, 61, 68, 70, 72, 73, 76, 38, 39, 40, 41, 42, 46, 62, 64, 8, 10, 11, 12, 38, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 25, 25, 25, 25, 25, 25, 25, 10, 10, 10, 10, 20, 25, 25, 25, 5, 10, 10, 5, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.engine.interfaces", "sqlalchemy.engine.util", "sqlalchemy.sql.compiler", "sqlalchemy.sql.util", "sqlalchemy.engine.reflection", "sqlalchemy.engine.url", "sqlalchemy.sql._typing", "sqlalchemy.sql.ddl", "sqlalchemy.sql.functions", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.exc", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.util", "sqlalchemy.sql", "sqlalchemy.engine", "sqlalchemy.event", "sqlalchemy.pool", "__future__", "contextlib", "sys", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "abc", "enum", "sqlalchemy.engine.cursor", "sqlalchemy.engine.result", "sqlalchemy.event.attr", "sqlalchemy.event.base", "sqlalchemy.event.registry", "sqlalchemy.pool.base", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.elements", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.sql.visitors", "sqlalchemy.util._collections", "sqlalchemy.util._concurrency_py3k", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "types"], "hash": "5e59d9ffb93330074a7900491dc3de87eea4c393c574dfea229dc8ae0e09daee", "id": "sqlalchemy.engine.base", "ignore_all": true, "interface_hash": "0f6bc3d26c7f9b74e8fb20a23e7c1ee33057f717173fb7b121b05a751639bd21", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", "plugin_data": null, "size": 126464, "suppressed": [], "version_id": "1.8.0"}