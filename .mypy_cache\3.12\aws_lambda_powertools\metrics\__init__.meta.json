{"data_mtime": 1759504515, "dep_lines": [4, 5, 11, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 20, 20, 30, 30], "dependencies": ["aws_lambda_powertools.metrics.base", "aws_lambda_powertools.metrics.exceptions", "aws_lambda_powertools.metrics.metrics", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "typing"], "hash": "cbe93ec755f1418dc3ef02274f1b00fecbbf8f1c59e15417dbd2f811e039cbf1", "id": "aws_lambda_powertools.metrics", "ignore_all": true, "interface_hash": "7bef36ae6eb4c5a8bcf1491ad49922a186e29c651fb15e8baf3c0c56f974a742", "mtime": 1757091873, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\aws_lambda_powertools\\metrics\\__init__.py", "plugin_data": null, "size": 593, "suppressed": [], "version_id": "1.8.0"}