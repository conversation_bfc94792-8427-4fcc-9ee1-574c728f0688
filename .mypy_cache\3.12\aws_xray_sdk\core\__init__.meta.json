{"data_mtime": 1759503830, "dep_lines": [1, 2, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 20, 20, 30, 30], "dependencies": ["aws_xray_sdk.core.patcher", "aws_xray_sdk.core.recorder", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "typing"], "hash": "a9f6befda3fe1e8f23cd8e270e72038c9f2c3c43c30586963c18d65db2386251", "id": "aws_xray_sdk.core", "ignore_all": true, "interface_hash": "df7789bf3d3725aedf89413880a239eecc6bec2c8ff37726211f66729aabd0b3", "mtime": 1757424623, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\aws_xray_sdk-stubs\\core\\__init__.pyi", "plugin_data": null, "size": 219, "suppressed": [], "version_id": "1.8.0"}