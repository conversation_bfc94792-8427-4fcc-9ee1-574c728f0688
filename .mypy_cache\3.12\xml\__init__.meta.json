{"data_mtime": 1759503830, "dep_lines": [1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 20, 20, 30, 30], "dependencies": ["xml.parsers", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "typing"], "hash": "d6fe4f5e459dd48f74f45ac7486578db3f805beefd9590477d222b6c1dc3796f", "id": "xml", "ignore_all": true, "interface_hash": "62e4fb63bd36599cedec39f910d9d51e9bf595bae78eaf04af7e211e8545b9bf", "mtime": 1757092231, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\xml\\__init__.pyi", "plugin_data": null, "size": 35, "suppressed": [], "version_id": "1.8.0"}