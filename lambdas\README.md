# Lambda Functions

This directory contains AWS Lambda functions for processing smart analytics data. Each function is designed for specific data processing tasks with enterprise-grade reliability, security, and observability.

## Available Functions

### ACD Event Processor
**Path**: `acd-event-processor/`
**Purpose**: Processes ACD (Automatic Call Distribution) agent events for real-time analytics
**Runtime**: Python 3.12
**Trigger**: SQS Queue
**Architecture**: Hexagonal Architecture with Lambda Layers

**Key Features**:
- **Lambda Layers**: Shared code and dependencies across functions
- **Hexagonal Architecture**: Clean separation of domain, infrastructure, and application layers
- Real-time processing of agent login/logout, call events, and state changes
- Idempotent processing with SHA-256 hash-based deduplication
- Aurora PostgreSQL integration with SCD Type 2 dimensions
- Comprehensive error handling with Dead Letter Queue support
- Multi-customer isolation with dedicated resources per customer
- **Ultra-small deployments**: 10 KB Lambda packages (vs 20 MB without layers)

**Data Types Processed**:
- Agent Login/Logout events
- Call Start/End events
- Agent State Change events
- Queue Assignment events
- Ring Group events
- Custom ACD events

**Documentation**: [acd-event-processor/README.md](acd-event-processor/README.md)

## Architecture Overview

```mermaid
graph TB
    subgraph "Event Sources"
        ACD[ACD Systems]
        API[REST APIs]
        Files[File Uploads]
    end

    subgraph "AWS Lambda Functions"
        ACDProcessor[ACD Event Processor<br/>Python 3.12 + Layers]
        FutureProcessor[Future Processors<br/>TBD]
    end

    subgraph "Lambda Layers"
        Utilities[Utilities Layer<br/>~15 MB]
        Domain[Domain Layer<br/>~50 KB]
        Infrastructure[Infrastructure Layer<br/>~4 MB]
    end

    subgraph "Data Storage"
        Aurora[(Aurora PostgreSQL<br/>Database)]
        S3[(S3 Buckets<br/>Raw Data)]
    end

    subgraph "Event Processing"
        SQS[SQS Queues]
        DLQ[Dead Letter Queues]
    end

    ACD --> SQS
    API --> SQS
    Files --> SQS
    SQS --> ACDProcessor
    SQS --> FutureProcessor
    DLQ --> ACDProcessor
    DLQ --> FutureProcessor

    Utilities --> ACDProcessor
    Domain --> ACDProcessor
    Infrastructure --> ACDProcessor

    ACDProcessor --> Aurora
    FutureProcessor --> Aurora
    ACDProcessor --> S3
    FutureProcessor --> S3
```

## Common Patterns

### Event Processing
All Lambda functions follow consistent patterns:
- **SQS Trigger**: Batch processing with configurable batch sizes
- **Idempotency**: Hash-based duplicate detection and prevention
- **Error Handling**: Partial batch failure with DLQ integration
- **Monitoring**: CloudWatch metrics and structured logging

### Data Storage
- **Aurora PostgreSQL**: High-performance managed database
- **Star Schema**: Dimensional modeling with fact and dimension tables
- **SCD Type 2**: Historical tracking for slowly changing dimensions
- **Atomic Operations**: Database transactions for data consistency
- **Schema Evolution**: Versioned database schemas with migration scripts

### Security
- **VPC Deployment**: Private subnet isolation for database access
- **IAM Authentication**: Role-based access with least privilege
- **Encryption**: Data encrypted in transit and at rest
- **Audit Logging**: Comprehensive logging for compliance

## Development Standards

### Code Quality
- **Ruff**: Code formatting and linting
- **MyPy**: Static type checking
- **Bandit**: Security vulnerability scanning
- **Pytest**: Unit and integration testing

### Project Structure
Each Lambda function follows a standardized structure:
```
function-name/
├── README.md              # Function documentation
├── src/                   # Source code
│   └── function_name/     # Python package
├── tests/                 # Test suites
│   ├── unit/             # Unit tests
│   └── integration/      # Integration tests
├── scripts/              # Database and utility scripts
├── requirements.txt      # Production dependencies
├── requirements-dev.txt  # Development dependencies
├── pyproject.toml        # Python project configuration
└── pytest.ini           # Test configuration
```

### Configuration Management
- **Environment Variables**: Runtime configuration via Lambda environment
- **Parameter Validation**: Pydantic models for type safety
- **Multi-Environment**: Support for dev, staging, and production
- **Customer Isolation**: Per-customer configuration and resources

## Getting Started

### Prerequisites
- Python 3.12+
- AWS CLI configured
- Access to target AWS account
- Aurora PostgreSQL cluster access

### Local Development

1. **Navigate to a function directory**
   ```bash
   cd acd-event-processor
   ```

2. **Set up Python environment**
   ```bash
   python -m venv .venv
   source .venv/bin/activate  # Windows: .venv\Scripts\activate
   pip install -r requirements-dev.txt
   ```

3. **Run tests**
   ```bash
   python -m pytest tests/unit/ -v
   ```

4. **Run quality checks**
   ```bash
   python -m ruff check .
   python -m mypy src
   python -m bandit -r src/
   ```

### Testing Against Real Environment

Each function includes scripts for testing against real AWS resources:

```bash
# Test with sample events
python test_all_event_types.py

# Test specific event type
python test_all_event_types.py test-events/agent_login.json
```

## Deployment

Lambda functions are deployed using Terraform modules located in the `terraform/` directory. Each function has a corresponding Terraform module that handles:

- Lambda function creation and configuration
- IAM roles and policies
- SQS queue integration
- CloudWatch monitoring and alarms
- VPC and security group configuration

See [terraform/README.md](../terraform/README.md) for deployment instructions.

## Monitoring

### CloudWatch Metrics
- Function invocations, errors, and duration
- SQS queue depth and message age
- Custom business metrics for data quality
- Memory usage and performance optimization

### Alerting
- Error rate thresholds with SNS notifications
- Performance degradation detection
- Queue backlog monitoring
- Anomaly detection for unusual patterns

### Logging
- Structured JSON logging with correlation IDs
- Comprehensive error context and stack traces
- Performance timing for optimization
- Business event tracking for audit trails

## Future Expansion

The Lambda functions architecture is designed to support additional processors:

### Planned Functions
- **Call Detail Record Processor**: CDR data processing and analytics
- **Real-time Dashboard Processor**: Live metrics and KPI calculations
- **Report Generator**: Scheduled report generation and distribution
- **Data Quality Processor**: Data validation and quality monitoring

### Scalability Considerations
- Shared libraries for common functionality
- Standardized event schemas and processing patterns
- Reusable Terraform modules for consistent deployment
- Common monitoring and alerting patterns

## Support

### Documentation
- Function-specific README files for detailed information
- Architecture documentation for design decisions
- Troubleshooting guides for common issues

### Development
- GitLab issues for bug reports and feature requests
- Code review process for quality assurance
- Automated testing and deployment pipelines

For function-specific documentation, see the README file in each function's directory.
