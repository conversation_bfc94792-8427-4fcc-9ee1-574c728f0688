{"data_mtime": 1759504546, "dep_lines": [27, 9, 11, 12, 1, 1, 1, 1, 1, 21], "dep_prios": [5, 5, 10, 5, 5, 20, 20, 30, 30, 10], "dependencies": ["sqlalchemy.util._concurrency_py3k", "__future__", "asyncio", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "abc"], "hash": "6dc6300f9863ce2f65b071f4c11d604e4551153eb6d73fbcd96f7d8c4b8ccf1d", "id": "sqlalchemy.util.concurrency", "ignore_all": true, "interface_hash": "1764a0bc6da4bca00f5f56aaa052a0827e3df86df3650f74d628bd954417aa47", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\concurrency.py", "plugin_data": null, "size": 3412, "suppressed": ["greenlet"], "version_id": "1.8.0"}