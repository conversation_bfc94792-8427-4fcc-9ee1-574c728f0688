{".class": "MypyFile", "_fullname": "requests.adapters", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "BaseAdapter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "requests.adapters.BaseAdapter", "name": "BaseAdapter", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "requests.adapters.BaseAdapter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "requests.adapters", "mro": ["requests.adapters.BaseAdapter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "requests.adapters.BaseAdapter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["requests.adapters.BaseAdapter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BaseAdapter", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "requests.adapters.BaseAdapter.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["requests.adapters.BaseAdapter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of BaseAdapter", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "send": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "request", "stream", "timeout", "verify", "cert", "proxies"], "dataclass_transform_spec": null, "flags": [], "fullname": "requests.adapters.BaseAdapter.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "request", "stream", "timeout", "verify", "cert", "proxies"], "arg_types": ["requests.adapters.BaseAdapter", "requests.models.PreparedRequest", "builtins.bool", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.float", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", {".class": "NoneType"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}]}, {".class": "UnionType", "items": ["builtins.bool", "builtins.str"]}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.bytes", "builtins.str", {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.bytes", "builtins.str"]}, {".class": "UnionType", "items": ["builtins.bytes", "builtins.str"]}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send of BaseAdapter", "ret_type": "requests.models.Response", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "requests.adapters.BaseAdapter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "requests.adapters.BaseAdapter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CaseInsensitiveDict": {".class": "SymbolTableNode", "cross_ref": "requests.structures.CaseInsensitiveDict", "kind": "Gdef"}, "ConnectTimeout": {".class": "SymbolTableNode", "cross_ref": "requests.exceptions.ConnectTimeout", "kind": "Gdef"}, "ConnectTimeoutError": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.ConnectTimeoutError", "kind": "Gdef"}, "ConnectionError": {".class": "SymbolTableNode", "cross_ref": "requests.exceptions.ConnectionError", "kind": "Gdef"}, "ConnectionPool": {".class": "SymbolTableNode", "cross_ref": "urllib3.connectionpool.ConnectionPool", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DEFAULT_CA_BUNDLE_PATH": {".class": "SymbolTableNode", "cross_ref": "requests.utils.DEFAULT_CA_BUNDLE_PATH", "kind": "Gdef"}, "DEFAULT_POOLBLOCK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "requests.adapters.DEFAULT_POOLBLOCK", "name": "DEFAULT_POOLBLOCK", "type": "builtins.bool"}}, "DEFAULT_POOLSIZE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "requests.adapters.DEFAULT_POOLSIZE", "name": "DEFAULT_POOLSIZE", "type": "builtins.int"}}, "DEFAULT_POOL_TIMEOUT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "requests.adapters.DEFAULT_POOL_TIMEOUT", "name": "DEFAULT_POOL_TIMEOUT", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}]}}}, "DEFAULT_RETRIES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "requests.adapters.DEFAULT_RETRIES", "name": "DEFAULT_RETRIES", "type": "builtins.int"}}, "HTTPAdapter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["requests.adapters.BaseAdapter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "requests.adapters.HTTPAdapter", "name": "HTTPAdapter", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "requests.adapters.HTTPAdapter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "requests.adapters", "mro": ["requests.adapters.HTTPAdapter", "requests.adapters.BaseAdapter", "builtins.object"], "names": {".class": "SymbolTable", "__attrs__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "requests.adapters.HTTPAdapter.__attrs__", "name": "__attrs__", "type": {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "pool_connections", "pool_maxsize", "max_retries", "pool_block"], "dataclass_transform_spec": null, "flags": [], "fullname": "requests.adapters.HTTPAdapter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "pool_connections", "pool_maxsize", "max_retries", "pool_block"], "arg_types": ["requests.adapters.HTTPAdapter", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["urllib3.util.retry.Retry", "builtins.int", {".class": "NoneType"}]}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HTTPAdapter", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "add_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "request", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "requests.adapters.HTTPAdapter.add_headers", "name": "add_headers", "type": null}}, "build_connection_pool_key_attributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "request", "verify", "cert"], "dataclass_transform_spec": null, "flags": [], "fullname": "requests.adapters.HTTPAdapter.build_connection_pool_key_attributes", "name": "build_connection_pool_key_attributes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "request", "verify", "cert"], "arg_types": ["requests.adapters.HTTPAdapter", "requests.models.PreparedRequest", {".class": "UnionType", "items": ["builtins.bool", "builtins.str"]}, {".class": "UnionType", "items": ["builtins.str", {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_connection_pool_key_attributes of HTTPAdapter", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "requests.adapters._HostParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "requests.adapters._PoolKwargs"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "build_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "req", "resp"], "dataclass_transform_spec": null, "flags": [], "fullname": "requests.adapters.HTTPAdapter.build_response", "name": "build_response", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "req", "resp"], "arg_types": ["requests.adapters.HTTPAdapter", "requests.models.PreparedRequest", "urllib3.response.BaseHTTPResponse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_response of HTTPAdapter", "ret_type": "requests.models.Response", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "cert_verify": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "conn", "url", "verify", "cert"], "dataclass_transform_spec": null, "flags": [], "fullname": "requests.adapters.HTTPAdapter.cert_verify", "name": "cert_verify", "type": null}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "requests.adapters.HTTPAdapter.close", "name": "close", "type": null}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "requests.adapters.HTTPAdapter.config", "name": "config", "type": {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}}}, "get_connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "url", "proxies"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "requests.adapters.HTTPAdapter.get_connection", "name": "get_connection", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "url", "proxies"], "arg_types": ["requests.adapters.HTTPAdapter", {".class": "TypeAliasType", "args": [], "type_ref": "requests.utils._<PERSON>ri"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_connection of HTTPAdapter", "ret_type": "urllib3.connectionpool.ConnectionPool", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "requests.adapters.HTTPAdapter.get_connection", "name": "get_connection", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "url", "proxies"], "arg_types": ["requests.adapters.HTTPAdapter", {".class": "TypeAliasType", "args": [], "type_ref": "requests.utils._<PERSON>ri"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_connection of HTTPAdapter", "ret_type": "urllib3.connectionpool.ConnectionPool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "get_connection_with_tls_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "request", "verify", "proxies", "cert"], "dataclass_transform_spec": null, "flags": [], "fullname": "requests.adapters.HTTPAdapter.get_connection_with_tls_context", "name": "get_connection_with_tls_context", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "request", "verify", "proxies", "cert"], "arg_types": ["requests.adapters.HTTPAdapter", "requests.models.PreparedRequest", {".class": "UnionType", "items": ["builtins.bool", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_connection_with_tls_context of HTTPAdapter", "ret_type": "urllib3.connectionpool.ConnectionPool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "init_poolmanager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connections", "maxsize", "block", "pool_kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "requests.adapters.HTTPAdapter.init_poolmanager", "name": "init_poolmanager", "type": null}}, "max_retries": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "requests.adapters.HTTPAdapter.max_retries", "name": "max_retries", "type": "urllib3.util.retry.Retry"}}, "poolmanager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "requests.adapters.HTTPAdapter.poolmanager", "name": "poolmanager", "type": {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}}}, "proxy_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "proxy"], "dataclass_transform_spec": null, "flags": [], "fullname": "requests.adapters.HTTPAdapter.proxy_headers", "name": "proxy_headers", "type": null}}, "proxy_manager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "requests.adapters.HTTPAdapter.proxy_manager", "name": "proxy_manager", "type": {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}}}, "proxy_manager_for": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "proxy", "proxy_kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "requests.adapters.HTTPAdapter.proxy_manager_for", "name": "proxy_manager_for", "type": null}}, "request_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "proxies"], "dataclass_transform_spec": null, "flags": [], "fullname": "requests.adapters.HTTPAdapter.request_url", "name": "request_url", "type": null}}, "send": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "request", "stream", "timeout", "verify", "cert", "proxies"], "dataclass_transform_spec": null, "flags": [], "fullname": "requests.adapters.HTTPAdapter.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "request", "stream", "timeout", "verify", "cert", "proxies"], "arg_types": ["requests.adapters.HTTPAdapter", "requests.models.PreparedRequest", "builtins.bool", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.float", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", {".class": "NoneType"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}]}, {".class": "UnionType", "items": ["builtins.bool", "builtins.str"]}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.bytes", "builtins.str", {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.bytes", "builtins.str"]}, {".class": "UnionType", "items": ["builtins.bytes", "builtins.str"]}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send of HTTPAdapter", "ret_type": "requests.models.Response", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "requests.adapters.HTTPAdapter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "requests.adapters.HTTPAdapter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Incomplete": {".class": "SymbolTableNode", "cross_ref": "_typeshed.Incomplete", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MaxRetryError": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.MaxRetryError", "kind": "Gdef"}, "NotRequired": {".class": "SymbolTableNode", "cross_ref": "typing.NotRequired", "kind": "Gdef", "module_hidden": true, "module_public": false}, "PoolManager": {".class": "SymbolTableNode", "cross_ref": "urllib3.poolmanager.PoolManager", "kind": "Gdef"}, "PreparedRequest": {".class": "SymbolTableNode", "cross_ref": "requests.models.PreparedRequest", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ProtocolError": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.ProtocolError", "kind": "Gdef"}, "ProxyError": {".class": "SymbolTableNode", "cross_ref": "requests.exceptions.ProxyError", "kind": "Gdef"}, "ReadTimeout": {".class": "SymbolTableNode", "cross_ref": "requests.exceptions.ReadTimeout", "kind": "Gdef"}, "ReadTimeoutError": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.ReadTimeoutError", "kind": "Gdef"}, "Response": {".class": "SymbolTableNode", "cross_ref": "requests.models.Response", "kind": "Gdef"}, "ResponseError": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.ResponseError", "kind": "Gdef"}, "Retry": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.retry.Retry", "kind": "Gdef"}, "RetryError": {".class": "SymbolTableNode", "cross_ref": "requests.exceptions.RetryError", "kind": "Gdef"}, "SOCKSProxyManager": {".class": "SymbolTableNode", "cross_ref": "urllib3.contrib.socks.SOCKSProxyManager", "kind": "Gdef"}, "SSLContext": {".class": "SymbolTableNode", "cross_ref": "ssl.SSLContext", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SSLError": {".class": "SymbolTableNode", "cross_ref": "requests.exceptions.SSLError", "kind": "Gdef"}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_HostParams": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "requests.adapters._HostParams", "name": "_HostParams", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "requests.adapters._HostParams", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "requests.adapters", "mro": ["requests.adapters._HostParams", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["host", "builtins.str"], ["scheme", "builtins.str"], ["port", "builtins.int"]], "required_keys": ["host", "port", "scheme"]}}}, "_PoolKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "requests.adapters._PoolKwargs", "name": "_PoolKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "requests.adapters._PoolKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "requests.adapters", "mro": ["requests.adapters._PoolKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["ssl_context", "ssl.SSLContext"], ["ca_certs", "builtins.str"], ["ca_cert_dir", "builtins.str"], ["cert_reqs", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "CERT_REQUIRED"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "CERT_NONE"}]}], ["cert_file", "builtins.str"], ["key_file", "builtins.str"]], "required_keys": ["cert_reqs"]}}}, "_Uri": {".class": "SymbolTableNode", "cross_ref": "requests.utils._<PERSON>ri", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "requests.adapters.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "requests.adapters.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "requests.adapters.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "requests.adapters.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "requests.adapters.__package__", "name": "__package__", "type": "builtins.str"}}, "deprecated": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.deprecated", "kind": "Gdef", "module_hidden": true, "module_public": false}, "extract_cookies_to_jar": {".class": "SymbolTableNode", "cross_ref": "requests.cookies.extract_cookies_to_jar", "kind": "Gdef"}, "get_auth_from_url": {".class": "SymbolTableNode", "cross_ref": "requests.utils.get_auth_from_url", "kind": "Gdef"}, "get_encoding_from_headers": {".class": "SymbolTableNode", "cross_ref": "requests.utils.get_encoding_from_headers", "kind": "Gdef"}, "prepend_scheme_if_needed": {".class": "SymbolTableNode", "cross_ref": "requests.utils.prepend_scheme_if_needed", "kind": "Gdef"}, "proxy_from_url": {".class": "SymbolTableNode", "cross_ref": "urllib3.poolmanager.proxy_from_url", "kind": "Gdef"}, "type_check_only": {".class": "SymbolTableNode", "cross_ref": "typing.type_check_only", "kind": "Gdef", "module_hidden": true, "module_public": false}, "urldefragauth": {".class": "SymbolTableNode", "cross_ref": "requests.utils.urldefragauth", "kind": "Gdef"}, "urllib3": {".class": "SymbolTableNode", "cross_ref": "urllib3", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\requests-stubs\\adapters.pyi"}