{"data_mtime": 1759503832, "dep_lines": [6, 11, 3, 4, 1, 1, 1, 1, 1], "dep_prios": [5, 25, 10, 5, 5, 20, 20, 30, 30], "dependencies": ["rich._extension", "rich.console", "os", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "posixpath"], "hash": "961d9672820e269e4ce7f95b02c494306bfca2225eba644e6b31c7fc060c4bc2", "id": "rich", "ignore_all": true, "interface_hash": "a79a834f3cf2f9b4cf493df81d8c95f150d7f4e140bd5dfbf054804605cb262b", "mtime": 1757092238, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\rich\\__init__.py", "plugin_data": null, "size": 6066, "suppressed": [], "version_id": "1.8.0"}