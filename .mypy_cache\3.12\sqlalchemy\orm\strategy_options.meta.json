{"data_mtime": 1759504547, "dep_lines": [27, 28, 32, 33, 34, 35, 48, 49, 50, 51, 52, 53, 54, 66, 72, 74, 27, 44, 46, 47, 11, 13, 44, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 5, 5, 25, 25, 25, 20, 10, 10, 5, 5, 5, 5, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.util", "sqlalchemy.orm._typing", "sqlalchemy.orm.attributes", "sqlalchemy.orm.base", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.path_registry", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.coercions", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.sql.base", "sqlalchemy.util.typing", "sqlalchemy.orm.context", "sqlalchemy.orm.mapper", "sqlalchemy.sql._typing", "sqlalchemy.orm", "sqlalchemy.exc", "sqlalchemy.util", "sqlalchemy.sql", "__future__", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "_typeshed", "abc", "enum", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.sql.annotation", "sqlalchemy.sql.dml", "sqlalchemy.sql.elements", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.operators", "sqlalchemy.sql.selectable", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "types"], "hash": "c526787d5b5cc33678eb5a3bad8507ee847e9f145abf4c5a2519b933306299a4", "id": "sqlalchemy.orm.strategy_options", "ignore_all": true, "interface_hash": "e752c9eec054e4fa9ec9e4f2ef4547aef6a7a2ec88e05882ffa36b711496a975", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\strategy_options.py", "plugin_data": null, "size": 88195, "suppressed": [], "version_id": "1.8.0"}