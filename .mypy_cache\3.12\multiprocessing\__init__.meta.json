{"data_mtime": 1759504508, "dep_lines": [2, 2, 10, 19, 20, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 10, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["multiprocessing.context", "multiprocessing.reduction", "multiprocessing.process", "multiprocessing.queues", "multiprocessing.spawn", "sys", "builtins", "pyexpat.model", "pyexpat.errors", "_ctypes", "_typeshed", "abc", "contextlib", "logging", "multiprocessing.connection", "multiprocessing.managers", "multiprocessing.pool", "multiprocessing.sharedctypes", "multiprocessing.synchronize", "threading", "typing"], "hash": "91b5edb5ac064810d3273ae8bdea492df1d960a7e6e2c89a73e73e9233cc7f95", "id": "multiprocessing", "ignore_all": true, "interface_hash": "c0d95664d1e0cb967c9b1cefbb09cfa81134ace1c3945d6331eae55c865a8f32", "mtime": 1757092231, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\multiprocessing\\__init__.pyi", "plugin_data": null, "size": 3241, "suppressed": [], "version_id": "1.8.0"}