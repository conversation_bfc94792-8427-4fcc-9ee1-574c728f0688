{"data_mtime": 1759503832, "dep_lines": [4, 5, 6, 7, 8, 1, 2, 110, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 30, 30, 30, 30, 30], "dependencies": ["rich.console", "rich.jupyter", "rich.live", "rich.spinner", "rich.style", "types", "typing", "time", "builtins", "pyexpat.model", "pyexpat.errors", "_random", "abc", "datetime", "rich.text", "rich.theme"], "hash": "9243e987761e019068f97fb8c0fa7c813a99c94e3ae8d2f06410383d94d37b0a", "id": "rich.status", "ignore_all": true, "interface_hash": "e0978308509ff862ab5e1c49350314b4464279138a3cc2d23120ab3527206148", "mtime": 1757092239, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\rich\\status.py", "plugin_data": null, "size": 4424, "suppressed": [], "version_id": "1.8.0"}