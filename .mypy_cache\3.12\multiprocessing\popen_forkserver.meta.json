{"data_mtime": 1759504508, "dep_lines": [4, 5, 1, 2, 4, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 5, 20, 5, 20, 20, 30], "dependencies": ["multiprocessing.popen_fork", "multiprocessing.util", "sys", "typing", "multiprocessing", "builtins", "pyexpat.model", "pyexpat.errors", "abc"], "hash": "f9ff39d5c1d011b33f2fda176b0e8fad41c8e9b28055ab11475ece8ab39277ad", "id": "multiprocessing.popen_forkserver", "ignore_all": true, "interface_hash": "afb7dd44dd7a2823a94a1573b0582154a5477dc6eec0073d5333cb02d143b364", "mtime": 1757092231, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\multiprocessing\\popen_forkserver.pyi", "plugin_data": null, "size": 353, "suppressed": [], "version_id": "1.8.0"}