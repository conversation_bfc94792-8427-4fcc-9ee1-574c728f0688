{".class": "MypyFile", "_fullname": "sqlalchemy.dialects.postgresql.psycopg2cffi", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "PGDialect_psycopg2": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.psycopg2.PGDialect_psycopg2", "kind": "Gdef"}, "PGDialect_psycopg2cffi": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.postgresql.psycopg2.PGDialect_psycopg2"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.psycopg2cffi.PGDialect_psycopg2cffi", "name": "PGDialect_psycopg2cffi", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.psycopg2cffi.PGDialect_psycopg2cffi", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.psycopg2cffi", "mro": ["sqlalchemy.dialects.postgresql.psycopg2cffi.PGDialect_psycopg2cffi", "sqlalchemy.dialects.postgresql.psycopg2.PGDialect_psycopg2", "sqlalchemy.dialects.postgresql._psycopg_common._PGDialect_common_psycopg", "sqlalchemy.dialects.postgresql.base.PGDialect", "sqlalchemy.engine.default.DefaultDialect", "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.event.registry.EventTarget", "builtins.object"], "names": {".class": "SymbolTable", "FEATURE_VERSION_MAP": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.psycopg2cffi.PGDialect_psycopg2cffi.FEATURE_VERSION_MAP", "name": "FEATURE_VERSION_MAP", "type": {".class": "Instance", "args": ["builtins.str", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.int"], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.dict"}}}, "_psycopg2_extensions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.psycopg2cffi.PGDialect_psycopg2cffi._psycopg2_extensions", "name": "_psycopg2_extensions", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.psycopg2cffi.PGDialect_psycopg2cffi._psycopg2_extensions", "name": "_psycopg2_extensions", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "_psycopg2_extras": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.psycopg2cffi.PGDialect_psycopg2cffi._psycopg2_extras", "name": "_psycopg2_extras", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.psycopg2cffi.PGDialect_psycopg2cffi._psycopg2_extras", "name": "_psycopg2_extras", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "driver": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.psycopg2cffi.PGDialect_psycopg2cffi.driver", "name": "driver", "type": "builtins.str"}}, "import_dbapi": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.psycopg2cffi.PGDialect_psycopg2cffi.import_dbapi", "name": "import_dbapi", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.psycopg2cffi.PGDialect_psycopg2cffi.import_dbapi", "name": "import_dbapi", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.dialects.postgresql.psycopg2cffi.PGDialect_psycopg2cffi"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "import_dbapi of PGDialect_psycopg2cffi", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "supports_statement_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.psycopg2cffi.PGDialect_psycopg2cffi.supports_statement_cache", "name": "supports_statement_cache", "type": "builtins.bool"}}, "supports_unicode_statements": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.psycopg2cffi.PGDialect_psycopg2cffi.supports_unicode_statements", "name": "supports_unicode_statements", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.psycopg2cffi.PGDialect_psycopg2cffi.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.psycopg2cffi.PGDialect_psycopg2cffi", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.postgresql.psycopg2cffi.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.postgresql.psycopg2cffi.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.postgresql.psycopg2cffi.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.postgresql.psycopg2cffi.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.postgresql.psycopg2cffi.__package__", "name": "__package__", "type": "builtins.str"}}, "dialect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.postgresql.psycopg2cffi.dialect", "line": 61, "no_args": true, "normalized": false, "target": "sqlalchemy.dialects.postgresql.psycopg2cffi.PGDialect_psycopg2cffi"}}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg2cffi.py"}