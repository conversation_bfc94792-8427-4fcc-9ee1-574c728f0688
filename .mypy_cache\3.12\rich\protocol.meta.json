{"data_mtime": 1759504511, "dep_lines": [5, 1, 2, 1, 1, 1, 1], "dep_prios": [25, 5, 5, 5, 20, 20, 30], "dependencies": ["rich.console", "typing", "inspect", "builtins", "pyexpat.model", "pyexpat.errors", "abc"], "hash": "5adfb61d977aece622a295240933b3ee5337f2fca8e6bdc711067d4ce3c39794", "id": "rich.protocol", "ignore_all": true, "interface_hash": "35b008168a387ceb3b38d29a2b2cb91e2587c090ff71f546b52eee5c14f08b03", "mtime": 1757092239, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\rich\\protocol.py", "plugin_data": null, "size": 1367, "suppressed": [], "version_id": "1.8.0"}