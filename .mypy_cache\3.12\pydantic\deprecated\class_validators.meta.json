{"data_mtime": 1759504514, "dep_lines": [12, 12, 12, 13, 14, 3, 5, 6, 7, 8, 10, 1, 1, 1, 1], "dep_prios": [10, 10, 20, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 30], "dependencies": ["pydantic._internal._decorators", "pydantic._internal._decorators_v1", "pydantic._internal", "pydantic.errors", "pydantic.warnings", "__future__", "functools", "types", "typing", "warnings", "typing_extensions", "builtins", "pyexpat.model", "pyexpat.errors", "abc"], "hash": "af07cfd7ae718adcb7e9fa32d4d34283825ff52ba5e38b092d6f80e6cb1e6a12", "id": "pydantic.deprecated.class_validators", "ignore_all": true, "interface_hash": "88404fb3f320864fca682978db22c4d149988bfaadf2a73d5f743e85ced0c452", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\deprecated\\class_validators.py", "plugin_data": null, "size": 10245, "suppressed": [], "version_id": "1.8.0"}