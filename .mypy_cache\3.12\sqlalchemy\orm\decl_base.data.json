{".class": "MypyFile", "_fullname": "sqlalchemy.orm.decl_base", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "ClassManager": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.instrumentation.ClassManager", "kind": "Gdef"}, "Column": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Column", "kind": "Gdef"}, "ColumnProperty": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.properties.ColumnProperty", "kind": "Gdef"}, "CompositeProperty": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.descriptor_props.CompositeProperty", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "FromClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.FromClause", "kind": "Gdef"}, "InspectionAttr": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.InspectionAttr", "kind": "Gdef"}, "InstrumentedAttribute": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.attributes.InstrumentedAttribute", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Mapped": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.Mapped", "kind": "Gdef"}, "MappedClassProtocol": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2], ["__mapper__", 1], ["__name__", 1], ["__table__", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.decl_base.MappedClassProtocol", "name": "MappedClassProtocol", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.decl_base.MappedClassProtocol", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": ["is_abstract", "is_protocol"], "fullname": "sqlalchemy.orm.decl_base.MappedClassProtocol", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.orm.decl_base", "mro": ["sqlalchemy.orm.decl_base.MappedClassProtocol", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 4], "arg_names": ["self", "kw"], "dataclass_transform_spec": null, "flags": ["is_trivial_body"], "fullname": "sqlalchemy.orm.decl_base.MappedClassProtocol.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.decl_base.MappedClassProtocol", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.decl_base.MappedClassProtocol"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of MappedClassProtocol", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.decl_base.MappedClassProtocol", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__mapper__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_abstract_var", "is_ready"], "fullname": "sqlalchemy.orm.decl_base.MappedClassProtocol.__mapper__", "name": "__mapper__", "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.decl_base.MappedClassProtocol", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}}}, "__name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_abstract_var", "is_ready"], "fullname": "sqlalchemy.orm.decl_base.MappedClassProtocol.__name__", "name": "__name__", "type": "builtins.str"}}, "__table__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_abstract_var", "is_ready"], "fullname": "sqlalchemy.orm.decl_base.MappedClassProtocol.__table__", "name": "__table__", "type": "sqlalchemy.sql.selectable.FromClause"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.decl_base.MappedClassProtocol.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.decl_base.MappedClassProtocol", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.decl_base.MappedClassProtocol"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_O"], "typeddict_type": null}}, "MappedColumn": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.properties.MappedColumn", "kind": "Gdef"}, "Mapper": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.mapper.Mapper", "kind": "Gdef"}, "MapperProperty": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.interfaces.MapperProperty", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "MetaData": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.MetaData", "kind": "Gdef"}, "NamedColumn": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.NamedColumn", "kind": "Gdef"}, "NamedTuple": {".class": "SymbolTableNode", "cross_ref": "typing.NamedTuple", "kind": "Gdef"}, "NoReturn": {".class": "SymbolTableNode", "cross_ref": "typing.NoReturn", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Protocol", "kind": "Gdef"}, "QueryableAttribute": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.attributes.QueryableAttribute", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "SynonymProperty": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.descriptor_props.SynonymProperty", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Table": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Table", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypedDict", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_AnnotationScanType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.typing._AnnotationScanType", "kind": "Gdef"}, "_AttributeOptions": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.interfaces._AttributeOptions", "kind": "Gdef"}, "_ClassDict": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing._ClassDict", "kind": "Gdef"}, "_ClassScanMapperConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.decl_base._MapperConfig"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig", "name": "_ClassScanMapperConfig", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.decl_base", "mro": ["sqlalchemy.orm.decl_base._ClassScanMapperConfig", "sqlalchemy.orm.decl_base._MapperConfig", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "registry", "cls_", "dict_", "table", "mapper_kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "registry", "cls_", "dict_", "table", "mapper_kw"], "arg_types": ["sqlalchemy.orm.decl_base._ClassScanMapperConfig", "sqlalchemy.orm.decl_api.registry", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing._ClassDict"}, {".class": "UnionType", "items": ["sqlalchemy.sql.selectable.FromClause", {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.decl_base._MapperKwArgs"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _ClassScanMapperConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_apply_dataclasses_to_any_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "dataclass_setup_arguments", "klass", "use_annotations"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig._apply_dataclasses_to_any_class", "name": "_apply_dataclasses_to_any_class", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "dataclass_setup_arguments", "klass", "use_annotations"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.decl_base._ClassScanMapperConfig"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.decl_base._DataclassArguments"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}], "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_apply_dataclasses_to_any_class of _ClassScanMapperConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig._apply_dataclasses_to_any_class", "name": "_apply_dataclasses_to_any_class", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "dataclass_setup_arguments", "klass", "use_annotations"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.decl_base._ClassScanMapperConfig"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.decl_base._DataclassArguments"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}], "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_apply_dataclasses_to_any_class of _ClassScanMapperConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "_assert_dc_arguments": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "arguments"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig._assert_dc_arguments", "name": "_assert_dc_arguments", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "arguments"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.decl_base._ClassScanMapperConfig"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.decl_base._DataclassArguments"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_assert_dc_arguments of _ClassScanMapperConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig._assert_dc_arguments", "name": "_assert_dc_arguments", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "arguments"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.decl_base._ClassScanMapperConfig"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.decl_base._DataclassArguments"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_assert_dc_arguments of _ClassScanMapperConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_cls_attr_override_checker": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cls"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig._cls_attr_override_checker", "name": "_cls_attr_override_checker", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "cls"], "arg_types": ["sqlalchemy.orm.decl_base._ClassScanMapperConfig", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_cls_attr_override_checker of _ClassScanMapperConfig", "ret_type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "_cls_attr_resolver": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cls"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig._cls_attr_resolver", "name": "_cls_attr_resolver", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "cls"], "arg_types": ["sqlalchemy.orm.decl_base._ClassScanMapperConfig", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_cls_attr_resolver of _ClassScanMapperConfig", "ret_type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "typing.Iterable"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_collect_annotation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "name", "raw_annotation", "originating_class", "expect_mapped", "attr_value"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig._collect_annotation", "name": "_collect_annotation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "name", "raw_annotation", "originating_class", "expect_mapped", "attr_value"], "arg_types": ["sqlalchemy.orm.decl_base._ClassScanMapperConfig", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_collect_annotation of _ClassScanMapperConfig", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.decl_base._CollectedAnnotation"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_extract_declared_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig._extract_declared_columns", "name": "_extract_declared_columns", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.decl_base._ClassScanMapperConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_declared_columns of _ClassScanMapperConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_extract_mappable_attributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig._extract_mappable_attributes", "name": "_extract_mappable_attributes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.decl_base._ClassScanMapperConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_mappable_attributes of _ClassScanMapperConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_include_dunders": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig._include_dunders", "name": "_include_dunders", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}}}, "_match_exclude_dunders": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig._match_exclude_dunders", "name": "_match_exclude_dunders", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "<PERSON><PERSON>"}}}, "_metadata_for_cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "manager"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig._metadata_for_cls", "name": "_metadata_for_cls", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "manager"], "arg_types": ["sqlalchemy.orm.decl_base._ClassScanMapperConfig", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_metadata_for_cls of _ClassScanMapperConfig", "ret_type": "sqlalchemy.sql.schema.MetaData", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_prepare_mapper_arguments": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mapper_kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig._prepare_mapper_arguments", "name": "_prepare_mapper_arguments", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mapper_kw"], "arg_types": ["sqlalchemy.orm.decl_base._ClassScanMapperConfig", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.decl_base._MapperKwArgs"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prepare_mapper_arguments of _ClassScanMapperConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_produce_column_copies": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "attributes_for_class", "attribute_is_overridden", "fixed_table", "originating_class"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig._produce_column_copies", "name": "_produce_column_copies", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "attributes_for_class", "attribute_is_overridden", "fixed_table", "originating_class"], "arg_types": ["sqlalchemy.orm.decl_base._ClassScanMapperConfig", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "typing.Iterable"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}, "builtins.bool", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_produce_column_copies of _ClassScanMapperConfig", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.properties.MappedColumn"}]}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_scan_attributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig._scan_attributes", "name": "_scan_attributes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.decl_base._ClassScanMapperConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_scan_attributes of _ClassScanMapperConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_setup_dataclasses_transforms": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig._setup_dataclasses_transforms", "name": "_setup_dataclasses_transforms", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.decl_base._ClassScanMapperConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_setup_dataclasses_transforms of _ClassScanMapperConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_setup_declared_events": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig._setup_declared_events", "name": "_setup_declared_events", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.decl_base._ClassScanMapperConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_setup_declared_events of _ClassScanMapperConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_setup_inheriting_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mapper_kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig._setup_inheriting_columns", "name": "_setup_inheriting_columns", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mapper_kw"], "arg_types": ["sqlalchemy.orm.decl_base._ClassScanMapperConfig", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.decl_base._MapperKwArgs"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_setup_inheriting_columns of _ClassScanMapperConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_setup_inheriting_mapper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mapper_kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig._setup_inheriting_mapper", "name": "_setup_inheriting_mapper", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mapper_kw"], "arg_types": ["sqlalchemy.orm.decl_base._ClassScanMapperConfig", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.decl_base._MapperKwArgs"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_setup_inheriting_mapper of _ClassScanMapperConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_setup_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "table"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig._setup_table", "name": "_setup_table", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "table"], "arg_types": ["sqlalchemy.orm.decl_base._ClassScanMapperConfig", {".class": "UnionType", "items": ["sqlalchemy.sql.selectable.FromClause", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_setup_table of _ClassScanMapperConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_update_annotations_for_non_mapped_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "klass"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig._update_annotations_for_non_mapped_class", "name": "_update_annotations_for_non_mapped_class", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "klass"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.decl_base._ClassScanMapperConfig"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_update_annotations_for_non_mapped_class of _ClassScanMapperConfig", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}], "type_ref": "typing.Mapping"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig._update_annotations_for_non_mapped_class", "name": "_update_annotations_for_non_mapped_class", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "klass"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.decl_base._ClassScanMapperConfig"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_update_annotations_for_non_mapped_class of _ClassScanMapperConfig", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}], "type_ref": "typing.Mapping"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "_warn_for_decl_attributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "cls", "key", "c"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig._warn_for_decl_attributes", "name": "_warn_for_decl_attributes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "cls", "key", "c"], "arg_types": ["sqlalchemy.orm.decl_base._ClassScanMapperConfig", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_warn_for_decl_attributes of _ClassScanMapperConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "allow_dataclass_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig.allow_dataclass_fields", "name": "allow_dataclass_fields", "type": "builtins.bool"}}, "allow_unmapped_annotations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig.allow_unmapped_annotations", "name": "allow_unmapped_annotations", "type": "builtins.bool"}}, "clsdict_view": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig.clsdict_view", "name": "clsdict_view", "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing._ClassDict"}}}, "collected_annotations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig.collected_annotations", "name": "collected_annotations", "type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.decl_base._CollectedAnnotation"}], "type_ref": "builtins.dict"}}}, "collected_attributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig.collected_attributes", "name": "collected_attributes", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}}}, "column_copies": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig.column_copies", "name": "column_copies", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.properties.MappedColumn"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.schema.Column"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.properties.MappedColumn"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.schema.Column"}]}], "type_ref": "builtins.dict"}}}, "column_ordering": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig.column_ordering", "name": "column_ordering", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.schema.Column"}, "builtins.int"], "type_ref": "builtins.dict"}}}, "dataclass_setup_arguments": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig.dataclass_setup_arguments", "name": "dataclass_setup_arguments", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.decl_base._DataclassArguments"}, {".class": "NoneType"}]}}}, "declared_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig.declared_columns", "name": "declared_columns", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.schema.Column"}], "type_ref": "sqlalchemy.util._py_collections.OrderedSet"}}}, "inherits": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig.inherits", "name": "inherits", "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "NoneType"}]}}}, "is_dataclass_prior_to_mapping": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig.is_dataclass_prior_to_mapping", "name": "is_dataclass_prior_to_mapping", "type": "builtins.bool"}}, "is_deferred": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig.is_deferred", "name": "is_deferred", "type": "builtins.bool"}}, "local_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig.local_table", "name": "local_table", "type": {".class": "UnionType", "items": ["sqlalchemy.sql.selectable.FromClause", {".class": "NoneType"}]}}}, "map": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "mapper_kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig.map", "name": "map", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "mapper_kw"], "arg_types": ["sqlalchemy.orm.decl_base._ClassScanMapperConfig", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.decl_base._MapperKwArgs"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "map of _ClassScanMapperConfig", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "mapper_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig.mapper_args", "name": "mapper_args", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}}}, "mapper_args_fn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig.mapper_args_fn", "name": "mapper_args_fn", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}}}, "persist_selectable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig.persist_selectable", "name": "persist_selectable", "type": {".class": "UnionType", "items": ["sqlalchemy.sql.selectable.FromClause", {".class": "NoneType"}]}}}, "registry": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig.registry", "name": "registry", "type": "sqlalchemy.orm.decl_api.registry"}}, "single": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig.single", "name": "single", "type": "builtins.bool"}}, "table_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig.table_args", "name": "table_args", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}}}, "table_fn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig.table_fn", "name": "table_fn", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_table_fn", "ret_type": "sqlalchemy.sql.selectable.FromClause", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}}}, "tablename": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig.tablename", "name": "tablename", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.decl_base._ClassScanMapperConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.decl_base._ClassScanMapperConfig", "values": [], "variance": 0}, "slots": ["__weakref__", "allow_dataclass_fields", "allow_unmapped_annotations", "classname", "cls", "clsdict_view", "collected_annotations", "collected_attributes", "column_copies", "column_ordering", "dataclass_setup_arguments", "declared_attr_reg", "declared_columns", "inherits", "is_dataclass_prior_to_mapping", "local_table", "mapper_args", "mapper_args_fn", "persist_selectable", "properties", "registry", "single", "table_args", "table_fn", "tablename"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_CollectedAnnotation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.decl_base._CollectedAnnotation", "name": "_CollectedAnnotation", "type_vars": []}, "deletable_attributes": [], "flags": ["is_named_tuple"], "fullname": "sqlalchemy.orm.decl_base._CollectedAnnotation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["raw_annotation", "mapped_container", "extracted_mapped_annotation", "is_dataclass", "attr_value", "originating_module", "originating_class"]}}, "module_name": "sqlalchemy.orm.decl_base", "mro": ["sqlalchemy.orm.decl_base._CollectedAnnotation", "builtins.tuple", "typing.Sequence", "typing.Collection", "typing.Reversible", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.decl_base._CollectedAnnotation._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.base.Mapped"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, "builtins.str"]}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._CollectedAnnotation.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._CollectedAnnotation.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._CollectedAnnotation.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "raw_annotation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mapped_container"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "extracted_mapped_annotation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "is_dataclass"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "attr_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "originating_module"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "originating_class"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["_cls", "raw_annotation", "mapped_container", "extracted_mapped_annotation", "is_dataclass", "attr_value", "originating_module", "originating_class"], "dataclass_transform_spec": null, "flags": ["is_static"], "fullname": "sqlalchemy.orm.decl_base._CollectedAnnotation.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["_cls", "raw_annotation", "mapped_container", "extracted_mapped_annotation", "is_dataclass", "attr_value", "originating_module", "originating_class"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.decl_base._CollectedAnnotation._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.base.Mapped"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, "builtins.str"]}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.base.Mapped"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, "builtins.str"]}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of _CollectedAnnotation", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.decl_base._CollectedAnnotation._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.base.Mapped"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, "builtins.str"]}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.decl_base._CollectedAnnotation._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.base.Mapped"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, "builtins.str"]}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.decl_base._CollectedAnnotation._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.decl_base._CollectedAnnotation._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.base.Mapped"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, "builtins.str"]}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of _CollectedAnnotation", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.decl_base._CollectedAnnotation._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.base.Mapped"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, "builtins.str"]}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._CollectedAnnotation._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._CollectedAnnotation._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._CollectedAnnotation._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.decl_base._CollectedAnnotation._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.decl_base._CollectedAnnotation._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.base.Mapped"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, "builtins.str"]}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of _CollectedAnnotation", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.decl_base._CollectedAnnotation._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.base.Mapped"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, "builtins.str"]}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.decl_base._CollectedAnnotation._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.base.Mapped"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, "builtins.str"]}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._CollectedAnnotation._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.decl_base._CollectedAnnotation._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.base.Mapped"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, "builtins.str"]}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of _CollectedAnnotation", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.decl_base._CollectedAnnotation._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.base.Mapped"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, "builtins.str"]}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.decl_base._CollectedAnnotation._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.base.Mapped"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, "builtins.str"]}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_self", "raw_annotation", "mapped_container", "extracted_mapped_annotation", "is_dataclass", "attr_value", "originating_module", "originating_class"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.decl_base._CollectedAnnotation._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_self", "raw_annotation", "mapped_container", "extracted_mapped_annotation", "is_dataclass", "attr_value", "originating_module", "originating_class"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.decl_base._CollectedAnnotation._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.base.Mapped"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, "builtins.str"]}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.base.Mapped"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, "builtins.str"]}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of _CollectedAnnotation", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.decl_base._CollectedAnnotation._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.base.Mapped"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, "builtins.str"]}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.decl_base._CollectedAnnotation._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.base.Mapped"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, "builtins.str"]}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._CollectedAnnotation._source", "name": "_source", "type": "builtins.str"}}, "attr_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._CollectedAnnotation.attr_value", "name": "attr_value", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "attr_value-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.decl_base._CollectedAnnotation.attr_value", "kind": "<PERSON><PERSON><PERSON>"}, "extracted_mapped_annotation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._CollectedAnnotation.extracted_mapped_annotation", "name": "extracted_mapped_annotation", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, "builtins.str"]}}}, "extracted_mapped_annotation-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.decl_base._CollectedAnnotation.extracted_mapped_annotation", "kind": "<PERSON><PERSON><PERSON>"}, "is_dataclass": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._CollectedAnnotation.is_dataclass", "name": "is_dataclass", "type": "builtins.bool"}}, "is_dataclass-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.decl_base._CollectedAnnotation.is_dataclass", "kind": "<PERSON><PERSON><PERSON>"}, "mapped_container": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._CollectedAnnotation.mapped_container", "name": "mapped_container", "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.base.Mapped"}}, {".class": "NoneType"}]}}}, "mapped_container-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.decl_base._CollectedAnnotation.mapped_container", "kind": "<PERSON><PERSON><PERSON>"}, "originating_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._CollectedAnnotation.originating_class", "name": "originating_class", "type": {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "originating_class-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.decl_base._CollectedAnnotation.originating_class", "kind": "<PERSON><PERSON><PERSON>"}, "originating_module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._CollectedAnnotation.originating_module", "name": "originating_module", "type": "builtins.str"}}, "originating_module-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.decl_base._CollectedAnnotation.originating_module", "kind": "<PERSON><PERSON><PERSON>"}, "raw_annotation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._CollectedAnnotation.raw_annotation", "name": "raw_annotation", "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}}}, "raw_annotation-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.decl_base._CollectedAnnotation.raw_annotation", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.decl_base._CollectedAnnotation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.base.Mapped"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, "builtins.str"]}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "partial_fallback": "sqlalchemy.orm.decl_base._CollectedAnnotation"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.base.Mapped"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, "builtins.str"]}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "_DCAttributeOptions": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.interfaces._DCAttributeOptions", "kind": "Gdef"}, "_DataclassArguments": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.decl_base._DataclassArguments", "name": "_DataclassArguments", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.decl_base._DataclassArguments", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.decl_base", "mro": ["sqlalchemy.orm.decl_base._DataclassArguments", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["init", {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"]}], ["repr", {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"]}], ["eq", {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"]}], ["order", {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"]}], ["unsafe_hash", {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"]}], ["match_args", {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"]}], ["kw_only", {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"]}], ["dataclass_callable", {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, "type_guard": null, "unpack_kwargs": false, "variables": []}]}]], "required_keys": ["dataclass_callable", "eq", "init", "kw_only", "match_args", "order", "repr", "unsafe_hash"]}}}, "_DeclMappedClassProtocol": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2], ["__declare_first__", 2], ["__declare_last__", 2], ["__mapper__", 1], ["__mapper_args__", 1], ["__name__", 1], ["__table__", 1], ["__table_args__", 1], ["__tablename__", 1], ["_sa_apply_dc_transforms", 1], ["metadata", 1]], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.decl_base._DeclMappedClassProtocol", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.decl_base.MappedClassProtocol"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.decl_base._DeclMappedClassProtocol", "name": "_DeclMappedClassProtocol", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.decl_base._DeclMappedClassProtocol", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": ["is_abstract", "is_protocol"], "fullname": "sqlalchemy.orm.decl_base._DeclMappedClassProtocol", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.orm.decl_base", "mro": ["sqlalchemy.orm.decl_base._DeclMappedClassProtocol", "sqlalchemy.orm.decl_base.MappedClassProtocol", "builtins.object"], "names": {".class": "SymbolTable", "__declare_first__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_trivial_body"], "fullname": "sqlalchemy.orm.decl_base._DeclMappedClassProtocol.__declare_first__", "name": "__declare_first__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.decl_base._DeclMappedClassProtocol", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.decl_base._DeclMappedClassProtocol"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__declare_first__ of _DeclMappedClassProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__declare_last__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_trivial_body"], "fullname": "sqlalchemy.orm.decl_base._DeclMappedClassProtocol.__declare_last__", "name": "__declare_last__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.decl_base._DeclMappedClassProtocol", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.decl_base._DeclMappedClassProtocol"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__declare_last__ of _DeclMappedClassProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__mapper_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_abstract_var", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._DeclMappedClassProtocol.__mapper_args__", "name": "__mapper_args__", "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.decl_base._MapperKwArgs"}}}, "__table_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_abstract_var", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._DeclMappedClassProtocol.__table_args__", "name": "__table_args__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}}}, "__tablename__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_abstract_var", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._DeclMappedClassProtocol.__tablename__", "name": "__tablename__", "type": "builtins.str"}}, "_sa_apply_dc_transforms": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_abstract_var", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._DeclMappedClassProtocol._sa_apply_dc_transforms", "name": "_sa_apply_dc_transforms", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.decl_base._DataclassArguments"}, {".class": "NoneType"}]}}}, "metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_abstract_var", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._DeclMappedClassProtocol.metadata", "name": "metadata", "type": "sqlalchemy.sql.schema.MetaData"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.decl_base._DeclMappedClassProtocol.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.decl_base._DeclMappedClassProtocol", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.decl_base._DeclMappedClassProtocol"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_O"], "typeddict_type": null}}, "_DeferredMapperConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.decl_base._ClassScanMapperConfig"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.decl_base._DeferredMapperConfig", "name": "_DeferredMapperConfig", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.decl_base._DeferredMapperConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.decl_base", "mro": ["sqlalchemy.orm.decl_base._DeferredMapperConfig", "sqlalchemy.orm.decl_base._ClassScanMapperConfig", "sqlalchemy.orm.decl_base._MapperConfig", "builtins.object"], "names": {".class": "SymbolTable", "_cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._DeferredMapperConfig._cls", "name": "_cls", "type": {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "type_ref": "_weakref.ReferenceType"}}}, "_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.orm.decl_base._DeferredMapperConfig._configs", "name": "_configs", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "type_ref": "_weakref.ReferenceType"}, "sqlalchemy.orm.decl_base._DeferredMapperConfig"], "type_ref": "builtins.dict"}}}, "_early_mapping": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mapper_kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.decl_base._DeferredMapperConfig._early_mapping", "name": "_early_mapping", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mapper_kw"], "arg_types": ["sqlalchemy.orm.decl_base._DeferredMapperConfig", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.decl_base._MapperKwArgs"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_early_mapping of _DeferredMapperConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_remove_config_cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "ref"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.decl_base._DeferredMapperConfig._remove_config_cls", "name": "_remove_config_cls", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "ref"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.decl_base._DeferredMapperConfig"}, {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "type_ref": "_weakref.ReferenceType"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_remove_config_cls of _DeferredMapperConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.decl_base._DeferredMapperConfig._remove_config_cls", "name": "_remove_config_cls", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "ref"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.decl_base._DeferredMapperConfig"}, {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "type_ref": "_weakref.ReferenceType"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_remove_config_cls of _DeferredMapperConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "classes_for_base": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cls", "base_cls", "sort"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.decl_base._DeferredMapperConfig.classes_for_base", "name": "classes_for_base", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "base_cls", "sort"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.decl_base._DeferredMapperConfig"}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "classes_for_base of _DeferredMapperConfig", "ret_type": {".class": "Instance", "args": ["sqlalchemy.orm.decl_base._DeferredMapperConfig"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.decl_base._DeferredMapperConfig.classes_for_base", "name": "classes_for_base", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "base_cls", "sort"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.decl_base._DeferredMapperConfig"}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "classes_for_base of _DeferredMapperConfig", "ret_type": {".class": "Instance", "args": ["sqlalchemy.orm.decl_base._DeferredMapperConfig"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": ["is_property"], "fullname": "sqlalchemy.orm.decl_base._DeferredMapperConfig.cls", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "sqlalchemy.orm.decl_base._DeferredMapperConfig.cls", "name": "cls", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.decl_base._DeferredMapperConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cls of _DeferredMapperConfig", "ret_type": {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.decl_base._DeferredMapperConfig.cls", "name": "cls", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.decl_base._DeferredMapperConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cls of _DeferredMapperConfig", "ret_type": {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "class_"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.decl_base._DeferredMapperConfig.cls", "name": "cls", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "class_"], "arg_types": ["sqlalchemy.orm.decl_base._DeferredMapperConfig", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cls of _DeferredMapperConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "cls", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.decl_base._DeferredMapperConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cls of _DeferredMapperConfig", "ret_type": {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, "type_guard": null, "unpack_kwargs": false, "variables": []}]}}}, "config_for_cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "class_"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.decl_base._DeferredMapperConfig.config_for_cls", "name": "config_for_cls", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "class_"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.decl_base._DeferredMapperConfig"}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "config_for_cls of _DeferredMapperConfig", "ret_type": "sqlalchemy.orm.decl_base._DeferredMapperConfig", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.decl_base._DeferredMapperConfig.config_for_cls", "name": "config_for_cls", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "class_"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.decl_base._DeferredMapperConfig"}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "config_for_cls of _DeferredMapperConfig", "ret_type": "sqlalchemy.orm.decl_base._DeferredMapperConfig", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "has_cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "class_"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.decl_base._DeferredMapperConfig.has_cls", "name": "has_cls", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "class_"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.decl_base._DeferredMapperConfig"}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_cls of _DeferredMapperConfig", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.decl_base._DeferredMapperConfig.has_cls", "name": "has_cls", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "class_"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.decl_base._DeferredMapperConfig"}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_cls of _DeferredMapperConfig", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "is_deferred": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.decl_base._DeferredMapperConfig.is_deferred", "name": "is_deferred", "type": "builtins.bool"}}, "map": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "mapper_kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.decl_base._DeferredMapperConfig.map", "name": "map", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "mapper_kw"], "arg_types": ["sqlalchemy.orm.decl_base._DeferredMapperConfig", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.decl_base._MapperKwArgs"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "map of _DeferredMapperConfig", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "raise_unmapped_for_cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "class_"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.decl_base._DeferredMapperConfig.raise_unmapped_for_cls", "name": "raise_unmapped_for_cls", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "class_"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.decl_base._DeferredMapperConfig"}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "raise_unmapped_for_cls of _DeferredMapperConfig", "ret_type": {".class": "UninhabitedType", "is_noreturn": true}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.decl_base._DeferredMapperConfig.raise_unmapped_for_cls", "name": "raise_unmapped_for_cls", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "class_"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.decl_base._DeferredMapperConfig"}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "raise_unmapped_for_cls of _DeferredMapperConfig", "ret_type": {".class": "UninhabitedType", "is_noreturn": true}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.decl_base._DeferredMapperConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.decl_base._DeferredMapperConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ImperativeMapperConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.decl_base._MapperConfig"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.decl_base._ImperativeMapperConfig", "name": "_ImperativeMapperConfig", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.decl_base._ImperativeMapperConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.decl_base", "mro": ["sqlalchemy.orm.decl_base._ImperativeMapperConfig", "sqlalchemy.orm.decl_base._MapperConfig", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "registry", "cls_", "table", "mapper_kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.decl_base._ImperativeMapperConfig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "registry", "cls_", "table", "mapper_kw"], "arg_types": ["sqlalchemy.orm.decl_base._ImperativeMapperConfig", "sqlalchemy.orm.decl_api.registry", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["sqlalchemy.sql.selectable.FromClause", {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.decl_base._MapperKwArgs"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _ImperativeMapperConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.decl_base._ImperativeMapperConfig.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_setup_inheritance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mapper_kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.decl_base._ImperativeMapperConfig._setup_inheritance", "name": "_setup_inheritance", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mapper_kw"], "arg_types": ["sqlalchemy.orm.decl_base._ImperativeMapperConfig", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.decl_base._MapperKwArgs"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_setup_inheritance of _ImperativeMapperConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "inherits": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.decl_base._ImperativeMapperConfig.inherits", "name": "inherits", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}, "local_table": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.decl_base._ImperativeMapperConfig.local_table", "name": "local_table", "type": {".class": "UnionType", "items": ["sqlalchemy.sql.selectable.FromClause", {".class": "NoneType"}]}}}, "map": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "mapper_kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.decl_base._ImperativeMapperConfig.map", "name": "map", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "mapper_kw"], "arg_types": ["sqlalchemy.orm.decl_base._ImperativeMapperConfig", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.decl_base._MapperKwArgs"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "map of _ImperativeMapperConfig", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.decl_base._ImperativeMapperConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.decl_base._ImperativeMapperConfig", "values": [], "variance": 0}, "slots": ["__weakref__", "classname", "cls", "declared_attr_reg", "inherits", "local_table", "properties"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_IntrospectsAnnotations": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.interfaces._IntrospectsAnnotations", "kind": "Gdef"}, "_MappedAttribute": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base._MappedAttribute", "kind": "Gdef"}, "_MapperConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.decl_base._MapperConfig", "name": "_MapperConfig", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.decl_base._MapperConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.decl_base", "mro": ["sqlalchemy.orm.decl_base._MapperConfig", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "registry", "cls_", "mapper_kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.decl_base._MapperConfig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "registry", "cls_", "mapper_kw"], "arg_types": ["sqlalchemy.orm.decl_base._MapperConfig", "sqlalchemy.orm.decl_api.registry", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.decl_base._MapperKwArgs"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _MapperConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.decl_base._MapperConfig.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_early_mapping": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mapper_kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.decl_base._MapperConfig._early_mapping", "name": "_early_mapping", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mapper_kw"], "arg_types": ["sqlalchemy.orm.decl_base._MapperConfig", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.decl_base._MapperKwArgs"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_early_mapping of _MapperConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "classname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._MapperConfig.classname", "name": "classname", "type": "builtins.str"}}, "cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._MapperConfig.cls", "name": "cls", "type": {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "declared_attr_reg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._MapperConfig.declared_attr_reg", "name": "declared_attr_reg", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.decl_api.declared_attr"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}}}, "map": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "mapper_kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.decl_base._MapperConfig.map", "name": "map", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "mapper_kw"], "arg_types": ["sqlalchemy.orm.decl_base._MapperConfig", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.decl_base._MapperKwArgs"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "map of _MapperConfig", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "properties": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.decl_base._MapperConfig.properties", "name": "properties", "type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.elements.NamedColumn"}], "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.elements.NamedColumn"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}]}], "type_ref": "builtins.dict"}}}, "set_cls_attribute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "attrname", "value"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.decl_base._MapperConfig.set_cls_attribute", "name": "set_cls_attribute", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "attrname", "value"], "arg_types": ["sqlalchemy.orm.decl_base._MapperConfig", "builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.decl_base._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_cls_attribute of _MapperConfig", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.decl_base._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.decl_base._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}, "setup_mapping": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["cls", "registry", "cls_", "dict_", "table", "mapper_kw"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.decl_base._MapperConfig.setup_mapping", "name": "setup_mapping", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["cls", "registry", "cls_", "dict_", "table", "mapper_kw"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.decl_base._MapperConfig"}, "sqlalchemy.orm.decl_api.registry", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing._ClassDict"}, {".class": "UnionType", "items": ["sqlalchemy.sql.selectable.FromClause", {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.decl_base._MapperKwArgs"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setup_mapping of _MapperConfig", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.orm.decl_base._MapperConfig", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.decl_base._MapperConfig.setup_mapping", "name": "setup_mapping", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["cls", "registry", "cls_", "dict_", "table", "mapper_kw"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.decl_base._MapperConfig"}, "sqlalchemy.orm.decl_api.registry", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing._ClassDict"}, {".class": "UnionType", "items": ["sqlalchemy.sql.selectable.FromClause", {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.decl_base._MapperKwArgs"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setup_mapping of _MapperConfig", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.orm.decl_base._MapperConfig", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.decl_base._MapperConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.decl_base._MapperConfig", "values": [], "variance": 0}, "slots": ["__weakref__", "classname", "cls", "declared_attr_reg", "properties"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_MapperKwArgs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.orm.decl_base._MapperKwArgs", "line": 86, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.Mapping"}}}, "_MapsColumns": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.interfaces._MapsColumns", "kind": "Gdef"}, "_NoArg": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base._NoArg", "kind": "Gdef"}, "_O": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing._O", "kind": "Gdef"}, "_RegistryType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing._RegistryType", "kind": "Gdef"}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.decl_base._T", "name": "_T", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, "_TableArgsType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.orm.decl_base._TableArgsType", "line": 87, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}]}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.decl_base.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.decl_base.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.decl_base.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.decl_base.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.decl_base.__package__", "name": "__package__", "type": "builtins.str"}}, "_add_attribute": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "key", "value"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.decl_base._add_attribute", "name": "_add_attribute", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "key", "value"], "arg_types": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_attribute", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_as_dc_declaredattr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["field_metadata", "sa_dataclass_metadata_key"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.decl_base._as_dc_declaredattr", "name": "_as_dc_declaredattr", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["field_metadata", "sa_dataclass_metadata_key"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_as_dc_declaredattr", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.decl_base._as_dc_declaredattr", "name": "_as_dc_declaredattr", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["field_metadata", "sa_dataclass_metadata_key"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_as_dc_declaredattr", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_as_declarative": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["registry", "cls", "dict_"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.decl_base._as_declarative", "name": "_as_declarative", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["registry", "cls", "dict_"], "arg_types": ["sqlalchemy.orm.decl_api.registry", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing._ClassDict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_as_declarative", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.orm.decl_base._MapperConfig", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_check_declared_props_nocascade": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["obj", "name", "cls"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.decl_base._check_declared_props_nocascade", "name": "_check_declared_props_nocascade", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["obj", "name", "cls"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_declared_props_nocascade", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "_declarative_constructor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.decl_base._declarative_constructor", "name": "_declarative_constructor", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_declarative_constructor", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_declared_mapping_info": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.decl_base._declared_mapping_info", "name": "_declared_mapping_info", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_declared_mapping_info", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.orm.decl_base._DeferredMapperConfig", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_del_attribute": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "key"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.decl_base._del_attribute", "name": "_del_attribute", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "key"], "arg_types": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_del_attribute", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_dive_for_cls_manager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.decl_base._dive_for_cls_manager", "name": "_dive_for_cls_manager", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_dive_for_cls_manager", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "_extract_mapped_subtype": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.util._extract_mapped_subtype", "kind": "Gdef"}, "_get_immediate_cls_attr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cls", "attrname", "strict"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.decl_base._get_immediate_cls_attr", "name": "_get_immediate_cls_attr", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "attrname", "strict"], "arg_types": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_immediate_cls_attr", "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_is_declarative_props": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.decl_base._is_declarative_props", "name": "_is_declarative_props", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_declarative_props", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.decl_base._is_declarative_props", "name": "_is_declarative_props", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_declarative_props", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_is_mapped_annotation": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.util._is_mapped_annotation", "kind": "Gdef"}, "_is_mapped_class": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base._is_mapped_class", "kind": "Gdef"}, "_is_supercls_for_inherits": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.decl_base._is_supercls_for_inherits", "name": "_is_supercls_for_inherits", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_supercls_for_inherits", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_mapper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["registry", "cls", "table", "mapper_kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.decl_base._mapper", "name": "_mapper", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["registry", "cls", "table", "mapper_kw"], "arg_types": ["sqlalchemy.orm.decl_api.registry", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["sqlalchemy.sql.selectable.FromClause", {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.decl_base._MapperKwArgs"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_mapper", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "_resolve_for_abstract_or_classical": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.decl_base._resolve_for_abstract_or_classical", "name": "_resolve_for_abstract_or_classical", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_resolve_for_abstract_or_classical", "ret_type": {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_undefer_column_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["key", "column"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.decl_base._undefer_column_name", "name": "_undefer_column_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["key", "column"], "arg_types": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.schema.Column"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_undefer_column_name", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "attr_is_internal_proxy": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing.attr_is_internal_proxy", "kind": "Gdef"}, "attributes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.attributes", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "class_mapper": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.class_mapper", "kind": "Gdef"}, "clsregistry": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.clsregistry", "kind": "Gdef"}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef"}, "dataclasses": {".class": "SymbolTableNode", "cross_ref": "dataclasses", "kind": "Gdef"}, "de_stringify_annotation": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.util.de_stringify_annotation", "kind": "Gdef"}, "declared_attr": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.decl_api.declared_attr", "kind": "Gdef"}, "event": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event", "kind": "Gdef"}, "exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc", "kind": "Gdef"}, "expression": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.expression", "kind": "Gdef"}, "get_args": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.get_args", "kind": "Gdef"}, "instrumentation": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.instrumentation", "kind": "Gdef"}, "is_fwd_ref": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.typing.is_fwd_ref", "kind": "Gdef"}, "is_literal": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.typing.is_literal", "kind": "Gdef"}, "mapperlib": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.mapper", "kind": "Gdef"}, "orm_exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.exc", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "topological": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.topological", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}, "weakref": {".class": "SymbolTableNode", "cross_ref": "weakref", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\decl_base.py"}