{"data_mtime": 1759504552, "dep_lines": [9, 9, 7, 8, 11, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 20, 10, 5, 10, 5, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["xml.parsers.expat", "xml.parsers", "json", "typing", "xmltodict", "aws_lambda_powertools", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "_typeshed", "abc", "aws_lambda_powertools.logging", "aws_lambda_powertools.logging.logger", "collections", "json.decoder", "logging", "types"], "hash": "a25106747a17f27c60fe8dfabdc010eb59ae09a8a0f669537db6337033fd05d2", "id": "smartanalytics_utilities.utils.xml_helper", "ignore_all": false, "interface_hash": "4c4d0ad5a23f3625fd8052cc336c23213492ed0e68dcbf7868fc3a25ff2d2c12", "mtime": 1759501803, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "layers\\utilities\\python\\smartanalytics_utilities\\utils\\xml_helper.py", "plugin_data": null, "size": 6548, "suppressed": [], "version_id": "1.8.0"}