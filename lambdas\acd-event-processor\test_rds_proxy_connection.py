#!/usr/bin/env python3
"""Test script for RDS Proxy connection with IAM authentication."""

import asyncio
import os
import sys
from pathlib import Path

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("⚠️  python-dotenv not installed. Install with: pip install python-dotenv")

# Add layers to path for local testing
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "layers" / "utilities" / "python"))
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "layers" / "domain" / "python"))
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "layers" / "infrastructure" / "python"))

from smartanalytics_utilities.config.database_config import DatabaseConfig
from smartanalytics_infrastructure.database.connection import DatabaseConnection


async def test_password_connection():
    """Test connection with password authentication."""
    print("🔐 Testing password authentication...")

    password = os.getenv("DATABASE_PASSWORD")
    if not password or password == "your-password-here":
        print("❌ No valid password provided. Set DATABASE_PASSWORD in .env file.")
        print("   Current value:", password or "None")
        return False

    # Set authentication mode for password auth
    os.environ["DATABASE_USE_IAM_AUTH"] = "false"

    print("✅ Using password authentication")
    print(f"   Host: {os.getenv('DATABASE_HOST', 'localhost')}")
    print(f"   Port: {os.getenv('DATABASE_PORT', '5432')}")
    print(f"   Database: {os.getenv('DATABASE_DATABASE_NAME', 'postgres')}")
    print(f"   Username: {os.getenv('DATABASE_USERNAME', 'postgres')}")
    print(f"   SSL Mode: {os.getenv('DATABASE_SSL_MODE', 'require')}")

    config = DatabaseConfig()
    connection = DatabaseConnection(config)
    
    try:
        success = await connection.test_connection()
        if success:
            print("✅ Password authentication successful!")
            return True
        else:
            print("❌ Password authentication failed!")
            return False
    except Exception as e:
        print(f"❌ Password authentication error: {e}")
        return False
    finally:
        await connection.close()


async def test_iam_connection():
    """Test connection with IAM authentication."""
    print("🔑 Testing IAM authentication...")
    
    # Set authentication mode for IAM
    os.environ["DATABASE_USE_IAM_AUTH"] = "true"

    print("✅ Using IAM authentication")
    print(f"   Host: {os.getenv('DATABASE_HOST', 'localhost')}")
    print(f"   Port: {os.getenv('DATABASE_PORT', '5432')}")
    print(f"   Database: {os.getenv('DATABASE_DATABASE_NAME', 'postgres')}")
    print(f"   Username: {os.getenv('DATABASE_USERNAME', 'postgres')}")
    print(f"   SSL Mode: {os.getenv('DATABASE_SSL_MODE', 'require')}")
    print(f"   AWS Region: {os.getenv('AWS_REGION', 'us-east-1')}")

    config = DatabaseConfig()
    
    connection = DatabaseConnection(config)
    
    try:
        success = await connection.test_connection()
        if success:
            print("✅ IAM authentication successful!")
            return True
        else:
            print("❌ IAM authentication failed!")
            return False
    except Exception as e:
        print(f"❌ IAM authentication error: {e}")
        return False
    finally:
        await connection.close()


async def main():
    """Main test function."""
    print("🚀 Testing RDS Proxy connections...\n")
    
    # Test password authentication first
    password_success = await test_password_connection()
    print()
    
    # Test IAM authentication
    iam_success = await test_iam_connection()
    print()
    
    # Summary
    print("📊 Test Results:")
    print(f"   Password Auth: {'✅ PASS' if password_success else '❌ FAIL'}")
    print(f"   IAM Auth:      {'✅ PASS' if iam_success else '❌ FAIL'}")
    
    if password_success or iam_success:
        print("\n🎉 At least one authentication method works!")
        return 0
    else:
        print("\n💥 Both authentication methods failed!")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
