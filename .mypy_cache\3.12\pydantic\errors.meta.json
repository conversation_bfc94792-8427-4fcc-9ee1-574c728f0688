{"data_mtime": 1759503836, "dep_lines": [11, 9, 11, 13, 14, 3, 5, 6, 8, 1, 1, 1, 1], "dep_prios": [10, 5, 20, 5, 5, 5, 10, 5, 5, 5, 20, 20, 30], "dependencies": ["pydantic._internal._repr", "typing_inspection.introspection", "pydantic._internal", "pydantic._migration", "pydantic.version", "__future__", "re", "typing", "typing_extensions", "builtins", "pyexpat.model", "pyexpat.errors", "abc"], "hash": "71908ee7ba5829bd1d430f4c7d3bfc63e41d2bb8fbf68b665ec9640a0b6a749b", "id": "pydantic.errors", "ignore_all": true, "interface_hash": "b16a045b6abb51d99884ed5eeb0d81161a8f3f5bc74f82ea0243cb2be41124cd", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\errors.py", "plugin_data": null, "size": 6034, "suppressed": [], "version_id": "1.8.0"}