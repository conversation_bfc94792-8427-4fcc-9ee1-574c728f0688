{"data_mtime": 1759503870, "dep_lines": [18, 19, 18, 20, 21, 22, 23, 24, 25, 27, 28, 31, 34, 35, 20, 8, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 20, 10, 10, 10, 10, 10, 5, 5, 5, 25, 25, 25, 20, 5, 5, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.dialects.postgresql.types", "sqlalchemy.dialects.postgresql.array", "sqlalchemy.dialects.postgresql", "sqlalchemy.sql.coercions", "sqlalchemy.sql.elements", "sqlalchemy.sql.expression", "sqlalchemy.sql.functions", "sqlalchemy.sql.roles", "sqlalchemy.sql.schema", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.visitors", "sqlalchemy.sql._typing", "sqlalchemy.sql.operators", "sqlalchemy.sql.selectable", "sqlalchemy.sql", "__future__", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "abc", "enum", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.util", "sqlalchemy.util.langhelpers", "types"], "hash": "d4f3571a422f3d83ee555ba3a4aae8ef7b3c0e26ae5ed8e219d57a360bb89385", "id": "sqlalchemy.dialects.postgresql.ext", "ignore_all": true, "interface_hash": "47d404f21be769a38176f89d00430ff69c8156a2858363d0840c69c4fd0ea722", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\ext.py", "plugin_data": null, "size": 17883, "suppressed": [], "version_id": "1.8.0"}