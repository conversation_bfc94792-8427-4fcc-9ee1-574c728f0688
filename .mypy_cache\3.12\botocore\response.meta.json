{"data_mtime": 1759504549, "dep_lines": [8, 13, 7, 9, 10, 12, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 10, 5, 20, 20, 30, 30], "dependencies": ["collections.abc", "botocore.model", "logging", "io", "typing", "requests", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "requests.models"], "hash": "05fa2db2e2cb3708bc1c6fec4bcbec2c32265af513c429b9c1fb6a892321ef6f", "id": "botocore.response", "ignore_all": true, "interface_hash": "dcd71fe79fd3345cb23d866946a201496167edd75638cb379ab8547873effa3d", "mtime": 1757092235, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\botocore-stubs\\response.pyi", "plugin_data": null, "size": 1170, "suppressed": [], "version_id": "1.8.0"}