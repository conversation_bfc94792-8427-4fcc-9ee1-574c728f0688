{".class": "MypyFile", "_fullname": "sqlalchemy.dialects.postgresql.hstore", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ARRAY": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.array.ARRAY", "kind": "Gdef", "module_public": false}, "CONTAINED_BY": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.operators.CONTAINED_BY", "kind": "Gdef", "module_public": false}, "CONTAINS": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.operators.CONTAINS", "kind": "Gdef", "module_public": false}, "GETITEM": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.operators.GETITEM", "kind": "Gdef", "module_public": false}, "HAS_ALL": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.operators.HAS_ALL", "kind": "Gdef", "module_public": false}, "HAS_ANY": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.operators.HAS_ANY", "kind": "Gdef", "module_public": false}, "HAS_KEY": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.operators.HAS_KEY", "kind": "Gdef", "module_public": false}, "HSTORE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.sqltypes.Indexable", "sqlalchemy.sql.sqltypes.Concatenable", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.hstore.HSTORE", "name": "HSTORE", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.hstore.HSTORE", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.hstore", "mro": ["sqlalchemy.dialects.postgresql.hstore.HSTORE", "sqlalchemy.sql.sqltypes.Indexable", "sqlalchemy.sql.sqltypes.Concatenable", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "Comparator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.sql.sqltypes.Indexable.Comparator"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.sql.sqltypes.Concatenable.Comparator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.hstore.HSTORE.Comparator", "name": "Comparator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.hstore.HSTORE.Comparator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.hstore", "mro": ["sqlalchemy.dialects.postgresql.hstore.HSTORE.Comparator", "sqlalchemy.sql.sqltypes.Indexable.Comparator", "sqlalchemy.sql.sqltypes.Concatenable.Comparator", "sqlalchemy.sql.type_api.TypeEngine.Comparator", "sqlalchemy.sql.operators.ColumnOperators", "sqlalchemy.sql.operators.Operators", "builtins.object"], "names": {".class": "SymbolTable", "_setup_getitem": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "index"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.hstore.HSTORE.Comparator._setup_getitem", "name": "_setup_getitem", "type": null}}, "array": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.hstore.HSTORE.Comparator.array", "name": "array", "type": null}}, "contained_by": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.hstore.HSTORE.Comparator.contained_by", "name": "contained_by", "type": null}}, "contains": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "other", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.hstore.HSTORE.Comparator.contains", "name": "contains", "type": null}}, "defined": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.hstore.HSTORE.Comparator.defined", "name": "defined", "type": null}}, "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.hstore.HSTORE.Comparator.delete", "name": "delete", "type": null}}, "has_all": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.hstore.HSTORE.Comparator.has_all", "name": "has_all", "type": null}}, "has_any": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.hstore.HSTORE.Comparator.has_any", "name": "has_any", "type": null}}, "has_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.hstore.HSTORE.Comparator.has_key", "name": "has_key", "type": null}}, "keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.hstore.HSTORE.Comparator.keys", "name": "keys", "type": null}}, "matrix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.hstore.HSTORE.Comparator.matrix", "name": "matrix", "type": null}}, "slice": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "array"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.hstore.HSTORE.Comparator.slice", "name": "slice", "type": null}}, "vals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.hstore.HSTORE.Comparator.vals", "name": "vals", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.hstore.HSTORE.Comparator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.hstore.HSTORE.Comparator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "text_type"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.hstore.HSTORE.__init__", "name": "__init__", "type": null}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.hstore.HSTORE.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "bind_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dialect"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.hstore.HSTORE.bind_processor", "name": "bind_processor", "type": null}}, "comparator_factory": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.hstore.HSTORE.comparator_factory", "name": "comparator_factory", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["expr"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "bound_args": ["sqlalchemy.dialects.postgresql.hstore.HSTORE.Comparator"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.dialects.postgresql.hstore.HSTORE.Comparator", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "hashable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.hstore.HSTORE.hashable", "name": "<PERSON><PERSON>le", "type": "builtins.bool"}}, "result_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dialect", "coltype"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.hstore.HSTORE.result_processor", "name": "result_processor", "type": null}}, "text_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.hstore.HSTORE.text_type", "name": "text_type", "type": "sqlalchemy.sql.sqltypes.Text"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.hstore.HSTORE.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.hstore.HSTORE", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HSTORE_DELIMITER_RE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.hstore.HSTORE_DELIMITER_RE", "name": "HSTORE_DELIMITER_RE", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "<PERSON><PERSON>"}}}, "HSTORE_PAIR_RE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.hstore.HSTORE_PAIR_RE", "name": "HSTORE_PAIR_RE", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "<PERSON><PERSON>"}}}, "_HStoreArrayFunction": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.sql.functions.GenericFunction"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.hstore._HStoreArrayFunction", "name": "_HStoreArrayFunction", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.hstore._HStoreArrayFunction", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.hstore", "mro": ["sqlalchemy.dialects.postgresql.hstore._HStoreArrayFunction", "sqlalchemy.sql.functions.GenericFunction", "sqlalchemy.sql.functions.Function", "sqlalchemy.sql.functions.FunctionElement", "sqlalchemy.sql.base.Executable", "sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.elements.ColumnElement", "sqlalchemy.sql.roles.ColumnArgumentOrKeyRole", "sqlalchemy.sql.roles.ColumnArgumentRole", "sqlalchemy.sql.roles.StatementOptionRole", "sqlalchemy.sql.roles.WhereHavingRole", "sqlalchemy.sql.roles.OnClauseRole", "sqlalchemy.sql.roles.BinaryElementRole", "sqlalchemy.sql.roles.OrderByRole", "sqlalchemy.sql.selectable.FromClause", "sqlalchemy.sql.roles.AnonymizedFromClauseRole", "sqlalchemy.sql.roles.StrictFromClauseRole", "sqlalchemy.sql.roles.FromClauseRole", "sqlalchemy.sql.roles.ColumnsClauseRole", "sqlalchemy.sql.roles.JoinTargetRole", "sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.roles.ByOfRole", "sqlalchemy.sql.roles.UsesInspection", "sqlalchemy.sql.roles.ColumnListRole", "sqlalchemy.sql.roles.LimitOffsetRole", "sqlalchemy.sql.roles.DMLColumnRole", "sqlalchemy.sql.roles.DDLConstraintColumnRole", "sqlalchemy.sql.roles.DDLExpressionRole", "sqlalchemy.sql.roles.StructuralRole", "sqlalchemy.sql.elements.SQLColumnExpression", "sqlalchemy.sql.elements.SQLCoreOperations", "sqlalchemy.sql.operators.ColumnOperators", "sqlalchemy.sql.operators.Operators", "sqlalchemy.sql.roles.ExpressionElementRole", "sqlalchemy.util.langhelpers.TypingOnly", "sqlalchemy.sql.roles.TypedColumnsClauseRole", "sqlalchemy.sql.selectable.Selectable", "sqlalchemy.sql.selectable.ReturnsRows", "sqlalchemy.sql.roles.ReturnsRowsRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.elements.DQLDMLClauseElement", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.sql.base.Generative", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "inherit_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.hstore._HStoreArrayFunction.inherit_cache", "name": "inherit_cache", "type": "builtins.bool"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.hstore._HStoreArrayFunction.name", "name": "name", "type": "builtins.str"}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.hstore._HStoreArrayFunction.type", "name": "type", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_of_any": 7}], "type_ref": "sqlalchemy.dialects.postgresql.array.ARRAY"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.hstore._HStoreArrayFunction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.hstore._HStoreArrayFunction", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_HStoreDefinedFunction": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.sql.functions.GenericFunction"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.hstore._HStoreDefinedFunction", "name": "_HStoreDefinedFunction", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.hstore._HStoreDefinedFunction", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.hstore", "mro": ["sqlalchemy.dialects.postgresql.hstore._HStoreDefinedFunction", "sqlalchemy.sql.functions.GenericFunction", "sqlalchemy.sql.functions.Function", "sqlalchemy.sql.functions.FunctionElement", "sqlalchemy.sql.base.Executable", "sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.elements.ColumnElement", "sqlalchemy.sql.roles.ColumnArgumentOrKeyRole", "sqlalchemy.sql.roles.ColumnArgumentRole", "sqlalchemy.sql.roles.StatementOptionRole", "sqlalchemy.sql.roles.WhereHavingRole", "sqlalchemy.sql.roles.OnClauseRole", "sqlalchemy.sql.roles.BinaryElementRole", "sqlalchemy.sql.roles.OrderByRole", "sqlalchemy.sql.selectable.FromClause", "sqlalchemy.sql.roles.AnonymizedFromClauseRole", "sqlalchemy.sql.roles.StrictFromClauseRole", "sqlalchemy.sql.roles.FromClauseRole", "sqlalchemy.sql.roles.ColumnsClauseRole", "sqlalchemy.sql.roles.JoinTargetRole", "sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.roles.ByOfRole", "sqlalchemy.sql.roles.UsesInspection", "sqlalchemy.sql.roles.ColumnListRole", "sqlalchemy.sql.roles.LimitOffsetRole", "sqlalchemy.sql.roles.DMLColumnRole", "sqlalchemy.sql.roles.DDLConstraintColumnRole", "sqlalchemy.sql.roles.DDLExpressionRole", "sqlalchemy.sql.roles.StructuralRole", "sqlalchemy.sql.elements.SQLColumnExpression", "sqlalchemy.sql.elements.SQLCoreOperations", "sqlalchemy.sql.operators.ColumnOperators", "sqlalchemy.sql.operators.Operators", "sqlalchemy.sql.roles.ExpressionElementRole", "sqlalchemy.util.langhelpers.TypingOnly", "sqlalchemy.sql.roles.TypedColumnsClauseRole", "sqlalchemy.sql.selectable.Selectable", "sqlalchemy.sql.selectable.ReturnsRows", "sqlalchemy.sql.roles.ReturnsRowsRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.elements.DQLDMLClauseElement", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.sql.base.Generative", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "inherit_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.hstore._HStoreDefinedFunction.inherit_cache", "name": "inherit_cache", "type": "builtins.bool"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.hstore._HStoreDefinedFunction.name", "name": "name", "type": "builtins.str"}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.hstore._HStoreDefinedFunction.type", "name": "type", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.hstore._HStoreDefinedFunction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.hstore._HStoreDefinedFunction", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_HStoreDeleteFunction": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.sql.functions.GenericFunction"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.hstore._HStoreDeleteFunction", "name": "_HStoreDeleteFunction", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.hstore._HStoreDeleteFunction", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.hstore", "mro": ["sqlalchemy.dialects.postgresql.hstore._HStoreDeleteFunction", "sqlalchemy.sql.functions.GenericFunction", "sqlalchemy.sql.functions.Function", "sqlalchemy.sql.functions.FunctionElement", "sqlalchemy.sql.base.Executable", "sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.elements.ColumnElement", "sqlalchemy.sql.roles.ColumnArgumentOrKeyRole", "sqlalchemy.sql.roles.ColumnArgumentRole", "sqlalchemy.sql.roles.StatementOptionRole", "sqlalchemy.sql.roles.WhereHavingRole", "sqlalchemy.sql.roles.OnClauseRole", "sqlalchemy.sql.roles.BinaryElementRole", "sqlalchemy.sql.roles.OrderByRole", "sqlalchemy.sql.selectable.FromClause", "sqlalchemy.sql.roles.AnonymizedFromClauseRole", "sqlalchemy.sql.roles.StrictFromClauseRole", "sqlalchemy.sql.roles.FromClauseRole", "sqlalchemy.sql.roles.ColumnsClauseRole", "sqlalchemy.sql.roles.JoinTargetRole", "sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.roles.ByOfRole", "sqlalchemy.sql.roles.UsesInspection", "sqlalchemy.sql.roles.ColumnListRole", "sqlalchemy.sql.roles.LimitOffsetRole", "sqlalchemy.sql.roles.DMLColumnRole", "sqlalchemy.sql.roles.DDLConstraintColumnRole", "sqlalchemy.sql.roles.DDLExpressionRole", "sqlalchemy.sql.roles.StructuralRole", "sqlalchemy.sql.elements.SQLColumnExpression", "sqlalchemy.sql.elements.SQLCoreOperations", "sqlalchemy.sql.operators.ColumnOperators", "sqlalchemy.sql.operators.Operators", "sqlalchemy.sql.roles.ExpressionElementRole", "sqlalchemy.util.langhelpers.TypingOnly", "sqlalchemy.sql.roles.TypedColumnsClauseRole", "sqlalchemy.sql.selectable.Selectable", "sqlalchemy.sql.selectable.ReturnsRows", "sqlalchemy.sql.roles.ReturnsRowsRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.elements.DQLDMLClauseElement", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.sql.base.Generative", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "inherit_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.hstore._HStoreDeleteFunction.inherit_cache", "name": "inherit_cache", "type": "builtins.bool"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.hstore._HStoreDeleteFunction.name", "name": "name", "type": "builtins.str"}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.hstore._HStoreDeleteFunction.type", "name": "type", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.hstore._HStoreDeleteFunction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.hstore._HStoreDeleteFunction", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_HStoreKeysFunction": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.sql.functions.GenericFunction"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.hstore._HStoreKeysFunction", "name": "_HStoreKeysFunction", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.hstore._HStoreKeysFunction", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.hstore", "mro": ["sqlalchemy.dialects.postgresql.hstore._HStoreKeysFunction", "sqlalchemy.sql.functions.GenericFunction", "sqlalchemy.sql.functions.Function", "sqlalchemy.sql.functions.FunctionElement", "sqlalchemy.sql.base.Executable", "sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.elements.ColumnElement", "sqlalchemy.sql.roles.ColumnArgumentOrKeyRole", "sqlalchemy.sql.roles.ColumnArgumentRole", "sqlalchemy.sql.roles.StatementOptionRole", "sqlalchemy.sql.roles.WhereHavingRole", "sqlalchemy.sql.roles.OnClauseRole", "sqlalchemy.sql.roles.BinaryElementRole", "sqlalchemy.sql.roles.OrderByRole", "sqlalchemy.sql.selectable.FromClause", "sqlalchemy.sql.roles.AnonymizedFromClauseRole", "sqlalchemy.sql.roles.StrictFromClauseRole", "sqlalchemy.sql.roles.FromClauseRole", "sqlalchemy.sql.roles.ColumnsClauseRole", "sqlalchemy.sql.roles.JoinTargetRole", "sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.roles.ByOfRole", "sqlalchemy.sql.roles.UsesInspection", "sqlalchemy.sql.roles.ColumnListRole", "sqlalchemy.sql.roles.LimitOffsetRole", "sqlalchemy.sql.roles.DMLColumnRole", "sqlalchemy.sql.roles.DDLConstraintColumnRole", "sqlalchemy.sql.roles.DDLExpressionRole", "sqlalchemy.sql.roles.StructuralRole", "sqlalchemy.sql.elements.SQLColumnExpression", "sqlalchemy.sql.elements.SQLCoreOperations", "sqlalchemy.sql.operators.ColumnOperators", "sqlalchemy.sql.operators.Operators", "sqlalchemy.sql.roles.ExpressionElementRole", "sqlalchemy.util.langhelpers.TypingOnly", "sqlalchemy.sql.roles.TypedColumnsClauseRole", "sqlalchemy.sql.selectable.Selectable", "sqlalchemy.sql.selectable.ReturnsRows", "sqlalchemy.sql.roles.ReturnsRowsRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.elements.DQLDMLClauseElement", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.sql.base.Generative", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "inherit_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.hstore._HStoreKeysFunction.inherit_cache", "name": "inherit_cache", "type": "builtins.bool"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.hstore._HStoreKeysFunction.name", "name": "name", "type": "builtins.str"}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.hstore._HStoreKeysFunction.type", "name": "type", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_of_any": 7}], "type_ref": "sqlalchemy.dialects.postgresql.array.ARRAY"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.hstore._HStoreKeysFunction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.hstore._HStoreKeysFunction", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_HStoreMatrixFunction": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.sql.functions.GenericFunction"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.hstore._HStoreMatrixFunction", "name": "_HStoreMatrixFunction", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.hstore._HStoreMatrixFunction", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.hstore", "mro": ["sqlalchemy.dialects.postgresql.hstore._HStoreMatrixFunction", "sqlalchemy.sql.functions.GenericFunction", "sqlalchemy.sql.functions.Function", "sqlalchemy.sql.functions.FunctionElement", "sqlalchemy.sql.base.Executable", "sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.elements.ColumnElement", "sqlalchemy.sql.roles.ColumnArgumentOrKeyRole", "sqlalchemy.sql.roles.ColumnArgumentRole", "sqlalchemy.sql.roles.StatementOptionRole", "sqlalchemy.sql.roles.WhereHavingRole", "sqlalchemy.sql.roles.OnClauseRole", "sqlalchemy.sql.roles.BinaryElementRole", "sqlalchemy.sql.roles.OrderByRole", "sqlalchemy.sql.selectable.FromClause", "sqlalchemy.sql.roles.AnonymizedFromClauseRole", "sqlalchemy.sql.roles.StrictFromClauseRole", "sqlalchemy.sql.roles.FromClauseRole", "sqlalchemy.sql.roles.ColumnsClauseRole", "sqlalchemy.sql.roles.JoinTargetRole", "sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.roles.ByOfRole", "sqlalchemy.sql.roles.UsesInspection", "sqlalchemy.sql.roles.ColumnListRole", "sqlalchemy.sql.roles.LimitOffsetRole", "sqlalchemy.sql.roles.DMLColumnRole", "sqlalchemy.sql.roles.DDLConstraintColumnRole", "sqlalchemy.sql.roles.DDLExpressionRole", "sqlalchemy.sql.roles.StructuralRole", "sqlalchemy.sql.elements.SQLColumnExpression", "sqlalchemy.sql.elements.SQLCoreOperations", "sqlalchemy.sql.operators.ColumnOperators", "sqlalchemy.sql.operators.Operators", "sqlalchemy.sql.roles.ExpressionElementRole", "sqlalchemy.util.langhelpers.TypingOnly", "sqlalchemy.sql.roles.TypedColumnsClauseRole", "sqlalchemy.sql.selectable.Selectable", "sqlalchemy.sql.selectable.ReturnsRows", "sqlalchemy.sql.roles.ReturnsRowsRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.elements.DQLDMLClauseElement", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.sql.base.Generative", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "inherit_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.hstore._HStoreMatrixFunction.inherit_cache", "name": "inherit_cache", "type": "builtins.bool"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.hstore._HStoreMatrixFunction.name", "name": "name", "type": "builtins.str"}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.hstore._HStoreMatrixFunction.type", "name": "type", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_of_any": 7}], "type_ref": "sqlalchemy.dialects.postgresql.array.ARRAY"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.hstore._HStoreMatrixFunction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.hstore._HStoreMatrixFunction", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_HStoreSliceFunction": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.sql.functions.GenericFunction"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.hstore._HStoreSliceFunction", "name": "_HStoreSliceFunction", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.hstore._HStoreSliceFunction", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.hstore", "mro": ["sqlalchemy.dialects.postgresql.hstore._HStoreSliceFunction", "sqlalchemy.sql.functions.GenericFunction", "sqlalchemy.sql.functions.Function", "sqlalchemy.sql.functions.FunctionElement", "sqlalchemy.sql.base.Executable", "sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.elements.ColumnElement", "sqlalchemy.sql.roles.ColumnArgumentOrKeyRole", "sqlalchemy.sql.roles.ColumnArgumentRole", "sqlalchemy.sql.roles.StatementOptionRole", "sqlalchemy.sql.roles.WhereHavingRole", "sqlalchemy.sql.roles.OnClauseRole", "sqlalchemy.sql.roles.BinaryElementRole", "sqlalchemy.sql.roles.OrderByRole", "sqlalchemy.sql.selectable.FromClause", "sqlalchemy.sql.roles.AnonymizedFromClauseRole", "sqlalchemy.sql.roles.StrictFromClauseRole", "sqlalchemy.sql.roles.FromClauseRole", "sqlalchemy.sql.roles.ColumnsClauseRole", "sqlalchemy.sql.roles.JoinTargetRole", "sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.roles.ByOfRole", "sqlalchemy.sql.roles.UsesInspection", "sqlalchemy.sql.roles.ColumnListRole", "sqlalchemy.sql.roles.LimitOffsetRole", "sqlalchemy.sql.roles.DMLColumnRole", "sqlalchemy.sql.roles.DDLConstraintColumnRole", "sqlalchemy.sql.roles.DDLExpressionRole", "sqlalchemy.sql.roles.StructuralRole", "sqlalchemy.sql.elements.SQLColumnExpression", "sqlalchemy.sql.elements.SQLCoreOperations", "sqlalchemy.sql.operators.ColumnOperators", "sqlalchemy.sql.operators.Operators", "sqlalchemy.sql.roles.ExpressionElementRole", "sqlalchemy.util.langhelpers.TypingOnly", "sqlalchemy.sql.roles.TypedColumnsClauseRole", "sqlalchemy.sql.selectable.Selectable", "sqlalchemy.sql.selectable.ReturnsRows", "sqlalchemy.sql.roles.ReturnsRowsRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.elements.DQLDMLClauseElement", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.sql.base.Generative", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "inherit_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.hstore._HStoreSliceFunction.inherit_cache", "name": "inherit_cache", "type": "builtins.bool"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.hstore._HStoreSliceFunction.name", "name": "name", "type": "builtins.str"}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.hstore._HStoreSliceFunction.type", "name": "type", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.hstore._HStoreSliceFunction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.hstore._HStoreSliceFunction", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_HStoreValsFunction": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.sql.functions.GenericFunction"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.hstore._HStoreValsFunction", "name": "_HStoreValsFunction", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.hstore._HStoreValsFunction", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.hstore", "mro": ["sqlalchemy.dialects.postgresql.hstore._HStoreValsFunction", "sqlalchemy.sql.functions.GenericFunction", "sqlalchemy.sql.functions.Function", "sqlalchemy.sql.functions.FunctionElement", "sqlalchemy.sql.base.Executable", "sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.elements.ColumnElement", "sqlalchemy.sql.roles.ColumnArgumentOrKeyRole", "sqlalchemy.sql.roles.ColumnArgumentRole", "sqlalchemy.sql.roles.StatementOptionRole", "sqlalchemy.sql.roles.WhereHavingRole", "sqlalchemy.sql.roles.OnClauseRole", "sqlalchemy.sql.roles.BinaryElementRole", "sqlalchemy.sql.roles.OrderByRole", "sqlalchemy.sql.selectable.FromClause", "sqlalchemy.sql.roles.AnonymizedFromClauseRole", "sqlalchemy.sql.roles.StrictFromClauseRole", "sqlalchemy.sql.roles.FromClauseRole", "sqlalchemy.sql.roles.ColumnsClauseRole", "sqlalchemy.sql.roles.JoinTargetRole", "sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.roles.ByOfRole", "sqlalchemy.sql.roles.UsesInspection", "sqlalchemy.sql.roles.ColumnListRole", "sqlalchemy.sql.roles.LimitOffsetRole", "sqlalchemy.sql.roles.DMLColumnRole", "sqlalchemy.sql.roles.DDLConstraintColumnRole", "sqlalchemy.sql.roles.DDLExpressionRole", "sqlalchemy.sql.roles.StructuralRole", "sqlalchemy.sql.elements.SQLColumnExpression", "sqlalchemy.sql.elements.SQLCoreOperations", "sqlalchemy.sql.operators.ColumnOperators", "sqlalchemy.sql.operators.Operators", "sqlalchemy.sql.roles.ExpressionElementRole", "sqlalchemy.util.langhelpers.TypingOnly", "sqlalchemy.sql.roles.TypedColumnsClauseRole", "sqlalchemy.sql.selectable.Selectable", "sqlalchemy.sql.selectable.ReturnsRows", "sqlalchemy.sql.roles.ReturnsRowsRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.elements.DQLDMLClauseElement", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.sql.base.Generative", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "inherit_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.hstore._HStoreValsFunction.inherit_cache", "name": "inherit_cache", "type": "builtins.bool"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.hstore._HStoreValsFunction.name", "name": "name", "type": "builtins.str"}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.hstore._HStoreValsFunction.type", "name": "type", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_of_any": 7}], "type_ref": "sqlalchemy.dialects.postgresql.array.ARRAY"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.hstore._HStoreValsFunction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.hstore._HStoreValsFunction", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.hstore.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.postgresql.hstore.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.postgresql.hstore.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.postgresql.hstore.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.postgresql.hstore.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.postgresql.hstore.__package__", "name": "__package__", "type": "builtins.str"}}, "_parse_error": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["hstore_str", "pos"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.hstore._parse_error", "name": "_parse_error", "type": null}}, "_parse_hstore": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["hstore_str"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.hstore._parse_hstore", "name": "_parse_hstore", "type": null}}, "_serialize_hstore": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["val"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.hstore._serialize_hstore", "name": "_serialize_hstore", "type": null}}, "hstore": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.sql.functions.GenericFunction"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.hstore.hstore", "name": "hstore", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.hstore.hstore", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.hstore", "mro": ["sqlalchemy.dialects.postgresql.hstore.hstore", "sqlalchemy.sql.functions.GenericFunction", "sqlalchemy.sql.functions.Function", "sqlalchemy.sql.functions.FunctionElement", "sqlalchemy.sql.base.Executable", "sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.elements.ColumnElement", "sqlalchemy.sql.roles.ColumnArgumentOrKeyRole", "sqlalchemy.sql.roles.ColumnArgumentRole", "sqlalchemy.sql.roles.StatementOptionRole", "sqlalchemy.sql.roles.WhereHavingRole", "sqlalchemy.sql.roles.OnClauseRole", "sqlalchemy.sql.roles.BinaryElementRole", "sqlalchemy.sql.roles.OrderByRole", "sqlalchemy.sql.selectable.FromClause", "sqlalchemy.sql.roles.AnonymizedFromClauseRole", "sqlalchemy.sql.roles.StrictFromClauseRole", "sqlalchemy.sql.roles.FromClauseRole", "sqlalchemy.sql.roles.ColumnsClauseRole", "sqlalchemy.sql.roles.JoinTargetRole", "sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.roles.ByOfRole", "sqlalchemy.sql.roles.UsesInspection", "sqlalchemy.sql.roles.ColumnListRole", "sqlalchemy.sql.roles.LimitOffsetRole", "sqlalchemy.sql.roles.DMLColumnRole", "sqlalchemy.sql.roles.DDLConstraintColumnRole", "sqlalchemy.sql.roles.DDLExpressionRole", "sqlalchemy.sql.roles.StructuralRole", "sqlalchemy.sql.elements.SQLColumnExpression", "sqlalchemy.sql.elements.SQLCoreOperations", "sqlalchemy.sql.operators.ColumnOperators", "sqlalchemy.sql.operators.Operators", "sqlalchemy.sql.roles.ExpressionElementRole", "sqlalchemy.util.langhelpers.TypingOnly", "sqlalchemy.sql.roles.TypedColumnsClauseRole", "sqlalchemy.sql.selectable.Selectable", "sqlalchemy.sql.selectable.ReturnsRows", "sqlalchemy.sql.roles.ReturnsRowsRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.elements.DQLDMLClauseElement", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.sql.base.Generative", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "inherit_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.hstore.hstore.inherit_cache", "name": "inherit_cache", "type": "builtins.bool"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.hstore.hstore.name", "name": "name", "type": "builtins.str"}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.hstore.hstore.type", "name": "type", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.hstore.hstore.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.hstore.hstore", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_public": false}, "sqlfunc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.functions", "kind": "Gdef", "module_public": false}, "sqltypes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.types", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\hstore.py"}