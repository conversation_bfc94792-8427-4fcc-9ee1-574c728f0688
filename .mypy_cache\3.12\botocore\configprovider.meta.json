{"data_mtime": **********, "dep_lines": [7, 11, 12, 8, 9, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 20, 20, 30], "dependencies": ["collections.abc", "botocore.session", "botocore.utils", "logging", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "abc"], "hash": "643b2d01ea5668a474ceb6eaa4d0e836e31230ee0be6db061cd9ef1b8683210f", "id": "botocore.configprovider", "ignore_all": true, "interface_hash": "ca5a609d5ea8f15fd506ad1dac1529d3354cd39c075a8614f43ca25e3e811fc4", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\botocore-stubs\\configprovider.pyi", "plugin_data": null, "size": 4125, "suppressed": [], "version_id": "1.8.0"}