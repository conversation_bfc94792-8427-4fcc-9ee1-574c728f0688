{".class": "MypyFile", "_fullname": "sqlalchemy.dialects.postgresql.dml", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "ClauseElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ClauseElement", "kind": "Gdef", "module_public": false}, "ColumnCollection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.ColumnCollection", "kind": "Gdef", "module_public": false}, "ColumnElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ColumnElement", "kind": "Gdef", "module_public": false}, "Insert": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.dml.Insert"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.dml.Insert", "name": "Insert", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.dml.Insert", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.dml", "mro": ["sqlalchemy.dialects.postgresql.dml.Insert", "sqlalchemy.sql.dml.Insert", "sqlalchemy.sql.dml.ValuesBase", "sqlalchemy.sql.dml.UpdateBase", "sqlalchemy.sql.roles.DMLRole", "sqlalchemy.sql.selectable.HasCTE", "sqlalchemy.sql.roles.HasCTERole", "sqlalchemy.sql.selectable.SelectsRows", "sqlalchemy.sql.base.HasCompileState", "sqlalchemy.sql.base.DialectKWArgs", "sqlalchemy.sql.selectable.HasPrefixes", "sqlalchemy.sql.base.Generative", "sqlalchemy.sql.selectable.ExecutableReturnsRows", "sqlalchemy.sql.base.Executable", "sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.selectable.ReturnsRows", "sqlalchemy.sql.roles.ReturnsRowsRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.elements.DQLDMLClauseElement", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "_on_conflict_exclusive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.dml.Insert._on_conflict_exclusive", "name": "_on_conflict_exclusive", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.base._Fn", "id": 17764, "name": "_Fn", "namespace": "", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.base._Fn", "id": 17764, "name": "_Fn", "namespace": "", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.base._Fn", "id": 17764, "name": "_Fn", "namespace": "", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}]}}}, "excluded": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.dml.Insert.excluded", "name": "excluded", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.dialects.postgresql.dml.Insert"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "excluded of Insert", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.elements.KeyedColumnElement"}], "type_ref": "sqlalchemy.sql.base.ReadOnlyColumnCollection"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.dml.Insert.excluded", "name": "excluded", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.elements.KeyedColumnElement"}], "type_ref": "sqlalchemy.sql.base.ReadOnlyColumnCollection"}], "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "inherit_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.dml.Insert.inherit_cache", "name": "inherit_cache", "type": "builtins.bool"}}, "on_conflict_do_nothing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "constraint", "index_elements", "index_where"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.dml.Insert.on_conflict_do_nothing", "name": "on_conflict_do_nothing", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "constraint", "index_elements", "index_where"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.dml.Insert.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.dml.Insert", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.dialects._typing._OnConflictConstraintT"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.dialects._typing._OnConflictIndexElementsT"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.dialects._typing._OnConflictIndexWhereT"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_conflict_do_nothing of Insert", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.dml.Insert.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.dml.Insert", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.dml.Insert.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.dml.Insert", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.dml.Insert.on_conflict_do_nothing", "name": "on_conflict_do_nothing", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "constraint", "index_elements", "index_where"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.dml.Insert.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.dml.Insert", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.dialects._typing._OnConflictConstraintT"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.dialects._typing._OnConflictIndexElementsT"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.dialects._typing._OnConflictIndexWhereT"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_conflict_do_nothing of Insert", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.dml.Insert.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.dml.Insert", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.dml.Insert.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.dml.Insert", "values": [], "variance": 0}]}}}}, "on_conflict_do_update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "constraint", "index_elements", "index_where", "set_", "where"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.dml.Insert.on_conflict_do_update", "name": "on_conflict_do_update", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "constraint", "index_elements", "index_where", "set_", "where"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.dml.Insert.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.dml.Insert", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.dialects._typing._OnConflictConstraintT"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.dialects._typing._OnConflictIndexElementsT"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.dialects._typing._OnConflictIndexWhereT"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.dialects._typing._OnConflictSetT"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.dialects._typing._OnConflictWhereT"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_conflict_do_update of Insert", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.dml.Insert.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.dml.Insert", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.dml.Insert.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.dml.Insert", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.dml.Insert.on_conflict_do_update", "name": "on_conflict_do_update", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "constraint", "index_elements", "index_where", "set_", "where"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.dml.Insert.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.dml.Insert", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.dialects._typing._OnConflictConstraintT"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.dialects._typing._OnConflictIndexElementsT"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.dialects._typing._OnConflictIndexWhereT"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.dialects._typing._OnConflictSetT"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.dialects._typing._OnConflictWhereT"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_conflict_do_update of Insert", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.dml.Insert.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.dml.Insert", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.dml.Insert.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.dml.Insert", "values": [], "variance": 0}]}}}}, "stringify_dialect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.dml.Insert.stringify_dialect", "name": "stringify_dialect", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.dml.Insert.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.dml.Insert", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "KeyedColumnElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.KeyedColumnElement", "kind": "Gdef", "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "OnConflictClause": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.elements.ClauseElement"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.dml.OnConflictClause", "name": "OnConflictClause", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.dml.OnConflictClause", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.dml", "mro": ["sqlalchemy.dialects.postgresql.dml.OnConflictClause", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "constraint", "index_elements", "index_where"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.dml.OnConflictClause.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "constraint", "index_elements", "index_where"], "arg_types": ["sqlalchemy.dialects.postgresql.dml.OnConflictClause", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.dialects._typing._OnConflictConstraintT"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.dialects._typing._OnConflictIndexElementsT"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.dialects._typing._OnConflictIndexWhereT"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of OnConflictClause", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "constraint_target": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.dialects.postgresql.dml.OnConflictClause.constraint_target", "name": "constraint_target", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "inferred_target_elements": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.dialects.postgresql.dml.OnConflictClause.inferred_target_elements", "name": "inferred_target_elements", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.schema.Column"}]}], "type_ref": "builtins.list"}, {".class": "NoneType"}]}}}, "inferred_target_whereclause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.dialects.postgresql.dml.OnConflictClause.inferred_target_whereclause", "name": "inferred_target_whereclause", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "sqlalchemy.sql.elements.TextClause", {".class": "NoneType"}]}}}, "stringify_dialect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.dml.OnConflictClause.stringify_dialect", "name": "stringify_dialect", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.dml.OnConflictClause.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.dml.OnConflictClause", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OnConflictDoNothing": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.postgresql.dml.OnConflictClause"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.dml.OnConflictDoNothing", "name": "OnConflictDoNothing", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.dml.OnConflictDoNothing", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.dml", "mro": ["sqlalchemy.dialects.postgresql.dml.OnConflictDoNothing", "sqlalchemy.dialects.postgresql.dml.OnConflictClause", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.dml.OnConflictDoNothing.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.dml.OnConflictDoNothing.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.dml.OnConflictDoNothing", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OnConflictDoUpdate": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.postgresql.dml.OnConflictClause"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.dml.OnConflictDoUpdate", "name": "OnConflictDoUpdate", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.dml.OnConflictDoUpdate", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.dml", "mro": ["sqlalchemy.dialects.postgresql.dml.OnConflictDoUpdate", "sqlalchemy.dialects.postgresql.dml.OnConflictClause", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "constraint", "index_elements", "index_where", "set_", "where"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.dml.OnConflictDoUpdate.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "constraint", "index_elements", "index_where", "set_", "where"], "arg_types": ["sqlalchemy.dialects.postgresql.dml.OnConflictDoUpdate", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.dialects._typing._OnConflictConstraintT"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.dialects._typing._OnConflictIndexElementsT"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.dialects._typing._OnConflictIndexWhereT"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.dialects._typing._OnConflictSetT"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.dialects._typing._OnConflictWhereT"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of OnConflictDoUpdate", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.dml.OnConflictDoUpdate.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "update_values_to_set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.dialects.postgresql.dml.OnConflictDoUpdate.update_values_to_set", "name": "update_values_to_set", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.schema.Column"}, "builtins.str"]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}}}, "update_whereclause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.dialects.postgresql.dml.OnConflictDoUpdate.update_whereclause", "name": "update_whereclause", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, {".class": "NoneType"}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.dml.OnConflictDoUpdate.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.dml.OnConflictDoUpdate", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "ReadOnlyColumnCollection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.ReadOnlyColumnCollection", "kind": "Gdef", "module_public": false}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing.Self", "kind": "Gdef", "module_public": false}, "StandardInsert": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.dml.Insert", "kind": "Gdef", "module_public": false}, "TextClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.TextClause", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "_DMLTableArgument": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._DMLTableArgument", "kind": "Gdef", "module_public": false}, "_OnConflictConstraintT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects._typing._OnConflictConstraintT", "kind": "Gdef", "module_public": false}, "_OnConflictIndexElementsT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects._typing._OnConflictIndexElementsT", "kind": "Gdef", "module_public": false}, "_OnConflictIndexWhereT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects._typing._OnConflictIndexWhereT", "kind": "Gdef", "module_public": false}, "_OnConflictSetT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects._typing._OnConflictSetT", "kind": "Gdef", "module_public": false}, "_OnConflictWhereT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects._typing._OnConflictWhereT", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.dml.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.postgresql.dml.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.postgresql.dml.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.postgresql.dml.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.postgresql.dml.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.postgresql.dml.__package__", "name": "__package__", "type": "builtins.str"}}, "_exclusive_against": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base._exclusive_against", "kind": "Gdef", "module_public": false}, "_generative": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base._generative", "kind": "Gdef", "module_public": false}, "alias": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.alias", "kind": "Gdef", "module_public": false}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "coercions": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.coercions", "kind": "Gdef", "module_public": false}, "ext": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.ext", "kind": "Gdef", "module_public": false}, "insert": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["table"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.dml.insert", "name": "insert", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["table"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._DMLTableArgument"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "insert", "ret_type": "sqlalchemy.dialects.postgresql.dml.Insert", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "roles": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.roles", "kind": "Gdef", "module_public": false}, "schema": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema", "kind": "Gdef", "module_public": false}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\dml.py"}