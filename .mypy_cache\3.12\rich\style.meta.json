{"data_mtime": 1759504511, "dep_lines": [7, 8, 9, 10, 1, 2, 3, 4, 5, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 10, 5, 5, 5, 5, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["rich.errors", "rich.color", "rich.repr", "rich.terminal_theme", "sys", "functools", "marshal", "random", "typing", "rich", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "_typeshed", "abc", "enum", "rich.color_triplet", "types", "typing_extensions"], "hash": "c698f8b8c05932db8db8da267d489a9a2825de9d6c0ef4c2670ac6d6d71355e8", "id": "rich.style", "ignore_all": true, "interface_hash": "8f7bfbe67e070efeffca0bd02b478c5032a35e985570aa1e9211adc7ff502cf7", "mtime": 1757092239, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\rich\\style.py", "plugin_data": null, "size": 27059, "suppressed": [], "version_id": "1.8.0"}