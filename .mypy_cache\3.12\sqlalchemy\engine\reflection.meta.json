{"data_mtime": 1759504547, "dep_lines": [49, 55, 56, 57, 58, 60, 61, 62, 63, 66, 51, 52, 53, 54, 27, 29, 30, 31, 34, 51, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 5, 5, 5, 10, 5, 25, 10, 10, 10, 10, 5, 10, 5, 5, 5, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.engine.base", "sqlalchemy.sql.operators", "sqlalchemy.sql.schema", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.elements", "sqlalchemy.sql.type_api", "sqlalchemy.sql.visitors", "sqlalchemy.util.topological", "sqlalchemy.util.typing", "sqlalchemy.engine.interfaces", "sqlalchemy.exc", "sqlalchemy.inspection", "sqlalchemy.sql", "sqlalchemy.util", "__future__", "contextlib", "dataclasses", "enum", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "_typeshed", "abc", "sqlalchemy.engine.url", "sqlalchemy.event", "sqlalchemy.event.attr", "sqlalchemy.event.base", "sqlalchemy.event.registry", "sqlalchemy.log", "sqlalchemy.pool", "sqlalchemy.pool.base", "sqlalchemy.sql._elements_constructors", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.roles", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.util._py_collections", "sqlalchemy.util.deprecations", "sqlalchemy.util.langhelpers", "types", "typing_extensions"], "hash": "001a74f84ad960dbcd5834bd22100874df2567e123aff35ad99cd7671a32ae77", "id": "sqlalchemy.engine.reflection", "ignore_all": true, "interface_hash": "dbd44595444baa62067ca1837e65cd4f8ba2e45dad51f2f4da7578f9fdf8d114", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\reflection.py", "plugin_data": null, "size": 77667, "suppressed": [], "version_id": "1.8.0"}