{"data_mtime": 1759503866, "dep_lines": [37, 38, 39, 40, 41, 45, 46, 49, 37, 42, 43, 44, 15, 17, 18, 35, 42, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 5, 5, 5, 25, 20, 10, 10, 10, 5, 10, 5, 10, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.attributes", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.descriptor_props", "sqlalchemy.orm.properties", "sqlalchemy.orm.util", "sqlalchemy.sql.schema", "sqlalchemy.util.typing", "sqlalchemy.orm.relationships", "sqlalchemy.orm", "sqlalchemy.exc", "sqlalchemy.inspection", "sqlalchemy.util", "__future__", "re", "typing", "weakref", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "_weakref", "abc", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.log", "sqlalchemy.orm.base", "sqlalchemy.sql", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.elements", "sqlalchemy.sql.roles", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util._collections", "sqlalchemy.util.langhelpers"], "hash": "e061bb869a802c375259e5e4e8fb7e9c8a2e6a40118712a3ebad17bba95a21a1", "id": "sqlalchemy.orm.clsregistry", "ignore_all": true, "interface_hash": "15e0ed10bb727666ae7cb9d4558f29659cc7e004a94131383fe1bc37e640509d", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\clsregistry.py", "plugin_data": null, "size": 18523, "suppressed": [], "version_id": "1.8.0"}