{"data_mtime": 1759503866, "dep_lines": [28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 42, 55, 60, 76, 77, 78, 80, 28, 61, 10, 12, 13, 14, 61, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 5, 5, 10, 5, 5, 5, 5, 5, 25, 25, 25, 25, 20, 10, 5, 10, 10, 5, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.annotation", "sqlalchemy.sql.coercions", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.schema", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.type_api", "sqlalchemy.sql.util", "sqlalchemy.sql._typing", "sqlalchemy.sql.base", "sqlalchemy.sql.elements", "sqlalchemy.sql.selectable", "sqlalchemy.sql.visitors", "sqlalchemy.engine.base", "sqlalchemy.engine.cursor", "sqlalchemy.engine.interfaces", "sqlalchemy.util.typing", "sqlalchemy.sql", "sqlalchemy.util", "__future__", "datetime", "decimal", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "_decimal", "_typeshed", "abc", "collections", "enum", "sqlalchemy.engine", "sqlalchemy.engine.result", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.compiler", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.traversals", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "types", "typing_extensions"], "hash": "5ff3cdb7e1813e51e5b797cbcc599d1e3f7ded5d021bbede2c0de5ad29d11e93", "id": "sqlalchemy.sql.functions", "ignore_all": true, "interface_hash": "af3d7376b8b6038bcf764b11bdefe87233b9fe8bcd2432cca57377657a44eafd", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\functions.py", "plugin_data": null, "size": 66930, "suppressed": [], "version_id": "1.8.0"}