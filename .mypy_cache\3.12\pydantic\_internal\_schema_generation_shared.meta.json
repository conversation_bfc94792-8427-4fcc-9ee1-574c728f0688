{"data_mtime": 1759504514, "dep_lines": [13, 14, 15, 7, 9, 12, 3, 5, 7, 1, 1, 1, 1], "dep_prios": [25, 25, 25, 10, 5, 25, 5, 5, 20, 5, 20, 20, 30], "dependencies": ["pydantic._internal._core_utils", "pydantic._internal._generate_schema", "pydantic._internal._namespace_utils", "pydantic_core.core_schema", "pydantic.annotated_handlers", "pydantic.json_schema", "__future__", "typing", "pydantic_core", "builtins", "pyexpat.model", "pyexpat.errors", "abc"], "hash": "17fadb41bae4a289a0c6cb24747a4fd23509ed309f7b40400c0124aba089e273", "id": "pydantic._internal._schema_generation_shared", "ignore_all": true, "interface_hash": "362e2599deab9a562c4f7c16e76ad97d2bfe1b2fa35cdabe53fd491e633cbb61", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\_internal\\_schema_generation_shared.py", "plugin_data": null, "size": 4842, "suppressed": [], "version_id": "1.8.0"}