{"data_mtime": 1759503833, "dep_lines": [11, 12, 17, 18, 23, 863, 11, 822, 1, 3, 4, 5, 6, 7, 8, 9, 21, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 25, 20, 20, 20, 5, 10, 10, 10, 10, 10, 10, 5, 25, 5, 20, 20, 30, 30, 30, 30], "dependencies": ["aws_lambda_powertools.shared.constants", "aws_lambda_powertools.shared.functions", "aws_lambda_powertools.shared.lazy_import", "aws_lambda_powertools.shared.types", "aws_lambda_powertools.tracing.base", "aws_xray_sdk.ext.httplib", "aws_lambda_powertools.shared", "aws_xray_sdk.core", "__future__", "contextlib", "copy", "functools", "inspect", "logging", "os", "typing", "numbers", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "aws_xray_sdk", "aws_xray_sdk.core.recorder", "types"], "hash": "3677fafaeebfc754f2f824e05a9dcd206c8bef4402ec742f05b5c174650b7ae9", "id": "aws_lambda_powertools.tracing.tracer", "ignore_all": true, "interface_hash": "4bf6aa447e74d8dbfea8e64ba12b7823075dbdd79aab5994152fdf097582554e", "mtime": 1757091873, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\aws_lambda_powertools\\tracing\\tracer.py", "plugin_data": null, "size": 31882, "suppressed": [], "version_id": "1.8.0"}