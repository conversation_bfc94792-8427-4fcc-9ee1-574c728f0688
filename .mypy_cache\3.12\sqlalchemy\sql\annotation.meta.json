{"data_mtime": 1759503866, "dep_lines": [35, 36, 37, 41, 45, 35, 40, 18, 20, 40, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 25, 20, 10, 5, 5, 20, 5, 20, 20, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.operators", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.visitors", "sqlalchemy.util.typing", "sqlalchemy.sql.base", "sqlalchemy.sql", "sqlalchemy.util", "__future__", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "enum", "sqlalchemy.sql._py_util", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers"], "hash": "d65168380ea22a263f631a9806c276ae361cdf41a6fc3d0510aba632998d1aef", "id": "sqlalchemy.sql.annotation", "ignore_all": true, "interface_hash": "10e817a8469d7718b305fd0607805b2f645f7c6993f58c2f51bd2776e7e50db2", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\annotation.py", "plugin_data": null, "size": 18830, "suppressed": [], "version_id": "1.8.0"}