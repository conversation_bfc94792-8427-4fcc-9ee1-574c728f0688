{"data_mtime": 1759503833, "dep_lines": [1, 2, 5, 6, 3, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 20, 20, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "http.cookiejar", "requests.models", "requests.sessions", "typing_extensions", "builtins", "pyexpat.model", "pyexpat.errors", "_typeshed", "abc", "http", "requests.auth", "typing"], "hash": "2aa3c01ba2f8636fc0dd6e02a616f9f230779faa2dcc0137795226eb2d3364b9", "id": "requests.api", "ignore_all": true, "interface_hash": "eebd36a6cbb6747b40e08a7a425daf350ca36a0d5ba1d62cb9c25da80c26a9fe", "mtime": 1757363991, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\requests-stubs\\api.pyi", "plugin_data": null, "size": 4763, "suppressed": [], "version_id": "1.8.0"}