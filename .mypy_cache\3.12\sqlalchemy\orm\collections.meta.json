{"data_mtime": 1759504547, "dep_lines": [130, 133, 134, 135, 138, 140, 144, 131, 132, 108, 110, 111, 112, 128, 131, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 25, 25, 25, 10, 10, 5, 10, 10, 5, 10, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.base", "sqlalchemy.sql.base", "sqlalchemy.util.compat", "sqlalchemy.util.typing", "sqlalchemy.orm.attributes", "sqlalchemy.orm.mapped_collection", "sqlalchemy.orm.state", "sqlalchemy.exc", "sqlalchemy.util", "__future__", "operator", "threading", "typing", "weakref", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "_typeshed", "_weakref", "abc", "enum", "sqlalchemy.util._py_collections", "sqlalchemy.util.deprecations"], "hash": "1c139355683617d53fa39c9d210ad723c35261404292e4761969978bcaeb702c", "id": "sqlalchemy.orm.collections", "ignore_all": true, "interface_hash": "41057efb07e1e06376504123c1d781ad1e0137eb25494122cd52c707c52de83e", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\collections.py", "plugin_data": null, "size": 53908, "suppressed": [], "version_id": "1.8.0"}