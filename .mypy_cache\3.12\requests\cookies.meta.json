{"data_mtime": 1759503830, "dep_lines": [2, 3, 4, 1, 5, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 20, 20, 30, 30], "dependencies": ["collections.abc", "http.cookiejar", "http.cookies", "_typeshed", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "http"], "hash": "ff2083b92b3fd428df2a21e442c0be3353f20bb746373b586de7ae2ffa7726fe", "id": "requests.cookies", "ignore_all": true, "interface_hash": "421f70ac269de470da4d5d0c67cbee7991e5b76cf90680d6dd4c0cdd7d46a629", "mtime": 1757363991, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\requests-stubs\\cookies.pyi", "plugin_data": null, "size": 2752, "suppressed": [], "version_id": "1.8.0"}