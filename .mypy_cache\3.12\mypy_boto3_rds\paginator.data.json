{".class": "MypyFile", "_fullname": "mypy_boto3_rds.paginator", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "CertificateMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.CertificateMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBClusterAutomatedBackupMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DBClusterAutomatedBackupMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBClusterBacktrackMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DBClusterBacktrackMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBClusterEndpointMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DBClusterEndpointMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBClusterMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DBClusterMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBClusterParameterGroupDetailsTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DBClusterParameterGroupDetailsTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBClusterParameterGroupsMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DBClusterParameterGroupsMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBClusterSnapshotMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DBClusterSnapshotMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBEngineVersionMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DBEngineVersionMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBInstanceAutomatedBackupMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DBInstanceAutomatedBackupMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBInstanceMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DBInstanceMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBParameterGroupDetailsTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DBParameterGroupDetailsTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBParameterGroupsMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DBParameterGroupsMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBRecommendationsMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DBRecommendationsMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBSecurityGroupMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DBSecurityGroupMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBSnapshotMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DBSnapshotMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBSnapshotTenantDatabasesMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DBSnapshotTenantDatabasesMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DBSubnetGroupMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DBSubnetGroupMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeBlueGreenDeploymentsPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeBlueGreenDeploymentsResponseTypeDef"}], "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.paginator.DescribeBlueGreenDeploymentsPaginator", "name": "DescribeBlueGreenDeploymentsPaginator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeBlueGreenDeploymentsPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.paginator", "mro": ["mypy_boto3_rds.paginator.DescribeBlueGreenDeploymentsPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeBlueGreenDeploymentsPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.paginator.DescribeBlueGreenDeploymentsPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeBlueGreenDeploymentsRequestPaginateTypeDef", "items": [["BlueGreenDeploymentIdentifier", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PaginatorConfigTypeDef"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "paginate of DescribeBlueGreenDeploymentsPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeBlueGreenDeploymentsResponseTypeDef"}], "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.paginator.DescribeBlueGreenDeploymentsPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.paginator.DescribeBlueGreenDeploymentsPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DescribeBlueGreenDeploymentsRequestPaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeBlueGreenDeploymentsRequestPaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeBlueGreenDeploymentsResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeBlueGreenDeploymentsResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeCertificatesMessagePaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeCertificatesMessagePaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeCertificatesPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.CertificateMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.paginator.DescribeCertificatesPaginator", "name": "DescribeCertificatesPaginator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeCertificatesPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.paginator", "mro": ["mypy_boto3_rds.paginator.DescribeCertificatesPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeCertificatesPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.paginator.DescribeCertificatesPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeCertificatesMessagePaginateTypeDef", "items": [["CertificateIdentifier", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PaginatorConfigTypeDef"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "paginate of DescribeCertificatesPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.CertificateMessageTypeDef"}], "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.paginator.DescribeCertificatesPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.paginator.DescribeCertificatesPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DescribeDBClusterAutomatedBackupsMessagePaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBClusterAutomatedBackupsMessagePaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBClusterAutomatedBackupsPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBClusterAutomatedBackupMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.paginator.DescribeDBClusterAutomatedBackupsPaginator", "name": "DescribeDBClusterAutomatedBackupsPaginator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBClusterAutomatedBackupsPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.paginator", "mro": ["mypy_boto3_rds.paginator.DescribeDBClusterAutomatedBackupsPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBClusterAutomatedBackupsPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.paginator.DescribeDBClusterAutomatedBackupsPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBClusterAutomatedBackupsMessagePaginateTypeDef", "items": [["DbClusterResourceId", "builtins.str"], ["DBClusterIdentifier", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PaginatorConfigTypeDef"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "paginate of DescribeDBClusterAutomatedBackupsPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBClusterAutomatedBackupMessageTypeDef"}], "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.paginator.DescribeDBClusterAutomatedBackupsPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.paginator.DescribeDBClusterAutomatedBackupsPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DescribeDBClusterBacktracksMessagePaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBClusterBacktracksMessagePaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBClusterBacktracksPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBClusterBacktrackMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.paginator.DescribeDBClusterBacktracksPaginator", "name": "DescribeDBClusterBacktracksPaginator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBClusterBacktracksPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.paginator", "mro": ["mypy_boto3_rds.paginator.DescribeDBClusterBacktracksPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBClusterBacktracksPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.paginator.DescribeDBClusterBacktracksPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBClusterBacktracksMessagePaginateTypeDef", "items": [["DBClusterIdentifier", "builtins.str"], ["BacktrackIdentifier", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PaginatorConfigTypeDef"}]], "required_keys": ["DBClusterIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "paginate of DescribeDBClusterBacktracksPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBClusterBacktrackMessageTypeDef"}], "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.paginator.DescribeDBClusterBacktracksPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.paginator.DescribeDBClusterBacktracksPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DescribeDBClusterEndpointsMessagePaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBClusterEndpointsMessagePaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBClusterEndpointsPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBClusterEndpointMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.paginator.DescribeDBClusterEndpointsPaginator", "name": "DescribeDBClusterEndpointsPaginator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBClusterEndpointsPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.paginator", "mro": ["mypy_boto3_rds.paginator.DescribeDBClusterEndpointsPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBClusterEndpointsPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.paginator.DescribeDBClusterEndpointsPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBClusterEndpointsMessagePaginateTypeDef", "items": [["DBClusterIdentifier", "builtins.str"], ["DBClusterEndpointIdentifier", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PaginatorConfigTypeDef"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "paginate of DescribeDBClusterEndpointsPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBClusterEndpointMessageTypeDef"}], "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.paginator.DescribeDBClusterEndpointsPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.paginator.DescribeDBClusterEndpointsPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DescribeDBClusterParameterGroupsMessagePaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBClusterParameterGroupsMessagePaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBClusterParameterGroupsPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBClusterParameterGroupsMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.paginator.DescribeDBClusterParameterGroupsPaginator", "name": "DescribeDBClusterParameterGroupsPaginator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBClusterParameterGroupsPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.paginator", "mro": ["mypy_boto3_rds.paginator.DescribeDBClusterParameterGroupsPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBClusterParameterGroupsPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.paginator.DescribeDBClusterParameterGroupsPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBClusterParameterGroupsMessagePaginateTypeDef", "items": [["DBClusterParameterGroupName", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PaginatorConfigTypeDef"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "paginate of DescribeDBClusterParameterGroupsPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBClusterParameterGroupsMessageTypeDef"}], "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.paginator.DescribeDBClusterParameterGroupsPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.paginator.DescribeDBClusterParameterGroupsPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DescribeDBClusterParametersMessagePaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBClusterParametersMessagePaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBClusterParametersPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBClusterParameterGroupDetailsTypeDef"}], "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.paginator.DescribeDBClusterParametersPaginator", "name": "DescribeDBClusterParametersPaginator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBClusterParametersPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.paginator", "mro": ["mypy_boto3_rds.paginator.DescribeDBClusterParametersPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBClusterParametersPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.paginator.DescribeDBClusterParametersPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBClusterParametersMessagePaginateTypeDef", "items": [["DBClusterParameterGroupName", "builtins.str"], ["Source", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PaginatorConfigTypeDef"}]], "required_keys": ["DBClusterParameterGroupName"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "paginate of DescribeDBClusterParametersPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBClusterParameterGroupDetailsTypeDef"}], "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.paginator.DescribeDBClusterParametersPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.paginator.DescribeDBClusterParametersPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DescribeDBClusterSnapshotsMessagePaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBClusterSnapshotsMessagePaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBClusterSnapshotsPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBClusterSnapshotMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.paginator.DescribeDBClusterSnapshotsPaginator", "name": "DescribeDBClusterSnapshotsPaginator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBClusterSnapshotsPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.paginator", "mro": ["mypy_boto3_rds.paginator.DescribeDBClusterSnapshotsPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBClusterSnapshotsPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.paginator.DescribeDBClusterSnapshotsPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBClusterSnapshotsMessagePaginateTypeDef", "items": [["DBClusterIdentifier", "builtins.str"], ["DBClusterSnapshotIdentifier", "builtins.str"], ["SnapshotType", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["IncludeShared", "builtins.bool"], ["IncludePublic", "builtins.bool"], ["DbClusterResourceId", "builtins.str"], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PaginatorConfigTypeDef"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "paginate of DescribeDBClusterSnapshotsPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBClusterSnapshotMessageTypeDef"}], "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.paginator.DescribeDBClusterSnapshotsPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.paginator.DescribeDBClusterSnapshotsPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DescribeDBClustersMessagePaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBClustersMessagePaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBClustersPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBClusterMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.paginator.DescribeDBClustersPaginator", "name": "DescribeDBClustersPaginator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBClustersPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.paginator", "mro": ["mypy_boto3_rds.paginator.DescribeDBClustersPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBClustersPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.paginator.DescribeDBClustersPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBClustersMessagePaginateTypeDef", "items": [["DBClusterIdentifier", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["IncludeShared", "builtins.bool"], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PaginatorConfigTypeDef"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "paginate of DescribeDBClustersPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBClusterMessageTypeDef"}], "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.paginator.DescribeDBClustersPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.paginator.DescribeDBClustersPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DescribeDBEngineVersionsMessagePaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBEngineVersionsMessagePaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBEngineVersionsPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBEngineVersionMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.paginator.DescribeDBEngineVersionsPaginator", "name": "DescribeDBEngineVersionsPaginator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBEngineVersionsPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.paginator", "mro": ["mypy_boto3_rds.paginator.DescribeDBEngineVersionsPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBEngineVersionsPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.paginator.DescribeDBEngineVersionsPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBEngineVersionsMessagePaginateTypeDef", "items": [["Engine", "builtins.str"], ["EngineVersion", "builtins.str"], ["DBParameterGroupFamily", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "builtins.bool"], ["ListSupportedCharacterSets", "builtins.bool"], ["ListSupportedTimezones", "builtins.bool"], ["IncludeAll", "builtins.bool"], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PaginatorConfigTypeDef"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "paginate of DescribeDBEngineVersionsPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBEngineVersionMessageTypeDef"}], "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.paginator.DescribeDBEngineVersionsPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.paginator.DescribeDBEngineVersionsPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DescribeDBInstanceAutomatedBackupsMessagePaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBInstanceAutomatedBackupsMessagePaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBInstanceAutomatedBackupsPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBInstanceAutomatedBackupMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.paginator.DescribeDBInstanceAutomatedBackupsPaginator", "name": "DescribeDBInstanceAutomatedBackupsPaginator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBInstanceAutomatedBackupsPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.paginator", "mro": ["mypy_boto3_rds.paginator.DescribeDBInstanceAutomatedBackupsPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBInstanceAutomatedBackupsPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.paginator.DescribeDBInstanceAutomatedBackupsPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBInstanceAutomatedBackupsMessagePaginateTypeDef", "items": [["DbiResourceId", "builtins.str"], ["DBInstanceIdentifier", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["DBInstanceAutomatedBackupsArn", "builtins.str"], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PaginatorConfigTypeDef"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "paginate of DescribeDBInstanceAutomatedBackupsPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBInstanceAutomatedBackupMessageTypeDef"}], "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.paginator.DescribeDBInstanceAutomatedBackupsPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.paginator.DescribeDBInstanceAutomatedBackupsPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DescribeDBInstancesMessagePaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBInstancesMessagePaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBInstancesPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBInstanceMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.paginator.DescribeDBInstancesPaginator", "name": "DescribeDBInstancesPaginator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBInstancesPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.paginator", "mro": ["mypy_boto3_rds.paginator.DescribeDBInstancesPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBInstancesPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.paginator.DescribeDBInstancesPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBInstancesMessagePaginateTypeDef", "items": [["DBInstanceIdentifier", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PaginatorConfigTypeDef"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "paginate of DescribeDBInstancesPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBInstanceMessageTypeDef"}], "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.paginator.DescribeDBInstancesPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.paginator.DescribeDBInstancesPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DescribeDBLogFilesMessagePaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBLogFilesMessagePaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBLogFilesPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeDBLogFilesResponseTypeDef"}], "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.paginator.DescribeDBLogFilesPaginator", "name": "DescribeDBLogFilesPaginator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBLogFilesPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.paginator", "mro": ["mypy_boto3_rds.paginator.DescribeDBLogFilesPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBLogFilesPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.paginator.DescribeDBLogFilesPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBLogFilesMessagePaginateTypeDef", "items": [["DBInstanceIdentifier", "builtins.str"], ["FilenameContains", "builtins.str"], ["FileLastWritten", "builtins.int"], ["FileSize", "builtins.int"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PaginatorConfigTypeDef"}]], "required_keys": ["DBInstanceIdentifier"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "paginate of DescribeDBLogFilesPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeDBLogFilesResponseTypeDef"}], "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.paginator.DescribeDBLogFilesPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.paginator.DescribeDBLogFilesPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DescribeDBLogFilesResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBLogFilesResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBMajorEngineVersionsPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeDBMajorEngineVersionsResponseTypeDef"}], "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.paginator.DescribeDBMajorEngineVersionsPaginator", "name": "DescribeDBMajorEngineVersionsPaginator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBMajorEngineVersionsPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.paginator", "mro": ["mypy_boto3_rds.paginator.DescribeDBMajorEngineVersionsPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBMajorEngineVersionsPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.paginator.DescribeDBMajorEngineVersionsPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBMajorEngineVersionsRequestPaginateTypeDef", "items": [["Engine", "builtins.str"], ["MajorEngineVersion", "builtins.str"], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PaginatorConfigTypeDef"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "paginate of DescribeDBMajorEngineVersionsPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeDBMajorEngineVersionsResponseTypeDef"}], "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.paginator.DescribeDBMajorEngineVersionsPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.paginator.DescribeDBMajorEngineVersionsPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DescribeDBMajorEngineVersionsRequestPaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBMajorEngineVersionsRequestPaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBMajorEngineVersionsResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBMajorEngineVersionsResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBParameterGroupsMessagePaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBParameterGroupsMessagePaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBParameterGroupsPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBParameterGroupsMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.paginator.DescribeDBParameterGroupsPaginator", "name": "DescribeDBParameterGroupsPaginator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBParameterGroupsPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.paginator", "mro": ["mypy_boto3_rds.paginator.DescribeDBParameterGroupsPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBParameterGroupsPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.paginator.DescribeDBParameterGroupsPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBParameterGroupsMessagePaginateTypeDef", "items": [["DBParameterGroupName", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PaginatorConfigTypeDef"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "paginate of DescribeDBParameterGroupsPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBParameterGroupsMessageTypeDef"}], "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.paginator.DescribeDBParameterGroupsPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.paginator.DescribeDBParameterGroupsPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DescribeDBParametersMessagePaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBParametersMessagePaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBParametersPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBParameterGroupDetailsTypeDef"}], "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.paginator.DescribeDBParametersPaginator", "name": "DescribeDBParametersPaginator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBParametersPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.paginator", "mro": ["mypy_boto3_rds.paginator.DescribeDBParametersPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBParametersPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.paginator.DescribeDBParametersPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBParametersMessagePaginateTypeDef", "items": [["DBParameterGroupName", "builtins.str"], ["Source", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PaginatorConfigTypeDef"}]], "required_keys": ["DBParameterGroupName"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "paginate of DescribeDBParametersPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBParameterGroupDetailsTypeDef"}], "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.paginator.DescribeDBParametersPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.paginator.DescribeDBParametersPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DescribeDBProxiesPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeDBProxiesResponseTypeDef"}], "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.paginator.DescribeDBProxiesPaginator", "name": "DescribeDBProxiesPaginator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBProxiesPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.paginator", "mro": ["mypy_boto3_rds.paginator.DescribeDBProxiesPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBProxiesPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.paginator.DescribeDBProxiesPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBProxiesRequestPaginateTypeDef", "items": [["DBProxyName", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PaginatorConfigTypeDef"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "paginate of DescribeDBProxiesPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeDBProxiesResponseTypeDef"}], "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.paginator.DescribeDBProxiesPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.paginator.DescribeDBProxiesPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DescribeDBProxiesRequestPaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBProxiesRequestPaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBProxiesResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBProxiesResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBProxyEndpointsPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeDBProxyEndpointsResponseTypeDef"}], "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.paginator.DescribeDBProxyEndpointsPaginator", "name": "DescribeDBProxyEndpointsPaginator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBProxyEndpointsPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.paginator", "mro": ["mypy_boto3_rds.paginator.DescribeDBProxyEndpointsPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBProxyEndpointsPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.paginator.DescribeDBProxyEndpointsPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBProxyEndpointsRequestPaginateTypeDef", "items": [["DBProxyName", "builtins.str"], ["DBProxyEndpointName", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PaginatorConfigTypeDef"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "paginate of DescribeDBProxyEndpointsPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeDBProxyEndpointsResponseTypeDef"}], "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.paginator.DescribeDBProxyEndpointsPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.paginator.DescribeDBProxyEndpointsPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DescribeDBProxyEndpointsRequestPaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBProxyEndpointsRequestPaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBProxyEndpointsResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBProxyEndpointsResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBProxyTargetGroupsPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeDBProxyTargetGroupsResponseTypeDef"}], "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.paginator.DescribeDBProxyTargetGroupsPaginator", "name": "DescribeDBProxyTargetGroupsPaginator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBProxyTargetGroupsPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.paginator", "mro": ["mypy_boto3_rds.paginator.DescribeDBProxyTargetGroupsPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBProxyTargetGroupsPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.paginator.DescribeDBProxyTargetGroupsPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBProxyTargetGroupsRequestPaginateTypeDef", "items": [["DBProxyName", "builtins.str"], ["TargetGroupName", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PaginatorConfigTypeDef"}]], "required_keys": ["DBProxyName"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "paginate of DescribeDBProxyTargetGroupsPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeDBProxyTargetGroupsResponseTypeDef"}], "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.paginator.DescribeDBProxyTargetGroupsPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.paginator.DescribeDBProxyTargetGroupsPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DescribeDBProxyTargetGroupsRequestPaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBProxyTargetGroupsRequestPaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBProxyTargetGroupsResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBProxyTargetGroupsResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBProxyTargetsPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeDBProxyTargetsResponseTypeDef"}], "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.paginator.DescribeDBProxyTargetsPaginator", "name": "DescribeDBProxyTargetsPaginator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBProxyTargetsPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.paginator", "mro": ["mypy_boto3_rds.paginator.DescribeDBProxyTargetsPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBProxyTargetsPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.paginator.DescribeDBProxyTargetsPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBProxyTargetsRequestPaginateTypeDef", "items": [["DBProxyName", "builtins.str"], ["TargetGroupName", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PaginatorConfigTypeDef"}]], "required_keys": ["DBProxyName"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "paginate of DescribeDBProxyTargetsPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeDBProxyTargetsResponseTypeDef"}], "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.paginator.DescribeDBProxyTargetsPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.paginator.DescribeDBProxyTargetsPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DescribeDBProxyTargetsRequestPaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBProxyTargetsRequestPaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBProxyTargetsResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBProxyTargetsResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBRecommendationsMessagePaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBRecommendationsMessagePaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBRecommendationsPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBRecommendationsMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.paginator.DescribeDBRecommendationsPaginator", "name": "DescribeDBRecommendationsPaginator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBRecommendationsPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.paginator", "mro": ["mypy_boto3_rds.paginator.DescribeDBRecommendationsPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBRecommendationsPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.paginator.DescribeDBRecommendationsPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBRecommendationsMessagePaginateTypeDef", "items": [["LastUpdatedAfter", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TimestampTypeDef"}], ["LastUpdatedBefore", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TimestampTypeDef"}], ["Locale", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PaginatorConfigTypeDef"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "paginate of DescribeDBRecommendationsPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBRecommendationsMessageTypeDef"}], "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.paginator.DescribeDBRecommendationsPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.paginator.DescribeDBRecommendationsPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DescribeDBSecurityGroupsMessagePaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBSecurityGroupsMessagePaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBSecurityGroupsPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBSecurityGroupMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.paginator.DescribeDBSecurityGroupsPaginator", "name": "DescribeDBSecurityGroupsPaginator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBSecurityGroupsPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.paginator", "mro": ["mypy_boto3_rds.paginator.DescribeDBSecurityGroupsPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBSecurityGroupsPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.paginator.DescribeDBSecurityGroupsPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBSecurityGroupsMessagePaginateTypeDef", "items": [["DBSecurityGroupName", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PaginatorConfigTypeDef"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "paginate of DescribeDBSecurityGroupsPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBSecurityGroupMessageTypeDef"}], "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.paginator.DescribeDBSecurityGroupsPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.paginator.DescribeDBSecurityGroupsPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DescribeDBSnapshotTenantDatabasesMessagePaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBSnapshotTenantDatabasesMessagePaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBSnapshotTenantDatabasesPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBSnapshotTenantDatabasesMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.paginator.DescribeDBSnapshotTenantDatabasesPaginator", "name": "DescribeDBSnapshotTenantDatabasesPaginator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBSnapshotTenantDatabasesPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.paginator", "mro": ["mypy_boto3_rds.paginator.DescribeDBSnapshotTenantDatabasesPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBSnapshotTenantDatabasesPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.paginator.DescribeDBSnapshotTenantDatabasesPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBSnapshotTenantDatabasesMessagePaginateTypeDef", "items": [["DBInstanceIdentifier", "builtins.str"], ["DBSnapshotIdentifier", "builtins.str"], ["SnapshotType", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["DbiResourceId", "builtins.str"], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PaginatorConfigTypeDef"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "paginate of DescribeDBSnapshotTenantDatabasesPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBSnapshotTenantDatabasesMessageTypeDef"}], "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.paginator.DescribeDBSnapshotTenantDatabasesPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.paginator.DescribeDBSnapshotTenantDatabasesPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DescribeDBSnapshotsMessagePaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBSnapshotsMessagePaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBSnapshotsPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBSnapshotMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.paginator.DescribeDBSnapshotsPaginator", "name": "DescribeDBSnapshotsPaginator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBSnapshotsPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.paginator", "mro": ["mypy_boto3_rds.paginator.DescribeDBSnapshotsPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBSnapshotsPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.paginator.DescribeDBSnapshotsPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBSnapshotsMessagePaginateTypeDef", "items": [["DBInstanceIdentifier", "builtins.str"], ["DBSnapshotIdentifier", "builtins.str"], ["SnapshotType", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["IncludeShared", "builtins.bool"], ["IncludePublic", "builtins.bool"], ["DbiResourceId", "builtins.str"], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PaginatorConfigTypeDef"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "paginate of DescribeDBSnapshotsPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBSnapshotMessageTypeDef"}], "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.paginator.DescribeDBSnapshotsPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.paginator.DescribeDBSnapshotsPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DescribeDBSubnetGroupsMessagePaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBSubnetGroupsMessagePaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBSubnetGroupsPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBSubnetGroupMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.paginator.DescribeDBSubnetGroupsPaginator", "name": "DescribeDBSubnetGroupsPaginator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBSubnetGroupsPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.paginator", "mro": ["mypy_boto3_rds.paginator.DescribeDBSubnetGroupsPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeDBSubnetGroupsPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.paginator.DescribeDBSubnetGroupsPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBSubnetGroupsMessagePaginateTypeDef", "items": [["DBSubnetGroupName", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PaginatorConfigTypeDef"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "paginate of DescribeDBSubnetGroupsPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBSubnetGroupMessageTypeDef"}], "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.paginator.DescribeDBSubnetGroupsPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.paginator.DescribeDBSubnetGroupsPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DescribeEngineDefaultClusterParametersMessagePaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeEngineDefaultClusterParametersMessagePaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeEngineDefaultClusterParametersPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeEngineDefaultClusterParametersResultTypeDef"}], "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.paginator.DescribeEngineDefaultClusterParametersPaginator", "name": "DescribeEngineDefaultClusterParametersPaginator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeEngineDefaultClusterParametersPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.paginator", "mro": ["mypy_boto3_rds.paginator.DescribeEngineDefaultClusterParametersPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeEngineDefaultClusterParametersPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.paginator.DescribeEngineDefaultClusterParametersPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeEngineDefaultClusterParametersMessagePaginateTypeDef", "items": [["DBParameterGroupFamily", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PaginatorConfigTypeDef"}]], "required_keys": ["DBParameterGroupFamily"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "paginate of DescribeEngineDefaultClusterParametersPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeEngineDefaultClusterParametersResultTypeDef"}], "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.paginator.DescribeEngineDefaultClusterParametersPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.paginator.DescribeEngineDefaultClusterParametersPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DescribeEngineDefaultClusterParametersResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeEngineDefaultClusterParametersResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeEngineDefaultParametersMessagePaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeEngineDefaultParametersMessagePaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeEngineDefaultParametersPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeEngineDefaultParametersResultTypeDef"}], "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.paginator.DescribeEngineDefaultParametersPaginator", "name": "DescribeEngineDefaultParametersPaginator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeEngineDefaultParametersPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.paginator", "mro": ["mypy_boto3_rds.paginator.DescribeEngineDefaultParametersPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeEngineDefaultParametersPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.paginator.DescribeEngineDefaultParametersPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeEngineDefaultParametersMessagePaginateTypeDef", "items": [["DBParameterGroupFamily", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PaginatorConfigTypeDef"}]], "required_keys": ["DBParameterGroupFamily"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "paginate of DescribeEngineDefaultParametersPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeEngineDefaultParametersResultTypeDef"}], "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.paginator.DescribeEngineDefaultParametersPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.paginator.DescribeEngineDefaultParametersPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DescribeEngineDefaultParametersResultTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeEngineDefaultParametersResultTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeEventSubscriptionsMessagePaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeEventSubscriptionsMessagePaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeEventSubscriptionsPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.EventSubscriptionsMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.paginator.DescribeEventSubscriptionsPaginator", "name": "DescribeEventSubscriptionsPaginator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeEventSubscriptionsPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.paginator", "mro": ["mypy_boto3_rds.paginator.DescribeEventSubscriptionsPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeEventSubscriptionsPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.paginator.DescribeEventSubscriptionsPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeEventSubscriptionsMessagePaginateTypeDef", "items": [["SubscriptionName", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PaginatorConfigTypeDef"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "paginate of DescribeEventSubscriptionsPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.EventSubscriptionsMessageTypeDef"}], "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.paginator.DescribeEventSubscriptionsPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.paginator.DescribeEventSubscriptionsPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DescribeEventsMessagePaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeEventsMessagePaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeEventsPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.EventsMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.paginator.DescribeEventsPaginator", "name": "DescribeEventsPaginator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeEventsPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.paginator", "mro": ["mypy_boto3_rds.paginator.DescribeEventsPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeEventsPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.paginator.DescribeEventsPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeEventsMessagePaginateTypeDef", "items": [["SourceIdentifier", "builtins.str"], ["SourceType", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.literals.SourceTypeType"}], ["StartTime", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TimestampTypeDef"}], ["EndTime", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TimestampTypeDef"}], ["Duration", "builtins.int"], ["EventCategories", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PaginatorConfigTypeDef"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "paginate of DescribeEventsPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.EventsMessageTypeDef"}], "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.paginator.DescribeEventsPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.paginator.DescribeEventsPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DescribeExportTasksMessagePaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeExportTasksMessagePaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeExportTasksPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ExportTasksMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.paginator.DescribeExportTasksPaginator", "name": "DescribeExportTasksPaginator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeExportTasksPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.paginator", "mro": ["mypy_boto3_rds.paginator.DescribeExportTasksPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeExportTasksPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.paginator.DescribeExportTasksPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeExportTasksMessagePaginateTypeDef", "items": [["ExportTaskIdentifier", "builtins.str"], ["SourceArn", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["SourceType", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.literals.ExportSourceTypeType"}], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PaginatorConfigTypeDef"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "paginate of DescribeExportTasksPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ExportTasksMessageTypeDef"}], "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.paginator.DescribeExportTasksPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.paginator.DescribeExportTasksPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DescribeGlobalClustersMessagePaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeGlobalClustersMessagePaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeGlobalClustersPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.GlobalClustersMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.paginator.DescribeGlobalClustersPaginator", "name": "DescribeGlobalClustersPaginator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeGlobalClustersPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.paginator", "mro": ["mypy_boto3_rds.paginator.DescribeGlobalClustersPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeGlobalClustersPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.paginator.DescribeGlobalClustersPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeGlobalClustersMessagePaginateTypeDef", "items": [["GlobalClusterIdentifier", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PaginatorConfigTypeDef"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "paginate of DescribeGlobalClustersPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.GlobalClustersMessageTypeDef"}], "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.paginator.DescribeGlobalClustersPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.paginator.DescribeGlobalClustersPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DescribeIntegrationsMessagePaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeIntegrationsMessagePaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeIntegrationsPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeIntegrationsResponseTypeDef"}], "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.paginator.DescribeIntegrationsPaginator", "name": "DescribeIntegrationsPaginator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeIntegrationsPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.paginator", "mro": ["mypy_boto3_rds.paginator.DescribeIntegrationsPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeIntegrationsPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.paginator.DescribeIntegrationsPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeIntegrationsMessagePaginateTypeDef", "items": [["IntegrationIdentifier", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PaginatorConfigTypeDef"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "paginate of DescribeIntegrationsPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeIntegrationsResponseTypeDef"}], "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.paginator.DescribeIntegrationsPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.paginator.DescribeIntegrationsPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DescribeIntegrationsResponseTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeIntegrationsResponseTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeOptionGroupOptionsMessagePaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeOptionGroupOptionsMessagePaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeOptionGroupOptionsPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.OptionGroupOptionsMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.paginator.DescribeOptionGroupOptionsPaginator", "name": "DescribeOptionGroupOptionsPaginator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeOptionGroupOptionsPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.paginator", "mro": ["mypy_boto3_rds.paginator.DescribeOptionGroupOptionsPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeOptionGroupOptionsPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.paginator.DescribeOptionGroupOptionsPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeOptionGroupOptionsMessagePaginateTypeDef", "items": [["EngineName", "builtins.str"], ["MajorEngineVersion", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PaginatorConfigTypeDef"}]], "required_keys": ["EngineName"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "paginate of DescribeOptionGroupOptionsPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.OptionGroupOptionsMessageTypeDef"}], "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.paginator.DescribeOptionGroupOptionsPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.paginator.DescribeOptionGroupOptionsPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DescribeOptionGroupsMessagePaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeOptionGroupsMessagePaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeOptionGroupsPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.OptionGroupsTypeDef"}], "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.paginator.DescribeOptionGroupsPaginator", "name": "DescribeOptionGroupsPaginator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeOptionGroupsPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.paginator", "mro": ["mypy_boto3_rds.paginator.DescribeOptionGroupsPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeOptionGroupsPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.paginator.DescribeOptionGroupsPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeOptionGroupsMessagePaginateTypeDef", "items": [["OptionGroupName", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["EngineName", "builtins.str"], ["MajorEngineVersion", "builtins.str"], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PaginatorConfigTypeDef"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "paginate of DescribeOptionGroupsPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.OptionGroupsTypeDef"}], "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.paginator.DescribeOptionGroupsPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.paginator.DescribeOptionGroupsPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DescribeOrderableDBInstanceOptionsMessagePaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeOrderableDBInstanceOptionsMessagePaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeOrderableDBInstanceOptionsPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.OrderableDBInstanceOptionsMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.paginator.DescribeOrderableDBInstanceOptionsPaginator", "name": "DescribeOrderableDBInstanceOptionsPaginator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeOrderableDBInstanceOptionsPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.paginator", "mro": ["mypy_boto3_rds.paginator.DescribeOrderableDBInstanceOptionsPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeOrderableDBInstanceOptionsPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.paginator.DescribeOrderableDBInstanceOptionsPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeOrderableDBInstanceOptionsMessagePaginateTypeDef", "items": [["Engine", "builtins.str"], ["EngineVersion", "builtins.str"], ["DBInstanceClass", "builtins.str"], ["LicenseModel", "builtins.str"], ["AvailabilityZoneGroup", "builtins.str"], ["Vpc", "builtins.bool"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PaginatorConfigTypeDef"}]], "required_keys": ["Engine"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "paginate of DescribeOrderableDBInstanceOptionsPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.OrderableDBInstanceOptionsMessageTypeDef"}], "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.paginator.DescribeOrderableDBInstanceOptionsPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.paginator.DescribeOrderableDBInstanceOptionsPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DescribePendingMaintenanceActionsMessagePaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribePendingMaintenanceActionsMessagePaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribePendingMaintenanceActionsPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PendingMaintenanceActionsMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.paginator.DescribePendingMaintenanceActionsPaginator", "name": "DescribePendingMaintenanceActionsPaginator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribePendingMaintenanceActionsPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.paginator", "mro": ["mypy_boto3_rds.paginator.DescribePendingMaintenanceActionsPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribePendingMaintenanceActionsPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.paginator.DescribePendingMaintenanceActionsPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribePendingMaintenanceActionsMessagePaginateTypeDef", "items": [["ResourceIdentifier", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PaginatorConfigTypeDef"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "paginate of DescribePendingMaintenanceActionsPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PendingMaintenanceActionsMessageTypeDef"}], "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.paginator.DescribePendingMaintenanceActionsPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.paginator.DescribePendingMaintenanceActionsPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DescribeReservedDBInstancesMessagePaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeReservedDBInstancesMessagePaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeReservedDBInstancesOfferingsMessagePaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeReservedDBInstancesOfferingsMessagePaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeReservedDBInstancesOfferingsPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ReservedDBInstancesOfferingMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.paginator.DescribeReservedDBInstancesOfferingsPaginator", "name": "DescribeReservedDBInstancesOfferingsPaginator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeReservedDBInstancesOfferingsPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.paginator", "mro": ["mypy_boto3_rds.paginator.DescribeReservedDBInstancesOfferingsPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeReservedDBInstancesOfferingsPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.paginator.DescribeReservedDBInstancesOfferingsPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeReservedDBInstancesOfferingsMessagePaginateTypeDef", "items": [["ReservedDBInstancesOfferingId", "builtins.str"], ["DBInstanceClass", "builtins.str"], ["Duration", "builtins.str"], ["ProductDescription", "builtins.str"], ["OfferingType", "builtins.str"], ["MultiAZ", "builtins.bool"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PaginatorConfigTypeDef"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "paginate of DescribeReservedDBInstancesOfferingsPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ReservedDBInstancesOfferingMessageTypeDef"}], "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.paginator.DescribeReservedDBInstancesOfferingsPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.paginator.DescribeReservedDBInstancesOfferingsPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DescribeReservedDBInstancesPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ReservedDBInstanceMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.paginator.DescribeReservedDBInstancesPaginator", "name": "DescribeReservedDBInstancesPaginator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeReservedDBInstancesPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.paginator", "mro": ["mypy_boto3_rds.paginator.DescribeReservedDBInstancesPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeReservedDBInstancesPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.paginator.DescribeReservedDBInstancesPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeReservedDBInstancesMessagePaginateTypeDef", "items": [["ReservedDBInstanceId", "builtins.str"], ["ReservedDBInstancesOfferingId", "builtins.str"], ["DBInstanceClass", "builtins.str"], ["Duration", "builtins.str"], ["ProductDescription", "builtins.str"], ["OfferingType", "builtins.str"], ["MultiAZ", "builtins.bool"], ["LeaseId", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PaginatorConfigTypeDef"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "paginate of DescribeReservedDBInstancesPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ReservedDBInstanceMessageTypeDef"}], "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.paginator.DescribeReservedDBInstancesPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.paginator.DescribeReservedDBInstancesPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DescribeSourceRegionsMessagePaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeSourceRegionsMessagePaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeSourceRegionsPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.SourceRegionMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.paginator.DescribeSourceRegionsPaginator", "name": "DescribeSourceRegionsPaginator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeSourceRegionsPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.paginator", "mro": ["mypy_boto3_rds.paginator.DescribeSourceRegionsPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeSourceRegionsPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.paginator.DescribeSourceRegionsPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeSourceRegionsMessagePaginateTypeDef", "items": [["RegionName", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PaginatorConfigTypeDef"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "paginate of DescribeSourceRegionsPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.SourceRegionMessageTypeDef"}], "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.paginator.DescribeSourceRegionsPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.paginator.DescribeSourceRegionsPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DescribeTenantDatabasesMessagePaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeTenantDatabasesMessagePaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeTenantDatabasesPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TenantDatabasesMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.paginator.DescribeTenantDatabasesPaginator", "name": "DescribeTenantDatabasesPaginator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeTenantDatabasesPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.paginator", "mro": ["mypy_boto3_rds.paginator.DescribeTenantDatabasesPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.paginator.DescribeTenantDatabasesPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.paginator.DescribeTenantDatabasesPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeTenantDatabasesMessagePaginateTypeDef", "items": [["DBInstanceIdentifier", "builtins.str"], ["TenantDBName", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PaginatorConfigTypeDef"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "paginate of DescribeTenantDatabasesPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TenantDatabasesMessageTypeDef"}], "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.paginator.DescribeTenantDatabasesPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.paginator.DescribeTenantDatabasesPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DownloadDBLogFilePortionDetailsTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DownloadDBLogFilePortionDetailsTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DownloadDBLogFilePortionMessagePaginateTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DownloadDBLogFilePortionMessagePaginateTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DownloadDBLogFilePortionPaginator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DownloadDBLogFilePortionDetailsTypeDef"}], "type_ref": "botocore.paginate.Paginator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.paginator.DownloadDBLogFilePortionPaginator", "name": "DownloadDBLogFilePortionPaginator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.paginator.DownloadDBLogFilePortionPaginator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.paginator", "mro": ["mypy_boto3_rds.paginator.DownloadDBLogFilePortionPaginator", "botocore.paginate.Paginator", "builtins.object"], "names": {".class": "SymbolTable", "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.paginator.DownloadDBLogFilePortionPaginator.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.paginator.DownloadDBLogFilePortionPaginator", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DownloadDBLogFilePortionMessagePaginateTypeDef", "items": [["DBInstanceIdentifier", "builtins.str"], ["LogFileName", "builtins.str"], ["PaginationConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PaginatorConfigTypeDef"}]], "required_keys": ["DBInstanceIdentifier", "LogFileName"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "paginate of DownloadDBLogFilePortionPaginator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DownloadDBLogFilePortionDetailsTypeDef"}], "type_ref": "botocore.paginate.PageIterator"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.paginator.DownloadDBLogFilePortionPaginator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.paginator.DownloadDBLogFilePortionPaginator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EventSubscriptionsMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.EventSubscriptionsMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "EventsMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.EventsMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ExportTasksMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ExportTasksMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "GlobalClustersMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.GlobalClustersMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "OptionGroupOptionsMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.OptionGroupOptionsMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "OptionGroupsTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.OptionGroupsTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "OrderableDBInstanceOptionsMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.OrderableDBInstanceOptionsMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "PageIterator": {".class": "SymbolTableNode", "cross_ref": "botocore.paginate.PageIterator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Paginator": {".class": "SymbolTableNode", "cross_ref": "botocore.paginate.Paginator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "PendingMaintenanceActionsMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.PendingMaintenanceActionsMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ReservedDBInstanceMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ReservedDBInstanceMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ReservedDBInstancesOfferingMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.ReservedDBInstancesOfferingMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SourceRegionMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.SourceRegionMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TenantDatabasesMessageTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.TenantDatabasesMessageTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Unpack": {".class": "SymbolTableNode", "cross_ref": "typing.Unpack", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_DescribeBlueGreenDeploymentsPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_rds.paginator._DescribeBlueGreenDeploymentsPaginatorBase", "line": 247, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeBlueGreenDeploymentsResponseTypeDef"}], "type_ref": "botocore.paginate.Paginator"}}}, "_DescribeCertificatesPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_rds.paginator._DescribeCertificatesPaginatorBase", "line": 267, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.CertificateMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}}}, "_DescribeDBClusterAutomatedBackupsPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_rds.paginator._DescribeDBClusterAutomatedBackupsPaginatorBase", "line": 285, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBClusterAutomatedBackupMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}}}, "_DescribeDBClusterBacktracksPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_rds.paginator._DescribeDBClusterBacktracksPaginatorBase", "line": 305, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBClusterBacktrackMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}}}, "_DescribeDBClusterEndpointsPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_rds.paginator._DescribeDBClusterEndpointsPaginatorBase", "line": 323, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBClusterEndpointMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}}}, "_DescribeDBClusterParameterGroupsPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_rds.paginator._DescribeDBClusterParameterGroupsPaginatorBase", "line": 341, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBClusterParameterGroupsMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}}}, "_DescribeDBClusterParametersPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_rds.paginator._DescribeDBClusterParametersPaginatorBase", "line": 361, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBClusterParameterGroupDetailsTypeDef"}], "type_ref": "botocore.paginate.Paginator"}}}, "_DescribeDBClusterSnapshotsPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_rds.paginator._DescribeDBClusterSnapshotsPaginatorBase", "line": 379, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBClusterSnapshotMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}}}, "_DescribeDBClustersPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_rds.paginator._DescribeDBClustersPaginatorBase", "line": 397, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBClusterMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}}}, "_DescribeDBEngineVersionsPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_rds.paginator._DescribeDBEngineVersionsPaginatorBase", "line": 415, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBEngineVersionMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}}}, "_DescribeDBInstanceAutomatedBackupsPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_rds.paginator._DescribeDBInstanceAutomatedBackupsPaginatorBase", "line": 433, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBInstanceAutomatedBackupMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}}}, "_DescribeDBInstancesPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_rds.paginator._DescribeDBInstancesPaginatorBase", "line": 453, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBInstanceMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}}}, "_DescribeDBLogFilesPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_rds.paginator._DescribeDBLogFilesPaginatorBase", "line": 471, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeDBLogFilesResponseTypeDef"}], "type_ref": "botocore.paginate.Paginator"}}}, "_DescribeDBMajorEngineVersionsPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_rds.paginator._DescribeDBMajorEngineVersionsPaginatorBase", "line": 489, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeDBMajorEngineVersionsResponseTypeDef"}], "type_ref": "botocore.paginate.Paginator"}}}, "_DescribeDBParameterGroupsPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_rds.paginator._DescribeDBParameterGroupsPaginatorBase", "line": 509, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBParameterGroupsMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}}}, "_DescribeDBParametersPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_rds.paginator._DescribeDBParametersPaginatorBase", "line": 527, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBParameterGroupDetailsTypeDef"}], "type_ref": "botocore.paginate.Paginator"}}}, "_DescribeDBProxiesPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_rds.paginator._DescribeDBProxiesPaginatorBase", "line": 545, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeDBProxiesResponseTypeDef"}], "type_ref": "botocore.paginate.Paginator"}}}, "_DescribeDBProxyEndpointsPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_rds.paginator._DescribeDBProxyEndpointsPaginatorBase", "line": 563, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeDBProxyEndpointsResponseTypeDef"}], "type_ref": "botocore.paginate.Paginator"}}}, "_DescribeDBProxyTargetGroupsPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_rds.paginator._DescribeDBProxyTargetGroupsPaginatorBase", "line": 581, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeDBProxyTargetGroupsResponseTypeDef"}], "type_ref": "botocore.paginate.Paginator"}}}, "_DescribeDBProxyTargetsPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_rds.paginator._DescribeDBProxyTargetsPaginatorBase", "line": 601, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeDBProxyTargetsResponseTypeDef"}], "type_ref": "botocore.paginate.Paginator"}}}, "_DescribeDBRecommendationsPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_rds.paginator._DescribeDBRecommendationsPaginatorBase", "line": 619, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBRecommendationsMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}}}, "_DescribeDBSecurityGroupsPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_rds.paginator._DescribeDBSecurityGroupsPaginatorBase", "line": 637, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBSecurityGroupMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}}}, "_DescribeDBSnapshotTenantDatabasesPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_rds.paginator._DescribeDBSnapshotTenantDatabasesPaginatorBase", "line": 655, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBSnapshotTenantDatabasesMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}}}, "_DescribeDBSnapshotsPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_rds.paginator._DescribeDBSnapshotsPaginatorBase", "line": 675, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBSnapshotMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}}}, "_DescribeDBSubnetGroupsPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_rds.paginator._DescribeDBSubnetGroupsPaginatorBase", "line": 693, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DBSubnetGroupMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}}}, "_DescribeEngineDefaultClusterParametersPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_rds.paginator._DescribeEngineDefaultClusterParametersPaginatorBase", "line": 711, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeEngineDefaultClusterParametersResultTypeDef"}], "type_ref": "botocore.paginate.Paginator"}}}, "_DescribeEngineDefaultParametersPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_rds.paginator._DescribeEngineDefaultParametersPaginatorBase", "line": 733, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeEngineDefaultParametersResultTypeDef"}], "type_ref": "botocore.paginate.Paginator"}}}, "_DescribeEventSubscriptionsPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_rds.paginator._DescribeEventSubscriptionsPaginatorBase", "line": 753, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.EventSubscriptionsMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}}}, "_DescribeEventsPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_rds.paginator._DescribeEventsPaginatorBase", "line": 771, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.EventsMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}}}, "_DescribeExportTasksPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_rds.paginator._DescribeExportTasksPaginatorBase", "line": 789, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ExportTasksMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}}}, "_DescribeGlobalClustersPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_rds.paginator._DescribeGlobalClustersPaginatorBase", "line": 807, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.GlobalClustersMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}}}, "_DescribeIntegrationsPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_rds.paginator._DescribeIntegrationsPaginatorBase", "line": 825, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DescribeIntegrationsResponseTypeDef"}], "type_ref": "botocore.paginate.Paginator"}}}, "_DescribeOptionGroupOptionsPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_rds.paginator._DescribeOptionGroupOptionsPaginatorBase", "line": 843, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.OptionGroupOptionsMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}}}, "_DescribeOptionGroupsPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_rds.paginator._DescribeOptionGroupsPaginatorBase", "line": 861, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.OptionGroupsTypeDef"}], "type_ref": "botocore.paginate.Paginator"}}}, "_DescribeOrderableDBInstanceOptionsPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_rds.paginator._DescribeOrderableDBInstanceOptionsPaginatorBase", "line": 879, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.OrderableDBInstanceOptionsMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}}}, "_DescribePendingMaintenanceActionsPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_rds.paginator._DescribePendingMaintenanceActionsPaginatorBase", "line": 899, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.PendingMaintenanceActionsMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}}}, "_DescribeReservedDBInstancesOfferingsPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_rds.paginator._DescribeReservedDBInstancesOfferingsPaginatorBase", "line": 919, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ReservedDBInstancesOfferingMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}}}, "_DescribeReservedDBInstancesPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_rds.paginator._DescribeReservedDBInstancesPaginatorBase", "line": 941, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.ReservedDBInstanceMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}}}, "_DescribeSourceRegionsPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_rds.paginator._DescribeSourceRegionsPaginatorBase", "line": 959, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.SourceRegionMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}}}, "_DescribeTenantDatabasesPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_rds.paginator._DescribeTenantDatabasesPaginatorBase", "line": 977, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.TenantDatabasesMessageTypeDef"}], "type_ref": "botocore.paginate.Paginator"}}}, "_DownloadDBLogFilePortionPaginatorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "mypy_boto3_rds.paginator._DownloadDBLogFilePortionPaginatorBase", "line": 995, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.DownloadDBLogFilePortionDetailsTypeDef"}], "type_ref": "botocore.paginate.Paginator"}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "mypy_boto3_rds.paginator.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mypy_boto3_rds.paginator.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mypy_boto3_rds.paginator.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mypy_boto3_rds.paginator.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mypy_boto3_rds.paginator.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mypy_boto3_rds.paginator.__package__", "name": "__package__", "type": "builtins.str"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\mypy_boto3_rds\\paginator.pyi"}