{"data_mtime": 1759503828, "dep_lines": [6, 7, 8, 9, 10, 11, 1, 2, 3, 4, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 5, 20, 20, 30, 30, 30], "dependencies": ["pydantic.v1.config", "pydantic.v1.fields", "pydantic.v1.main", "pydantic.v1.types", "pydantic.v1.typing", "pydantic.v1.utils", "os", "warnings", "pathlib", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "abc", "enum"], "hash": "03d557c2d465d36018fa31f40b4a2ecb954dc377e2e85fe9933b870e380000e3", "id": "pydantic.v1.env_settings", "ignore_all": true, "interface_hash": "143b3092bd316738a5e3b543de9f5bc08d3cf1fac6c54868c6d980888699e85c", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\v1\\env_settings.py", "plugin_data": null, "size": 14105, "suppressed": [], "version_id": "1.8.0"}