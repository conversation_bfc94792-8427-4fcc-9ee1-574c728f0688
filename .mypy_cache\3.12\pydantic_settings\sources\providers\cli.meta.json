{"data_mtime": **********, "dep_lines": [60, 39, 40, 50, 51, 19, 41, 42, 45, 46, 48, 49, 63, 3, 5, 6, 7, 8, 9, 10, 18, 20, 21, 22, 37, 38, 43, 45, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 25, 5, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic_settings.sources.providers.env", "pydantic._internal._repr", "pydantic._internal._utils", "pydantic_settings.sources.types", "pydantic_settings.sources.utils", "collections.abc", "pydantic.dataclasses", "pydantic.fields", "typing_inspection.typing_objects", "typing_inspection.introspection", "pydantic_settings.exceptions", "pydantic_settings.utils", "pydantic_settings.main", "__future__", "json", "re", "shlex", "sys", "typing", "<PERSON><PERSON><PERSON><PERSON>", "collections", "enum", "textwrap", "types", "typing_extensions", "pydantic", "pydantic_core", "typing_inspection", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "_typeshed", "abc", "os", "pathlib", "pydantic._internal", "pydantic._internal._generate_schema", "pydantic._internal._model_construction", "pydantic.aliases", "pydantic.main", "pydantic_core._pydantic_core", "pydantic_settings.sources.base"], "hash": "cd4a4996b8c63668daa144afa937ee33bbbf505341e4d7f9a6c464865e51a323", "id": "pydantic_settings.sources.providers.cli", "ignore_all": true, "interface_hash": "36cf7585bd37df927f3d7a8b4de474561c7740ec75489db171de5809e7bd6c5b", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic_settings\\sources\\providers\\cli.py", "plugin_data": null, "size": 51158, "suppressed": [], "version_id": "1.8.0"}