{"data_mtime": 1759503866, "dep_lines": [25, 27, 30, 24, 10, 12, 13, 14, 15, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 25, 5, 5, 10, 10, 10, 5, 5, 20, 20, 30, 30, 30, 30], "dependencies": ["sqlalchemy.util.concurrency", "sqlalchemy.util.typing", "sqlalchemy.engine.interfaces", "sqlalchemy.engine", "__future__", "asyncio", "collections", "sys", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "asyncio.locks", "sqlalchemy.util", "sqlalchemy.util._concurrency_py3k"], "hash": "3a15ed14250bd471abbc4a9f96986c28642fe4e206a94a2ea3e54fe72e94b9c3", "id": "sqlalchemy.connectors.asyncio", "ignore_all": true, "interface_hash": "f87b2d19646fd9aafc320826d610059be188166f6e886f5024dd3eb86b42f276", "mtime": 1759106563, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\connectors\\asyncio.py", "plugin_data": null, "size": 10473, "suppressed": [], "version_id": "1.8.0"}