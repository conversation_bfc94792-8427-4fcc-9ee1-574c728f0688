{"data_mtime": 1759503836, "dep_lines": [10, 10, 10, 10, 11, 16, 3, 5, 6, 7, 8, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 20, 5, 25, 5, 10, 5, 5, 5, 5, 20, 20, 30], "dependencies": ["pydantic._internal._generate_schema", "pydantic._internal._typing_extra", "pydantic._internal._validate_call", "pydantic._internal", "pydantic.errors", "pydantic.config", "__future__", "inspect", "functools", "types", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "abc"], "hash": "f23a8b9605d38d6123e1d5c3834c08dc41902a46f42670ac2ff2525236d4e528", "id": "pydantic.validate_call_decorator", "ignore_all": true, "interface_hash": "2945531a7d1d8ba11508e481027153f724c5e9cec7cd58e0dbed1b3f970c2efc", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\validate_call_decorator.py", "plugin_data": null, "size": 4389, "suppressed": [], "version_id": "1.8.0"}