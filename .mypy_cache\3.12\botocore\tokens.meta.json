{"data_mtime": 1759504549, "dep_lines": [9, 12, 13, 7, 8, 10, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 5, 5, 20, 20, 30], "dependencies": ["collections.abc", "botocore.session", "botocore.utils", "datetime", "logging", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "abc"], "hash": "582b9dfc32bb3cab5648821a1d6c27fd7352a9c986e82d3861771ee4488bbfa6", "id": "botocore.tokens", "ignore_all": true, "interface_hash": "3df6766412f92ec7bbf8a41c5ced4ec0727dc5f77386ba3ea051acfeb2a2aaa1", "mtime": 1757092235, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\botocore-stubs\\tokens.pyi", "plugin_data": null, "size": 1510, "suppressed": [], "version_id": "1.8.0"}