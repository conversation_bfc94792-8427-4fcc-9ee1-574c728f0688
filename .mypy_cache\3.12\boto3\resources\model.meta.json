{"data_mtime": 1759504551, "dep_lines": [10, 7, 8, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 20, 20, 30, 30], "dependencies": ["botocore.model", "logging", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "botocore"], "hash": "929a041955bd1f058b0b5fe9438a4fdf221d6634459daa5658a1030ea9d6686f", "id": "boto3.resources.model", "ignore_all": true, "interface_hash": "a79fbcb148b7db3be7b3b3815250c11b29ec942e0df9cdb4c452c7996dca5675", "mtime": 1757092242, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\boto3-stubs\\resources\\model.pyi", "plugin_data": null, "size": 3348, "suppressed": [], "version_id": "1.8.0"}