{"data_mtime": 1759503833, "dep_lines": [13, 6, 7, 10, 11, 12, 1, 3, 4, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 5, 5, 25, 25, 25, 5, 10, 10, 10, 5, 20, 20, 30, 30, 30, 30, 30, 30], "dependencies": ["urllib3.util.retry", "email.errors", "http.client", "urllib3.connection", "urllib3.connectionpool", "urllib3.response", "__future__", "socket", "typing", "warnings", "builtins", "pyexpat.model", "pyexpat.errors", "_socket", "abc", "email", "http", "io", "urllib3.util"], "hash": "a738ae9877f4570c77cf882f532ee8bbc9e5336c88617d0dde5df39c475e1795", "id": "urllib3.exceptions", "ignore_all": true, "interface_hash": "d99a5ad18806e66f5614326da7098a31f5878b8f323e7c22c93468d2085517bc", "mtime": 1757091871, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\urllib3\\exceptions.py", "plugin_data": null, "size": 9938, "suppressed": [], "version_id": "1.8.0"}