{"data_mtime": 1759503828, "dep_lines": [261, 3, 1, 2, 4, 30, 50, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 5, 10, 5, 5, 5, 5, 5, 20, 20, 30, 30, 30], "dependencies": ["pydantic.v1.fields", "collections.abc", "sys", "typing", "os", "typing_extensions", "types", "builtins", "pyexpat.model", "pyexpat.errors", "_typeshed", "abc", "pydantic.v1.utils"], "hash": "1a34e139b6aa1cc84b6840b3614ac39345fe4478e8ef1eafb2fe19e2a51857c2", "id": "pydantic.v1.typing", "ignore_all": true, "interface_hash": "c2a4ede01d0b716e198639ae3e4bc08881eb468494290c66ef45e95a2589a9a7", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\v1\\typing.py", "plugin_data": null, "size": 19387, "suppressed": [], "version_id": "1.8.0"}