{"data_mtime": 1759503830, "dep_lines": [12, 13, 18, 23, 24, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 20, 20, 30, 30], "dependencies": ["smartanalytics_domain.models.agent", "smartanalytics_domain.models.agent_event", "smartanalytics_domain.models.processing_result", "smartanalytics_domain.models.ring_group", "smartanalytics_domain.models.tenant", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "typing"], "hash": "cad3d79d03b31542747f02c349d5f0b9f11f10d5dfbd9f4c5a7bfda0c6cb163e", "id": "smartanalytics_domain.models", "ignore_all": false, "interface_hash": "4f57e963b94979b527fa63a65fad11a56bc476949775e4b8e21ecb481969856a", "mtime": 1759501803, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "layers\\domain\\python\\smartanalytics_domain\\models\\__init__.py", "plugin_data": null, "size": 919, "suppressed": [], "version_id": "1.8.0"}