{"data_mtime": 1759503869, "dep_lines": [7, 11, 12, 13, 15, 8, 9, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 30, 30], "dependencies": ["collections.abc", "botocore.exceptions", "botocore.model", "botocore.utils", "jmespath.parser", "logging", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "jmespath"], "hash": "2b8acbc9d45a8fd538910c83e691c10bf71f6c664d54d19d62276a007edc598c", "id": "botocore.paginate", "ignore_all": true, "interface_hash": "5495169b0562b4a6e0e579e45dc16742db0912ea6c1e4282d1df5d71ae524b3d", "mtime": 1757092235, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\botocore-stubs\\paginate.pyi", "plugin_data": null, "size": 2314, "suppressed": [], "version_id": "1.8.0"}