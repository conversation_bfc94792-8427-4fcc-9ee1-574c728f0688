{"data_mtime": 1759503829, "dep_lines": [5, 6, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 20, 20, 30, 30, 30], "dependencies": ["markdown_it.common.utils", "markdown_it.rules_inline.state_inline", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "markdown_it.ruler", "typing"], "hash": "286ba5c2b3f9167a9933b19763c95fee9c95bf4624479f6d6990de1dbe5c98a8", "id": "markdown_it.rules_inline.escape", "ignore_all": true, "interface_hash": "693306c46420a6f619c70e6303d4a7f986c8443f73aac45c39ffe425bceaa4d1", "mtime": 1757092234, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\markdown_it\\rules_inline\\escape.py", "plugin_data": null, "size": 1659, "suppressed": [], "version_id": "1.8.0"}