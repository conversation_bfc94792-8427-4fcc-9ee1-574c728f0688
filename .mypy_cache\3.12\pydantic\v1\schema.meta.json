{"data_mtime": 1759503828, "dep_lines": [34, 49, 50, 51, 72, 83, 86, 87, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 30, 32, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 25, 25, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 30, 30, 30, 30, 30], "dependencies": ["pydantic.v1.fields", "pydantic.v1.json", "pydantic.v1.networks", "pydantic.v1.types", "pydantic.v1.typing", "pydantic.v1.utils", "pydantic.v1.dataclasses", "pydantic.v1.main", "re", "warnings", "collections", "dataclasses", "datetime", "decimal", "enum", "ipaddress", "pathlib", "typing", "uuid", "typing_extensions", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "_decimal", "_typeshed", "abc", "os"], "hash": "6aa06e03ef9cabc8005648a6e4124f140487cce67c745b66157fdf346afa8a9e", "id": "pydantic.v1.schema", "ignore_all": true, "interface_hash": "5ee51c0bde8a01ba1bf0c34012f1f93a489d030f6ac696ebb19449558cb0ece5", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\v1\\schema.py", "plugin_data": null, "size": 47801, "suppressed": [], "version_id": "1.8.0"}