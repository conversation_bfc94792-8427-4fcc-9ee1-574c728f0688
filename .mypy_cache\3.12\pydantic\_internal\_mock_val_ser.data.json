{".class": "MypyFile", "_fullname": "pydantic._internal._mock_val_ser", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "pydantic.main.BaseModel", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "CoreSchema": {".class": "SymbolTableNode", "cross_ref": "pydantic_core.core_schema.CoreSchema", "kind": "Gdef"}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "MockCoreSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic._internal._mock_val_ser.MockCoreSchema", "name": "MockCoreSchema", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic._internal._mock_val_ser.MockCoreSchema", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic._internal._mock_val_ser", "mro": ["pydantic._internal._mock_val_ser.MockCoreSchema", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._mock_val_ser.MockCoreSchema.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pydantic._internal._mock_val_ser.MockCoreSchema", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of MockCoreSchema", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 5], "arg_names": ["self", "error_message", "code", "attempt_rebuild"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._mock_val_ser.MockCoreSchema.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 5], "arg_names": ["self", "error_message", "code", "attempt_rebuild"], "arg_types": ["pydantic._internal._mock_val_ser.MockCoreSchema", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.errors.PydanticErrorCodes"}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MockCoreSchema", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._mock_val_ser.MockCoreSchema.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pydantic._internal._mock_val_ser.MockCoreSchema"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iter__ of MockCoreSchema", "ret_type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._mock_val_ser.MockCoreSchema.__len__", "name": "__len__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pydantic._internal._mock_val_ser.MockCoreSchema"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__len__ of MockCoreSchema", "ret_type": "builtins.int", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "pydantic._internal._mock_val_ser.MockCoreSchema.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_attempt_rebuild": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic._internal._mock_val_ser.MockCoreSchema._attempt_rebuild", "name": "_attempt_rebuild", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}}}, "_built_memo": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic._internal._mock_val_ser.MockCoreSchema._built_memo", "name": "_built_memo", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "NoneType"}]}}}, "_code": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic._internal._mock_val_ser.MockCoreSchema._code", "name": "_code", "type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.errors.PydanticErrorCodes"}}}, "_error_message": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic._internal._mock_val_ser.MockCoreSchema._error_message", "name": "_error_message", "type": "builtins.str"}}, "_get_built": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._mock_val_ser.MockCoreSchema._get_built", "name": "_get_built", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic._internal._mock_val_ser.MockCoreSchema"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_built of MockCoreSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "rebuild": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._mock_val_ser.MockCoreSchema.rebuild", "name": "rebuild", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic._internal._mock_val_ser.MockCoreSchema"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rebuild of MockCoreSchema", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._mock_val_ser.MockCoreSchema.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic._internal._mock_val_ser.MockCoreSchema", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MockValSer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic._internal._mock_val_ser.MockValSer", "name": "MockVal<PERSON>er", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._mock_val_ser.Val<PERSON>er", "id": 1, "name": "ValSer", "namespace": "pydantic._internal._mock_val_ser.MockValSer", "upper_bound": {".class": "UnionType", "items": ["pydantic_core._pydantic_core.SchemaValidator", "pydantic.plugin._schema_validator.PluggableSchemaValidator", "pydantic_core._pydantic_core.SchemaSerializer"]}, "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "pydantic._internal._mock_val_ser.MockValSer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic._internal._mock_val_ser", "mro": ["pydantic._internal._mock_val_ser.MockValSer", "builtins.object"], "names": {".class": "SymbolTable", "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._mock_val_ser.<PERSON>ckValSer.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._mock_val_ser.Val<PERSON>er", "id": 1, "name": "ValSer", "namespace": "pydantic._internal._mock_val_ser.MockValSer", "upper_bound": {".class": "UnionType", "items": ["pydantic_core._pydantic_core.SchemaValidator", "pydantic.plugin._schema_validator.PluggableSchemaValidator", "pydantic_core._pydantic_core.SchemaSerializer"]}, "values": [], "variance": 0}], "type_ref": "pydantic._internal._mock_val_ser.MockValSer"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattr__ of MockValSer", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3, 5], "arg_names": ["self", "error_message", "code", "val_or_ser", "attempt_rebuild"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._mock_val_ser.MockValSer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3, 5], "arg_names": ["self", "error_message", "code", "val_or_ser", "attempt_rebuild"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._mock_val_ser.Val<PERSON>er", "id": 1, "name": "ValSer", "namespace": "pydantic._internal._mock_val_ser.MockValSer", "upper_bound": {".class": "UnionType", "items": ["pydantic_core._pydantic_core.SchemaValidator", "pydantic.plugin._schema_validator.PluggableSchemaValidator", "pydantic_core._pydantic_core.SchemaSerializer"]}, "values": [], "variance": 0}], "type_ref": "pydantic._internal._mock_val_ser.MockValSer"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.errors.PydanticErrorCodes"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "validator"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "serializer"}]}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._mock_val_ser.Val<PERSON>er", "id": 1, "name": "ValSer", "namespace": "pydantic._internal._mock_val_ser.MockValSer", "upper_bound": {".class": "UnionType", "items": ["pydantic_core._pydantic_core.SchemaValidator", "pydantic.plugin._schema_validator.PluggableSchemaValidator", "pydantic_core._pydantic_core.SchemaSerializer"]}, "values": [], "variance": 0}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MockValSer", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "pydantic._internal._mock_val_ser.MockValSer.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_attempt_rebuild": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic._internal._mock_val_ser.MockValSer._attempt_rebuild", "name": "_attempt_rebuild", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._mock_val_ser.Val<PERSON>er", "id": 1, "name": "ValSer", "namespace": "pydantic._internal._mock_val_ser.MockValSer", "upper_bound": {".class": "UnionType", "items": ["pydantic_core._pydantic_core.SchemaValidator", "pydantic.plugin._schema_validator.PluggableSchemaValidator", "pydantic_core._pydantic_core.SchemaSerializer"]}, "values": [], "variance": 0}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}}}, "_code": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic._internal._mock_val_ser.MockValSer._code", "name": "_code", "type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.errors.PydanticErrorCodes"}}}, "_error_message": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic._internal._mock_val_ser.MockValSer._error_message", "name": "_error_message", "type": "builtins.str"}}, "_val_or_ser": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic._internal._mock_val_ser.MockValSer._val_or_ser", "name": "_val_or_ser", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["schema", "config"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreConfig"}, {".class": "NoneType"}]}], "bound_args": ["pydantic_core._pydantic_core.SchemaSerializer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.object", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "rebuild": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._mock_val_ser.MockValSer.rebuild", "name": "rebuild", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._mock_val_ser.Val<PERSON>er", "id": 1, "name": "ValSer", "namespace": "pydantic._internal._mock_val_ser.MockValSer", "upper_bound": {".class": "UnionType", "items": ["pydantic_core._pydantic_core.SchemaValidator", "pydantic.plugin._schema_validator.PluggableSchemaValidator", "pydantic_core._pydantic_core.SchemaSerializer"]}, "values": [], "variance": 0}], "type_ref": "pydantic._internal._mock_val_ser.MockValSer"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rebuild of MockValSer", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._mock_val_ser.Val<PERSON>er", "id": 1, "name": "ValSer", "namespace": "pydantic._internal._mock_val_ser.MockValSer", "upper_bound": {".class": "UnionType", "items": ["pydantic_core._pydantic_core.SchemaValidator", "pydantic.plugin._schema_validator.PluggableSchemaValidator", "pydantic_core._pydantic_core.SchemaSerializer"]}, "values": [], "variance": 0}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._mock_val_ser.MockValSer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._mock_val_ser.Val<PERSON>er", "id": 1, "name": "ValSer", "namespace": "pydantic._internal._mock_val_ser.MockValSer", "upper_bound": {".class": "UnionType", "items": ["pydantic_core._pydantic_core.SchemaValidator", "pydantic.plugin._schema_validator.PluggableSchemaValidator", "pydantic_core._pydantic_core.SchemaSerializer"]}, "values": [], "variance": 0}], "type_ref": "pydantic._internal._mock_val_ser.MockValSer"}, "values": [], "variance": 0}, "slots": ["_attempt_rebuild", "_code", "_error_message", "_val_or_ser"], "tuple_type": null, "type_vars": ["ValSer"], "typeddict_type": null}}, "PluggableSchemaValidator": {".class": "SymbolTableNode", "cross_ref": "pydantic.plugin._schema_validator.PluggableSchemaValidator", "kind": "Gdef"}, "PydanticDataclass": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._dataclasses.PydanticDataclass", "kind": "Gdef"}, "PydanticErrorCodes": {".class": "SymbolTableNode", "cross_ref": "pydantic.errors.PydanticErrorCodes", "kind": "Gdef"}, "PydanticUserError": {".class": "SymbolTableNode", "cross_ref": "pydantic.errors.PydanticUserError", "kind": "Gdef"}, "SchemaSerializer": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.SchemaSerializer", "kind": "Gdef"}, "SchemaValidator": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.SchemaValidator", "kind": "Gdef"}, "T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._mock_val_ser.T", "name": "T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TypeAdapter": {".class": "SymbolTableNode", "cross_ref": "pydantic.type_adapter.TypeAdapter", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "ValSer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._mock_val_ser.Val<PERSON>er", "name": "ValSer", "upper_bound": {".class": "UnionType", "items": ["pydantic_core._pydantic_core.SchemaValidator", "pydantic.plugin._schema_validator.PluggableSchemaValidator", "pydantic_core._pydantic_core.SchemaSerializer"]}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._mock_val_ser.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._mock_val_ser.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._mock_val_ser.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._mock_val_ser.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._mock_val_ser.__package__", "name": "__package__", "type": "builtins.str"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "set_dataclass_mocks": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["cls", "undefined_name"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._mock_val_ser.set_dataclass_mocks", "name": "set_dataclass_mocks", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["cls", "undefined_name"], "arg_types": [{".class": "TypeType", "item": "pydantic._internal._dataclasses.PydanticDataclass"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_dataclass_mocks", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "set_model_mocks": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["cls", "undefined_name"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._mock_val_ser.set_model_mocks", "name": "set_model_mocks", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["cls", "undefined_name"], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_model_mocks", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "set_type_adapter_mocks": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["adapter"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._mock_val_ser.set_type_adapter_mocks", "name": "set_type_adapter_mocks", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["adapter"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "pydantic.type_adapter.TypeAdapter"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_type_adapter_mocks", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\_internal\\_mock_val_ser.py"}