{".class": "MypyFile", "_fullname": "rich.syntax", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ABC": {".class": "SymbolTableNode", "cross_ref": "abc.ABC", "kind": "Gdef"}, "ANSISyntaxTheme": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["rich.syntax.SyntaxTheme"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.syntax.ANSISyntaxTheme", "name": "ANSISyntaxTheme", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "rich.syntax.ANSISyntaxTheme", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "rich.syntax", "mro": ["rich.syntax.ANSISyntaxTheme", "rich.syntax.SyntaxTheme", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "style_map"], "dataclass_transform_spec": null, "flags": [], "fullname": "rich.syntax.ANSISyntaxTheme.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "style_map"], "arg_types": ["rich.syntax.ANSISyntaxTheme", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.TokenType"}, "rich.style.Style"], "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ANSISyntaxTheme", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_background_style": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.syntax.ANSISyntaxTheme._background_style", "name": "_background_style", "type": "rich.style.Style"}}, "_missing_style": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.syntax.ANSISyntaxTheme._missing_style", "name": "_missing_style", "type": "rich.style.Style"}}, "_style_cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "rich.syntax.ANSISyntaxTheme._style_cache", "name": "_style_cache", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.TokenType"}, "rich.style.Style"], "type_ref": "builtins.dict"}}}, "get_background_style": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "rich.syntax.ANSISyntaxTheme.get_background_style", "name": "get_background_style", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.syntax.ANSISyntaxTheme"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_background_style of ANSISyntaxTheme", "ret_type": "rich.style.Style", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_style_for_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "token_type"], "dataclass_transform_spec": null, "flags": [], "fullname": "rich.syntax.ANSISyntaxTheme.get_style_for_token", "name": "get_style_for_token", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "token_type"], "arg_types": ["rich.syntax.ANSISyntaxTheme", {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.TokenType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_style_for_token of ANSISyntaxTheme", "ret_type": "rich.style.Style", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "style_map": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.syntax.ANSISyntaxTheme.style_map", "name": "style_map", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.TokenType"}, "rich.style.Style"], "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.syntax.ANSISyntaxTheme.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.syntax.ANSISyntaxTheme", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ANSI_DARK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "rich.syntax.ANSI_DARK", "name": "ANSI_DARK", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.TokenType"}, "rich.style.Style"], "type_ref": "builtins.dict"}}}, "ANSI_LIGHT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "rich.syntax.ANSI_LIGHT", "name": "ANSI_LIGHT", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.TokenType"}, "rich.style.Style"], "type_ref": "builtins.dict"}}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ClassNotFound": {".class": "SymbolTableNode", "cross_ref": "pygments.util.ClassNotFound", "kind": "Gdef"}, "Color": {".class": "SymbolTableNode", "cross_ref": "rich.color.Color", "kind": "Gdef"}, "Comment": {".class": "SymbolTableNode", "cross_ref": "pygments.token.Comment", "kind": "Gdef"}, "Console": {".class": "SymbolTableNode", "cross_ref": "rich.console.Console", "kind": "Gdef"}, "ConsoleOptions": {".class": "SymbolTableNode", "cross_ref": "rich.console.ConsoleOptions", "kind": "Gdef"}, "DEFAULT_THEME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "rich.syntax.DEFAULT_THEME", "name": "DEFAULT_THEME", "type": "builtins.str"}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Error": {".class": "SymbolTableNode", "cross_ref": "pygments.token.Error", "kind": "Gdef"}, "Generic": {".class": "SymbolTableNode", "cross_ref": "pygments.token.Generic", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "JupyterMixin": {".class": "SymbolTableNode", "cross_ref": "rich.jupyter.Ju<PERSON>", "kind": "Gdef"}, "JustifyMethod": {".class": "SymbolTableNode", "cross_ref": "rich.console.JustifyMethod", "kind": "Gdef"}, "Keyword": {".class": "SymbolTableNode", "cross_ref": "pygments.token.Keyword", "kind": "Gdef"}, "Lexer": {".class": "SymbolTableNode", "cross_ref": "pygments.lexer.<PERSON>er", "kind": "Gdef"}, "Lines": {".class": "SymbolTableNode", "cross_ref": "rich.containers.Lines", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Measurement": {".class": "SymbolTableNode", "cross_ref": "rich.measure.Measurement", "kind": "Gdef"}, "NUMBERS_COLUMN_DEFAULT_PADDING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "rich.syntax.NUMBERS_COLUMN_DEFAULT_PADDING", "name": "NUMBERS_COLUMN_DEFAULT_PADDING", "type": "builtins.int"}}, "Name": {".class": "SymbolTableNode", "cross_ref": "pygments.token.Name", "kind": "Gdef"}, "NamedTuple": {".class": "SymbolTableNode", "cross_ref": "typing.NamedTuple", "kind": "Gdef"}, "Number": {".class": "SymbolTableNode", "cross_ref": "pygments.token.Number", "kind": "Gdef"}, "Operator": {".class": "SymbolTableNode", "cross_ref": "pygments.token.Operator", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Padding": {".class": "SymbolTableNode", "cross_ref": "rich.padding.Padding", "kind": "Gdef"}, "PaddingDimensions": {".class": "SymbolTableNode", "cross_ref": "rich.padding.PaddingDimensions", "kind": "Gdef"}, "PaddingProperty": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.syntax.PaddingProperty", "name": "PaddingProperty", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "rich.syntax.PaddingProperty", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "rich.syntax", "mro": ["rich.syntax.PaddingProperty", "builtins.object"], "names": {".class": "SymbolTable", "__get__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "objtype"], "dataclass_transform_spec": null, "flags": [], "fullname": "rich.syntax.PaddingProperty.__get__", "name": "__get__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "objtype"], "arg_types": ["rich.syntax.PaddingProperty", "rich.syntax.Syntax", {".class": "TypeType", "item": "rich.syntax.Syntax"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of PaddingProperty", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__set__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "padding"], "dataclass_transform_spec": null, "flags": [], "fullname": "rich.syntax.PaddingProperty.__set__", "name": "__set__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "padding"], "arg_types": ["rich.syntax.PaddingProperty", "rich.syntax.Syntax", {".class": "TypeAliasType", "args": [], "type_ref": "rich.padding.PaddingDimensions"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__set__ of PaddingProperty", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.syntax.PaddingProperty.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.syntax.PaddingProperty", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "PygmentsStyle": {".class": "SymbolTableNode", "cross_ref": "pygments.style.Style", "kind": "Gdef"}, "PygmentsSyntaxTheme": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["rich.syntax.SyntaxTheme"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.syntax.PygmentsSyntaxTheme", "name": "PygmentsSyntaxTheme", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "rich.syntax.PygmentsSyntaxTheme", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "rich.syntax", "mro": ["rich.syntax.PygmentsSyntaxTheme", "rich.syntax.SyntaxTheme", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "theme"], "dataclass_transform_spec": null, "flags": [], "fullname": "rich.syntax.PygmentsSyntaxTheme.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "theme"], "arg_types": ["rich.syntax.PygmentsSyntaxTheme", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeType", "item": "pygments.style.Style"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PygmentsSyntaxTheme", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_background_color": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.syntax.PygmentsSyntaxTheme._background_color", "name": "_background_color", "type": "builtins.str"}}, "_background_style": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.syntax.PygmentsSyntaxTheme._background_style", "name": "_background_style", "type": "rich.style.Style"}}, "_pygments_style_class": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.syntax.PygmentsSyntaxTheme._pygments_style_class", "name": "_pygments_style_class", "type": {".class": "TypeType", "item": "pygments.style.Style"}}}, "_style_cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "rich.syntax.PygmentsSyntaxTheme._style_cache", "name": "_style_cache", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.TokenType"}, "rich.style.Style"], "type_ref": "builtins.dict"}}}, "get_background_style": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "rich.syntax.PygmentsSyntaxTheme.get_background_style", "name": "get_background_style", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.syntax.PygmentsSyntaxTheme"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_background_style of PygmentsSyntaxTheme", "ret_type": "rich.style.Style", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_style_for_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "token_type"], "dataclass_transform_spec": null, "flags": [], "fullname": "rich.syntax.PygmentsSyntaxTheme.get_style_for_token", "name": "get_style_for_token", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "token_type"], "arg_types": ["rich.syntax.PygmentsSyntaxTheme", {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.TokenType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_style_for_token of PygmentsSyntaxTheme", "ret_type": "rich.style.Style", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.syntax.PygmentsSyntaxTheme.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.syntax.PygmentsSyntaxTheme", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RICH_SYNTAX_THEMES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "rich.syntax.RICH_SYNTAX_THEMES", "name": "RICH_SYNTAX_THEMES", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "rich.style.Style"], "type_ref": "builtins.dict"}], "type_ref": "builtins.dict"}}}, "RenderResult": {".class": "SymbolTableNode", "cross_ref": "rich.console.RenderResult", "kind": "Gdef"}, "Segment": {".class": "SymbolTableNode", "cross_ref": "rich.segment.Segment", "kind": "Gdef"}, "Segments": {".class": "SymbolTableNode", "cross_ref": "rich.segment.Segments", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef"}, "String": {".class": "SymbolTableNode", "cross_ref": "pygments.token.String", "kind": "Gdef"}, "Style": {".class": "SymbolTableNode", "cross_ref": "rich.style.Style", "kind": "Gdef"}, "StyleType": {".class": "SymbolTableNode", "cross_ref": "rich.style.StyleType", "kind": "Gdef"}, "Syntax": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["rich.jupyter.Ju<PERSON>"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.syntax.Syntax", "name": "Syntax", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "rich.syntax.Syntax", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "rich.syntax", "mro": ["rich.syntax.Syntax", "rich.jupyter.Ju<PERSON>", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "code", "lexer", "theme", "dedent", "line_numbers", "start_line", "line_range", "highlight_lines", "code_width", "tab_size", "word_wrap", "background_color", "indent_guides", "padding"], "dataclass_transform_spec": null, "flags": [], "fullname": "rich.syntax.Syntax.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "code", "lexer", "theme", "dedent", "line_numbers", "start_line", "line_range", "highlight_lines", "code_width", "tab_size", "word_wrap", "background_color", "indent_guides", "padding"], "arg_types": ["rich.syntax.Syntax", "builtins.str", {".class": "UnionType", "items": ["pygments.lexer.<PERSON>er", "builtins.str"]}, {".class": "UnionType", "items": ["builtins.str", "rich.syntax.SyntaxTheme"]}, "builtins.bool", "builtins.bool", "builtins.int", {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "type_ref": "builtins.set"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, "builtins.int", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "rich.padding.PaddingDimensions"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Syntax", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__rich_console__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "dataclass_transform_spec": null, "flags": [], "fullname": "rich.syntax.Syntax.__rich_console__", "name": "__rich_console__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "arg_types": ["rich.syntax.Syntax", "rich.console.Console", "rich.console.ConsoleOptions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rich_console__ of Syntax", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.RenderResult"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__rich_measure__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "dataclass_transform_spec": null, "flags": [], "fullname": "rich.syntax.Syntax.__rich_measure__", "name": "__rich_measure__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "arg_types": ["rich.syntax.Syntax", "rich.console.Console", "rich.console.ConsoleOptions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rich_measure__ of Syntax", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.measure.Measurement"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_apply_stylized_ranges": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "flags": [], "fullname": "rich.syntax.Syntax._apply_stylized_ranges", "name": "_apply_stylized_ranges", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["rich.syntax.Syntax", "rich.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_apply_stylized_ranges of Syntax", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_get_base_style": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "rich.syntax.Syntax._get_base_style", "name": "_get_base_style", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.syntax.Syntax"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_base_style of Syntax", "ret_type": "rich.style.Style", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_get_line_numbers_color": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "blend"], "dataclass_transform_spec": null, "flags": [], "fullname": "rich.syntax.Syntax._get_line_numbers_color", "name": "_get_line_numbers_color", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "blend"], "arg_types": ["rich.syntax.Syntax", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_line_numbers_color of Syntax", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.color.Color"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_get_number_styles": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "console"], "dataclass_transform_spec": null, "flags": [], "fullname": "rich.syntax.Syntax._get_number_styles", "name": "_get_number_styles", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "console"], "arg_types": ["rich.syntax.Syntax", "rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_number_styles of Syntax", "ret_type": {".class": "TupleType", "implicit": false, "items": ["rich.style.Style", "rich.style.Style", "rich.style.Style"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_get_syntax": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "dataclass_transform_spec": null, "flags": ["is_generator"], "fullname": "rich.syntax.Syntax._get_syntax", "name": "_get_syntax", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "arg_types": ["rich.syntax.Syntax", "rich.console.Console", "rich.console.ConsoleOptions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_syntax of Syntax", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "type_ref": "typing.Iterable"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_get_token_color": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "token_type"], "dataclass_transform_spec": null, "flags": [], "fullname": "rich.syntax.Syntax._get_token_color", "name": "_get_token_color", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "token_type"], "arg_types": ["rich.syntax.Syntax", {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.TokenType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_token_color of Syntax", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.color.Color"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_lexer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.syntax.Syntax._lexer", "name": "_lexer", "type": {".class": "UnionType", "items": ["pygments.lexer.<PERSON>er", "builtins.str"]}}}, "_numbers_column_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "rich.syntax.Syntax._numbers_column_width", "name": "_numbers_column_width", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.syntax.Syntax"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_numbers_column_width of Syntax", "ret_type": "builtins.int", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "rich.syntax.Syntax._numbers_column_width", "name": "_numbers_column_width", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.syntax.Syntax"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_numbers_column_width of Syntax", "ret_type": "builtins.int", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_padding": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.syntax.Syntax._padding", "name": "_padding", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_process_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "code"], "dataclass_transform_spec": null, "flags": [], "fullname": "rich.syntax.Syntax._process_code", "name": "_process_code", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "code"], "arg_types": ["rich.syntax.Syntax", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_process_code of Syntax", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_pygments_style_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "rich.syntax.Syntax._pygments_style_class", "name": "_pygments_style_class", "type": {".class": "TypeType", "item": "pygments.style.Style"}}}, "_stylized_ranges": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "rich.syntax.Syntax._stylized_ranges", "name": "_stylized_ranges", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax._SyntaxHighlightRange"}], "type_ref": "builtins.list"}}}, "_theme": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "rich.syntax.Syntax._theme", "name": "_theme", "type": "rich.syntax.SyntaxTheme"}}, "background_color": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.syntax.Syntax.background_color", "name": "background_color", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "background_style": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.syntax.Syntax.background_style", "name": "background_style", "type": "rich.style.Style"}}, "code": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.syntax.Syntax.code", "name": "code", "type": "builtins.str"}}, "code_width": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.syntax.Syntax.code_width", "name": "code_width", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}}}, "dedent": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.syntax.Syntax.dedent", "name": "dedent", "type": "builtins.bool"}}, "default_lexer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "rich.syntax.Syntax.default_lexer", "name": "default_lexer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.syntax.Syntax"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_lexer of Syntax", "ret_type": "pygments.lexer.<PERSON>er", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "rich.syntax.Syntax.default_lexer", "name": "default_lexer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.syntax.Syntax"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_lexer of Syntax", "ret_type": "pygments.lexer.<PERSON>er", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "from_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["cls", "path", "encoding", "lexer", "theme", "dedent", "line_numbers", "line_range", "start_line", "highlight_lines", "code_width", "tab_size", "word_wrap", "background_color", "indent_guides", "padding"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "rich.syntax.Syntax.from_path", "name": "from_path", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["cls", "path", "encoding", "lexer", "theme", "dedent", "line_numbers", "line_range", "start_line", "highlight_lines", "code_width", "tab_size", "word_wrap", "background_color", "indent_guides", "padding"], "arg_types": [{".class": "TypeType", "item": "rich.syntax.Syntax"}, "builtins.str", "builtins.str", {".class": "UnionType", "items": ["pygments.lexer.<PERSON>er", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", "rich.syntax.SyntaxTheme"]}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "NoneType"}]}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "type_ref": "builtins.set"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, "builtins.int", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "rich.padding.PaddingDimensions"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_path of Syntax", "ret_type": "rich.syntax.Syntax", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "rich.syntax.Syntax.from_path", "name": "from_path", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["cls", "path", "encoding", "lexer", "theme", "dedent", "line_numbers", "line_range", "start_line", "highlight_lines", "code_width", "tab_size", "word_wrap", "background_color", "indent_guides", "padding"], "arg_types": [{".class": "TypeType", "item": "rich.syntax.Syntax"}, "builtins.str", "builtins.str", {".class": "UnionType", "items": ["pygments.lexer.<PERSON>er", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", "rich.syntax.SyntaxTheme"]}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "NoneType"}]}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "type_ref": "builtins.set"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, "builtins.int", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "rich.padding.PaddingDimensions"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_path of Syntax", "ret_type": "rich.syntax.Syntax", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "get_theme": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "name"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "rich.syntax.Syntax.get_theme", "name": "get_theme", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "name"], "arg_types": [{".class": "TypeType", "item": "rich.syntax.Syntax"}, {".class": "UnionType", "items": ["builtins.str", "rich.syntax.SyntaxTheme"]}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_theme of Syntax", "ret_type": "rich.syntax.SyntaxTheme", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "rich.syntax.Syntax.get_theme", "name": "get_theme", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "name"], "arg_types": [{".class": "TypeType", "item": "rich.syntax.Syntax"}, {".class": "UnionType", "items": ["builtins.str", "rich.syntax.SyntaxTheme"]}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_theme of Syntax", "ret_type": "rich.syntax.SyntaxTheme", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "guess_lexer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cls", "path", "code"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "rich.syntax.Syntax.guess_lexer", "name": "guess_lexer", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "path", "code"], "arg_types": [{".class": "TypeType", "item": "rich.syntax.Syntax"}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "guess_lexer of Syntax", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "rich.syntax.Syntax.guess_lexer", "name": "guess_lexer", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "path", "code"], "arg_types": [{".class": "TypeType", "item": "rich.syntax.Syntax"}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "guess_lexer of Syntax", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "highlight": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "code", "line_range"], "dataclass_transform_spec": null, "flags": [], "fullname": "rich.syntax.Syntax.highlight", "name": "highlight", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "code", "line_range"], "arg_types": ["rich.syntax.Syntax", "builtins.str", {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "highlight of Syntax", "ret_type": "rich.text.Text", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "highlight_lines": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.syntax.Syntax.highlight_lines", "name": "highlight_lines", "type": {".class": "Instance", "args": ["builtins.int"], "type_ref": "builtins.set"}}}, "indent_guides": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.syntax.Syntax.indent_guides", "name": "indent_guides", "type": "builtins.bool"}}, "lexer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "rich.syntax.Syntax.lexer", "name": "lexer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.syntax.Syntax"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lexer of Syntax", "ret_type": {".class": "UnionType", "items": ["pygments.lexer.<PERSON>er", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "rich.syntax.Syntax.lexer", "name": "lexer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.syntax.Syntax"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lexer of Syntax", "ret_type": {".class": "UnionType", "items": ["pygments.lexer.<PERSON>er", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "line_numbers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.syntax.Syntax.line_numbers", "name": "line_numbers", "type": "builtins.bool"}}, "line_range": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.syntax.Syntax.line_range", "name": "line_range", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "NoneType"}]}}}, "padding": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "rich.syntax.Syntax.padding", "name": "padding", "type": "rich.syntax.PaddingProperty"}}, "start_line": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.syntax.Syntax.start_line", "name": "start_line", "type": "builtins.int"}}, "stylize_range": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "style", "start", "end", "style_before"], "dataclass_transform_spec": null, "flags": [], "fullname": "rich.syntax.Syntax.stylize_range", "name": "stylize_range", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "style", "start", "end", "style_before"], "arg_types": ["rich.syntax.Syntax", {".class": "TypeAliasType", "args": [], "type_ref": "rich.style.StyleType"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.SyntaxPosition"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.SyntaxPosition"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stylize_range of Syntax", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "tab_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.syntax.Syntax.tab_size", "name": "tab_size", "type": "builtins.int"}}, "word_wrap": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.syntax.Syntax.word_wrap", "name": "word_wrap", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.syntax.Syntax.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.syntax.Syntax", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SyntaxPosition": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "rich.syntax.SyntaxPosition", "line": 213, "no_args": false, "normalized": false, "target": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "SyntaxTheme": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["get_background_style", 1], ["get_style_for_token", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.syntax.SyntaxTheme", "name": "SyntaxTheme", "type_vars": []}, "deletable_attributes": [], "flags": ["is_abstract"], "fullname": "rich.syntax.SyntaxTheme", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "rich.syntax", "mro": ["rich.syntax.SyntaxTheme", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "get_background_style": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "rich.syntax.SyntaxTheme.get_background_style", "name": "get_background_style", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.syntax.SyntaxTheme"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_background_style of SyntaxTheme", "ret_type": "rich.style.Style", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "rich.syntax.SyntaxTheme.get_background_style", "name": "get_background_style", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.syntax.SyntaxTheme"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_background_style of SyntaxTheme", "ret_type": "rich.style.Style", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "get_style_for_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "token_type"], "dataclass_transform_spec": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "rich.syntax.SyntaxTheme.get_style_for_token", "name": "get_style_for_token", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "token_type"], "arg_types": ["rich.syntax.SyntaxTheme", {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.TokenType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_style_for_token of SyntaxTheme", "ret_type": "rich.style.Style", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "rich.syntax.SyntaxTheme.get_style_for_token", "name": "get_style_for_token", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "token_type"], "arg_types": ["rich.syntax.SyntaxTheme", {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.TokenType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_style_for_token of SyntaxTheme", "ret_type": "rich.style.Style", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.syntax.SyntaxTheme.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.syntax.SyntaxTheme", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Text": {".class": "SymbolTableNode", "cross_ref": "rich.text.Text", "kind": "Gdef"}, "Token": {".class": "SymbolTableNode", "cross_ref": "pygments.token.Token", "kind": "Gdef"}, "TokenType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "rich.syntax.TokenType", "line": 54, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "WINDOWS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "rich.syntax.WINDOWS", "name": "WINDOWS", "type": "builtins.bool"}}, "Whitespace": {".class": "SymbolTableNode", "cross_ref": "pygments.token.Whitespace", "kind": "Gdef"}, "_SyntaxHighlightRange": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.syntax._SyntaxHighlightRange", "name": "_SyntaxHighlightRange", "type_vars": []}, "deletable_attributes": [], "flags": ["is_named_tuple"], "fullname": "rich.syntax._SyntaxHighlightRange", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["style", "start", "end", "style_before"]}}, "module_name": "rich.syntax", "mro": ["rich.syntax._SyntaxHighlightRange", "builtins.tuple", "typing.Sequence", "typing.Collection", "typing.Reversible", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.syntax._SyntaxHighlightRange._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.style.StyleType"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.SyntaxPosition"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.SyntaxPosition"}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bool", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "builtins.str", "rich.style.Style"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "rich.syntax._SyntaxHighlightRange.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "rich.syntax._SyntaxHighlightRange.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "rich.syntax._SyntaxHighlightRange.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "style"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "start"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "end"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "style_before"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["_cls", "style", "start", "end", "style_before"], "dataclass_transform_spec": null, "flags": ["is_static"], "fullname": "rich.syntax._SyntaxHighlightRange.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["_cls", "style", "start", "end", "style_before"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.syntax._SyntaxHighlightRange._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.style.StyleType"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.SyntaxPosition"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.SyntaxPosition"}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bool", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "builtins.str", "rich.style.Style"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.style.StyleType"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.SyntaxPosition"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.SyntaxPosition"}, "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of _SyntaxHighlightRange", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.syntax._SyntaxHighlightRange._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.style.StyleType"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.SyntaxPosition"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.SyntaxPosition"}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bool", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "builtins.str", "rich.style.Style"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.syntax._SyntaxHighlightRange._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.style.StyleType"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.SyntaxPosition"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.SyntaxPosition"}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bool", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "builtins.str", "rich.style.Style"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "flags": [], "fullname": "rich.syntax._SyntaxHighlightRange._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.syntax._SyntaxHighlightRange._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.style.StyleType"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.SyntaxPosition"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.SyntaxPosition"}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bool", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "builtins.str", "rich.style.Style"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of _SyntaxHighlightRange", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.syntax._SyntaxHighlightRange._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.style.StyleType"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.SyntaxPosition"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.SyntaxPosition"}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bool", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "builtins.str", "rich.style.Style"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "rich.syntax._SyntaxHighlightRange._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "rich.syntax._SyntaxHighlightRange._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "rich.syntax._SyntaxHighlightRange._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "rich.syntax._SyntaxHighlightRange._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.syntax._SyntaxHighlightRange._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.style.StyleType"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.SyntaxPosition"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.SyntaxPosition"}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bool", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "builtins.str", "rich.style.Style"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of _SyntaxHighlightRange", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.syntax._SyntaxHighlightRange._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.style.StyleType"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.SyntaxPosition"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.SyntaxPosition"}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bool", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "builtins.str", "rich.style.Style"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.syntax._SyntaxHighlightRange._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.style.StyleType"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.SyntaxPosition"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.SyntaxPosition"}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bool", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "builtins.str", "rich.style.Style"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "rich.syntax._SyntaxHighlightRange._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.syntax._SyntaxHighlightRange._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.style.StyleType"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.SyntaxPosition"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.SyntaxPosition"}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bool", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "builtins.str", "rich.style.Style"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of _SyntaxHighlightRange", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.syntax._SyntaxHighlightRange._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.style.StyleType"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.SyntaxPosition"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.SyntaxPosition"}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bool", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "builtins.str", "rich.style.Style"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.syntax._SyntaxHighlightRange._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.style.StyleType"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.SyntaxPosition"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.SyntaxPosition"}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bool", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "builtins.str", "rich.style.Style"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["_self", "style", "start", "end", "style_before"], "dataclass_transform_spec": null, "flags": [], "fullname": "rich.syntax._SyntaxHighlightRange._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["_self", "style", "start", "end", "style_before"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.syntax._SyntaxHighlightRange._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.style.StyleType"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.SyntaxPosition"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.SyntaxPosition"}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bool", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "builtins.str", "rich.style.Style"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.style.StyleType"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.SyntaxPosition"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.SyntaxPosition"}, "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of _SyntaxHighlightRange", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.syntax._SyntaxHighlightRange._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.style.StyleType"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.SyntaxPosition"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.SyntaxPosition"}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bool", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "builtins.str", "rich.style.Style"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.syntax._SyntaxHighlightRange._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.style.StyleType"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.SyntaxPosition"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.SyntaxPosition"}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bool", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "builtins.str", "rich.style.Style"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "rich.syntax._SyntaxHighlightRange._source", "name": "_source", "type": "builtins.str"}}, "end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "rich.syntax._SyntaxHighlightRange.end", "name": "end", "type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.SyntaxPosition"}}}, "end-redefinition": {".class": "SymbolTableNode", "cross_ref": "rich.syntax._SyntaxHighlightRange.end", "kind": "<PERSON><PERSON><PERSON>"}, "start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "rich.syntax._SyntaxHighlightRange.start", "name": "start", "type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.SyntaxPosition"}}}, "start-redefinition": {".class": "SymbolTableNode", "cross_ref": "rich.syntax._SyntaxHighlightRange.start", "kind": "<PERSON><PERSON><PERSON>"}, "style": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "rich.syntax._SyntaxHighlightRange.style", "name": "style", "type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.style.StyleType"}}}, "style-redefinition": {".class": "SymbolTableNode", "cross_ref": "rich.syntax._SyntaxHighlightRange.style", "kind": "<PERSON><PERSON><PERSON>"}, "style_before": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "rich.syntax._SyntaxHighlightRange.style_before", "name": "style_before", "type": "builtins.bool"}}, "style_before-redefinition": {".class": "SymbolTableNode", "cross_ref": "rich.syntax._SyntaxHighlightRange.style_before", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.syntax._SyntaxHighlightRange.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.style.StyleType"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.SyntaxPosition"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.SyntaxPosition"}, "builtins.bool"], "partial_fallback": "rich.syntax._SyntaxHighlightRange"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.style.StyleType"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.SyntaxPosition"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.SyntaxPosition"}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bool", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "builtins.str", "rich.style.Style"]}], "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.syntax.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.syntax.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.syntax.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.syntax.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.syntax.__package__", "name": "__package__", "type": "builtins.str"}}, "_get_code_index_for_syntax_position": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["newlines_offsets", "position"], "dataclass_transform_spec": null, "flags": [], "fullname": "rich.syntax._get_code_index_for_syntax_position", "name": "_get_code_index_for_syntax_position", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["newlines_offsets", "position"], "arg_types": [{".class": "Instance", "args": ["builtins.int"], "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.syntax.SyntaxPosition"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_code_index_for_syntax_position", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "argparse": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON><PERSON>", "kind": "Gdef"}, "args": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "rich.syntax.args", "name": "args", "type": "argparse.Namespace"}}, "blend_rgb": {".class": "SymbolTableNode", "cross_ref": "rich.color.blend_rgb", "kind": "Gdef"}, "cell_len": {".class": "SymbolTableNode", "cross_ref": "rich.cells.cell_len", "kind": "Gdef"}, "code": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "rich.syntax.code", "name": "code", "type": "builtins.str"}}, "console": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "rich.syntax.console", "name": "console", "type": "rich.console.Console"}}, "get_lexer_by_name": {".class": "SymbolTableNode", "cross_ref": "pygments.lexers.get_lexer_by_name", "kind": "Gdef"}, "get_style_by_name": {".class": "SymbolTableNode", "cross_ref": "pygments.styles.get_style_by_name", "kind": "Gdef"}, "guess_lexer_for_filename": {".class": "SymbolTableNode", "cross_ref": "pygments.lexers.guess_lexer_for_filename", "kind": "Gdef"}, "loop_first": {".class": "SymbolTableNode", "cross_ref": "rich._loop.loop_first", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "parser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "rich.syntax.parser", "name": "parser", "type": "argparse.ArgumentParser"}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "syntax": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "rich.syntax.syntax", "name": "syntax", "type": "rich.syntax.Syntax"}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "textwrap": {".class": "SymbolTableNode", "cross_ref": "textwrap", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\rich\\syntax.py"}