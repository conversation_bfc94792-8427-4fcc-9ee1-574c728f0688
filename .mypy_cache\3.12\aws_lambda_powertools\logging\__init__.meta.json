{"data_mtime": 1759504512, "dep_lines": [4, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 20, 20, 30, 30], "dependencies": ["aws_lambda_powertools.logging.logger", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "typing"], "hash": "f9c300abffbe3e29704e1063af4da28a8c55f4a863a0a72662a1dcf11fc613e2", "id": "aws_lambda_powertools.logging", "ignore_all": true, "interface_hash": "42c2a3ce9b1af0e42d354976a8de56916088ff0c6511acb9ace3b8bf3a80152b", "mtime": 1757091873, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\aws_lambda_powertools\\logging\\__init__.py", "plugin_data": null, "size": 73, "suppressed": [], "version_id": "1.8.0"}