{"data_mtime": 1759503833, "dep_lines": [4, 4, 4, 4, 5, 1, 2, 3, 4, 6, 7, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 5, 10, 10, 5, 20, 5, 5, 5, 20, 20, 30, 30], "dependencies": ["asyncio.events", "asyncio.protocols", "asyncio.streams", "asyncio.transports", "collections.abc", "subprocess", "sys", "_typeshed", "asyncio", "typing", "typing_extensions", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "os"], "hash": "59d2842b83b55c46248cc4fef9c6b9ee2409bc6b3c53f6b8e6f6c90f300d1bdd", "id": "asyncio.subprocess", "ignore_all": true, "interface_hash": "af05f46724cb4b5028bd6c294a9e059c27220eb21f2d6b7104efcd478fc4decb", "mtime": 1757092231, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\asyncio\\subprocess.pyi", "plugin_data": null, "size": 9358, "suppressed": [], "version_id": "1.8.0"}