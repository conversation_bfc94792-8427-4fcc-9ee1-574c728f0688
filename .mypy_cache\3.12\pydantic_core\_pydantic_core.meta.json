{"data_mtime": 1759503829, "dep_lines": [2, 9, 1, 3, 5, 6, 8, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 5, 5, 20, 20, 30], "dependencies": ["collections.abc", "pydantic_core.core_schema", "datetime", "typing", "_typeshed", "typing_extensions", "pydantic_core", "builtins", "pyexpat.model", "pyexpat.errors", "abc"], "hash": "2fc3d8ecda3246dd80107ae6e9295ec12450492e4da2a93e852f9a4011c497cf", "id": "pydantic_core._pydantic_core", "ignore_all": true, "interface_hash": "c5d65dafd5c9a41fbdd249b99b9c170fe55968931fdb57b7e6ec76d8deb965fd", "mtime": 1757091872, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic_core\\_pydantic_core.pyi", "plugin_data": null, "size": 44398, "suppressed": [], "version_id": "1.8.0"}