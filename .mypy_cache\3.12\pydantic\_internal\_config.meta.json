{"data_mtime": 1759503836, "dep_lines": [28, 14, 17, 18, 19, 20, 29, 1, 3, 4, 5, 6, 14, 15, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 10, 5, 5, 5, 5, 25, 5, 10, 5, 5, 5, 20, 5, 5, 20, 20, 30, 30, 30, 30], "dependencies": ["pydantic._internal._schema_generation_shared", "pydantic_core.core_schema", "pydantic.aliases", "pydantic.config", "pydantic.errors", "pydantic.warnings", "pydantic.fields", "__future__", "warnings", "contextlib", "re", "typing", "pydantic_core", "typing_extensions", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "abc", "pydantic._internal._generate_schema", "pydantic._internal._repr"], "hash": "595d3b869f317f4434c8ff48c0cbee18b426bb7e00665e6c06cd8968e80293d2", "id": "pydantic._internal._config", "ignore_all": true, "interface_hash": "3906c3847db0319b794420bab700dd2718451a00e17d5b48330d827d879216f9", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\_internal\\_config.py", "plugin_data": null, "size": 14253, "suppressed": [], "version_id": "1.8.0"}