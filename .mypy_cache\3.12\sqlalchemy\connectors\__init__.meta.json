{"data_mtime": 1759504547, "dep_lines": [9, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 20, 20, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.engine.interfaces", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "sqlalchemy.engine", "sqlalchemy.event", "sqlalchemy.event.registry", "typing"], "hash": "dbcbf997a169426a3ad95497d2bcb467290e2e81f604f1b2012b5a5da6ab7d5a", "id": "sqlalchemy.connectors", "ignore_all": true, "interface_hash": "f3fa33f5b377cd1b20767ebe21dab8ee98ac94e321def5ff09a7e8c315a061c1", "mtime": 1759106563, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\connectors\\__init__.py", "plugin_data": null, "size": 494, "suppressed": [], "version_id": "1.8.0"}