{"data_mtime": 1759504549, "dep_lines": [7, 11, 12, 13, 8, 9, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 20, 20, 30], "dependencies": ["collections.abc", "botocore.compat", "botocore.model", "botocore.utils", "re", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "abc"], "hash": "67784f401a417800d260b973f96ba6ac6b93de19141823b4e05f7a5d471ebd46", "id": "botocore.serialize", "ignore_all": true, "interface_hash": "808c1965d21898da5ba1f4fab5dcb9db94b4fb147b7b9941edfded410ea1488c", "mtime": 1757092235, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\botocore-stubs\\serialize.pyi", "plugin_data": null, "size": 2291, "suppressed": [], "version_id": "1.8.0"}