{".class": "MypyFile", "_fullname": "pydantic.warnings", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "GenericBeforeBaseModelWarning": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Warning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.warnings.GenericBeforeBaseModelWarning", "name": "GenericBeforeBaseModelWarning", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic.warnings.GenericBeforeBaseModelWarning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.warnings", "mro": ["pydantic.warnings.GenericBeforeBaseModelWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.warnings.GenericBeforeBaseModelWarning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.warnings.GenericBeforeBaseModelWarning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PydanticDeprecatedSince20": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.warnings.PydanticDeprecationWarning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.warnings.PydanticDeprecatedSince20", "name": "PydanticDeprecatedSince20", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic.warnings.PydanticDeprecatedSince20", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.warnings", "mro": ["pydantic.warnings.PydanticDeprecatedSince20", "pydantic.warnings.PydanticDeprecationWarning", "builtins.DeprecationWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["self", "message", "args"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.warnings.PydanticDeprecatedSince20.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["self", "message", "args"], "arg_types": ["pydantic.warnings.PydanticDeprecatedSince20", "builtins.str", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PydanticDeprecatedSince20", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.warnings.PydanticDeprecatedSince20.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.warnings.PydanticDeprecatedSince20", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PydanticDeprecatedSince210": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.warnings.PydanticDeprecationWarning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.warnings.PydanticDeprecatedSince210", "name": "PydanticDeprecatedSince210", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic.warnings.PydanticDeprecatedSince210", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.warnings", "mro": ["pydantic.warnings.PydanticDeprecatedSince210", "pydantic.warnings.PydanticDeprecationWarning", "builtins.DeprecationWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["self", "message", "args"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.warnings.PydanticDeprecatedSince210.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["self", "message", "args"], "arg_types": ["pydantic.warnings.PydanticDeprecatedSince210", "builtins.str", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PydanticDeprecatedSince210", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.warnings.PydanticDeprecatedSince210.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.warnings.PydanticDeprecatedSince210", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PydanticDeprecatedSince211": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.warnings.PydanticDeprecationWarning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.warnings.PydanticDeprecatedSince211", "name": "PydanticDeprecatedSince211", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic.warnings.PydanticDeprecatedSince211", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.warnings", "mro": ["pydantic.warnings.PydanticDeprecatedSince211", "pydantic.warnings.PydanticDeprecationWarning", "builtins.DeprecationWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["self", "message", "args"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.warnings.PydanticDeprecatedSince211.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["self", "message", "args"], "arg_types": ["pydantic.warnings.PydanticDeprecatedSince211", "builtins.str", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PydanticDeprecatedSince211", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.warnings.PydanticDeprecatedSince211.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.warnings.PydanticDeprecatedSince211", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PydanticDeprecatedSince26": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.warnings.PydanticDeprecationWarning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.warnings.PydanticDeprecatedSince26", "name": "PydanticDeprecatedSince26", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic.warnings.PydanticDeprecatedSince26", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.warnings", "mro": ["pydantic.warnings.PydanticDeprecatedSince26", "pydantic.warnings.PydanticDeprecationWarning", "builtins.DeprecationWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["self", "message", "args"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.warnings.PydanticDeprecatedSince26.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["self", "message", "args"], "arg_types": ["pydantic.warnings.PydanticDeprecatedSince26", "builtins.str", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PydanticDeprecatedSince26", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.warnings.PydanticDeprecatedSince26.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.warnings.PydanticDeprecatedSince26", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PydanticDeprecatedSince29": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.warnings.PydanticDeprecationWarning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.warnings.PydanticDeprecatedSince29", "name": "PydanticDeprecatedSince29", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic.warnings.PydanticDeprecatedSince29", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.warnings", "mro": ["pydantic.warnings.PydanticDeprecatedSince29", "pydantic.warnings.PydanticDeprecationWarning", "builtins.DeprecationWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["self", "message", "args"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.warnings.PydanticDeprecatedSince29.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["self", "message", "args"], "arg_types": ["pydantic.warnings.PydanticDeprecatedSince29", "builtins.str", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PydanticDeprecatedSince29", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.warnings.PydanticDeprecatedSince29.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.warnings.PydanticDeprecatedSince29", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PydanticDeprecationWarning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.DeprecationWarning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.warnings.PydanticDeprecationWarning", "name": "PydanticDeprecationWarning", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic.warnings.PydanticDeprecationWarning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.warnings", "mro": ["pydantic.warnings.PydanticDeprecationWarning", "builtins.DeprecationWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 3, 5], "arg_names": ["self", "message", "args", "since", "expected_removal"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.warnings.PydanticDeprecationWarning.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 3, 5], "arg_names": ["self", "message", "args", "since", "expected_removal"], "arg_types": ["pydantic.warnings.PydanticDeprecationWarning", "builtins.str", "builtins.object", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PydanticDeprecationWarning", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.warnings.PydanticDeprecationWarning.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pydantic.warnings.PydanticDeprecationWarning"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of PydanticDeprecationWarning", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "expected_removal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic.warnings.PydanticDeprecationWarning.expected_removal", "name": "expected_removal", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic.warnings.PydanticDeprecationWarning.message", "name": "message", "type": "builtins.str"}}, "since": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic.warnings.PydanticDeprecationWarning.since", "name": "since", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.warnings.PydanticDeprecationWarning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.warnings.PydanticDeprecationWarning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PydanticExperimentalWarning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Warning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.warnings.PydanticExperimentalWarning", "name": "PydanticExperimentalWarning", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic.warnings.PydanticExperimentalWarning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.warnings", "mro": ["pydantic.warnings.PydanticExperimentalWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.warnings.PydanticExperimentalWarning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.warnings.PydanticExperimentalWarning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic.warnings.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.warnings.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.warnings.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.warnings.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.warnings.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.warnings.__package__", "name": "__package__", "type": "builtins.str"}}, "_annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "version_short": {".class": "SymbolTableNode", "cross_ref": "pydantic.version.version_short", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\warnings.py"}