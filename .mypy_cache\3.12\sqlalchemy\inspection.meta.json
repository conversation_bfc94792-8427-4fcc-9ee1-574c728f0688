{"data_mtime": 1759504546, "dep_lines": [44, 43, 31, 33, 43, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 20, 5, 20, 20, 30], "dependencies": ["sqlalchemy.util.typing", "sqlalchemy.exc", "__future__", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "abc"], "hash": "8a45792b1d91075b6feff7e6b1d99b800be0d52315dc099c9af7c4c8910bb458", "id": "sqlalchemy.inspection", "ignore_all": true, "interface_hash": "99a70934662d8a384f4933f49fb165a8fa2c5ea6ff6ef80e56fd7686d823c648", "mtime": 1759106563, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\inspection.py", "plugin_data": null, "size": 5237, "suppressed": [], "version_id": "1.8.0"}