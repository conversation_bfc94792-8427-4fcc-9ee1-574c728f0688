{"data_mtime": 1759504553, "dep_lines": [13, 14, 24, 26, 13, 15, 16, 20, 25, 8, 9, 10, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 20, 5, 5, 5, 5, 10, 10, 5, 5, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["aws_lambda_powertools.logging.correlation_paths", "aws_lambda_powertools.utilities.typing", "smartanalytics_infrastructure.database.session", "smartanalytics_utilities.config.settings", "aws_lambda_powertools.logging", "aws_lambda_typing.events", "smartanalytics_domain.command_handlers", "smartanalytics_domain.commands", "smartanalytics_infrastructure.unit_of_work", "asyncio", "json", "typing", "aws_lambda_powertools", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "abc", "asyncio.events", "asyncio.runners", "aws_lambda_powertools.logging.logger", "aws_lambda_powertools.tracing", "aws_lambda_powertools.tracing.base", "aws_lambda_powertools.tracing.tracer", "aws_lambda_powertools.utilities", "aws_lambda_powertools.utilities.typing.lambda_context", "aws_lambda_typing", "aws_lambda_typing.events.sqs", "contextlib", "functools", "json.decoder", "json.encoder", "logging", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "pydantic_settings", "pydantic_settings.main", "smartanalytics_domain", "smartanalytics_domain.command_handlers.process_agent_event_handler", "smartanalytics_domain.command_handlers.process_agent_events_batch_handler", "smartanalytics_domain.commands.process_agent_event_command", "smartanalytics_domain.commands.process_agent_events_batch_command", "smartanalytics_domain.models", "smartanalytics_domain.models.processing_result", "smartanalytics_domain.ports", "smartanalytics_domain.ports.unit_of_work", "smartanalytics_infrastructure", "smartanalytics_utilities", "smartanalytics_utilities.config", "smartanalytics_utilities.config.base", "sqlalchemy", "sqlalchemy.ext", "sqlalchemy.ext.asyncio", "sqlalchemy.ext.asyncio.base", "sqlalchemy.ext.asyncio.session", "types"], "hash": "002c5c99bd002fa5e90c7f42345b5b96a92d56590ba93bec745bb9a23705e6b9", "id": "src.lambda_function", "ignore_all": false, "interface_hash": "127aa27e1cae318b075ad2d0bd681700a95953c7c3dcd10cfaaf62fd9665901b", "mtime": 1759501803, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "lambdas\\acd-event-processor\\src\\lambda_function.py", "plugin_data": null, "size": 6051, "suppressed": [], "version_id": "1.8.0"}