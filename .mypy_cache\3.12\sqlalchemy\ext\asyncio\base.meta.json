{"data_mtime": 1759503867, "dep_lines": [28, 28, 30, 29, 8, 10, 11, 12, 26, 29, 1, 1, 1, 1, 1], "dep_prios": [10, 20, 5, 10, 5, 10, 10, 5, 10, 20, 5, 20, 20, 30, 30], "dependencies": ["sqlalchemy.ext.asyncio.exc", "sqlalchemy.ext.asyncio", "sqlalchemy.util.typing", "sqlalchemy.util", "__future__", "abc", "functools", "typing", "weakref", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "_weakref", "sqlalchemy.exc"], "hash": "73c0f6a80fdc72e3afab1c7cdc4d059da7f314b867e8a0a1a23327be33d67d37", "id": "sqlalchemy.ext.asyncio.base", "ignore_all": true, "interface_hash": "45a219e875a9e184c9548a0ac775cc5aec68c1e84e4f0e35b4e006f9fe004792", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\base.py", "plugin_data": null, "size": 9314, "suppressed": [], "version_id": "1.8.0"}