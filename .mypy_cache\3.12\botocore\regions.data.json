{".class": "MypyFile", "_fullname": "botocore.regions", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "AUTH_TYPE_MAPS": {".class": "SymbolTableNode", "cross_ref": "botocore.auth.AUTH_TYPE_MAPS", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseEndpointResolver": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.regions.BaseEndpointResolver", "name": "BaseEndpointResolver", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.regions.BaseEndpointResolver", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.regions", "mro": ["botocore.regions.BaseEndpointResolver", "builtins.object"], "names": {".class": "SymbolTable", "construct_endpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "service_name", "region_name"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.regions.BaseEndpointResolver.construct_endpoint", "name": "construct_endpoint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "service_name", "region_name"], "arg_types": ["botocore.regions.BaseEndpointResolver", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "construct_endpoint of BaseEndpointResolver", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_available_endpoints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "service_name", "partition_name", "allow_non_regional"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.regions.BaseEndpointResolver.get_available_endpoints", "name": "get_available_endpoints", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "service_name", "partition_name", "allow_non_regional"], "arg_types": ["botocore.regions.BaseEndpointResolver", "builtins.str", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_available_endpoints of BaseEndpointResolver", "ret_type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_available_partitions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.regions.BaseEndpointResolver.get_available_partitions", "name": "get_available_partitions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.regions.BaseEndpointResolver"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_available_partitions of BaseEndpointResolver", "ret_type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.regions.BaseEndpointResolver.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.regions.BaseEndpointResolver", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BaseEventHooks": {".class": "SymbolTableNode", "cross_ref": "botocore.hooks.BaseEventHooks", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BotoCoreError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.BotoCoreError", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CRT_SUPPORTED_AUTH_TYPES": {".class": "SymbolTableNode", "cross_ref": "botocore.crt.CRT_SUPPORTED_AUTH_TYPES", "kind": "Gdef"}, "DEFAULT_SERVICE_DATA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.regions.DEFAULT_SERVICE_DATA", "name": "DEFAULT_SERVICE_DATA", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "type_ref": "builtins.dict"}}}, "DEFAULT_URI_TEMPLATE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.regions.DEFAULT_URI_TEMPLATE", "name": "DEFAULT_URI_TEMPLATE", "type": "builtins.str"}}, "EndpointResolver": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.regions.BaseEndpointResolver"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.regions.EndpointResolver", "name": "EndpointResolver", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.regions.EndpointResolver", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.regions", "mro": ["botocore.regions.EndpointResolver", "botocore.regions.BaseEndpointResolver", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "endpoint_data", "uses_builtin_data"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.regions.EndpointResolver.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "endpoint_data", "uses_builtin_data"], "arg_types": ["botocore.regions.EndpointResolver", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of EndpointResolver", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "construct_endpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "service_name", "region_name", "partition_name", "use_dualstack_endpoint", "use_fips_endpoint"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.regions.EndpointResolver.construct_endpoint", "name": "construct_endpoint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "service_name", "region_name", "partition_name", "use_dualstack_endpoint", "use_fips_endpoint"], "arg_types": ["botocore.regions.EndpointResolver", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "construct_endpoint of EndpointResolver", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_available_endpoints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "service_name", "partition_name", "allow_non_regional", "endpoint_variant_tags"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.regions.EndpointResolver.get_available_endpoints", "name": "get_available_endpoints", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "service_name", "partition_name", "allow_non_regional", "endpoint_variant_tags"], "arg_types": ["botocore.regions.EndpointResolver", "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_available_endpoints of EndpointResolver", "ret_type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_available_partitions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.regions.EndpointResolver.get_available_partitions", "name": "get_available_partitions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.regions.EndpointResolver"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_available_partitions of EndpointResolver", "ret_type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_partition_dns_suffix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "partition_name", "endpoint_variant_tags"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.regions.EndpointResolver.get_partition_dns_suffix", "name": "get_partition_dns_suffix", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "partition_name", "endpoint_variant_tags"], "arg_types": ["botocore.regions.EndpointResolver", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_partition_dns_suffix of EndpointResolver", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_partition_for_region": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "region_name"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.regions.EndpointResolver.get_partition_for_region", "name": "get_partition_for_region", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "region_name"], "arg_types": ["botocore.regions.EndpointResolver", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_partition_for_region of EndpointResolver", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_service_endpoints_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "service_name", "partition_name"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.regions.EndpointResolver.get_service_endpoints_data", "name": "get_service_endpoints_data", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "service_name", "partition_name"], "arg_types": ["botocore.regions.EndpointResolver", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_service_endpoints_data of EndpointResolver", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.regions.EndpointResolver.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.regions.EndpointResolver", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EndpointResolverBuiltins": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.regions.EndpointResolverBuiltins", "name": "EndpointResolverBuiltins", "type_vars": []}, "deletable_attributes": [], "flags": ["is_enum"], "fullname": "botocore.regions.EndpointResolverBuiltins", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "botocore.regions", "mro": ["botocore.regions.EndpointResolverBuiltins", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "ACCOUNT_ID": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "botocore.regions.EndpointResolverBuiltins.ACCOUNT_ID", "name": "ACCOUNT_ID", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "AWS::Auth::AccountId"}, "type_ref": "builtins.str"}}}, "ACCOUNT_ID_ENDPOINT_MODE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "botocore.regions.EndpointResolverBuiltins.ACCOUNT_ID_ENDPOINT_MODE", "name": "ACCOUNT_ID_ENDPOINT_MODE", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "AWS::Auth::AccountIdEndpointMode"}, "type_ref": "builtins.str"}}}, "AWS_REGION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "botocore.regions.EndpointResolverBuiltins.AWS_REGION", "name": "AWS_REGION", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "AWS::Region"}, "type_ref": "builtins.str"}}}, "AWS_S3CONTROL_USE_ARN_REGION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "botocore.regions.EndpointResolverBuiltins.AWS_S3CONTROL_USE_ARN_REGION", "name": "AWS_S3CONTROL_USE_ARN_REGION", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "AWS::S3Control::UseArnRegion"}, "type_ref": "builtins.str"}}}, "AWS_S3_ACCELERATE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "botocore.regions.EndpointResolverBuiltins.AWS_S3_ACCELERATE", "name": "AWS_S3_ACCELERATE", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "AWS::S3::Accelerate"}, "type_ref": "builtins.str"}}}, "AWS_S3_DISABLE_MRAP": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "botocore.regions.EndpointResolverBuiltins.AWS_S3_DISABLE_MRAP", "name": "AWS_S3_DISABLE_MRAP", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "AWS::S3::DisableMultiRegionAccessPoints"}, "type_ref": "builtins.str"}}}, "AWS_S3_FORCE_PATH_STYLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "botocore.regions.EndpointResolverBuiltins.AWS_S3_FORCE_PATH_STYLE", "name": "AWS_S3_FORCE_PATH_STYLE", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "AWS::S3::ForcePathStyle"}, "type_ref": "builtins.str"}}}, "AWS_S3_USE_ARN_REGION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "botocore.regions.EndpointResolverBuiltins.AWS_S3_USE_ARN_REGION", "name": "AWS_S3_USE_ARN_REGION", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "AWS::S3::UseArnRegion"}, "type_ref": "builtins.str"}}}, "AWS_S3_USE_GLOBAL_ENDPOINT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "botocore.regions.EndpointResolverBuiltins.AWS_S3_USE_GLOBAL_ENDPOINT", "name": "AWS_S3_USE_GLOBAL_ENDPOINT", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "AWS::S3::UseGlobalEndpoint"}, "type_ref": "builtins.str"}}}, "AWS_STS_USE_GLOBAL_ENDPOINT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "botocore.regions.EndpointResolverBuiltins.AWS_STS_USE_GLOBAL_ENDPOINT", "name": "AWS_STS_USE_GLOBAL_ENDPOINT", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "AWS::STS::UseGlobalEndpoint"}, "type_ref": "builtins.str"}}}, "AWS_USE_DUALSTACK": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "botocore.regions.EndpointResolverBuiltins.AWS_USE_DUALSTACK", "name": "AWS_USE_DUALSTACK", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "AWS::UseDualStack"}, "type_ref": "builtins.str"}}}, "AWS_USE_FIPS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "botocore.regions.EndpointResolverBuiltins.AWS_USE_FIPS", "name": "AWS_USE_FIPS", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "AWS::UseFIPS"}, "type_ref": "builtins.str"}}}, "SDK_ENDPOINT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "botocore.regions.EndpointResolverBuiltins.SDK_ENDPOINT", "name": "SDK_ENDPOINT", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "SDK::Endpoint"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.regions.EndpointResolverBuiltins.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.regions.EndpointResolverBuiltins", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EndpointRulesetResolver": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.regions.EndpointRulesetResolver", "name": "EndpointRulesetResolver", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.regions.EndpointRulesetResolver", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.regions", "mro": ["botocore.regions.EndpointRulesetResolver", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1, 1], "arg_names": ["self", "endpoint_ruleset_data", "partition_data", "service_model", "builtins", "client_context", "event_emitter", "use_ssl", "requested_auth_scheme"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.regions.EndpointRulesetResolver.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1, 1], "arg_names": ["self", "endpoint_ruleset_data", "partition_data", "service_model", "builtins", "client_context", "event_emitter", "use_ssl", "requested_auth_scheme"], "arg_types": ["botocore.regions.EndpointRulesetResolver", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, "botocore.model.ServiceModel", "botocore.regions.EndpointResolverBuiltins", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, "botocore.hooks.BaseEventHooks", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of EndpointRulesetResolver", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "auth_schemes_to_signing_ctx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "auth_schemes"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.regions.EndpointRulesetResolver.auth_schemes_to_signing_ctx", "name": "auth_schemes_to_signing_ctx", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "auth_schemes"], "arg_types": ["botocore.regions.EndpointRulesetResolver", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}], "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "auth_schemes_to_signing_ctx of EndpointRulesetResolver", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "construct_endpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "operation_model", "call_args", "request_context"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.regions.EndpointRulesetResolver.construct_endpoint", "name": "construct_endpoint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "operation_model", "call_args", "request_context"], "arg_types": ["botocore.regions.EndpointRulesetResolver", "botocore.model.OperationModel", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "construct_endpoint of EndpointRulesetResolver", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.endpoint_provider.RuleSetEndpoint"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "ruleset_error_to_botocore_exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "ruleset_exception", "params"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.regions.EndpointRulesetResolver.ruleset_error_to_botocore_exception", "name": "ruleset_error_to_botocore_exception", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "ruleset_exception", "params"], "arg_types": ["botocore.regions.EndpointRulesetResolver", "builtins.Exception", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ruleset_error_to_botocore_exception of EndpointRulesetResolver", "ret_type": "botocore.exceptions.BotoCoreError", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.regions.EndpointRulesetResolver.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.regions.EndpointRulesetResolver", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef", "module_hidden": true, "module_public": false}, "HAS_CRT": {".class": "SymbolTableNode", "cross_ref": "botocore.compat.HAS_CRT", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "LOG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "botocore.regions.LOG", "name": "LOG", "type": "logging.Logger"}}, "Logger": {".class": "SymbolTableNode", "cross_ref": "logging.Logger", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NoRegionError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.NoRegionError", "kind": "Gdef"}, "OperationModel": {".class": "SymbolTableNode", "cross_ref": "botocore.model.OperationModel", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RuleSetEndpoint": {".class": "SymbolTableNode", "cross_ref": "botocore.endpoint_provider.RuleSetEndpoint", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ServiceModel": {".class": "SymbolTableNode", "cross_ref": "botocore.model.ServiceModel", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.regions.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.regions.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.regions.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.regions.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.regions.__package__", "name": "__package__", "type": "builtins.str"}}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\botocore-stubs\\regions.pyi"}