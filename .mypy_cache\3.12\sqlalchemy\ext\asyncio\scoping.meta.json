{"data_mtime": 1759503867, "dep_lines": [24, 36, 37, 29, 46, 48, 49, 52, 57, 58, 59, 60, 27, 28, 40, 8, 10, 27, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 25, 25, 5, 25, 25, 25, 25, 25, 25, 25, 25, 10, 5, 25, 5, 5, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.ext.asyncio.session", "sqlalchemy.ext.asyncio.engine", "sqlalchemy.ext.asyncio.result", "sqlalchemy.orm.session", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.result", "sqlalchemy.orm._typing", "sqlalchemy.orm.interfaces", "sqlalchemy.sql.base", "sqlalchemy.sql.dml", "sqlalchemy.sql.elements", "sqlalchemy.sql.selectable", "sqlalchemy.exc", "sqlalchemy.util", "sqlalchemy.engine", "__future__", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "sqlalchemy.engine._py_row", "sqlalchemy.engine.base", "sqlalchemy.engine.cursor", "sqlalchemy.engine.row", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.ext.asyncio.base", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.orm", "sqlalchemy.orm.base", "sqlalchemy.orm.identity", "sqlalchemy.orm.mapper", "sqlalchemy.sql", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.compiler", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util._collections", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers"], "hash": "e6b3270f90b7b0b2bf76416305519a811c2a5de9bf6ce571800d7d1185424c85", "id": "sqlalchemy.ext.asyncio.scoping", "ignore_all": true, "interface_hash": "dbc905f0ef0587fc4f2d99bac1edfc62c3bba82b43a733ff408f75c4feb46a8a", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\scoping.py", "plugin_data": null, "size": 54183, "suppressed": [], "version_id": "1.8.0"}