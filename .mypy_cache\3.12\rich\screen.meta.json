{"data_mtime": 1759503832, "dep_lines": [3, 4, 5, 9, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 20, 5, 5, 20, 20, 30, 30], "dependencies": ["rich.segment", "rich.style", "rich._loop", "rich.console", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "enum"], "hash": "acbfe3db05fee127ae20e236a0e95ce35f103fd100bc3e7d1889d4984004ea34", "id": "rich.screen", "ignore_all": true, "interface_hash": "62bf9c5dacbb5e4490b37e2e822bedcc1d72fcee328c439e1ebbc6598f6c627c", "mtime": 1757092239, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\rich\\screen.py", "plugin_data": null, "size": 1579, "suppressed": [], "version_id": "1.8.0"}