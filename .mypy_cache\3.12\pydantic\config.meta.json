{"data_mtime": 1759504514, "dep_lines": [17, 11, 12, 13, 14, 18, 3, 5, 6, 7, 9, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 5, 5, 5, 5, 25, 5, 10, 5, 5, 5, 5, 20, 20, 30, 30, 30, 30], "dependencies": ["pydantic._internal._generate_schema", "pydantic._migration", "pydantic.aliases", "pydantic.errors", "pydantic.warnings", "pydantic.fields", "__future__", "warnings", "re", "typing", "typing_extensions", "builtins", "pyexpat.model", "pyexpat.errors", "_typeshed", "abc", "pydantic._internal", "pydantic._internal._repr"], "hash": "ae8cff15b7c53e8569255a41d46edd27c037b3082142374dfa8ceb0716cb4a13", "id": "pydantic.config", "ignore_all": true, "interface_hash": "7ea5fb82818655a63c167e0abbcb63a61ee98dea2d192f2ae33c11232530d68c", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\config.py", "plugin_data": null, "size": 42048, "suppressed": [], "version_id": "1.8.0"}