{"data_mtime": 1759504549, "dep_lines": [10, 11, 7, 8, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 20, 20, 30], "dependencies": ["botocore.awsrequest", "botocore.config", "logging", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "abc"], "hash": "8da79341f5b2f67994111ad08d7091991007f7458e5620dd61e2c2be0f0918bf", "id": "botocore.useragent", "ignore_all": true, "interface_hash": "ab53edf8978f5106e36b103b4c0ec52ec9c22ddfc4da4a356e11f2b8b87b5d92", "mtime": 1757092235, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\botocore-stubs\\useragent.pyi", "plugin_data": null, "size": 1763, "suppressed": [], "version_id": "1.8.0"}