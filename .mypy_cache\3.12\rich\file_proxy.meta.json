{"data_mtime": 1759504512, "dep_lines": [4, 5, 8, 1, 2, 1, 1, 1, 1], "dep_prios": [5, 5, 25, 10, 5, 5, 20, 20, 30], "dependencies": ["rich.ansi", "rich.text", "rich.console", "io", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "abc"], "hash": "4e5f531cc0d9f8f9395a6f2c23580683f5390e1bac9b10fe159d1f51b714d16d", "id": "rich.file_proxy", "ignore_all": true, "interface_hash": "cf8bf8bb7e8e58165c36da6006255d976a80c535547c6bce29007f6e5f7712da", "mtime": 1757092238, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\rich\\file_proxy.py", "plugin_data": null, "size": 1683, "suppressed": [], "version_id": "1.8.0"}