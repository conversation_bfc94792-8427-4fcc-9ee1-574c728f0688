{"data_mtime": 1759503866, "dep_lines": [10, 13, 14, 18, 22, 108, 7, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 30], "dependencies": ["sqlalchemy.sql._typing", "sqlalchemy.sql.base", "sqlalchemy.sql.compiler", "sqlalchemy.sql.ddl", "sqlalchemy.sql.expression", "sqlalchemy.sql.visitors", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "abc"], "hash": "f3eda95b83ecb0570e339d1b5bcbb76e6b15123100eb349b388fef89386e2e1b", "id": "sqlalchemy.sql", "ignore_all": true, "interface_hash": "01561147b39145eea435691affd106006540569e51afebaf359f2e7cb59ce9dd", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\__init__.py", "plugin_data": null, "size": 5965, "suppressed": [], "version_id": "1.8.0"}