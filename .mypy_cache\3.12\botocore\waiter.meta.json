{"data_mtime": 1759503869, "dep_lines": [7, 11, 8, 9, 13, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 20, 20, 30], "dependencies": ["collections.abc", "botocore.client", "logging", "typing", "botocore", "builtins", "pyexpat.model", "pyexpat.errors", "abc"], "hash": "e8a8a960d4febf1f95837876ccaab579553f2eb395c40b7faa7f3a22b782d2f4", "id": "botocore.waiter", "ignore_all": true, "interface_hash": "030a7d1d8bef5b64a3fdb65348470c75f8e17c555abf4b38fc428048031a071e", "mtime": 1757092235, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\botocore-stubs\\waiter.pyi", "plugin_data": null, "size": 1687, "suppressed": [], "version_id": "1.8.0"}