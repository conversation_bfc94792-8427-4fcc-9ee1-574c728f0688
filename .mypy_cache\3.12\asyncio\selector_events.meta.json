{"data_mtime": 1759504514, "dep_lines": [3, 1, 3, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 20, 5, 20, 20, 30, 30, 30], "dependencies": ["asyncio.base_events", "selectors", "asyncio", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "asyncio.events", "typing"], "hash": "57603bd439e8bc0593343071538914941ea6da2e06bcded9cf3bd32ce088b97a", "id": "asyncio.selector_events", "ignore_all": true, "interface_hash": "ec1d0a79a93fd6fb3b69dc0859e567889043d82a64007e70d9efca0131bc57ba", "mtime": 1757092231, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\asyncio\\selector_events.pyi", "plugin_data": null, "size": 223, "suppressed": [], "version_id": "1.8.0"}