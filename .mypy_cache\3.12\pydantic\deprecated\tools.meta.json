{"data_mtime": 1759504514, "dep_lines": [9, 10, 11, 1, 3, 4, 5, 7, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 10, 5, 5, 5, 20, 20, 30], "dependencies": ["pydantic.json_schema", "pydantic.type_adapter", "pydantic.warnings", "__future__", "json", "warnings", "typing", "typing_extensions", "builtins", "pyexpat.model", "pyexpat.errors", "abc"], "hash": "36b9bda054595a9f3e8e57ef3e02482c4b32c29e18cd90f9d972063c32f11dc2", "id": "pydantic.deprecated.tools", "ignore_all": true, "interface_hash": "c16206ba9543db32e53357c3190df4b2567978c453beed411489d8627aa4e6e6", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\deprecated\\tools.py", "plugin_data": null, "size": 3330, "suppressed": [], "version_id": "1.8.0"}