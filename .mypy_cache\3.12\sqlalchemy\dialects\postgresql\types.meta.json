{"data_mtime": 1759504547, "dep_lines": [17, 18, 19, 22, 23, 17, 7, 9, 10, 15, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 25, 25, 20, 5, 10, 5, 5, 5, 20, 20, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.sqltypes", "sqlalchemy.sql.type_api", "sqlalchemy.util.typing", "sqlalchemy.engine.interfaces", "sqlalchemy.sql.operators", "sqlalchemy.sql", "__future__", "datetime", "typing", "uuid", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "sqlalchemy.engine", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.sql.base", "sqlalchemy.sql.visitors"], "hash": "8d762e11fec336dbfb9e5d4ece55442399c9803038db7fe497a49b0dd5e785b5", "id": "sqlalchemy.dialects.postgresql.types", "ignore_all": true, "interface_hash": "d6e7c2f2df27ed4063bfeffc9d32cb379f2cb22db166ba192c1d4a8a5efb06b1", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\types.py", "plugin_data": null, "size": 7942, "suppressed": [], "version_id": "1.8.0"}