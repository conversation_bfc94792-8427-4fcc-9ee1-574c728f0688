{"data_mtime": 1759503866, "dep_lines": [27, 28, 31, 32, 36, 37, 38, 39, 41, 43, 27, 29, 30, 31, 9, 11, 12, 13, 14, 15, 29, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 5, 25, 25, 25, 25, 25, 25, 20, 10, 10, 20, 5, 5, 5, 10, 10, 5, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.base", "sqlalchemy.orm._typing", "sqlalchemy.sql.visitors", "sqlalchemy.sql.cache_key", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.mapper", "sqlalchemy.orm.relationships", "sqlalchemy.orm.util", "sqlalchemy.sql.elements", "sqlalchemy.util.typing", "sqlalchemy.orm", "sqlalchemy.exc", "sqlalchemy.util", "sqlalchemy.sql", "__future__", "functools", "itertools", "logging", "operator", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "abc", "enum", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.orm.query", "sqlalchemy.sql._py_util", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.util.langhelpers", "types"], "hash": "65da331d43921766dc6335eb5e0f976b42e64ab1507a85a76fa699e322ddb499", "id": "sqlalchemy.orm.path_registry", "ignore_all": true, "interface_hash": "bdf8d3bf983b2bbdb2e90b82c611cb645d2e7887088ab97998c957a874c7cf71", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\path_registry.py", "plugin_data": null, "size": 26723, "suppressed": [], "version_id": "1.8.0"}