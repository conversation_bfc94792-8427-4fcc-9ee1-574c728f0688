{".class": "MypyFile", "_fullname": "smartanalytics_domain.ports.repositories", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ABC": {".class": "SymbolTableNode", "cross_ref": "abc.ABC", "kind": "Gdef"}, "Agent": {".class": "SymbolTableNode", "cross_ref": "smartanalytics_domain.models.agent.Agent", "kind": "Gdef"}, "AgentEvent": {".class": "SymbolTableNode", "cross_ref": "smartanalytics_domain.models.agent_event.AgentEvent", "kind": "Gdef"}, "AgentEventRepository": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["save", 1], ["save_batch", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "smartanalytics_domain.ports.repositories.AgentEventRepository", "name": "AgentEventRepository", "type_vars": []}, "deletable_attributes": [], "flags": ["is_abstract"], "fullname": "smartanalytics_domain.ports.repositories.AgentEventRepository", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "smartanalytics_domain.ports.repositories", "mro": ["smartanalytics_domain.ports.repositories.AgentEventRepository", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "save": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0], "arg_names": ["self", "agent_event", "dimension_keys"], "dataclass_transform_spec": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_body"], "fullname": "smartanalytics_domain.ports.repositories.AgentEventRepository.save", "name": "save", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "agent_event", "dimension_keys"], "arg_types": ["smartanalytics_domain.ports.repositories.AgentEventRepository", "smartanalytics_domain.models.agent_event.AgentEvent", {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "save of AgentEventRepository", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.int"], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "smartanalytics_domain.ports.repositories.AgentEventRepository.save", "name": "save", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "agent_event", "dimension_keys"], "arg_types": ["smartanalytics_domain.ports.repositories.AgentEventRepository", "smartanalytics_domain.models.agent_event.AgentEvent", {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "save of AgentEventRepository", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.int"], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "save_batch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0], "arg_names": ["self", "agent_events", "dimension_keys_list"], "dataclass_transform_spec": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_body"], "fullname": "smartanalytics_domain.ports.repositories.AgentEventRepository.save_batch", "name": "save_batch", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "agent_events", "dimension_keys_list"], "arg_types": ["smartanalytics_domain.ports.repositories.AgentEventRepository", {".class": "Instance", "args": ["smartanalytics_domain.models.agent_event.AgentEvent"], "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "type_ref": "builtins.dict"}], "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "save_batch of AgentEventRepository", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["builtins.int"], "type_ref": "builtins.list"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "smartanalytics_domain.ports.repositories.AgentEventRepository.save_batch", "name": "save_batch", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "agent_events", "dimension_keys_list"], "arg_types": ["smartanalytics_domain.ports.repositories.AgentEventRepository", {".class": "Instance", "args": ["smartanalytics_domain.models.agent_event.AgentEvent"], "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "type_ref": "builtins.dict"}], "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "save_batch of AgentEventRepository", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["builtins.int"], "type_ref": "builtins.list"}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "smartanalytics_domain.ports.repositories.AgentEventRepository.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "smartanalytics_domain.ports.repositories.AgentEventRepository", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AgentRepository": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["create", 1], ["get_by_tenant_and_name", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "smartanalytics_domain.ports.repositories.AgentRepository", "name": "AgentRepository", "type_vars": []}, "deletable_attributes": [], "flags": ["is_abstract"], "fullname": "smartanalytics_domain.ports.repositories.AgentRepository", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "smartanalytics_domain.ports.repositories", "mro": ["smartanalytics_domain.ports.repositories.AgentRepository", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "tenant_key", "agent_name", "agent_role"], "dataclass_transform_spec": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_body"], "fullname": "smartanalytics_domain.ports.repositories.AgentRepository.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "tenant_key", "agent_name", "agent_role"], "arg_types": ["smartanalytics_domain.ports.repositories.AgentRepository", "builtins.int", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of AgentRepository", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.int"], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "smartanalytics_domain.ports.repositories.AgentRepository.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "tenant_key", "agent_name", "agent_role"], "arg_types": ["smartanalytics_domain.ports.repositories.AgentRepository", "builtins.int", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of AgentRepository", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.int"], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "get_by_tenant_and_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tenant_key", "agent_name"], "dataclass_transform_spec": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_body"], "fullname": "smartanalytics_domain.ports.repositories.AgentRepository.get_by_tenant_and_name", "name": "get_by_tenant_and_name", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tenant_key", "agent_name"], "arg_types": ["smartanalytics_domain.ports.repositories.AgentRepository", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_by_tenant_and_name of AgentRepository", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["smartanalytics_domain.models.agent.Agent", {".class": "NoneType"}]}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "smartanalytics_domain.ports.repositories.AgentRepository.get_by_tenant_and_name", "name": "get_by_tenant_and_name", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tenant_key", "agent_name"], "arg_types": ["smartanalytics_domain.ports.repositories.AgentRepository", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_by_tenant_and_name of AgentRepository", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["smartanalytics_domain.models.agent.Agent", {".class": "NoneType"}]}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "smartanalytics_domain.ports.repositories.AgentRepository.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "smartanalytics_domain.ports.repositories.AgentRepository", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RingGroup": {".class": "SymbolTableNode", "cross_ref": "smartanalytics_domain.models.ring_group.RingGroup", "kind": "Gdef"}, "RingGroupRepository": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["create", 1], ["get_by_tenant_and_name", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "smartanalytics_domain.ports.repositories.RingGroupRepository", "name": "RingGroupRepository", "type_vars": []}, "deletable_attributes": [], "flags": ["is_abstract"], "fullname": "smartanalytics_domain.ports.repositories.RingGroupRepository", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "smartanalytics_domain.ports.repositories", "mro": ["smartanalytics_domain.ports.repositories.RingGroupRepository", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tenant_key", "ring_group_name"], "dataclass_transform_spec": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_body"], "fullname": "smartanalytics_domain.ports.repositories.RingGroupRepository.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tenant_key", "ring_group_name"], "arg_types": ["smartanalytics_domain.ports.repositories.RingGroupRepository", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of RingGroupRepository", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.int"], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "smartanalytics_domain.ports.repositories.RingGroupRepository.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tenant_key", "ring_group_name"], "arg_types": ["smartanalytics_domain.ports.repositories.RingGroupRepository", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of RingGroupRepository", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.int"], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "get_by_tenant_and_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tenant_key", "ring_group_name"], "dataclass_transform_spec": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_body"], "fullname": "smartanalytics_domain.ports.repositories.RingGroupRepository.get_by_tenant_and_name", "name": "get_by_tenant_and_name", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tenant_key", "ring_group_name"], "arg_types": ["smartanalytics_domain.ports.repositories.RingGroupRepository", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_by_tenant_and_name of RingGroupRepository", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["smartanalytics_domain.models.ring_group.RingGroup", {".class": "NoneType"}]}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "smartanalytics_domain.ports.repositories.RingGroupRepository.get_by_tenant_and_name", "name": "get_by_tenant_and_name", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tenant_key", "ring_group_name"], "arg_types": ["smartanalytics_domain.ports.repositories.RingGroupRepository", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_by_tenant_and_name of RingGroupRepository", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["smartanalytics_domain.models.ring_group.RingGroup", {".class": "NoneType"}]}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "smartanalytics_domain.ports.repositories.RingGroupRepository.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "smartanalytics_domain.ports.repositories.RingGroupRepository", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tenant": {".class": "SymbolTableNode", "cross_ref": "smartanalytics_domain.models.tenant.Tenant", "kind": "Gdef"}, "TenantRepository": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["create", 1], ["get_by_name", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "smartanalytics_domain.ports.repositories.TenantRepository", "name": "TenantRepository", "type_vars": []}, "deletable_attributes": [], "flags": ["is_abstract"], "fullname": "smartanalytics_domain.ports.repositories.TenantRepository", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "smartanalytics_domain.ports.repositories", "mro": ["smartanalytics_domain.ports.repositories.TenantRepository", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tenant_name", "timezone_name"], "dataclass_transform_spec": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_body"], "fullname": "smartanalytics_domain.ports.repositories.TenantRepository.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tenant_name", "timezone_name"], "arg_types": ["smartanalytics_domain.ports.repositories.TenantRepository", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of TenantRepository", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.int"], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "smartanalytics_domain.ports.repositories.TenantRepository.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tenant_name", "timezone_name"], "arg_types": ["smartanalytics_domain.ports.repositories.TenantRepository", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of TenantRepository", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.int"], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "get_by_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "tenant_name"], "dataclass_transform_spec": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_body"], "fullname": "smartanalytics_domain.ports.repositories.TenantRepository.get_by_name", "name": "get_by_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tenant_name"], "arg_types": ["smartanalytics_domain.ports.repositories.TenantRepository", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_by_name of TenantRepository", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["smartanalytics_domain.models.tenant.Tenant", {".class": "NoneType"}]}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "smartanalytics_domain.ports.repositories.TenantRepository.get_by_name", "name": "get_by_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tenant_name"], "arg_types": ["smartanalytics_domain.ports.repositories.TenantRepository", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_by_name of TenantRepository", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["smartanalytics_domain.models.tenant.Tenant", {".class": "NoneType"}]}], "type_ref": "typing.Coroutine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "smartanalytics_domain.ports.repositories.TenantRepository.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "smartanalytics_domain.ports.repositories.TenantRepository", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "smartanalytics_domain.ports.repositories.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "smartanalytics_domain.ports.repositories.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "smartanalytics_domain.ports.repositories.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "smartanalytics_domain.ports.repositories.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "smartanalytics_domain.ports.repositories.__package__", "name": "__package__", "type": "builtins.str"}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef"}}, "path": "layers\\domain\\python\\smartanalytics_domain\\ports\\repositories.py"}