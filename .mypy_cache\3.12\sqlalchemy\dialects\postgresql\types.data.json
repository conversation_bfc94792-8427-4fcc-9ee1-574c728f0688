{".class": "MypyFile", "_fullname": "sqlalchemy.dialects.postgresql.types", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BIT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.int"], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.types.BIT", "name": "BIT", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.types.BIT", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.types", "mro": ["sqlalchemy.dialects.postgresql.types.BIT", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "length", "varying"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.types.BIT.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "length", "varying"], "arg_types": ["sqlalchemy.dialects.postgresql.types.BIT", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BIT", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.types.BIT.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "length": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.types.BIT.length", "name": "length", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}}}, "varying": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.types.BIT.varying", "name": "varying", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.types.BIT.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.types.BIT", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BYTEA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.sqltypes.LargeBinary"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.types.BYTEA", "name": "BYTEA", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.types.BYTEA", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.types", "mro": ["sqlalchemy.dialects.postgresql.types.BYTEA", "sqlalchemy.sql.sqltypes.LargeBinary", "sqlalchemy.sql.sqltypes._Binary", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.types.BYTEA.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.types.BYTEA.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.types.BYTEA", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CIDR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.postgresql.types._NetworkAddressTypeMixin", {".class": "Instance", "args": ["builtins.str"], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.types.CIDR", "name": "CIDR", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.types.CIDR", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.types", "mro": ["sqlalchemy.dialects.postgresql.types.CIDR", "sqlalchemy.dialects.postgresql.types._NetworkAddressTypeMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.types.CIDR.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.types.CIDR.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.types.CIDR", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CITEXT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.sqltypes.TEXT"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.types.CITEXT", "name": "CITEXT", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.types.CITEXT", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.types", "mro": ["sqlalchemy.dialects.postgresql.types.CITEXT", "sqlalchemy.sql.sqltypes.TEXT", "sqlalchemy.sql.sqltypes.Text", "sqlalchemy.sql.sqltypes.String", "sqlalchemy.sql.sqltypes.Concatenable", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.types.CITEXT.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "coerce_compared_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "op", "value"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.types.CITEXT.coerce_compared_value", "name": "coerce_compared_value", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "op", "value"], "arg_types": ["sqlalchemy.dialects.postgresql.types.CITEXT", {".class": "UnionType", "items": ["sqlalchemy.sql.operators.OperatorType", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "coerce_compared_value of CITEXT", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.types.CITEXT.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.types.CITEXT", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dialect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.Dialect", "kind": "Gdef"}, "INET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.postgresql.types._NetworkAddressTypeMixin", {".class": "Instance", "args": ["builtins.str"], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.types.INET", "name": "INET", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.types.INET", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.types", "mro": ["sqlalchemy.dialects.postgresql.types.INET", "sqlalchemy.dialects.postgresql.types._NetworkAddressTypeMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.types.INET.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.types.INET.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.types.INET", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "INTERVAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.type_api.NativeForEmulated", "sqlalchemy.sql.sqltypes._AbstractInterval"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.types.INTERVAL", "name": "INTERVAL", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.types.INTERVAL", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.types", "mro": ["sqlalchemy.dialects.postgresql.types.INTERVAL", "sqlalchemy.sql.type_api.NativeForEmulated", "sqlalchemy.sql.sqltypes._AbstractInterval", "sqlalchemy.sql.sqltypes.HasExpressionLookup", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "precision", "fields"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.types.INTERVAL.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "precision", "fields"], "arg_types": ["sqlalchemy.dialects.postgresql.types.INTERVAL", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of INTERVAL", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.types.INTERVAL.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "_type_affinity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.types.INTERVAL._type_affinity", "name": "_type_affinity", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.dialects.postgresql.types.INTERVAL"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_type_affinity of INTERVAL", "ret_type": {".class": "TypeType", "item": "sqlalchemy.sql.sqltypes.Interval"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.types.INTERVAL._type_affinity", "name": "_type_affinity", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.dialects.postgresql.types.INTERVAL"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_type_affinity of INTERVAL", "ret_type": {".class": "TypeType", "item": "sqlalchemy.sql.sqltypes.Interval"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "adapt_emulated_to_native": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["cls", "interval", "kw"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.types.INTERVAL.adapt_emulated_to_native", "name": "adapt_emulated_to_native", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["cls", "interval", "kw"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.dialects.postgresql.types.INTERVAL"}, "sqlalchemy.sql.sqltypes.Interval", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "adapt_emulated_to_native of INTERVAL", "ret_type": "sqlalchemy.dialects.postgresql.types.INTERVAL", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.types.INTERVAL.adapt_emulated_to_native", "name": "adapt_emulated_to_native", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["cls", "interval", "kw"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.dialects.postgresql.types.INTERVAL"}, "sqlalchemy.sql.sqltypes.Interval", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "adapt_emulated_to_native of INTERVAL", "ret_type": "sqlalchemy.dialects.postgresql.types.INTERVAL", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "as_generic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "allow_nulltype"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.types.INTERVAL.as_generic", "name": "as_generic", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "allow_nulltype"], "arg_types": ["sqlalchemy.dialects.postgresql.types.INTERVAL", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_generic of INTERVAL", "ret_type": "sqlalchemy.sql.sqltypes.Interval", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "fields": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.types.INTERVAL.fields", "name": "fields", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "literal_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dialect"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.types.INTERVAL.literal_processor", "name": "literal_processor", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dialect"], "arg_types": ["sqlalchemy.dialects.postgresql.types.INTERVAL", "sqlalchemy.engine.interfaces.Dialect"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "literal_processor of INTERVAL", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["datetime.<PERSON><PERSON><PERSON>"], "type_ref": "sqlalchemy.sql.type_api._LiteralProcessorType"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "native": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.types.INTERVAL.native", "name": "native", "type": "builtins.bool"}}, "precision": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.types.INTERVAL.precision", "name": "precision", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}}}, "python_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.types.INTERVAL.python_type", "name": "python_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.dialects.postgresql.types.INTERVAL"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "python_type of INTERVAL", "ret_type": {".class": "TypeType", "item": "datetime.<PERSON><PERSON><PERSON>"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.types.INTERVAL.python_type", "name": "python_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.dialects.postgresql.types.INTERVAL"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "python_type of INTERVAL", "ret_type": {".class": "TypeType", "item": "datetime.<PERSON><PERSON><PERSON>"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.types.INTERVAL.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.types.INTERVAL", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef"}, "MACADDR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.postgresql.types._NetworkAddressTypeMixin", {".class": "Instance", "args": ["builtins.str"], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.types.MACADDR", "name": "MACADDR", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.types.MACADDR", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.types", "mro": ["sqlalchemy.dialects.postgresql.types.MACADDR", "sqlalchemy.dialects.postgresql.types._NetworkAddressTypeMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.types.MACADDR.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.types.MACADDR.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.types.MACADDR", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MACADDR8": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.postgresql.types._NetworkAddressTypeMixin", {".class": "Instance", "args": ["builtins.str"], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.types.MACADDR8", "name": "MACADDR8", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.types.MACADDR8", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.types", "mro": ["sqlalchemy.dialects.postgresql.types.MACADDR8", "sqlalchemy.dialects.postgresql.types._NetworkAddressTypeMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.types.MACADDR8.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.types.MACADDR8.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.types.MACADDR8", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MONEY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.types.MONEY", "name": "MONEY", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.types.MONEY", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.types", "mro": ["sqlalchemy.dialects.postgresql.types.MONEY", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.types.MONEY.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.types.MONEY.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.types.MONEY", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.int"], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.types.OID", "name": "OID", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.types.OID", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.types", "mro": ["sqlalchemy.dialects.postgresql.types.OID", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.types.OID.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.types.OID.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.types.OID", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OperatorType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.operators.OperatorType", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PGBit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.postgresql.types.PGBit", "line": 282, "no_args": true, "normalized": false, "target": "sqlalchemy.dialects.postgresql.types.BIT"}}, "PGCidr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.postgresql.types.PGCidr", "line": 76, "no_args": true, "normalized": false, "target": "sqlalchemy.dialects.postgresql.types.CIDR"}}, "PGInet": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.postgresql.types.PGInet", "line": 69, "no_args": true, "normalized": false, "target": "sqlalchemy.dialects.postgresql.types.INET"}}, "PGInterval": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.postgresql.types.PGInterval", "line": 264, "no_args": true, "normalized": false, "target": "sqlalchemy.dialects.postgresql.types.INTERVAL"}}, "PGMacAddr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.postgresql.types.PGMacAddr", "line": 83, "no_args": true, "normalized": false, "target": "sqlalchemy.dialects.postgresql.types.MACADDR"}}, "PGMacAddr8": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.postgresql.types.PGMacAddr8", "line": 90, "no_args": true, "normalized": false, "target": "sqlalchemy.dialects.postgresql.types.MACADDR8"}}, "PGUuid": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.sqltypes._UUID_RETURN", "id": 1, "name": "sqltypes._UUID_RETURN", "namespace": "sqlalchemy.dialects.postgresql.types.PGUuid", "upper_bound": "builtins.object", "values": ["builtins.str", "uuid.UUID"], "variance": 0}], "type_ref": "sqlalchemy.sql.sqltypes.UUID"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.types.PGUuid", "name": "PGUuid", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.sqltypes._UUID_RETURN", "id": 1, "name": "sqltypes._UUID_RETURN", "namespace": "sqlalchemy.dialects.postgresql.types.PGUuid", "upper_bound": "builtins.object", "values": ["builtins.str", "uuid.UUID"], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.types.PGUuid", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.types", "mro": ["sqlalchemy.dialects.postgresql.types.PGUuid", "sqlalchemy.sql.sqltypes.UUID", "sqlalchemy.sql.sqltypes.Uuid", "sqlalchemy.sql.type_api.Emulated", "sqlalchemy.sql.type_api.NativeForEmulated", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "sqlalchemy.dialects.postgresql.types.PGUuid.__init__", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "as_uuid"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_mypy_only"], "fullname": "sqlalchemy.dialects.postgresql.types.PGUuid.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "as_uuid"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.sqltypes._UUID_RETURN", "id": 1, "name": "sqltypes._UUID_RETURN", "namespace": "sqlalchemy.dialects.postgresql.types.PGUuid", "upper_bound": "builtins.object", "values": ["builtins.str", "uuid.UUID"], "variance": 0}], "type_ref": "sqlalchemy.dialects.postgresql.types.PGUuid"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PGUuid", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "as_uuid"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated", "is_mypy_only"], "fullname": "sqlalchemy.dialects.postgresql.types.PGUuid.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "as_uuid"], "arg_types": [{".class": "Instance", "args": ["uuid.UUID"], "type_ref": "sqlalchemy.dialects.postgresql.types.PGUuid"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PGUuid", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.types.PGUuid.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "as_uuid"], "arg_types": [{".class": "Instance", "args": ["uuid.UUID"], "type_ref": "sqlalchemy.dialects.postgresql.types.PGUuid"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PGUuid", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "as_uuid"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated", "is_mypy_only"], "fullname": "sqlalchemy.dialects.postgresql.types.PGUuid.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "as_uuid"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "sqlalchemy.dialects.postgresql.types.PGUuid"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PGUuid", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.types.PGUuid.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "as_uuid"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "sqlalchemy.dialects.postgresql.types.PGUuid"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PGUuid", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "as_uuid"], "arg_types": [{".class": "Instance", "args": ["uuid.UUID"], "type_ref": "sqlalchemy.dialects.postgresql.types.PGUuid"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PGUuid", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "as_uuid"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "sqlalchemy.dialects.postgresql.types.PGUuid"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PGUuid", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}]}}}, "render_bind_cast": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.types.PGUuid.render_bind_cast", "name": "render_bind_cast", "type": "builtins.bool"}}, "render_literal_cast": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.types.PGUuid.render_literal_cast", "name": "render_literal_cast", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.types.PGUuid.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.sqltypes._UUID_RETURN", "id": 1, "name": "sqltypes._UUID_RETURN", "namespace": "sqlalchemy.dialects.postgresql.types.PGUuid", "upper_bound": "builtins.object", "values": ["builtins.str", "uuid.UUID"], "variance": 0}], "type_ref": "sqlalchemy.dialects.postgresql.types.PGUuid"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["sqltypes._UUID_RETURN"], "typeddict_type": null}}, "REGCLASS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.types.REGCLASS", "name": "REGCLASS", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.types.REGCLASS", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.types", "mro": ["sqlalchemy.dialects.postgresql.types.REGCLASS", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.types.REGCLASS.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.types.REGCLASS.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.types.REGCLASS", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "REGCONFIG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.types.REGCONFIG", "name": "REGCONFIG", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.types.REGCONFIG", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.types", "mro": ["sqlalchemy.dialects.postgresql.types.REGCONFIG", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.types.REGCONFIG.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.types.REGCONFIG.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.types.REGCONFIG", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TIME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.sqltypes.TIME"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.types.TIME", "name": "TIME", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.types.TIME", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.types", "mro": ["sqlalchemy.dialects.postgresql.types.TIME", "sqlalchemy.sql.sqltypes.TIME", "sqlalchemy.sql.sqltypes.Time", "sqlalchemy.sql.sqltypes._RenderISO8601NoT", "sqlalchemy.sql.sqltypes.HasExpressionLookup", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "timezone", "precision"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.types.TIME.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "timezone", "precision"], "arg_types": ["sqlalchemy.dialects.postgresql.types.TIME", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TIME", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.types.TIME.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "precision": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.types.TIME.precision", "name": "precision", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.types.TIME.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.types.TIME", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TIMESTAMP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.sqltypes.TIMESTAMP"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.types.TIMESTAMP", "name": "TIMESTAMP", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.types.TIMESTAMP", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.types", "mro": ["sqlalchemy.dialects.postgresql.types.TIMESTAMP", "sqlalchemy.sql.sqltypes.TIMESTAMP", "sqlalchemy.sql.sqltypes.DateTime", "sqlalchemy.sql.sqltypes._RenderISO8601NoT", "sqlalchemy.sql.sqltypes.HasExpressionLookup", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "timezone", "precision"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.types.TIMESTAMP.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "timezone", "precision"], "arg_types": ["sqlalchemy.dialects.postgresql.types.TIMESTAMP", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TIMESTAMP", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.types.TIMESTAMP.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "precision": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.types.TIMESTAMP.precision", "name": "precision", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.types.TIMESTAMP.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.types.TIMESTAMP", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TSQUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.types.TSQUERY", "name": "TSQUERY", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.types.TSQUERY", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.types", "mro": ["sqlalchemy.dialects.postgresql.types.TSQUERY", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.types.TSQUERY.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.types.TSQUERY.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.types.TSQUERY", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TSVECTOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.types.TSVECTOR", "name": "TSVECTOR", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.types.TSVECTOR", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.types", "mro": ["sqlalchemy.dialects.postgresql.types.TSVECTOR", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.types.TSVECTOR.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.types.TSVECTOR.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.types.TSVECTOR", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypeEngine": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.type_api.TypeEngine", "kind": "Gdef"}, "_DECIMAL_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.types._DECIMAL_TYPES", "name": "_DECIMAL_TYPES", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_FLOAT_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.types._FLOAT_TYPES", "name": "_FLOAT_TYPES", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_INT_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.types._INT_TYPES", "name": "_INT_TYPES", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_LiteralProcessorType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.type_api._LiteralProcessorType", "kind": "Gdef"}, "_NetworkAddressTypeMixin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.types._NetworkAddressTypeMixin", "name": "_NetworkAddressTypeMixin", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.types._NetworkAddressTypeMixin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.types", "mro": ["sqlalchemy.dialects.postgresql.types._NetworkAddressTypeMixin", "builtins.object"], "names": {".class": "SymbolTable", "coerce_compared_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "op", "value"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.types._NetworkAddressTypeMixin.coerce_compared_value", "name": "coerce_compared_value", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "op", "value"], "arg_types": ["sqlalchemy.dialects.postgresql.types._NetworkAddressTypeMixin", {".class": "UnionType", "items": ["sqlalchemy.sql.operators.OperatorType", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "coerce_compared_value of _NetworkAddressTypeMixin", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.types._NetworkAddressTypeMixin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.types._NetworkAddressTypeMixin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.postgresql.types.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.postgresql.types.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.postgresql.types.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.postgresql.types.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.postgresql.types.__package__", "name": "__package__", "type": "builtins.str"}}, "_python_UUID": {".class": "SymbolTableNode", "cross_ref": "uuid.UUID", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "dt": {".class": "SymbolTableNode", "cross_ref": "datetime", "kind": "Gdef"}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "sqltypes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes", "kind": "Gdef"}, "type_api": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.type_api", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\types.py"}