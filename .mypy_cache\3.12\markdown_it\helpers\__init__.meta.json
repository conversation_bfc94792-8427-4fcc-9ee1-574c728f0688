{"data_mtime": 1759503829, "dep_lines": [4, 5, 6, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 20, 20, 30, 30], "dependencies": ["markdown_it.helpers.parse_link_destination", "markdown_it.helpers.parse_link_label", "markdown_it.helpers.parse_link_title", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "typing"], "hash": "607db3edd4b459473ff65e753163efacbb45a013e1e092c6c39f0eba119108ad", "id": "markdown_it.helpers", "ignore_all": true, "interface_hash": "502fdb4a66a7a55b3cc52b3445b39d56909835b8c5c81dd01b56f08f749c9edf", "mtime": 1757092234, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\markdown_it\\helpers\\__init__.py", "plugin_data": null, "size": 253, "suppressed": [], "version_id": "1.8.0"}