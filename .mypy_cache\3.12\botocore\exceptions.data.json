{".class": "MypyFile", "_fullname": "botocore.exceptions", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "AliasConflictParameterError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.ValidationError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.AliasConflictParameterError", "name": "AliasConflictParameterError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.AliasConflictParameterError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.AliasConflictParameterError", "botocore.exceptions.ValidationError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 4], "arg_names": ["self", "original", "alias", "operation", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.AliasConflictParameterError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 4], "arg_names": ["self", "original", "alias", "operation", "kwargs"], "arg_types": ["botocore.exceptions.AliasConflictParameterError", "builtins.str", "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AliasConflictParameterError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.AliasConflictParameterError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._AliasConflictParameterErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.AliasConflictParameterError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.AliasConflictParameterError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ApiVersionNotFoundError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.ApiVersionNotFoundError", "name": "ApiVersionNotFoundError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.ApiVersionNotFoundError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.ApiVersionNotFoundError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 4], "arg_names": ["self", "data_path", "api_version", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.ApiVersionNotFoundError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 4], "arg_names": ["self", "data_path", "api_version", "kwargs"], "arg_types": ["botocore.exceptions.ApiVersionNotFoundError", "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ApiVersionNotFoundError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.ApiVersionNotFoundError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._ApiVersionNotFoundErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.ApiVersionNotFoundError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.ApiVersionNotFoundError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AwsChunkedWrapperError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.AwsChunkedWrapperError", "name": "AwsChunkedWrapperError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.AwsChunkedWrapperError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.AwsChunkedWrapperError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "error_msg", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.AwsChunkedWrapperError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "error_msg", "kwargs"], "arg_types": ["botocore.exceptions.AwsChunkedWrapperError", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AwsChunkedWrapperError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.AwsChunkedWrapperError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._AwsChunkedWrapperErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.AwsChunkedWrapperError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.AwsChunkedWrapperError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BaseEndpointResolverError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.BaseEndpointResolverError", "name": "BaseEndpointResolverError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.BaseEndpointResolverError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.BaseEndpointResolverError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.BaseEndpointResolverError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.BaseEndpointResolverError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BotoCoreError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.BotoCoreError", "name": "BotoCoreError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.BotoCoreError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.BotoCoreError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["botocore.exceptions.BotoCoreError", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BotoCoreError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "fmt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "botocore.exceptions.BotoCoreError.fmt", "name": "fmt", "type": "builtins.str"}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.BotoCoreError.kwargs", "name": "kwargs", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.BotoCoreError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.BotoCoreError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CapacityNotAvailableError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.CapacityNotAvailableError", "name": "CapacityNotAvailableError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.CapacityNotAvailableError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.CapacityNotAvailableError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.CapacityNotAvailableError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.CapacityNotAvailableError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ChecksumError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.ChecksumError", "name": "ChecksumError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.ChecksumError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.ChecksumError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 4], "arg_names": ["self", "checksum_type", "expected_checksum", "actual_checksum", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.ChecksumError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 4], "arg_names": ["self", "checksum_type", "expected_checksum", "actual_checksum", "kwargs"], "arg_types": ["botocore.exceptions.ChecksumError", "builtins.str", "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ChecksumError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.ChecksumError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._ChecksumErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.ChecksumError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.ChecksumError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClientError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.ClientError", "name": "ClientError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.ClientError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.ClientError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "MSG_TEMPLATE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "botocore.exceptions.ClientError.MSG_TEMPLATE", "name": "MSG_TEMPLATE", "type": "builtins.str"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "error_response", "operation_name"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.ClientError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "error_response", "operation_name"], "arg_types": ["botocore.exceptions.ClientError", {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._ClientErrorResponseTypeDef"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ClientError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "operation_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.ClientError.operation_name", "name": "operation_name", "type": "builtins.str"}}, "response": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.ClientError.response", "name": "response", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._ClientErrorResponseTypeDef"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.ClientError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.ClientError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConfigNotFound": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.ConfigNotFound", "name": "ConfigNotFound", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.ConfigNotFound", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.ConfigNotFound", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "path", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.ConfigNotFound.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "path", "kwargs"], "arg_types": ["botocore.exceptions.ConfigNotFound", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ConfigNotFound", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.ConfigNotFound.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._ConfigNotFoundKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.ConfigNotFound.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.ConfigNotFound", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConfigParseError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.ConfigParseError", "name": "ConfigParseError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.ConfigParseError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.ConfigParseError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "path", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.ConfigParseError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "path", "kwargs"], "arg_types": ["botocore.exceptions.ConfigParseError", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ConfigParseError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.ConfigParseError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._ConfigParseErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.ConfigParseError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.ConfigParseError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConnectTimeoutError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.ConnectionError", "requests.exceptions.ConnectTimeout"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.ConnectTimeoutError", "name": "ConnectTimeoutError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.ConnectTimeoutError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.ConnectTimeoutError", "botocore.exceptions.ConnectionError", "botocore.exceptions.BotoCoreError", "requests.exceptions.ConnectTimeout", "requests.exceptions.ConnectionError", "requests.exceptions.Timeout", "requests.exceptions.RequestException", "builtins.OSError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "endpoint_url", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.ConnectTimeoutError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "endpoint_url", "kwargs"], "arg_types": ["botocore.exceptions.ConnectTimeoutError", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ConnectTimeoutError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.ConnectTimeoutError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._EndpointURLErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.ConnectTimeoutError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.ConnectTimeoutError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConnectionClosedError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.HTTPClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.ConnectionClosedError", "name": "ConnectionClosedError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.ConnectionClosedError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.ConnectionClosedError", "botocore.exceptions.HTTPClientError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 5, 4], "arg_names": ["self", "request", "response", "endpoint_url", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.ConnectionClosedError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5, 4], "arg_names": ["self", "request", "response", "endpoint_url", "kwargs"], "arg_types": ["botocore.exceptions.ConnectionClosedError", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ConnectionClosedError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.ConnectionClosedError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._EndpointURLErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.ConnectionClosedError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.ConnectionClosedError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConnectionError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.ConnectionError", "name": "ConnectionError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.ConnectionError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.ConnectionError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "error", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.ConnectionError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "error", "kwargs"], "arg_types": ["botocore.exceptions.ConnectionError", "builtins.Exception", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ConnectionError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.ConnectionError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._ConnectionErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.ConnectionError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.ConnectionError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CredentialRetrievalError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.CredentialRetrievalError", "name": "CredentialRetrievalError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.CredentialRetrievalError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.CredentialRetrievalError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "provider", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.CredentialRetrievalError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "provider", "kwargs"], "arg_types": ["botocore.exceptions.CredentialRetrievalError", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CredentialRetrievalError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.CredentialRetrievalError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._CredentialRetrievalErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.CredentialRetrievalError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.CredentialRetrievalError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DataNotFoundError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.DataNotFoundError", "name": "DataNotFoundError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.DataNotFoundError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.DataNotFoundError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "data_path", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.DataNotFoundError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "data_path", "kwargs"], "arg_types": ["botocore.exceptions.DataNotFoundError", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DataNotFoundError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.DataNotFoundError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._DataNotFoundErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.DataNotFoundError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.DataNotFoundError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EndpointConnectionError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.ConnectionError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.EndpointConnectionError", "name": "EndpointConnectionError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.EndpointConnectionError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.EndpointConnectionError", "botocore.exceptions.ConnectionError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "endpoint_url", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.EndpointConnectionError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "endpoint_url", "kwargs"], "arg_types": ["botocore.exceptions.EndpointConnectionError", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of EndpointConnectionError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.EndpointConnectionError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._EndpointURLErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.EndpointConnectionError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.EndpointConnectionError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EndpointProviderError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.EndpointProviderError", "name": "EndpointProviderError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.EndpointProviderError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.EndpointProviderError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "msg", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.EndpointProviderError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "msg", "kwargs"], "arg_types": ["botocore.exceptions.EndpointProviderError", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of EndpointProviderError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.EndpointProviderError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._EndpointProviderErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.EndpointProviderError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.EndpointProviderError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EndpointResolutionError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.EndpointProviderError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.EndpointResolutionError", "name": "EndpointResolutionError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.EndpointResolutionError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.EndpointResolutionError", "botocore.exceptions.EndpointProviderError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.EndpointResolutionError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.EndpointResolutionError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EndpointVariantError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BaseEndpointResolverError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.EndpointVariantError", "name": "EndpointVariantError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.EndpointVariantError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.EndpointVariantError", "botocore.exceptions.BaseEndpointResolverError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "tags", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.EndpointVariantError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "tags", "kwargs"], "arg_types": ["botocore.exceptions.EndpointVariantError", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of EndpointVariantError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.EndpointVariantError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._EndpointVariantErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.EndpointVariantError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.EndpointVariantError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EventStreamError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.EventStreamError", "name": "EventStreamError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.EventStreamError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.EventStreamError", "botocore.exceptions.ClientError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.EventStreamError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.EventStreamError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlexibleChecksumError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.FlexibleChecksumError", "name": "FlexibleChecksumError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.FlexibleChecksumError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.FlexibleChecksumError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "error_msg", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.FlexibleChecksumError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "error_msg", "kwargs"], "arg_types": ["botocore.exceptions.FlexibleChecksumError", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FlexibleChecksumError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.FlexibleChecksumError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._FlexibleChecksumErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.FlexibleChecksumError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.FlexibleChecksumError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPClientError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.HTTPClientError", "name": "HTTPClientError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.HTTPClientError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.HTTPClientError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "request", "response", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.HTTPClientError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "request", "response", "kwargs"], "arg_types": ["botocore.exceptions.HTTPClientError", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HTTPClientError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.HTTPClientError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._HTTPClientErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.HTTPClientError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.HTTPClientError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IO": {".class": "SymbolTableNode", "cross_ref": "typing.IO", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ImminentRemovalWarning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Warning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.ImminentRemovalWarning", "name": "ImminentRemovalWarning", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.ImminentRemovalWarning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.ImminentRemovalWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.ImminentRemovalWarning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.ImminentRemovalWarning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IncompleteReadError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.IncompleteReadError", "name": "IncompleteReadError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.IncompleteReadError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.IncompleteReadError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 4], "arg_names": ["self", "actual_bytes", "expected_bytes", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.IncompleteReadError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 4], "arg_names": ["self", "actual_bytes", "expected_bytes", "kwargs"], "arg_types": ["botocore.exceptions.IncompleteReadError", "builtins.int", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of IncompleteReadError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.IncompleteReadError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._IncompleteReadErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.IncompleteReadError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.IncompleteReadError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InfiniteLoopConfigError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.InvalidConfigError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.InfiniteLoopConfigError", "name": "InfiniteLoopConfigError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.InfiniteLoopConfigError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.InfiniteLoopConfigError", "botocore.exceptions.InvalidConfigError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 4], "arg_names": ["self", "source_profile", "visited_profiles", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.InfiniteLoopConfigError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 4], "arg_names": ["self", "source_profile", "visited_profiles", "kwargs"], "arg_types": ["botocore.exceptions.InfiniteLoopConfigError", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InfiniteLoopConfigError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.InfiniteLoopConfigError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._InfiniteLoopConfigErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.InfiniteLoopConfigError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.InfiniteLoopConfigError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidChecksumConfigError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.InvalidChecksumConfigError", "name": "InvalidChecksumConfigError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.InvalidChecksumConfigError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.InvalidChecksumConfigError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.InvalidChecksumConfigError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.InvalidChecksumConfigError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidConfigError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.InvalidConfigError", "name": "InvalidConfigError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.InvalidConfigError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.InvalidConfigError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "error_msg", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.InvalidConfigError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "error_msg", "kwargs"], "arg_types": ["botocore.exceptions.InvalidConfigError", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidConfigError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.InvalidConfigError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._InvalidConfigErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.InvalidConfigError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.InvalidConfigError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidDNSNameError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.InvalidDNSNameError", "name": "InvalidDNSNameError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.InvalidDNSNameError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.InvalidDNSNameError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "bucket_name", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.InvalidDNSNameError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "bucket_name", "kwargs"], "arg_types": ["botocore.exceptions.InvalidDNSNameError", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidDNSNameError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.InvalidDNSNameError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._InvalidDNSNameErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.InvalidDNSNameError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.InvalidDNSNameError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidDefaultsMode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.InvalidDefaultsMode", "name": "InvalidDefaultsMode", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.InvalidDefaultsMode", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.InvalidDefaultsMode", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 4], "arg_names": ["self", "mode", "valid_modes", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.InvalidDefaultsMode.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 4], "arg_names": ["self", "mode", "valid_modes", "kwargs"], "arg_types": ["botocore.exceptions.InvalidDefaultsMode", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidDefaultsMode", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.InvalidDefaultsMode.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._InvalidDefaultsModeKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.InvalidDefaultsMode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.InvalidDefaultsMode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidEndpointConfigurationError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.InvalidEndpointConfigurationError", "name": "InvalidEndpointConfigurationError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.InvalidEndpointConfigurationError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.InvalidEndpointConfigurationError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "msg", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.InvalidEndpointConfigurationError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "msg", "kwargs"], "arg_types": ["botocore.exceptions.InvalidEndpointConfigurationError", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidEndpointConfigurationError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.InvalidEndpointConfigurationError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._InvalidEndpointConfigurationErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.InvalidEndpointConfigurationError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.InvalidEndpointConfigurationError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidEndpointDiscoveryConfigurationError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.InvalidEndpointDiscoveryConfigurationError", "name": "InvalidEndpointDiscoveryConfigurationError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.InvalidEndpointDiscoveryConfigurationError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.InvalidEndpointDiscoveryConfigurationError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "config_value", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.InvalidEndpointDiscoveryConfigurationError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "config_value", "kwargs"], "arg_types": ["botocore.exceptions.InvalidEndpointDiscoveryConfigurationError", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidEndpointDiscoveryConfigurationError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.InvalidEndpointDiscoveryConfigurationError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._InvalidEndpointDiscoveryConfigurationErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.InvalidEndpointDiscoveryConfigurationError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.InvalidEndpointDiscoveryConfigurationError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidExpressionError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.InvalidExpressionError", "name": "InvalidExpressionError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.InvalidExpressionError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.InvalidExpressionError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "expression", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.InvalidExpressionError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "expression", "kwargs"], "arg_types": ["botocore.exceptions.InvalidExpressionError", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidExpressionError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.InvalidExpressionError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._InvalidExpressionErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.InvalidExpressionError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.InvalidExpressionError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidHostLabelError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.InvalidHostLabelError", "name": "InvalidHostLabelError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.InvalidHostLabelError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.InvalidHostLabelError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "label", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.InvalidHostLabelError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "label", "kwargs"], "arg_types": ["botocore.exceptions.InvalidHostLabelError", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidHostLabelError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.InvalidHostLabelError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._InvalidHostLabelErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.InvalidHostLabelError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.InvalidHostLabelError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidIMDSEndpointError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.InvalidIMDSEndpointError", "name": "InvalidIMDSEndpointError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.InvalidIMDSEndpointError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.InvalidIMDSEndpointError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "endpoint", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.InvalidIMDSEndpointError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "endpoint", "kwargs"], "arg_types": ["botocore.exceptions.InvalidIMDSEndpointError", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidIMDSEndpointError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.InvalidIMDSEndpointError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._InvalidIMDSEndpointErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.InvalidIMDSEndpointError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.InvalidIMDSEndpointError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidIMDSEndpointModeError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.InvalidIMDSEndpointModeError", "name": "InvalidIMDSEndpointModeError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.InvalidIMDSEndpointModeError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.InvalidIMDSEndpointModeError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 3, 4], "arg_names": ["self", "mode", "valid_modes", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.InvalidIMDSEndpointModeError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3, 4], "arg_names": ["self", "mode", "valid_modes", "kwargs"], "arg_types": ["botocore.exceptions.InvalidIMDSEndpointModeError", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidIMDSEndpointModeError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.InvalidIMDSEndpointModeError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._InvalidIMDSEndpointModeErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.InvalidIMDSEndpointModeError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.InvalidIMDSEndpointModeError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidMaxRetryAttemptsError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.InvalidRetryConfigurationError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.InvalidMaxRetryAttemptsError", "name": "InvalidMaxRetryAttemptsError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.InvalidMaxRetryAttemptsError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.InvalidMaxRetryAttemptsError", "botocore.exceptions.InvalidRetryConfigurationError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 4], "arg_names": ["self", "provided_max_attempts", "min_value", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.InvalidMaxRetryAttemptsError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 4], "arg_names": ["self", "provided_max_attempts", "min_value", "kwargs"], "arg_types": ["botocore.exceptions.InvalidMaxRetryAttemptsError", "builtins.int", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidMaxRetryAttemptsError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.InvalidMaxRetryAttemptsError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._InvalidMaxRetryAttemptsErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.InvalidMaxRetryAttemptsError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.InvalidMaxRetryAttemptsError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidProxiesConfigError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.InvalidProxiesConfigError", "name": "InvalidProxiesConfigError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.InvalidProxiesConfigError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.InvalidProxiesConfigError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.InvalidProxiesConfigError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.InvalidProxiesConfigError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidRegionError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.ValidationError", "builtins.ValueError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.InvalidRegionError", "name": "InvalidRegionError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.InvalidRegionError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.InvalidRegionError", "botocore.exceptions.ValidationError", "botocore.exceptions.BotoCoreError", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "region_name", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.InvalidRegionError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "region_name", "kwargs"], "arg_types": ["botocore.exceptions.InvalidRegionError", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidRegionError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.InvalidRegionError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._InvalidRegionErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.InvalidRegionError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.InvalidRegionError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidRetryConfigurationError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.InvalidRetryConfigurationError", "name": "InvalidRetryConfigurationError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.InvalidRetryConfigurationError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.InvalidRetryConfigurationError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "retry_config_option", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.InvalidRetryConfigurationError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "retry_config_option", "kwargs"], "arg_types": ["botocore.exceptions.InvalidRetryConfigurationError", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidRetryConfigurationError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.InvalidRetryConfigurationError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._InvalidRetryConfigurationErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.InvalidRetryConfigurationError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.InvalidRetryConfigurationError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidRetryModeError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.InvalidRetryConfigurationError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.InvalidRetryModeError", "name": "InvalidRetryModeError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.InvalidRetryModeError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.InvalidRetryModeError", "botocore.exceptions.InvalidRetryConfigurationError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "provided_retry_mode", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.InvalidRetryModeError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "provided_retry_mode", "kwargs"], "arg_types": ["botocore.exceptions.InvalidRetryModeError", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidRetryModeError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.InvalidRetryModeError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._InvalidRetryModeErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.InvalidRetryModeError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.InvalidRetryModeError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidS3AddressingStyleError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.InvalidS3AddressingStyleError", "name": "InvalidS3AddressingStyleError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.InvalidS3AddressingStyleError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.InvalidS3AddressingStyleError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "s3_addressing_style", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.InvalidS3AddressingStyleError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "s3_addressing_style", "kwargs"], "arg_types": ["botocore.exceptions.InvalidS3AddressingStyleError", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidS3AddressingStyleError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.InvalidS3AddressingStyleError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._InvalidS3AddressingStyleErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.InvalidS3AddressingStyleError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.InvalidS3AddressingStyleError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidS3UsEast1RegionalEndpointConfigError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.InvalidS3UsEast1RegionalEndpointConfigError", "name": "InvalidS3UsEast1RegionalEndpointConfigError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.InvalidS3UsEast1RegionalEndpointConfigError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.InvalidS3UsEast1RegionalEndpointConfigError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "s3_us_east_1_regional_endpoint_config", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.InvalidS3UsEast1RegionalEndpointConfigError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "s3_us_east_1_regional_endpoint_config", "kwargs"], "arg_types": ["botocore.exceptions.InvalidS3UsEast1RegionalEndpointConfigError", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidS3UsEast1RegionalEndpointConfigError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.InvalidS3UsEast1RegionalEndpointConfigError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._InvalidS3UsEast1RegionalEndpointConfigErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.InvalidS3UsEast1RegionalEndpointConfigError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.InvalidS3UsEast1RegionalEndpointConfigError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidSTSRegionalEndpointsConfigError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.InvalidSTSRegionalEndpointsConfigError", "name": "InvalidSTSRegionalEndpointsConfigError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.InvalidSTSRegionalEndpointsConfigError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.InvalidSTSRegionalEndpointsConfigError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "sts_regional_endpoints_config", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.InvalidSTSRegionalEndpointsConfigError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "sts_regional_endpoints_config", "kwargs"], "arg_types": ["botocore.exceptions.InvalidSTSRegionalEndpointsConfigError", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidSTSRegionalEndpointsConfigError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.InvalidSTSRegionalEndpointsConfigError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._InvalidSTSRegionalEndpointsConfigErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.InvalidSTSRegionalEndpointsConfigError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.InvalidSTSRegionalEndpointsConfigError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MD5UnavailableError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.MD5UnavailableError", "name": "MD5UnavailableError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.MD5UnavailableError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.MD5UnavailableError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.MD5UnavailableError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.MD5UnavailableError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MetadataRetrievalError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.MetadataRetrievalError", "name": "MetadataRetrievalError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.MetadataRetrievalError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.MetadataRetrievalError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "error_msg", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.MetadataRetrievalError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "error_msg", "kwargs"], "arg_types": ["botocore.exceptions.MetadataRetrievalError", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MetadataRetrievalError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.MetadataRetrievalError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._MetadataRetrievalErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.MetadataRetrievalError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.MetadataRetrievalError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MissingDependencyException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.MissingDependencyException", "name": "MissingDependencyException", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.MissingDependencyException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.MissingDependencyException", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "msg", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.MissingDependencyException.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "msg", "kwargs"], "arg_types": ["botocore.exceptions.MissingDependencyException", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MissingDependencyException", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.MissingDependencyException.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._MissingDependencyExceptionKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.MissingDependencyException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.MissingDependencyException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MissingParametersError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.MissingParametersError", "name": "MissingParametersError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.MissingParametersError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.MissingParametersError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 4], "arg_names": ["self", "object", "missing", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.MissingParametersError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 4], "arg_names": ["self", "object", "missing", "kwargs"], "arg_types": ["botocore.exceptions.MissingParametersError", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MissingParametersError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.MissingParametersError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._MissingParametersErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.MissingParametersError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.MissingParametersError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MissingServiceIdError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.UndefinedModelAttributeError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.MissingServiceIdError", "name": "MissingServiceIdError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.MissingServiceIdError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.MissingServiceIdError", "botocore.exceptions.UndefinedModelAttributeError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "service_name", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.MissingServiceIdError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "service_name", "kwargs"], "arg_types": ["botocore.exceptions.MissingServiceIdError", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MissingServiceIdError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "fmt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "botocore.exceptions.MissingServiceIdError.fmt", "name": "fmt", "type": "builtins.str"}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.MissingServiceIdError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._MissingServiceIdErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.MissingServiceIdError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.MissingServiceIdError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoAuthTokenError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.NoAuthTokenError", "name": "NoAuthTokenError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.NoAuthTokenError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.NoAuthTokenError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.NoAuthTokenError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.NoAuthTokenError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoCredentialsError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.NoCredentialsError", "name": "NoCredentialsError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.NoCredentialsError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.NoCredentialsError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.NoCredentialsError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.NoCredentialsError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoRegionError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BaseEndpointResolverError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.NoRegionError", "name": "NoRegionError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.NoRegionError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.NoRegionError", "botocore.exceptions.BaseEndpointResolverError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.NoRegionError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.NoRegionError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OperationNotPageableError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.OperationNotPageableError", "name": "OperationNotPageableError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.OperationNotPageableError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.OperationNotPageableError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "operation_name", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.OperationNotPageableError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "operation_name", "kwargs"], "arg_types": ["botocore.exceptions.OperationNotPageableError", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of OperationNotPageableError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.OperationNotPageableError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._OperationNotPageableErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.OperationNotPageableError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.OperationNotPageableError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PaginationError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.PaginationError", "name": "PaginationError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.PaginationError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.PaginationError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "message", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.PaginationError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "message", "kwargs"], "arg_types": ["botocore.exceptions.PaginationError", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PaginationError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.PaginationError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._PaginationErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.PaginationError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.PaginationError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ParamValidationError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.ParamValidationError", "name": "ParamValidationError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.ParamValidationError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.ParamValidationError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "report", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.ParamValidationError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "report", "kwargs"], "arg_types": ["botocore.exceptions.ParamValidationError", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ParamValidationError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.ParamValidationError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._ParamValidationErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.ParamValidationError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.ParamValidationError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PartialCredentialsError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.PartialCredentialsError", "name": "PartialCredentialsError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.PartialCredentialsError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.PartialCredentialsError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 4], "arg_names": ["self", "provider", "cred_var", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.PartialCredentialsError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 4], "arg_names": ["self", "provider", "cred_var", "kwargs"], "arg_types": ["botocore.exceptions.PartialCredentialsError", "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PartialCredentialsError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.PartialCredentialsError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._PartialCredentialsErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.PartialCredentialsError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.PartialCredentialsError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ProfileNotFound": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.ProfileNotFound", "name": "ProfileNotFound", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.ProfileNotFound", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.ProfileNotFound", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "profile", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.ProfileNotFound.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "profile", "kwargs"], "arg_types": ["botocore.exceptions.ProfileNotFound", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ProfileNotFound", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.ProfileNotFound.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._ProfileNotFoundKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.ProfileNotFound.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.ProfileNotFound", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ProxyConnectionError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.ConnectionError", "requests.exceptions.ProxyError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.ProxyConnectionError", "name": "ProxyConnectionError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.ProxyConnectionError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.ProxyConnectionError", "botocore.exceptions.ConnectionError", "botocore.exceptions.BotoCoreError", "requests.exceptions.ProxyError", "requests.exceptions.ConnectionError", "requests.exceptions.RequestException", "builtins.OSError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "proxy_url", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.ProxyConnectionError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "proxy_url", "kwargs"], "arg_types": ["botocore.exceptions.ProxyConnectionError", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ProxyConnectionError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.ProxyConnectionError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._ProxyConnectionErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.ProxyConnectionError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.ProxyConnectionError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RangeError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.ValidationError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.RangeError", "name": "RangeError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.RangeError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.RangeError", "botocore.exceptions.ValidationError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 4], "arg_names": ["self", "value", "param", "min_value", "max_value", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.RangeError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 4], "arg_names": ["self", "value", "param", "min_value", "max_value", "kwargs"], "arg_types": ["botocore.exceptions.RangeError", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RangeError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.RangeError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._RangeErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.RangeError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.RangeError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ReadTimeoutError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.HTTPClientError", "requests.exceptions.ReadTimeout", "urllib3.exceptions.ReadTimeoutError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.ReadTimeoutError", "name": "ReadTimeoutError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.ReadTimeoutError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.ReadTimeoutError", "botocore.exceptions.HTTPClientError", "botocore.exceptions.BotoCoreError", "requests.exceptions.ReadTimeout", "requests.exceptions.Timeout", "requests.exceptions.RequestException", "builtins.OSError", "urllib3.exceptions.ReadTimeoutError", "urllib3.exceptions.TimeoutError", "urllib3.exceptions.RequestError", "urllib3.exceptions.PoolError", "urllib3.exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 5, 4], "arg_names": ["self", "request", "response", "endpoint_url", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.ReadTimeoutError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5, 4], "arg_names": ["self", "request", "response", "endpoint_url", "kwargs"], "arg_types": ["botocore.exceptions.ReadTimeoutError", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ReadTimeoutError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.ReadTimeoutError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._EndpointURLErrorKwargs"}}}, "request": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.ReadTimeoutError.request", "name": "request", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "response": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.ReadTimeoutError.response", "name": "response", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.ReadTimeoutError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.ReadTimeoutError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RefreshWithMFAUnsupportedError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.RefreshWithMFAUnsupportedError", "name": "RefreshWithMFAUnsupportedError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.RefreshWithMFAUnsupportedError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.RefreshWithMFAUnsupportedError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.RefreshWithMFAUnsupportedError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.RefreshWithMFAUnsupportedError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ResponseStreamingError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.HTTPClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.ResponseStreamingError", "name": "ResponseStreamingError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.ResponseStreamingError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.ResponseStreamingError", "botocore.exceptions.HTTPClientError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 5, 4], "arg_names": ["self", "request", "response", "error", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.ResponseStreamingError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5, 4], "arg_names": ["self", "request", "response", "error", "kwargs"], "arg_types": ["botocore.exceptions.ResponseStreamingError", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ResponseStreamingError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.ResponseStreamingError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._ResponseStreamingErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.ResponseStreamingError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.ResponseStreamingError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SSLError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.ConnectionError", "requests.exceptions.SSLError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.SSLError", "name": "SSLError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.SSLError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.SSLError", "botocore.exceptions.ConnectionError", "botocore.exceptions.BotoCoreError", "requests.exceptions.SSLError", "requests.exceptions.ConnectionError", "requests.exceptions.RequestException", "builtins.OSError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 4], "arg_names": ["self", "endpoint_url", "error", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.SSLError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 4], "arg_names": ["self", "endpoint_url", "error", "kwargs"], "arg_types": ["botocore.exceptions.SSLError", "builtins.str", "builtins.Exception", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SSLError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.SSLError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._SSLErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.SSLError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.SSLError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SSOError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.SSOError", "name": "SSOError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.SSOError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.SSOError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.SSOError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.SSOError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SSOTokenLoadError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.SSOError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.SSOTokenLoadError", "name": "SSOTokenLoadError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.SSOTokenLoadError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.SSOTokenLoadError", "botocore.exceptions.SSOError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "error_msg", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.SSOTokenLoadError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "error_msg", "kwargs"], "arg_types": ["botocore.exceptions.SSOTokenLoadError", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SSOTokenLoadError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.SSOTokenLoadError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._SSOTokenLoadErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.SSOTokenLoadError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.SSOTokenLoadError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ServiceNotInRegionError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.ServiceNotInRegionError", "name": "ServiceNotInRegionError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.ServiceNotInRegionError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.ServiceNotInRegionError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 4], "arg_names": ["self", "service_name", "region_name", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.ServiceNotInRegionError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 4], "arg_names": ["self", "service_name", "region_name", "kwargs"], "arg_types": ["botocore.exceptions.ServiceNotInRegionError", "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ServiceNotInRegionError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.ServiceNotInRegionError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._ServiceNotInRegionErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.ServiceNotInRegionError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.ServiceNotInRegionError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StubAssertionError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.StubResponseError", "builtins.AssertionError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.StubAssertionError", "name": "StubAssertionError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.StubAssertionError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.StubAssertionError", "botocore.exceptions.StubResponseError", "botocore.exceptions.BotoCoreError", "builtins.AssertionError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.StubAssertionError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.StubAssertionError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StubResponseError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.StubResponseError", "name": "StubResponseError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.StubResponseError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.StubResponseError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "operation_name", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.StubResponseError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "operation_name", "kwargs"], "arg_types": ["botocore.exceptions.StubResponseError", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of StubResponseError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.StubResponseError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._StubResponseErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.StubResponseError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.StubResponseError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TokenRetrievalError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.TokenRetrievalError", "name": "TokenRetrievalError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.TokenRetrievalError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.TokenRetrievalError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 4], "arg_names": ["self", "provider", "error_msg", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.TokenRetrievalError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 4], "arg_names": ["self", "provider", "error_msg", "kwargs"], "arg_types": ["botocore.exceptions.TokenRetrievalError", "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TokenRetrievalError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.TokenRetrievalError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._TokenRetrievalErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.TokenRetrievalError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.TokenRetrievalError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "UnStubbedResponseError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.StubResponseError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.UnStubbedResponseError", "name": "UnStubbedResponseError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.UnStubbedResponseError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.UnStubbedResponseError", "botocore.exceptions.StubResponseError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.UnStubbedResponseError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.UnStubbedResponseError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnauthorizedSSOTokenError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.SSOError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.UnauthorizedSSOTokenError", "name": "UnauthorizedSSOTokenError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.UnauthorizedSSOTokenError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.UnauthorizedSSOTokenError", "botocore.exceptions.SSOError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.UnauthorizedSSOTokenError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.UnauthorizedSSOTokenError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UndefinedModelAttributeError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.UndefinedModelAttributeError", "name": "UndefinedModelAttributeError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.UndefinedModelAttributeError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.UndefinedModelAttributeError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.UndefinedModelAttributeError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.UndefinedModelAttributeError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnknownClientMethodError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.UnknownClientMethodError", "name": "UnknownClientMethodError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.UnknownClientMethodError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.UnknownClientMethodError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "method_name", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.UnknownClientMethodError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "method_name", "kwargs"], "arg_types": ["botocore.exceptions.UnknownClientMethodError", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UnknownClientMethodError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.UnknownClientMethodError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._UnknownClientMethodErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.UnknownClientMethodError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.UnknownClientMethodError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnknownCredentialError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.UnknownCredentialError", "name": "UnknownCredentialError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.UnknownCredentialError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.UnknownCredentialError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "name", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.UnknownCredentialError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "name", "kwargs"], "arg_types": ["botocore.exceptions.UnknownCredentialError", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UnknownCredentialError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.UnknownCredentialError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._UnknownCredentialErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.UnknownCredentialError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.UnknownCredentialError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnknownEndpointError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BaseEndpointResolverError", "builtins.ValueError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.UnknownEndpointError", "name": "UnknownEndpointError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.UnknownEndpointError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.UnknownEndpointError", "botocore.exceptions.BaseEndpointResolverError", "botocore.exceptions.BotoCoreError", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 4], "arg_names": ["self", "service_name", "region_name", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.UnknownEndpointError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 4], "arg_names": ["self", "service_name", "region_name", "kwargs"], "arg_types": ["botocore.exceptions.UnknownEndpointError", "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UnknownEndpointError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.UnknownEndpointError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._UnknownEndpointErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.UnknownEndpointError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.UnknownEndpointError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnknownEndpointResolutionBuiltInName": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.EndpointProviderError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.UnknownEndpointResolutionBuiltInName", "name": "UnknownEndpointResolutionBuiltInName", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.UnknownEndpointResolutionBuiltInName", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.UnknownEndpointResolutionBuiltInName", "botocore.exceptions.EndpointProviderError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.UnknownEndpointResolutionBuiltInName.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.UnknownEndpointResolutionBuiltInName", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnknownFIPSEndpointError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BaseEndpointResolverError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.UnknownFIPSEndpointError", "name": "UnknownFIPSEndpointError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.UnknownFIPSEndpointError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.UnknownFIPSEndpointError", "botocore.exceptions.BaseEndpointResolverError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 4], "arg_names": ["self", "service_name", "region_name", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.UnknownFIPSEndpointError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 4], "arg_names": ["self", "service_name", "region_name", "kwargs"], "arg_types": ["botocore.exceptions.UnknownFIPSEndpointError", "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UnknownFIPSEndpointError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.UnknownFIPSEndpointError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._UnknownFIPSEndpointErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.UnknownFIPSEndpointError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.UnknownFIPSEndpointError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnknownKeyError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.ValidationError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.<PERSON><PERSON><PERSON><PERSON><PERSON>r", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.<PERSON><PERSON><PERSON><PERSON><PERSON>r", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.<PERSON><PERSON><PERSON><PERSON><PERSON>r", "botocore.exceptions.ValidationError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 4], "arg_names": ["self", "value", "param", "choices", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.UnknownKeyError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 4], "arg_names": ["self", "value", "param", "choices", "kwargs"], "arg_types": ["botocore.exceptions.<PERSON><PERSON><PERSON><PERSON><PERSON>r", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Iterable"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UnknownKeyError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.UnknownKeyError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._Unknown<PERSON>eyErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.UnknownKeyError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.<PERSON><PERSON><PERSON><PERSON><PERSON>r", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnknownParameterError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.ValidationError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.UnknownParameterError", "name": "UnknownParameterError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.UnknownParameterError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.UnknownParameterError", "botocore.exceptions.ValidationError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 4], "arg_names": ["self", "name", "operation", "choices", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.UnknownParameterError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 4], "arg_names": ["self", "name", "operation", "choices", "kwargs"], "arg_types": ["botocore.exceptions.UnknownParameterError", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UnknownParameterError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.UnknownParameterError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._UnknownParameterErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.UnknownParameterError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.UnknownParameterError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnknownRegionError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.UnknownRegionError", "name": "UnknownRegionError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.UnknownRegionError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.UnknownRegionError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 4], "arg_names": ["self", "region_name", "error_msg", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.UnknownRegionError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 4], "arg_names": ["self", "region_name", "error_msg", "kwargs"], "arg_types": ["botocore.exceptions.UnknownRegionError", "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UnknownRegionError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.UnknownRegionError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._UnknownRegionErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.UnknownRegionError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.UnknownRegionError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnknownServiceError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.DataNotFoundError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.UnknownServiceError", "name": "UnknownServiceError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.UnknownServiceError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.UnknownServiceError", "botocore.exceptions.DataNotFoundError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 4], "arg_names": ["self", "service_name", "known_service_names", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.UnknownServiceError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 4], "arg_names": ["self", "service_name", "known_service_names", "kwargs"], "arg_types": ["botocore.exceptions.UnknownServiceError", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UnknownServiceError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.UnknownServiceError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._UnknownServiceErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.UnknownServiceError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.UnknownServiceError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnknownServiceStyle": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.UnknownServiceStyle", "name": "UnknownServiceStyle", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.UnknownServiceStyle", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.UnknownServiceStyle", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "service_style", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.UnknownServiceStyle.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "service_style", "kwargs"], "arg_types": ["botocore.exceptions.UnknownServiceStyle", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UnknownServiceStyle", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.UnknownServiceStyle.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._UnknownServiceStyleKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.UnknownServiceStyle.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.UnknownServiceStyle", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnknownSignatureVersionError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.UnknownSignatureVersionError", "name": "UnknownSignatureVersionError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.UnknownSignatureVersionError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.UnknownSignatureVersionError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "signature_version", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.UnknownSignatureVersionError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "signature_version", "kwargs"], "arg_types": ["botocore.exceptions.UnknownSignatureVersionError", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UnknownSignatureVersionError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.UnknownSignatureVersionError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._UnknownSignatureVersionErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.UnknownSignatureVersionError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.UnknownSignatureVersionError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnseekableStreamError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.UnseekableStreamError", "name": "UnseekableStreamError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.UnseekableStreamError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.UnseekableStreamError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "stream_object", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.UnseekableStreamError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "stream_object", "kwargs"], "arg_types": ["botocore.exceptions.UnseekableStreamError", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.IO"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UnseekableStreamError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.UnseekableStreamError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._UnseekableStreamErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.UnseekableStreamError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.UnseekableStreamError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnsupportedOutpostResourceError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.UnsupportedOutpostResourceError", "name": "UnsupportedOutpostResourceError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.UnsupportedOutpostResourceError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.UnsupportedOutpostResourceError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "resource_name", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.UnsupportedOutpostResourceError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "resource_name", "kwargs"], "arg_types": ["botocore.exceptions.UnsupportedOutpostResourceError", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UnsupportedOutpostResourceError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.UnsupportedOutpostResourceError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._UnsupportedOutpostResourceErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.UnsupportedOutpostResourceError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.UnsupportedOutpostResourceError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnsupportedS3AccesspointConfigurationError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.UnsupportedS3AccesspointConfigurationError", "name": "UnsupportedS3AccesspointConfigurationError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.UnsupportedS3AccesspointConfigurationError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.UnsupportedS3AccesspointConfigurationError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "msg", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.UnsupportedS3AccesspointConfigurationError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "msg", "kwargs"], "arg_types": ["botocore.exceptions.UnsupportedS3AccesspointConfigurationError", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UnsupportedS3AccesspointConfigurationError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.UnsupportedS3AccesspointConfigurationError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._UnsupportedS3ErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.UnsupportedS3AccesspointConfigurationError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.UnsupportedS3AccesspointConfigurationError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnsupportedS3ArnError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.UnsupportedS3ArnError", "name": "UnsupportedS3ArnError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.UnsupportedS3ArnError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.UnsupportedS3ArnError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "arn", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.UnsupportedS3ArnError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "arn", "kwargs"], "arg_types": ["botocore.exceptions.UnsupportedS3ArnError", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UnsupportedS3ArnError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.UnsupportedS3ArnError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._UnsupportedS3ArnErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.UnsupportedS3ArnError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.UnsupportedS3ArnError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnsupportedS3ConfigurationError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.UnsupportedS3ConfigurationError", "name": "UnsupportedS3ConfigurationError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.UnsupportedS3ConfigurationError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.UnsupportedS3ConfigurationError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "msg", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.UnsupportedS3ConfigurationError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "msg", "kwargs"], "arg_types": ["botocore.exceptions.UnsupportedS3ConfigurationError", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UnsupportedS3ConfigurationError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.UnsupportedS3ConfigurationError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._UnsupportedS3ErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.UnsupportedS3ConfigurationError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.UnsupportedS3ConfigurationError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnsupportedS3ControlArnError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.UnsupportedS3ControlArnError", "name": "UnsupportedS3ControlArnError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.UnsupportedS3ControlArnError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.UnsupportedS3ControlArnError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 4], "arg_names": ["self", "arn", "msg", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.UnsupportedS3ControlArnError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 4], "arg_names": ["self", "arn", "msg", "kwargs"], "arg_types": ["botocore.exceptions.UnsupportedS3ControlArnError", "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UnsupportedS3ControlArnError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.UnsupportedS3ControlArnError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._UnsupportedS3ControlArnErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.UnsupportedS3ControlArnError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.UnsupportedS3ControlArnError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnsupportedS3ControlConfigurationError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.UnsupportedS3ControlConfigurationError", "name": "UnsupportedS3ControlConfigurationError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.UnsupportedS3ControlConfigurationError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.UnsupportedS3ControlConfigurationError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "msg", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.UnsupportedS3ControlConfigurationError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "msg", "kwargs"], "arg_types": ["botocore.exceptions.UnsupportedS3ControlConfigurationError", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UnsupportedS3ControlConfigurationError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.UnsupportedS3ControlConfigurationError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._UnsupportedS3ErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.UnsupportedS3ControlConfigurationError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.UnsupportedS3ControlConfigurationError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnsupportedServiceProtocolsError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.UnsupportedServiceProtocolsError", "name": "UnsupportedServiceProtocolsError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.UnsupportedServiceProtocolsError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.UnsupportedServiceProtocolsError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.UnsupportedServiceProtocolsError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.UnsupportedServiceProtocolsError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnsupportedSignatureVersionError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.UnsupportedSignatureVersionError", "name": "UnsupportedSignatureVersionError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.UnsupportedSignatureVersionError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.UnsupportedSignatureVersionError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "signature_version", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.UnsupportedSignatureVersionError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "signature_version", "kwargs"], "arg_types": ["botocore.exceptions.UnsupportedSignatureVersionError", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UnsupportedSignatureVersionError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.UnsupportedSignatureVersionError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._UnsupportedSignatureVersionErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.UnsupportedSignatureVersionError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.UnsupportedSignatureVersionError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnsupportedTLSVersionWarning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Warning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.UnsupportedTLSVersionWarning", "name": "UnsupportedTLSVersionWarning", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.UnsupportedTLSVersionWarning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.UnsupportedTLSVersionWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.UnsupportedTLSVersionWarning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.UnsupportedTLSVersionWarning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ValidationError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.ValidationError", "name": "ValidationError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.ValidationError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.ValidationError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 4], "arg_names": ["self", "value", "param", "type_name", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.ValidationError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 4], "arg_names": ["self", "value", "param", "type_name", "kwargs"], "arg_types": ["botocore.exceptions.ValidationError", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ValidationError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.ValidationError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._ValidationErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.ValidationError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.ValidationError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WaiterConfigError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.WaiterConfigError", "name": "WaiterConfigError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.WaiterConfigError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.WaiterConfigError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "error_msg", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.WaiterConfigError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "error_msg", "kwargs"], "arg_types": ["botocore.exceptions.WaiterConfigError", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of WaiterConfigError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.WaiterConfigError.kwargs", "name": "kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._WaiterConfigErrorKwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.WaiterConfigError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.WaiterConfigError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WaiterError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.exceptions.BotoCoreError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions.WaiterError", "name": "Waiter<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions.WaiterError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions.WaiterError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "reason", "last_response"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.exceptions.WaiterError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "reason", "last_response"], "arg_types": ["botocore.exceptions.WaiterError", "builtins.str", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._ClientErrorResponseTypeDef"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of WaiterError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "last_response": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.exceptions.WaiterError.last_response", "name": "last_response", "type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._ClientErrorResponseTypeDef"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.exceptions.WaiterError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.exceptions.WaiterError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_AliasConflictParameterErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._AliasConflictParameterErrorKwargs", "name": "_AliasConflictParameterErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._AliasConflictParameterErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._AliasConflictParameterErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["value", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["param", "builtins.str"], ["type_name", "builtins.str"], ["original", "builtins.str"], ["alias", "builtins.str"], ["operation", "builtins.str"]], "required_keys": ["alias", "operation", "original", "param", "type_name", "value"]}}}, "_ApiVersionNotFoundErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._ApiVersionNotFoundErrorKwargs", "name": "_ApiVersionNotFoundErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._ApiVersionNotFoundErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._ApiVersionNotFoundErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["service_name", "builtins.str"], ["api_version", "builtins.str"]], "required_keys": ["api_version", "service_name"]}}}, "_AttributeMapTypeDef": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._AttributeMapTypeDef", "name": "_AttributeMapTypeDef", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._AttributeMapTypeDef", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._AttributeMapTypeDef", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["key", "builtins.str"], ["value", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}]], "required_keys": []}}}, "_AwsChunkedWrapperErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._AwsChunkedWrapperErrorKwargs", "name": "_AwsChunkedWrapperErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._AwsChunkedWrapperErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._AwsChunkedWrapperErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["error_msg", "builtins.str"]], "required_keys": ["error_msg"]}}}, "_CancellationReasonTypeDef": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._CancellationReasonTypeDef", "name": "_CancellationReasonTypeDef", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._CancellationReasonTypeDef", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._CancellationReasonTypeDef", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["Code", "builtins.str"], ["Message", "builtins.str"], ["<PERSON><PERSON>", {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._AttributeMapTypeDef"}]], "required_keys": []}}}, "_ChecksumErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._ChecksumErrorKwargs", "name": "_ChecksumErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._ChecksumErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._ChecksumErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["checksum_type", "builtins.str"], ["expected_checksum", "builtins.str"], ["actual_checksum", "builtins.str"]], "required_keys": ["actual_checksum", "checksum_type", "expected_checksum"]}}}, "_ClientErrorResponseError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._ClientErrorResponseError", "name": "_ClientErrorResponseError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._ClientErrorResponseError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._ClientErrorResponseError", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["Code", "builtins.str"], ["Message", "builtins.str"]], "required_keys": []}}}, "_ClientErrorResponseTypeDef": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._ClientErrorResponseTypeDef", "name": "_ClientErrorResponseTypeDef", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._ClientErrorResponseTypeDef", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._ClientErrorResponseTypeDef", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["Status", "builtins.str"], ["StatusReason", "builtins.str"], ["Error", {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._ClientErrorResponseError"}], ["ResponseMetadata", {".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._ResponseMetadataTypeDef"}], ["CancellationReasons", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "botocore.exceptions._CancellationReasonTypeDef"}], "type_ref": "builtins.list"}]], "required_keys": []}}}, "_ConfigNotFoundKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._ConfigNotFoundKwargs", "name": "_ConfigNotFoundKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._ConfigNotFoundKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._ConfigNotFoundKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["path", "builtins.str"]], "required_keys": ["path"]}}}, "_ConfigParseErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._ConfigParseErrorKwargs", "name": "_ConfigParseErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._ConfigParseErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._ConfigParseErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["path", "builtins.str"]], "required_keys": ["path"]}}}, "_ConnectionErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._ConnectionErrorKwargs", "name": "_ConnectionErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._ConnectionErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._ConnectionErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["error", "builtins.Exception"]], "required_keys": ["error"]}}}, "_CredentialRetrievalErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._CredentialRetrievalErrorKwargs", "name": "_CredentialRetrievalErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._CredentialRetrievalErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._CredentialRetrievalErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["provider", "builtins.str"]], "required_keys": ["provider"]}}}, "_DataNotFoundErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._DataNotFoundErrorKwargs", "name": "_DataNotFoundErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._DataNotFoundErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._DataNotFoundErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["data_path", "builtins.str"]], "required_keys": ["data_path"]}}}, "_EndpointProviderErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._EndpointProviderErrorKwargs", "name": "_EndpointProviderErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._EndpointProviderErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._EndpointProviderErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["msg", "builtins.str"]], "required_keys": ["msg"]}}}, "_EndpointURLErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._EndpointURLErrorKwargs", "name": "_EndpointURLErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._EndpointURLErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._EndpointURLErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["error", "builtins.Exception"], ["endpoint_url", "builtins.str"]], "required_keys": ["endpoint_url", "error"]}}}, "_EndpointVariantErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._EndpointVariantErrorKwargs", "name": "_EndpointVariantErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._EndpointVariantErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._EndpointVariantErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["tags", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}]], "required_keys": ["tags"]}}}, "_FlexibleChecksumErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._FlexibleChecksumErrorKwargs", "name": "_FlexibleChecksumErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._FlexibleChecksumErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._FlexibleChecksumErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["error_msg", "builtins.str"]], "required_keys": ["error_msg"]}}}, "_HTTPClientErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._HTTPClientErrorKwargs", "name": "_HTTPClientErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._HTTPClientErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._HTTPClientErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["error", "builtins.Exception"]], "required_keys": ["error"]}}}, "_IncompleteReadErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._IncompleteReadErrorKwargs", "name": "_IncompleteReadErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._IncompleteReadErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._IncompleteReadErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["actual_bytes", "builtins.int"], ["expected_bytes", "builtins.int"]], "required_keys": ["actual_bytes", "expected_bytes"]}}}, "_InfiniteLoopConfigErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._InfiniteLoopConfigErrorKwargs", "name": "_InfiniteLoopConfigErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._InfiniteLoopConfigErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._InfiniteLoopConfigErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["error_msg", "builtins.str"], ["source_profile", "builtins.str"], ["visited_profiles", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}]], "required_keys": ["error_msg", "source_profile", "visited_profiles"]}}}, "_InvalidConfigErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._InvalidConfigErrorKwargs", "name": "_InvalidConfigErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._InvalidConfigErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._InvalidConfigErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["error_msg", "builtins.str"]], "required_keys": ["error_msg"]}}}, "_InvalidDNSNameErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._InvalidDNSNameErrorKwargs", "name": "_InvalidDNSNameErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._InvalidDNSNameErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._InvalidDNSNameErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["bucket_name", "builtins.str"]], "required_keys": ["bucket_name"]}}}, "_InvalidDefaultsModeKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._InvalidDefaultsModeKwargs", "name": "_InvalidDefaultsModeKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._InvalidDefaultsModeKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._InvalidDefaultsModeKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["mode", "builtins.str"], ["valid_modes", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}]], "required_keys": ["mode", "valid_modes"]}}}, "_InvalidEndpointConfigurationErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._InvalidEndpointConfigurationErrorKwargs", "name": "_InvalidEndpointConfigurationErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._InvalidEndpointConfigurationErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._InvalidEndpointConfigurationErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["msg", "builtins.str"]], "required_keys": ["msg"]}}}, "_InvalidEndpointDiscoveryConfigurationErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._InvalidEndpointDiscoveryConfigurationErrorKwargs", "name": "_InvalidEndpointDiscoveryConfigurationErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._InvalidEndpointDiscoveryConfigurationErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._InvalidEndpointDiscoveryConfigurationErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["config_value", "builtins.str"]], "required_keys": ["config_value"]}}}, "_InvalidExpressionErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._InvalidExpressionErrorKwargs", "name": "_InvalidExpressionErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._InvalidExpressionErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._InvalidExpressionErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["expression", "builtins.str"]], "required_keys": ["expression"]}}}, "_InvalidHostLabelErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._InvalidHostLabelErrorKwargs", "name": "_InvalidHostLabelErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._InvalidHostLabelErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._InvalidHostLabelErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["label", "builtins.str"]], "required_keys": ["label"]}}}, "_InvalidIMDSEndpointErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._InvalidIMDSEndpointErrorKwargs", "name": "_InvalidIMDSEndpointErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._InvalidIMDSEndpointErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._InvalidIMDSEndpointErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["endpoint", "builtins.str"]], "required_keys": ["endpoint"]}}}, "_InvalidIMDSEndpointModeErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._InvalidIMDSEndpointModeErrorKwargs", "name": "_InvalidIMDSEndpointModeErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._InvalidIMDSEndpointModeErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._InvalidIMDSEndpointModeErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["mode", "builtins.str"], ["valid_modes", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}]], "required_keys": ["mode", "valid_modes"]}}}, "_InvalidMaxRetryAttemptsErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._InvalidMaxRetryAttemptsErrorKwargs", "name": "_InvalidMaxRetryAttemptsErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._InvalidMaxRetryAttemptsErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._InvalidMaxRetryAttemptsErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["retry_config_option", "builtins.str"], ["provided_max_attempts", "builtins.int"], ["min_value", "builtins.int"]], "required_keys": ["min_value", "provided_max_attempts", "retry_config_option"]}}}, "_InvalidRegionErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._InvalidRegionErrorKwargs", "name": "_InvalidRegionErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._InvalidRegionErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._InvalidRegionErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["value", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["param", "builtins.str"], ["type_name", "builtins.str"], ["region_name", "builtins.str"]], "required_keys": ["param", "region_name", "type_name", "value"]}}}, "_InvalidRetryConfigurationErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._InvalidRetryConfigurationErrorKwargs", "name": "_InvalidRetryConfigurationErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._InvalidRetryConfigurationErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._InvalidRetryConfigurationErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["retry_config_option", "builtins.str"]], "required_keys": ["retry_config_option"]}}}, "_InvalidRetryModeErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._InvalidRetryModeErrorKwargs", "name": "_InvalidRetryModeErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._InvalidRetryModeErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._InvalidRetryModeErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["retry_config_option", "builtins.str"], ["provided_max_attempts", "builtins.int"], ["min_value", "builtins.int"], ["provided_retry_mode", "builtins.str"]], "required_keys": ["min_value", "provided_max_attempts", "provided_retry_mode", "retry_config_option"]}}}, "_InvalidS3AddressingStyleErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._InvalidS3AddressingStyleErrorKwargs", "name": "_InvalidS3AddressingStyleErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._InvalidS3AddressingStyleErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._InvalidS3AddressingStyleErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["s3_addressing_style", "builtins.str"]], "required_keys": ["s3_addressing_style"]}}}, "_InvalidS3UsEast1RegionalEndpointConfigErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._InvalidS3UsEast1RegionalEndpointConfigErrorKwargs", "name": "_InvalidS3UsEast1RegionalEndpointConfigErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._InvalidS3UsEast1RegionalEndpointConfigErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._InvalidS3UsEast1RegionalEndpointConfigErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["s3_us_east_1_regional_endpoint_config", "builtins.str"]], "required_keys": ["s3_us_east_1_regional_endpoint_config"]}}}, "_InvalidSTSRegionalEndpointsConfigErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._InvalidSTSRegionalEndpointsConfigErrorKwargs", "name": "_InvalidSTSRegionalEndpointsConfigErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._InvalidSTSRegionalEndpointsConfigErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._InvalidSTSRegionalEndpointsConfigErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["sts_regional_endpoints_config", "builtins.str"]], "required_keys": ["sts_regional_endpoints_config"]}}}, "_MetadataRetrievalErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._MetadataRetrievalErrorKwargs", "name": "_MetadataRetrievalErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._MetadataRetrievalErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._MetadataRetrievalErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["error_msg", "builtins.str"]], "required_keys": ["error_msg"]}}}, "_MissingDependencyExceptionKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._MissingDependencyExceptionKwargs", "name": "_MissingDependencyExceptionKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._MissingDependencyExceptionKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._MissingDependencyExceptionKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["msg", "builtins.str"]], "required_keys": ["msg"]}}}, "_MissingParametersErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._MissingParametersErrorKwargs", "name": "_MissingParametersErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._MissingParametersErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._MissingParametersErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["object", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["missing", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}]], "required_keys": ["missing", "object"]}}}, "_MissingServiceIdErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._MissingServiceIdErrorKwargs", "name": "_MissingServiceIdErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._MissingServiceIdErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._MissingServiceIdErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["service_name", "builtins.str"]], "required_keys": ["service_name"]}}}, "_OperationNotPageableErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._OperationNotPageableErrorKwargs", "name": "_OperationNotPageableErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._OperationNotPageableErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._OperationNotPageableErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["operation_name", "builtins.str"]], "required_keys": ["operation_name"]}}}, "_PaginationErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._PaginationErrorKwargs", "name": "_PaginationErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._PaginationErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._PaginationErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["message", "builtins.str"]], "required_keys": ["message"]}}}, "_ParamValidationErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._ParamValidationErrorKwargs", "name": "_ParamValidationErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._ParamValidationErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._ParamValidationErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["report", "builtins.str"]], "required_keys": ["report"]}}}, "_PartialCredentialsErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._PartialCredentialsErrorKwargs", "name": "_PartialCredentialsErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._PartialCredentialsErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._PartialCredentialsErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["provider", "builtins.str"], ["cred_var", "builtins.str"]], "required_keys": ["cred_var", "provider"]}}}, "_ProfileNotFoundKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._ProfileNotFoundKwargs", "name": "_ProfileNotFoundKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._ProfileNotFoundKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._ProfileNotFoundKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["profile", "builtins.str"]], "required_keys": ["profile"]}}}, "_ProxyConnectionErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._ProxyConnectionErrorKwargs", "name": "_ProxyConnectionErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._ProxyConnectionErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._ProxyConnectionErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["error", "builtins.Exception"], ["proxy_url", "builtins.str"]], "required_keys": ["error", "proxy_url"]}}}, "_RangeErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._RangeErrorKwargs", "name": "_RangeErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._RangeErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._RangeErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["value", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["param", "builtins.str"], ["type_name", "builtins.str"], ["min_value", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["max_value", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}]], "required_keys": ["max_value", "min_value", "param", "type_name", "value"]}}}, "_ReadTimeoutError": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.ReadTimeoutError", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ResponseMetadataTypeDef": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._ResponseMetadataTypeDef", "name": "_ResponseMetadataTypeDef", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._ResponseMetadataTypeDef", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._ResponseMetadataTypeDef", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["RequestId", "builtins.str"], ["HostId", "builtins.str"], ["HTTPStatusCode", "builtins.int"], ["HTTPHeaders", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], ["RetryAttempts", "builtins.int"]], "required_keys": ["HTTPHeaders", "HTTPStatusCode", "HostId", "RequestId", "RetryAttempts"]}}}, "_ResponseStreamingErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._ResponseStreamingErrorKwargs", "name": "_ResponseStreamingErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._ResponseStreamingErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._ResponseStreamingErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["error", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}]], "required_keys": ["error"]}}}, "_SSLErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._SSLErrorKwargs", "name": "_SSLErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._SSLErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._SSLErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["endpoint_url", "builtins.str"], ["error", "builtins.Exception"]], "required_keys": ["endpoint_url", "error"]}}}, "_SSOTokenLoadErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._SSOTokenLoadErrorKwargs", "name": "_SSOTokenLoadErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._SSOTokenLoadErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._SSOTokenLoadErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["error_msg", "builtins.str"]], "required_keys": ["error_msg"]}}}, "_ServiceNotInRegionErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._ServiceNotInRegionErrorKwargs", "name": "_ServiceNotInRegionErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._ServiceNotInRegionErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._ServiceNotInRegionErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["service_name", "builtins.str"], ["region_name", "builtins.str"]], "required_keys": ["region_name", "service_name"]}}}, "_StubResponseErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._StubResponseErrorKwargs", "name": "_StubResponseErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._StubResponseErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._StubResponseErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["operation_name", "builtins.str"]], "required_keys": ["operation_name"]}}}, "_TokenRetrievalErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._TokenRetrievalErrorKwargs", "name": "_TokenRetrievalErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._TokenRetrievalErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._TokenRetrievalErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["provider", "builtins.str"], ["error_msg", "builtins.str"]], "required_keys": ["error_msg", "provider"]}}}, "_UnknownClientMethodErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._UnknownClientMethodErrorKwargs", "name": "_UnknownClientMethodErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._UnknownClientMethodErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._UnknownClientMethodErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["method_name", "builtins.str"]], "required_keys": ["method_name"]}}}, "_UnknownCredentialErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._UnknownCredentialErrorKwargs", "name": "_UnknownCredentialErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._UnknownCredentialErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._UnknownCredentialErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["name", "builtins.str"]], "required_keys": ["name"]}}}, "_UnknownEndpointErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._UnknownEndpointErrorKwargs", "name": "_UnknownEndpointErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._UnknownEndpointErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._UnknownEndpointErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["service_name", "builtins.str"], ["region_name", "builtins.str"]], "required_keys": ["region_name", "service_name"]}}}, "_UnknownFIPSEndpointErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._UnknownFIPSEndpointErrorKwargs", "name": "_UnknownFIPSEndpointErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._UnknownFIPSEndpointErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._UnknownFIPSEndpointErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["service_name", "builtins.str"], ["region_name", "builtins.str"]], "required_keys": ["region_name", "service_name"]}}}, "_UnknownKeyErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._Unknown<PERSON>eyErrorKwargs", "name": "_Unknown<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._Unknown<PERSON>eyErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._Unknown<PERSON>eyErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["value", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["param", "builtins.str"], ["type_name", "builtins.str"], ["choices", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Iterable"}]], "required_keys": ["choices", "param", "type_name", "value"]}}}, "_UnknownParameterErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._UnknownParameterErrorKwargs", "name": "_UnknownParameterErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._UnknownParameterErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._UnknownParameterErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["value", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["param", "builtins.str"], ["type_name", "builtins.str"], ["name", "builtins.str"], ["operation", "builtins.str"], ["choices", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}]], "required_keys": ["choices", "name", "operation", "param", "type_name", "value"]}}}, "_UnknownRegionErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._UnknownRegionErrorKwargs", "name": "_UnknownRegionErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._UnknownRegionErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._UnknownRegionErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["service_name", "builtins.str"], ["error_msg", "builtins.str"]], "required_keys": ["error_msg", "service_name"]}}}, "_UnknownServiceErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._UnknownServiceErrorKwargs", "name": "_UnknownServiceErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._UnknownServiceErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._UnknownServiceErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["data_path", "builtins.str"], ["service_name", "builtins.str"], ["known_service_names", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}]], "required_keys": ["data_path", "known_service_names", "service_name"]}}}, "_UnknownServiceStyleKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._UnknownServiceStyleKwargs", "name": "_UnknownServiceStyleKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._UnknownServiceStyleKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._UnknownServiceStyleKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["service_style", "builtins.str"]], "required_keys": ["service_style"]}}}, "_UnknownSignatureVersionErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._UnknownSignatureVersionErrorKwargs", "name": "_UnknownSignatureVersionErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._UnknownSignatureVersionErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._UnknownSignatureVersionErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["signature_version", "builtins.str"]], "required_keys": ["signature_version"]}}}, "_UnseekableStreamErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._UnseekableStreamErrorKwargs", "name": "_UnseekableStreamErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._UnseekableStreamErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._UnseekableStreamErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["stream_object", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.IO"}]], "required_keys": ["stream_object"]}}}, "_UnsupportedOutpostResourceErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._UnsupportedOutpostResourceErrorKwargs", "name": "_UnsupportedOutpostResourceErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._UnsupportedOutpostResourceErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._UnsupportedOutpostResourceErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["resource_name", "builtins.str"]], "required_keys": ["resource_name"]}}}, "_UnsupportedS3ArnErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._UnsupportedS3ArnErrorKwargs", "name": "_UnsupportedS3ArnErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._UnsupportedS3ArnErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._UnsupportedS3ArnErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["arn", "builtins.str"]], "required_keys": ["arn"]}}}, "_UnsupportedS3ControlArnErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._UnsupportedS3ControlArnErrorKwargs", "name": "_UnsupportedS3ControlArnErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._UnsupportedS3ControlArnErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._UnsupportedS3ControlArnErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["arn", "builtins.str"], ["msg", "builtins.str"]], "required_keys": ["arn", "msg"]}}}, "_UnsupportedS3ErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._UnsupportedS3ErrorKwargs", "name": "_UnsupportedS3ErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._UnsupportedS3ErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._UnsupportedS3ErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["msg", "builtins.str"]], "required_keys": ["msg"]}}}, "_UnsupportedSignatureVersionErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._UnsupportedSignatureVersionErrorKwargs", "name": "_UnsupportedSignatureVersionErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._UnsupportedSignatureVersionErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._UnsupportedSignatureVersionErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["signature_version", "builtins.str"]], "required_keys": ["signature_version"]}}}, "_ValidationErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._ValidationErrorKwargs", "name": "_ValidationErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._ValidationErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._ValidationErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["value", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["param", "builtins.str"], ["type_name", "builtins.str"]], "required_keys": ["param", "type_name", "value"]}}}, "_WaiterConfigErrorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.exceptions._WaiterConfigErrorKwargs", "name": "_WaiterConfigErrorKwargs", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.exceptions._WaiterConfigErrorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.exceptions", "mro": ["botocore.exceptions._WaiterConfigErrorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["error_msg", "builtins.str"]], "required_keys": ["error_msg"]}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.exceptions.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.exceptions.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.exceptions.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.exceptions.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.exceptions.__package__", "name": "__package__", "type": "builtins.str"}}, "requests": {".class": "SymbolTableNode", "cross_ref": "requests", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\botocore-stubs\\exceptions.pyi"}