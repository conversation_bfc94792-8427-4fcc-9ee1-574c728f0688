{"data_mtime": 1759503836, "dep_lines": [13, 10, 11, 13, 3, 5, 6, 9, 10, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 20, 5, 10, 5, 10, 20, 5, 20, 20, 30], "dependencies": ["pydantic._internal._typing_extra", "typing_inspection.typing_objects", "typing_inspection.introspection", "pydantic._internal", "__future__", "types", "typing", "typing_extensions", "typing_inspection", "builtins", "pyexpat.model", "pyexpat.errors", "abc"], "hash": "b7b18dc9a514f31beac250c7c551362320de68d66b2bba74d688d03cfd1423fa", "id": "pydantic._internal._repr", "ignore_all": true, "interface_hash": "0c4877e911e3aec0b89d189f079f8649218405ecaa588276fa29de47d1272bd5", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\_internal\\_repr.py", "plugin_data": null, "size": 5081, "suppressed": [], "version_id": "1.8.0"}