{"data_mtime": 1759503828, "dep_lines": [7, 1, 2, 3, 4, 5, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 5, 5, 5, 20, 20, 30, 30, 30], "dependencies": ["pydantic.v1.types", "json", "pickle", "enum", "pathlib", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "json.decoder", "os"], "hash": "049b5daa2651b5abfd55114298ec6863e2889909a33f2f80fcda288e21546716", "id": "pydantic.v1.parse", "ignore_all": true, "interface_hash": "35bc900099fc55c09dfb16e9185abb0519389bb3e7e6d61ac00e3e50777cc7c0", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\v1\\parse.py", "plugin_data": null, "size": 1821, "suppressed": [], "version_id": "1.8.0"}