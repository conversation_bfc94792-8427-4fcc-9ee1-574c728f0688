{"data_mtime": 1759503870, "dep_lines": [110, 112, 105, 107, 108, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 20, 20, 30, 30, 30, 30, 30], "dependencies": ["botocore.paginate", "mypy_boto3_rds.type_defs", "__future__", "sys", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "_typeshed", "abc", "botocore", "botocore.model", "datetime"], "hash": "9066363c45c7048478c68160050a11e039a02f7aedd086744377eac426a187b3", "id": "mypy_boto3_rds.paginator", "ignore_all": true, "interface_hash": "c72260884d171772d5c9a25c2a846827169c538ef3afaeb61a163a6115262e4b", "mtime": 1759495924, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\mypy_boto3_rds\\paginator.pyi", "plugin_data": null, "size": 62731, "suppressed": [], "version_id": "1.8.0"}