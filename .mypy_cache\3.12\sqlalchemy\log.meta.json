{"data_mtime": 1759504546, "dep_lines": [34, 32, 20, 22, 23, 24, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 5, 5, 20, 20, 30, 30], "dependencies": ["sqlalchemy.util.typing", "sqlalchemy.util", "__future__", "logging", "sys", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "sqlalchemy.util.compat"], "hash": "8f2ee2b190e381e8cc616f8b14ef85fb076c7b62e03cc8bc5103143a83c50298", "id": "sqlalchemy.log", "ignore_all": true, "interface_hash": "cd3528fe7d458a62461ed188498244f08d9fd95ad2717836db66805760f41650", "mtime": 1759106563, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\log.py", "plugin_data": null, "size": 8895, "suppressed": [], "version_id": "1.8.0"}