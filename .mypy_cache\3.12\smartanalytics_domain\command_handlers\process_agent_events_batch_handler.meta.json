{"data_mtime": 1759504553, "dep_lines": [6, 9, 12, 15, 16, 17, 3, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 10, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["smartanalytics_domain.command_handlers.process_agent_event_handler", "smartanalytics_domain.commands.process_agent_event_command", "smartanalytics_domain.commands.process_agent_events_batch_command", "smartanalytics_domain.models.processing_result", "smartanalytics_domain.ports.unit_of_work", "smartanalytics_utilities.utils.logging_helper", "time", "uuid", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "abc", "aws_lambda_powertools", "aws_lambda_powertools.logging", "aws_lambda_powertools.logging.logger", "smartanalytics_domain.commands", "smartanalytics_domain.models", "smartanalytics_domain.ports", "smartanalytics_utilities", "smartanalytics_utilities.utils", "types", "typing"], "hash": "1cf70b724210a4cfd25dbf2dec77b9d98f2bb512a598ac8de76a98d2a1bb2117", "id": "smartanalytics_domain.command_handlers.process_agent_events_batch_handler", "ignore_all": false, "interface_hash": "bebf2cd683d8c5c28e15ba297505d2412e7c471afeaa083c1688d822651b896b", "mtime": 1759502092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "layers\\domain\\python\\smartanalytics_domain\\command_handlers\\process_agent_events_batch_handler.py", "plugin_data": null, "size": 4274, "suppressed": [], "version_id": "1.8.0"}