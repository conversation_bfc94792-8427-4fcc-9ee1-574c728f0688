{"data_mtime": 1759503829, "dep_lines": [6, 7, 2, 4, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 20, 20, 30, 30, 30], "dependencies": ["markdown_it.common.utils", "markdown_it.rules_block.state_block", "__future__", "logging", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "markdown_it.ruler", "typing"], "hash": "eeeca64b7e9d72b9de7770ec21a45ca9c6c5535365ca686fb19a445d30f7fe7f", "id": "markdown_it.rules_block.blockquote", "ignore_all": true, "interface_hash": "311e71a9904a2a61512f26d588d7721ef304c9e5903557b0f755fcb781169ab8", "mtime": 1757092234, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\markdown_it\\rules_block\\blockquote.py", "plugin_data": null, "size": 8887, "suppressed": [], "version_id": "1.8.0"}