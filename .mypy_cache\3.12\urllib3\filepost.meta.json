{"data_mtime": 1759503830, "dep_lines": [9, 1, 3, 4, 5, 6, 7, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 10, 10, 5, 5, 20, 20, 30, 30], "dependencies": ["urllib3.fields", "__future__", "<PERSON><PERSON><PERSON><PERSON>", "codecs", "os", "typing", "io", "builtins", "pyexpat.model", "pyexpat.errors", "_codecs", "abc"], "hash": "53c78d67e9a928a1e1ae56c7104893c7180ad7a21e8e111aeeecf8db2a80fdd2", "id": "urllib3.filepost", "ignore_all": true, "interface_hash": "afa0da1122193f24c8285e04265cf9f90c2d898ccef9580acab4464008846174", "mtime": 1757091871, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\urllib3\\filepost.py", "plugin_data": null, "size": 2388, "suppressed": [], "version_id": "1.8.0"}