{"data_mtime": 1759504547, "dep_lines": [44, 45, 46, 64, 65, 66, 67, 69, 71, 72, 74, 78, 83, 85, 88, 89, 90, 91, 93, 94, 95, 96, 97, 99, 100, 44, 61, 62, 63, 64, 19, 21, 22, 23, 60, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 10, 10, 5, 5, 5, 5, 5, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 20, 10, 10, 5, 20, 5, 10, 10, 5, 5, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.exc", "sqlalchemy.orm.path_registry", "sqlalchemy.orm.base", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.visitors", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.schema", "sqlalchemy.sql.type_api", "sqlalchemy.util.typing", "sqlalchemy.orm._typing", "sqlalchemy.orm.attributes", "sqlalchemy.orm.context", "sqlalchemy.orm.decl_api", "sqlalchemy.orm.decl_base", "sqlalchemy.orm.loading", "sqlalchemy.orm.mapper", "sqlalchemy.orm.query", "sqlalchemy.orm.session", "sqlalchemy.orm.state", "sqlalchemy.orm.strategy_options", "sqlalchemy.orm.util", "sqlalchemy.engine.result", "sqlalchemy.sql._typing", "sqlalchemy.orm", "sqlalchemy.exc", "sqlalchemy.inspection", "sqlalchemy.util", "sqlalchemy.sql", "__future__", "collections", "dataclasses", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "_typeshed", "abc", "enum", "sqlalchemy.engine", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.log", "sqlalchemy.sql.annotation", "sqlalchemy.sql.elements", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.util", "sqlalchemy.util.deprecations", "sqlalchemy.util.langhelpers"], "hash": "964b2995122ab13b8e94f2ddaa4c4ec8a7bb6c7131849678c6d640cd3285d2c1", "id": "sqlalchemy.orm.interfaces", "ignore_all": true, "interface_hash": "fa82dda7ad8ab5c2db6a1bee223fc748508b73f2839786e2168bb4538216083c", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\interfaces.py", "plugin_data": null, "size": 50568, "suppressed": [], "version_id": "1.8.0"}