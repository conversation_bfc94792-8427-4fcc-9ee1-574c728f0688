{"data_mtime": 1759503866, "dep_lines": [24, 27, 25, 26, 9, 11, 12, 13, 14, 25, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 10, 5, 10, 5, 20, 5, 20, 20, 30, 30, 30], "dependencies": ["sqlalchemy.util.langhelpers", "sqlalchemy.util.typing", "sqlalchemy.exc", "sqlalchemy.util", "__future__", "asyncio", "<PERSON><PERSON><PERSON>", "sys", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "asyncio.events", "asyncio.locks"], "hash": "3597caeed5e771389c785278266d9d895d73dd96ae0c1594665a9fdaa7d799c0", "id": "sqlalchemy.util._concurrency_py3k", "ignore_all": true, "interface_hash": "6f075f4065478e06e5c10bf4ded990881251b4c9a187519a0ea280f78bb4f48c", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\_concurrency_py3k.py", "plugin_data": null, "size": 9458, "suppressed": [], "version_id": "1.8.0"}