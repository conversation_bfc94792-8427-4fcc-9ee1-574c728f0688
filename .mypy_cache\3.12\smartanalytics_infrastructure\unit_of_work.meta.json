{"data_mtime": 1759504553, "dep_lines": [7, 10, 12, 15, 18, 21, 4, 3, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 25, 5, 5, 5, 5, 5, 10, 5, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["smartanalytics_domain.ports.unit_of_work", "sqlalchemy.ext.asyncio", "smartanalytics_infrastructure.repositories.agent_event_repository", "smartanalytics_infrastructure.repositories.agent_repository", "smartanalytics_infrastructure.repositories.ring_group_repository", "smartanalytics_infrastructure.repositories.tenant_repository", "collections.abc", "types", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "smartanalytics_domain", "smartanalytics_domain.ports", "smartanalytics_domain.ports.repositories", "smartanalytics_infrastructure.repositories", "sqlalchemy", "sqlalchemy.ext", "sqlalchemy.ext.asyncio.base", "sqlalchemy.ext.asyncio.session"], "hash": "aee2db1e5624e0e2a6416b8f8faa5cdd8ac3cb4b4e907de343987b97319b7ce8", "id": "smartanalytics_infrastructure.unit_of_work", "ignore_all": false, "interface_hash": "36497422cb5ab2595cc7eb0737968d42e25d44b99b18cc5ffb41a61c2620c845", "mtime": 1759501803, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "layers\\infrastructure\\python\\smartanalytics_infrastructure\\unit_of_work.py", "plugin_data": null, "size": 2307, "suppressed": [], "version_id": "1.8.0"}