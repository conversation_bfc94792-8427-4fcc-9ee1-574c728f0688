{"data_mtime": 1759504552, "dep_lines": [11, 18, 19, 20, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 20, 20, 30, 30], "dependencies": ["smartanalytics_utilities.utils.datetime_helper", "smartanalytics_utilities.utils.hash_helper", "smartanalytics_utilities.utils.logging_helper", "smartanalytics_utilities.utils.xml_helper", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "typing"], "hash": "7520df3a4ec707c00535e77ce47030668556ed3d36a1e39c02e7a6e66f729a49", "id": "smartanalytics_utilities.utils", "ignore_all": false, "interface_hash": "d5de0bc018e6087a62217fa23824c94422b78ba0910745a76484486e4e8415b5", "mtime": 1759501803, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "layers\\utilities\\python\\smartanalytics_utilities\\utils\\__init__.py", "plugin_data": null, "size": 953, "suppressed": [], "version_id": "1.8.0"}