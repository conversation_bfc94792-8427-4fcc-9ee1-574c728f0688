{".class": "MypyFile", "_fullname": "dateutil", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "dateutil.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dateutil.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__dir__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "flags": [], "fullname": "dateutil.__dir__", "name": "__dir__", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__dir__", "ret_type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dateutil.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dateutil.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dateutil.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dateutil.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dateutil.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}, "easter": {".class": "SymbolTableNode", "cross_ref": "dateutil.easter", "kind": "Gdef"}, "parser": {".class": "SymbolTableNode", "cross_ref": "dateutil.parser", "kind": "Gdef"}, "relativedelta": {".class": "SymbolTableNode", "cross_ref": "dateutil.<PERSON>del<PERSON>", "kind": "Gdef"}, "rrule": {".class": "SymbolTableNode", "cross_ref": "dateutil.rrule", "kind": "Gdef"}, "tz": {".class": "SymbolTableNode", "cross_ref": "dateutil.tz", "kind": "Gdef"}, "utils": {".class": "SymbolTableNode", "cross_ref": "dateutil.utils", "kind": "Gdef"}, "zoneinfo": {".class": "SymbolTableNode", "cross_ref": "dateutil.zoneinfo", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\dateutil-stubs\\__init__.pyi"}