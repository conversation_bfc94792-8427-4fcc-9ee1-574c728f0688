{"data_mtime": 1759503866, "dep_lines": [57, 58, 64, 65, 70, 57, 62, 63, 31, 33, 34, 35, 36, 37, 55, 62, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 25, 20, 10, 10, 5, 10, 5, 10, 5, 5, 10, 20, 5, 20, 20, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.event.legacy", "sqlalchemy.event.registry", "sqlalchemy.util.concurrency", "sqlalchemy.util.typing", "sqlalchemy.event.base", "sqlalchemy.event", "sqlalchemy.exc", "sqlalchemy.util", "__future__", "collections", "itertools", "threading", "types", "typing", "weakref", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "_typeshed", "_weakref", "abc", "sqlalchemy.util.compat", "sqlalchemy.util.langhelpers"], "hash": "38934392b7e730dff3546e679dd09b30b41d8c770059493287adc1482b1440ee", "id": "sqlalchemy.event.attr", "ignore_all": true, "interface_hash": "c3b74b4cc3d18d121db3e8089e402d4989de88dde3be11b7f80db14af7fb9970", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\event\\attr.py", "plugin_data": null, "size": 21406, "suppressed": [], "version_id": "1.8.0"}