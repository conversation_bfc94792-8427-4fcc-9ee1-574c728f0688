{"data_mtime": 1759504508, "dep_lines": [4, 8, 1, 3, 5, 8, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 10, 5, 5, 5, 20, 20, 30], "dependencies": ["collections.abc", "pydantic_core.core_schema", "__future__", "collections", "typing", "pydantic_core", "builtins", "pyexpat.model", "pyexpat.errors", "abc"], "hash": "a90dd16a4e09e9ba9b9e318246388063833ca682e8d2ce6a1f8eac5d949042e0", "id": "pydantic._internal._serializers", "ignore_all": true, "interface_hash": "7a767c87df2429ce231b2f9753d494d8e207d93737b5af89edc04e1cbada9c2f", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\_internal\\_serializers.py", "plugin_data": null, "size": 1474, "suppressed": [], "version_id": "1.8.0"}