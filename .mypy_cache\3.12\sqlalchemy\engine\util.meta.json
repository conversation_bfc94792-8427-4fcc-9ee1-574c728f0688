{"data_mtime": 1759504546, "dep_lines": [18, 19, 23, 16, 17, 8, 10, 16, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 25, 10, 10, 5, 5, 20, 5, 20, 20, 30, 30], "dependencies": ["sqlalchemy.util._has_cy", "sqlalchemy.util.typing", "sqlalchemy.engine._py_util", "sqlalchemy.exc", "sqlalchemy.util", "__future__", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "sqlalchemy.util.langhelpers"], "hash": "44a600bd406d22f37b6c52a65afebbc1ee9058d98d16c2b263df901680a8e932", "id": "sqlalchemy.engine.util", "ignore_all": true, "interface_hash": "40fa780841115b0d1f473426bede3171e711a87ffb3b3df0e7554b0222993cc4", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\util.py", "plugin_data": null, "size": 5849, "suppressed": [], "version_id": "1.8.0"}