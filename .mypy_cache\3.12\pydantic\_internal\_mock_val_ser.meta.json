{"data_mtime": 1759504514, "dep_lines": [9, 3, 8, 12, 13, 14, 1, 4, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 25, 25, 25, 5, 5, 5, 5, 20, 20, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic.plugin._schema_validator", "collections.abc", "pydantic.errors", "pydantic.dataclasses", "pydantic.main", "pydantic.type_adapter", "__future__", "typing", "pydantic_core", "builtins", "pyexpat.model", "pyexpat.errors", "_typeshed", "abc", "pydantic._internal._dataclasses", "pydantic._internal._model_construction", "pydantic.plugin", "pydantic_core._pydantic_core"], "hash": "c2645115206fa9f70b6c8e353ec165881e2ed80677989a597a21116c3df1293a", "id": "pydantic._internal._mock_val_ser", "ignore_all": true, "interface_hash": "7917ab5a63f09aedff98a5ce4a737478b684be75609b1629453a8618e6c56e66", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\_internal\\_mock_val_ser.py", "plugin_data": null, "size": 8885, "suppressed": [], "version_id": "1.8.0"}