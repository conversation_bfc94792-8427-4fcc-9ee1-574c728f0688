{"data_mtime": 1759504547, "dep_lines": [32, 33, 34, 35, 39, 42, 43, 48, 52, 53, 59, 61, 62, 63, 64, 65, 66, 67, 32, 45, 46, 47, 51, 18, 20, 45, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 5, 5, 5, 5, 10, 5, 25, 25, 25, 25, 25, 25, 25, 25, 20, 10, 5, 5, 5, 5, 5, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.attributes", "sqlalchemy.orm.exc", "sqlalchemy.orm.path_registry", "sqlalchemy.orm.base", "sqlalchemy.orm.context", "sqlalchemy.orm.strategies", "sqlalchemy.orm.util", "sqlalchemy.engine.result", "sqlalchemy.sql.util", "sqlalchemy.sql.selectable", "sqlalchemy.orm._typing", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.mapper", "sqlalchemy.orm.query", "sqlalchemy.orm.session", "sqlalchemy.orm.state", "sqlalchemy.engine.cursor", "sqlalchemy.engine.interfaces", "sqlalchemy.orm", "sqlalchemy.exc", "sqlalchemy.util", "sqlalchemy.engine", "sqlalchemy.sql", "__future__", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "_typeshed", "abc", "enum", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.elements", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util._py_collections", "sqlalchemy.util.deprecations", "sqlalchemy.util.langhelpers", "sqlalchemy.util.preloaded"], "hash": "b713e991673b14c0f8d08827725609f6593cfb322a8e1a9730c6afa1bc87a31c", "id": "sqlalchemy.orm.loading", "ignore_all": true, "interface_hash": "429d541974905ed094bc0b0321e0e6d3b043cb58cdabe44be3d0ec26d0325e94", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\loading.py", "plugin_data": null, "size": 60174, "suppressed": [], "version_id": "1.8.0"}