{"data_mtime": 1759503829, "dep_lines": [11, 5, 9, 10, 12, 13, 3, 6, 7, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 5, 10, 5, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["markdown_it.rules_block.state_block", "collections.abc", "markdown_it.rules_block", "markdown_it.ruler", "markdown_it.token", "markdown_it.utils", "__future__", "logging", "typing", "markdown_it", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "markdown_it.main", "markdown_it.rules_block.blockquote", "markdown_it.rules_block.code", "markdown_it.rules_block.fence", "markdown_it.rules_block.heading", "markdown_it.rules_block.hr", "markdown_it.rules_block.html_block", "markdown_it.rules_block.lheading", "markdown_it.rules_block.list", "markdown_it.rules_block.paragraph", "markdown_it.rules_block.reference", "markdown_it.rules_block.table"], "hash": "f8ccae81707add37bbd6ce0d71241988ae5b13a04791d17266ffdbbe2b9ab5d2", "id": "markdown_it.parser_block", "ignore_all": true, "interface_hash": "c2a0690ec4bf3a382f8f3143ef6d9647b874c9f3025bf98338022ec74974b056", "mtime": 1757092234, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\markdown_it\\parser_block.py", "plugin_data": null, "size": 3939, "suppressed": [], "version_id": "1.8.0"}