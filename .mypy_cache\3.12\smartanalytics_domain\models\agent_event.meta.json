{"data_mtime": 1759503829, "dep_lines": [8, 3, 4, 5, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["smartanalytics_utilities.utils.hash_helper", "dataclasses", "datetime", "enum", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "_typeshed", "abc", "smartanalytics_utilities", "smartanalytics_utilities.utils", "types", "typing_extensions"], "hash": "7bc6a83796db52134b696822faf3bbc719a92d433b3bfad43dee86501fcd866c", "id": "smartanalytics_domain.models.agent_event", "ignore_all": false, "interface_hash": "39402d888cfb92661d544996b15f2238504eecbcf81f436725b7ff96a4e6f9f0", "mtime": 1759503404, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "layers\\domain\\python\\smartanalytics_domain\\models\\agent_event.py", "plugin_data": null, "size": 7886, "suppressed": [], "version_id": "1.8.0"}