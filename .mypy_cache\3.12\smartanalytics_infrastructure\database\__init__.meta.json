{"data_mtime": 1759504553, "dep_lines": [10, 14, 22, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 20, 20, 30, 30], "dependencies": ["smartanalytics_infrastructure.database.connection", "smartanalytics_infrastructure.database.models", "smartanalytics_infrastructure.database.session", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "typing"], "hash": "ee9fce6db34434983f36b30a8425b5aed433f970a9126ce5d741ebdf2577c7fa", "id": "smartanalytics_infrastructure.database", "ignore_all": false, "interface_hash": "a39013ce667fb8a92f1f10b797a01f5cacecb405e70bcfbd42c0ab29a53d92bc", "mtime": 1759502092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "layers\\infrastructure\\python\\smartanalytics_infrastructure\\database\\__init__.py", "plugin_data": null, "size": 840, "suppressed": [], "version_id": "1.8.0"}