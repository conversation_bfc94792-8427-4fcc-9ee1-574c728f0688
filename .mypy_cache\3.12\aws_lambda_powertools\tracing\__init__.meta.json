{"data_mtime": 1759504512, "dep_lines": [4, 5, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 20, 20, 30, 30], "dependencies": ["aws_lambda_powertools.tracing.extensions", "aws_lambda_powertools.tracing.tracer", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "typing"], "hash": "65d93b984f2550c2026d83a7c7fd72163be0873a0f3a93e646ff96923fa4ce45", "id": "aws_lambda_powertools.tracing", "ignore_all": true, "interface_hash": "41fe5e28d57ef238677692ca2e6c7a94a342abde6ff80e3d0eb75d14c6364247", "mtime": 1757091873, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\aws_lambda_powertools\\tracing\\__init__.py", "plugin_data": null, "size": 142, "suppressed": [], "version_id": "1.8.0"}