{"data_mtime": 1759504509, "dep_lines": [5, 6, 7, 33, 1, 3, 30, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 25, 5, 10, 25, 5, 20, 20, 30, 30, 30, 30], "dependencies": ["urllib3.util.connection", "urllib3.util.timeout", "urllib3.util.url", "urllib3.response", "__future__", "typing", "ssl", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "enum", "io", "urllib3.util"], "hash": "4f57301f7461cecac187a073dc03865436e846c13bbde8a3a993d75d04d1d918", "id": "urllib3._base_connection", "ignore_all": true, "interface_hash": "e403808bf293b0923c59a2396305165221ff90c25c9d6231a5aa9e2604a15eeb", "mtime": 1757091871, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\urllib3\\_base_connection.py", "plugin_data": null, "size": 5568, "suppressed": [], "version_id": "1.8.0"}