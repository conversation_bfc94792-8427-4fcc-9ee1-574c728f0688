{"data_mtime": 1759503825, "dep_lines": [2, 5, 1, 3, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 30], "dependencies": ["collections.abc", "jmespath.functions", "_typeshed", "typing", "builtins", "abc"], "hash": "6c9a1d5df176fd991a83b664766804474f9f39e7331c293e30e6bacfb30888a7", "id": "jmespath.visitor", "ignore_all": true, "interface_hash": "e77d6c04361ef053d61ac07a49fd44dc3618a46ad629fe57864a7fda084cc244", "mtime": 1757424622, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\jmespath-stubs\\visitor.pyi", "plugin_data": null, "size": 3376, "suppressed": [], "version_id": "1.8.0"}