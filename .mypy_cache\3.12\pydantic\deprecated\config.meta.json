{"data_mtime": 1759503836, "dep_lines": [8, 8, 9, 1, 3, 4, 6, 1, 1, 1, 1], "dep_prios": [10, 20, 5, 5, 10, 5, 5, 5, 20, 20, 30], "dependencies": ["pydantic._internal._config", "pydantic._internal", "pydantic.warnings", "__future__", "warnings", "typing", "typing_extensions", "builtins", "pyexpat.model", "pyexpat.errors", "abc"], "hash": "93f96c564e7ba5ac4b24e701b9e1f4edcbb53a012059d541c66ea57da0b70825", "id": "pydantic.deprecated.config", "ignore_all": true, "interface_hash": "59570aad725bc8524aea3efd7e4ba060ea08e24986401dceaf494e3b3382365b", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\deprecated\\config.py", "plugin_data": null, "size": 2663, "suppressed": [], "version_id": "1.8.0"}