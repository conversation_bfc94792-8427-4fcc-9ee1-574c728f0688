{"data_mtime": 1759503828, "dep_lines": [29, 30, 31, 32, 33, 42, 43, 44, 45, 46, 56, 1, 2, 3, 4, 5, 6, 7, 8, 27, 74, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 25, 5, 20, 20, 30, 30, 30], "dependencies": ["pydantic.v1.class_validators", "pydantic.v1.config", "pydantic.v1.error_wrappers", "pydantic.v1.errors", "pydantic.v1.fields", "pydantic.v1.json", "pydantic.v1.parse", "pydantic.v1.schema", "pydantic.v1.types", "pydantic.v1.typing", "pydantic.v1.utils", "warnings", "abc", "copy", "enum", "functools", "pathlib", "types", "typing", "typing_extensions", "inspect", "builtins", "pyexpat.model", "pyexpat.errors", "_typeshed", "os", "pydantic.v1.dataclasses"], "hash": "9c5be9a141f650a01edafcf0ef05543645c178851ed57b38108bfe241a92a369", "id": "pydantic.v1.main", "ignore_all": true, "interface_hash": "7fcb5e3a0273a04e1cff92e6154f32b38ea0e9af66a04d41ca9fdfe5d72c65fa", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\v1\\main.py", "plugin_data": null, "size": 44555, "suppressed": [], "version_id": "1.8.0"}