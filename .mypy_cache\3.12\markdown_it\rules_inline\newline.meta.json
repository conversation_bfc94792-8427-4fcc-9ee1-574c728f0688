{"data_mtime": 1759503829, "dep_lines": [3, 4, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 20, 20, 30, 30, 30], "dependencies": ["markdown_it.common.utils", "markdown_it.rules_inline.state_inline", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "markdown_it.ruler", "typing"], "hash": "df6f6bd15dda0e3ccdb4972fcc0de5b05623ce006b4a12c0579b9ff61c102ff3", "id": "markdown_it.rules_inline.newline", "ignore_all": true, "interface_hash": "2ffb0ccd94f415d39a34bd21f53caf7a84e9ff4683118f23243c580ea7c079d8", "mtime": 1757092234, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\markdown_it\\rules_inline\\newline.py", "plugin_data": null, "size": 1297, "suppressed": [], "version_id": "1.8.0"}