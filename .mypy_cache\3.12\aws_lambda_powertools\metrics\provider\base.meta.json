{"data_mtime": **********, "dep_lines": [8, 8, 11, 12, 1, 3, 4, 5, 6, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 20, 25, 25, 5, 10, 10, 5, 5, 5, 20, 20, 30, 30, 30], "dependencies": ["aws_lambda_powertools.metrics.provider.cold_start", "aws_lambda_powertools.metrics.provider", "aws_lambda_powertools.shared.types", "aws_lambda_powertools.utilities.typing", "__future__", "functools", "logging", "abc", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "aws_lambda_powertools.utilities", "aws_lambda_powertools.utilities.typing.lambda_context", "types"], "hash": "c352415b6b987b3c5d62fdc16deaec0a9cd2e3e1847e6f46f31938a294b50bea", "id": "aws_lambda_powertools.metrics.provider.base", "ignore_all": true, "interface_hash": "54aff92657d8b72f2745b795ee9651a1b38ea38a18d3a3e167ee2db5cb5368ae", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\aws_lambda_powertools\\metrics\\provider\\base.py", "plugin_data": null, "size": 6879, "suppressed": [], "version_id": "1.8.0"}