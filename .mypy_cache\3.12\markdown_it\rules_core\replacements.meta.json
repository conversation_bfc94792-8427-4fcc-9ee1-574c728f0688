{"data_mtime": 1759504508, "dep_lines": [23, 22, 17, 19, 20, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 5, 20, 20, 30, 30, 30, 30], "dependencies": ["markdown_it.rules_core.state_core", "markdown_it.token", "__future__", "logging", "re", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "enum", "markdown_it.ruler", "typing"], "hash": "087ef99a27beb5dcdd2ca42d301b824dc5c09758a37a075919f6d5fd593bb2dd", "id": "markdown_it.rules_core.replacements", "ignore_all": true, "interface_hash": "20d86aca636516057b592fe79cc046508e7045b40e46d4a15eb7d01326b436ff", "mtime": 1757092234, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\markdown_it\\rules_core\\replacements.py", "plugin_data": null, "size": 3471, "suppressed": [], "version_id": "1.8.0"}