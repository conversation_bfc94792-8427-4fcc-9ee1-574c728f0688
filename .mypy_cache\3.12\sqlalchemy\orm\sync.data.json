{".class": "MypyFile", "_fullname": "sqlalchemy.orm.sync", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "PassiveFlag": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.PassiveFlag", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.sync.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.sync.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.sync.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.sync.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.sync.__package__", "name": "__package__", "type": "builtins.str"}}, "_raise_col_to_prop": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["isdest", "source_mapper", "source_column", "dest_mapper", "dest_column", "err"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.sync._raise_col_to_prop", "name": "_raise_col_to_prop", "type": null}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "bulk_populate_inherit_keys": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["source_dict", "source_mapper", "synchronize_pairs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.sync.bulk_populate_inherit_keys", "name": "bulk_populate_inherit_keys", "type": null}}, "clear": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["dest", "dest_mapper", "synchronize_pairs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.sync.clear", "name": "clear", "type": null}}, "exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.exc", "kind": "Gdef"}, "orm_util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.util", "kind": "Gdef"}, "populate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["source", "source_mapper", "dest", "dest_mapper", "synchronize_pairs", "uowcommit", "flag_cascaded_pks"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.sync.populate", "name": "populate", "type": null}}, "populate_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["source", "source_mapper", "dict_", "synchronize_pairs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.sync.populate_dict", "name": "populate_dict", "type": null}}, "source_modified": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["uowcommit", "source", "source_mapper", "synchronize_pairs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.sync.source_modified", "name": "source_modified", "type": null}}, "update": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["source", "source_mapper", "dest", "old_prefix", "synchronize_pairs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.sync.update", "name": "update", "type": null}}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\sync.py"}