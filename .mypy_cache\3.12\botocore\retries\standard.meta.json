{"data_mtime": 1759504550, "dep_lines": [18, 19, 20, 7, 11, 12, 17, 18, 8, 9, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 5, 5, 20, 5, 5, 5, 20, 20, 30, 30], "dependencies": ["botocore.retries.quota", "botocore.retries.special", "botocore.retries.base", "collections.abc", "botocore.client", "botocore.exceptions", "botocore.model", "botocore.retries", "logging", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "_random", "abc"], "hash": "1b310aa27c6bf22adcbb764d280d5f36e0b654fe01158c250e3f101b1edb9b0c", "id": "botocore.retries.standard", "ignore_all": true, "interface_hash": "004cf5eb4317272ab0df9cbb831adc46b33c59fd2a90c65a017c5b1b9318c321", "mtime": 1757092235, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\botocore-stubs\\retries\\standard.pyi", "plugin_data": null, "size": 4478, "suppressed": [], "version_id": "1.8.0"}