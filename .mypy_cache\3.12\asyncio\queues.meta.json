{"data_mtime": 1759503833, "dep_lines": [2, 1, 3, 6, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 20, 20, 30, 30], "dependencies": ["asyncio.events", "sys", "typing", "types", "builtins", "pyexpat.model", "pyexpat.errors", "_typeshed", "abc"], "hash": "90b47bf317dd1ffbe90cb436540fe5c90611317c4654569f0c05c85e4840f06a", "id": "asyncio.queues", "ignore_all": true, "interface_hash": "108c6900a99d986ae52f187e26187e81cd37e4a654f670e20fd02144a8bc91df", "mtime": 1757092231, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\asyncio\\queues.pyi", "plugin_data": null, "size": 1270, "suppressed": [], "version_id": "1.8.0"}