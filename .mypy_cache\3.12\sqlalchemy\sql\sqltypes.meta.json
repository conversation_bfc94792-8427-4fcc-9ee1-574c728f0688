{"data_mtime": 1759504547, "dep_lines": [35, 36, 37, 38, 39, 40, 43, 54, 59, 60, 63, 69, 74, 80, 12, 35, 55, 56, 57, 58, 59, 10, 12, 13, 14, 15, 16, 17, 18, 33, 55, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 10, 5, 5, 5, 5, 10, 10, 5, 25, 25, 25, 10, 20, 10, 10, 10, 5, 20, 5, 20, 10, 10, 10, 10, 10, 5, 5, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.coercions", "sqlalchemy.sql.elements", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.type_api", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.visitors", "sqlalchemy.engine.processors", "sqlalchemy.util.langhelpers", "sqlalchemy.util.typing", "sqlalchemy.sql._typing", "sqlalchemy.sql.schema", "sqlalchemy.engine.interfaces", "collections.abc", "sqlalchemy.sql", "sqlalchemy.event", "sqlalchemy.exc", "sqlalchemy.inspection", "sqlalchemy.util", "sqlalchemy.engine", "__future__", "collections", "datetime", "decimal", "enum", "json", "pickle", "typing", "uuid", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "_decimal", "_typeshed", "abc", "sqlalchemy.engine.base", "sqlalchemy.engine.mock", "sqlalchemy.event.api", "sqlalchemy.event.base", "sqlalchemy.event.registry", "sqlalchemy.log", "sqlalchemy.sql.annotation", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.util._collections", "sqlalchemy.util._py_collections", "sqlalchemy.util.preloaded", "types", "typing_extensions"], "hash": "5762dd747ba8de88cd3e93896866d1e068a7b832237d183ac9df36fd4e9f77de", "id": "sqlalchemy.sql.sqltypes", "ignore_all": true, "interface_hash": "f6b0fae54b755ff03c359d0b821193c82b65e0099401c64a3e8761d345c3ad6f", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\sqltypes.py", "plugin_data": null, "size": 136086, "suppressed": [], "version_id": "1.8.0"}