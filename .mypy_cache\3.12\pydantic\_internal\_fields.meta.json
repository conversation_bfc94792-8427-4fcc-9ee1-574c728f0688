{"data_mtime": 1759503836, "dep_lines": [22, 22, 23, 24, 25, 26, 27, 28, 35, 36, 7, 16, 17, 20, 22, 33, 34, 3, 5, 6, 8, 9, 10, 11, 12, 14, 15, 16, 19, 31, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 5, 5, 5, 5, 25, 25, 5, 10, 5, 5, 20, 25, 25, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 20, 5, 25, 5, 20, 20, 30, 30, 30], "dependencies": ["pydantic._internal._generics", "pydantic._internal._typing_extra", "pydantic._internal._config", "pydantic._internal._docs_extraction", "pydantic._internal._import_utils", "pydantic._internal._namespace_utils", "pydantic._internal._repr", "pydantic._internal._utils", "pydantic._internal._dataclasses", "pydantic._internal._decorators", "collections.abc", "typing_inspection.typing_objects", "typing_inspection.introspection", "pydantic.errors", "pydantic._internal", "pydantic.fields", "pydantic.main", "__future__", "dataclasses", "warnings", "copy", "functools", "inspect", "re", "typing", "pydantic_core", "typing_extensions", "typing_inspection", "pydantic", "annotated_types", "builtins", "pyexpat.model", "pyexpat.errors", "_typeshed", "abc", "pydantic._internal._model_construction"], "hash": "0033e37ad22b2969c9693583d19b718b30858e6425a84ba8c0ae3bea990d5b2c", "id": "pydantic._internal._fields", "ignore_all": true, "interface_hash": "f88931ec5a015a5d34c3b2f9325d39a0a1710d8948d264e72ed5ad245c70ee6c", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\_internal\\_fields.py", "plugin_data": null, "size": 20647, "suppressed": [], "version_id": "1.8.0"}