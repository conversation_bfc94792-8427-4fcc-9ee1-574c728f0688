{"data_mtime": 1759504551, "dep_lines": [187, 188, 189, 190, 202, 187, 209, 210, 211, 205, 206, 207, 208, 210, 179, 181, 182, 183, 184, 185, 205, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1017], "dep_prios": [10, 10, 5, 5, 5, 20, 10, 10, 5, 10, 10, 10, 5, 20, 5, 5, 10, 10, 10, 10, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20], "dependencies": ["sqlalchemy.dialects.postgresql.json", "sqlalchemy.dialects.postgresql.ranges", "sqlalchemy.dialects.postgresql.array", "sqlalchemy.dialects.postgresql.base", "sqlalchemy.dialects.postgresql.types", "sqlalchemy.dialects.postgresql", "sqlalchemy.engine.processors", "sqlalchemy.sql.sqltypes", "sqlalchemy.util.concurrency", "sqlalchemy.exc", "sqlalchemy.pool", "sqlalchemy.util", "sqlalchemy.engine", "sqlalchemy.sql", "__future__", "collections", "decimal", "json", "re", "time", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "_decimal", "_typeshed", "abc", "asyncio", "asyncio.exceptions", "asyncio.futures", "asyncio.locks", "asyncio.tasks", "enum", "sqlalchemy.connectors", "sqlalchemy.connectors.asyncio", "sqlalchemy.dialects.postgresql.named_types", "sqlalchemy.engine.default", "sqlalchemy.engine.interfaces", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.compiler", "sqlalchemy.sql.elements", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.sql.visitors", "sqlalchemy.util._collections", "sqlalchemy.util._concurrency_py3k", "sqlalchemy.util.langhelpers", "types", "typing", "typing_extensions"], "hash": "9b9248457912692656e55711940631a6ee9b803dfacecded710ba2b3bc2911b0", "id": "sqlalchemy.dialects.postgresql.asyncpg", "ignore_all": true, "interface_hash": "7e34b5a27988add4b062dca8f50d826c7578c05e016267d5411dc73c968c3661", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\asyncpg.py", "plugin_data": null, "size": 42841, "suppressed": ["asyncpg"], "version_id": "1.8.0"}