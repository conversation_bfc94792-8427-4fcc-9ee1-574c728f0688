{"data_mtime": 1759504546, "dep_lines": [22, 23, 10, 12, 22, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 20, 5, 20, 20, 30, 30], "dependencies": ["sqlalchemy.util", "sqlalchemy.exc", "__future__", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "collections"], "hash": "9591f7cefc332100096254028a35fbb066dbbe8290b2c6818db74e6bedf6d2fe", "id": "sqlalchemy.util.topological", "ignore_all": true, "interface_hash": "21ee649133f586f2c3b9dd4aa1069c4a3e443577e179425aead2890b68c9cfe1", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\topological.py", "plugin_data": null, "size": 3571, "suppressed": [], "version_id": "1.8.0"}