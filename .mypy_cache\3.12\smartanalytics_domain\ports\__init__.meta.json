{"data_mtime": 1759503833, "dep_lines": [13, 14, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 20, 20, 30, 30], "dependencies": ["smartanalytics_domain.ports.repositories", "smartanalytics_domain.ports.unit_of_work", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "typing"], "hash": "733ed443b890c8401fdb8ef53a2e3d8786652f3108d60de701b7890c3e361cba", "id": "smartanalytics_domain.ports", "ignore_all": false, "interface_hash": "2285f510eb558c55c2856ca523608230c499af552209e70f7053d28033393075", "mtime": 1759421720, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "layers\\domain\\python\\smartanalytics_domain\\ports\\__init__.py", "plugin_data": null, "size": 723, "suppressed": [], "version_id": "1.8.0"}