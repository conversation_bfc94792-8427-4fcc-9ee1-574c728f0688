{"data_mtime": 1759503832, "dep_lines": [4, 7, 8, 1, 6, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 5, 5, 5, 5, 5, 20, 20, 30, 30, 30], "dependencies": ["rich.console", "rich.segment", "rich.terminal_theme", "typing", "rich", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "enum", "rich.style"], "hash": "1bda4e26647811220561277830a1aa987a82b6cb71d28456a7279382fe78f977", "id": "rich.jupyter", "ignore_all": true, "interface_hash": "c8798145262dcc1d55f482e4f5974c2dc34b8fe9d297530878ae65b314eb4f2c", "mtime": 1757092238, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\rich\\jupyter.py", "plugin_data": null, "size": 3228, "suppressed": [], "version_id": "1.8.0"}