{"data_mtime": 1759503866, "dep_lines": [13, 16, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 20, 20, 30, 30], "dependencies": ["sqlalchemy.future.engine", "sqlalchemy.sql._selectable_constructors", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "typing"], "hash": "6d1324e086f4e660b10c165f267853664db8d6b44a8013b50b944430a9f20f83", "id": "sqlalchemy.future", "ignore_all": true, "interface_hash": "621ac4a89883898fa7b52e96920491ced95b2dc74a25cfd4d05795ffb009f273", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\future\\__init__.py", "plugin_data": null, "size": 528, "suppressed": [], "version_id": "1.8.0"}