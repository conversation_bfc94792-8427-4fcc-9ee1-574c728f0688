{"data_mtime": 1759503836, "dep_lines": [6, 1, 2, 5, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 5, 5, 25, 5, 20, 20, 30, 30, 30, 30], "dependencies": ["pydantic.fields", "functools", "typing", "pydantic", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.main"], "hash": "4d18710f93ae63a094a2c8a805d05c254b34a26ec820e36265d60057bcd0f233", "id": "pydantic._internal._import_utils", "ignore_all": true, "interface_hash": "2864439b3cce5624e07790520efc6befb05628510dfe7c0679c239c19272c2cf", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\_internal\\_import_utils.py", "plugin_data": null, "size": 402, "suppressed": [], "version_id": "1.8.0"}