{"data_mtime": 1759504507, "dep_lines": [7, 8, 9, 10, 1, 3, 4, 5, 13, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 25, 5, 20, 20, 30, 30, 30], "dependencies": ["markdown_it.common.utils", "markdown_it.ruler", "markdown_it.token", "markdown_it.utils", "__future__", "collections", "dataclasses", "typing", "markdown_it", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "markdown_it.main", "typing_extensions"], "hash": "77e99e9c5cdbcf9143cb524d806045f810126ac9d523ef51b8ec56cfd3e72a7e", "id": "markdown_it.rules_inline.state_inline", "ignore_all": true, "interface_hash": "8c2a6eff2c1891f4110cb10597c4e1b4b5800ece1200f848b882ea9869de2a64", "mtime": 1757092234, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\markdown_it\\rules_inline\\state_inline.py", "plugin_data": null, "size": 5003, "suppressed": [], "version_id": "1.8.0"}