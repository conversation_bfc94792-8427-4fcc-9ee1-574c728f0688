{"data_mtime": 1759504551, "dep_lines": [8, 9, 10, 11, 3, 4, 6, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["smartanalytics_utilities.config.aws_config", "smartanalytics_utilities.config.base", "smartanalytics_utilities.config.database_config", "smartanalytics_utilities.config.logging_config", "functools", "typing", "pydantic", "pydantic_settings", "builtins", "pyexpat.model", "pyexpat.errors", "_typeshed", "abc", "annotated_types", "enum", "os", "pathlib", "pydantic._internal", "pydantic._internal._generate_schema", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.fields", "pydantic.main", "pydantic.types", "pydantic_settings.main", "re", "typing_extensions"], "hash": "12775a0a3a143668555e1a3e814e844098b6f80bec6d81e265662f43810dad2f", "id": "smartanalytics_utilities.config.settings", "ignore_all": false, "interface_hash": "cae2f01bbc9fa89e444ac6a7c2b392d89e274fcce67f451f977d2058a6236fff", "mtime": 1759504062, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "layers\\utilities\\python\\smartanalytics_utilities\\config\\settings.py", "plugin_data": null, "size": 3331, "suppressed": [], "version_id": "1.8.0"}