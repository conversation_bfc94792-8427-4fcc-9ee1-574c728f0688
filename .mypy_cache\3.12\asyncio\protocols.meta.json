{"data_mtime": 1759503833, "dep_lines": [2, 1, 2, 3, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 20, 5, 5, 20, 20, 30, 30], "dependencies": ["asyncio.transports", "_typeshed", "asyncio", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "abc"], "hash": "ede7386cc6e43b45ddd3976c2c5a28382a79accc20f2636ed9e3eebeedf08959", "id": "asyncio.protocols", "ignore_all": true, "interface_hash": "d824a8b6edec90a38086202b8e5f21e143fbc724aa7070f7149d02f2b71b6acf", "mtime": 1757092230, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\asyncio\\protocols.pyi", "plugin_data": null, "size": 1631, "suppressed": [], "version_id": "1.8.0"}