{"data_mtime": 1759504546, "dep_lines": [35, 37, 21, 23, 24, 25, 26, 27, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 10, 5, 5, 5, 20, 20, 30, 30], "dependencies": ["sqlalchemy.util.concurrency", "sqlalchemy.util.langhelpers", "__future__", "asyncio", "collections", "threading", "time", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "asyncio.queues"], "hash": "35ef151654b56f802b662d6c88107c1dea940c7bbbbeabda71b84c553e15788f", "id": "sqlalchemy.util.queue", "ignore_all": true, "interface_hash": "6c8e46900a42dd420e1d1f112396783645c48d34dbec12da901722993f4256d1", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\queue.py", "plugin_data": null, "size": 10507, "suppressed": [], "version_id": "1.8.0"}