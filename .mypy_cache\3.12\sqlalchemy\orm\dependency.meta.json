{"data_mtime": 1759504547, "dep_lines": [14, 15, 16, 17, 18, 19, 14, 22, 23, 24, 12, 22, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 5, 20, 10, 10, 10, 5, 20, 5, 20, 20, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.attributes", "sqlalchemy.orm.exc", "sqlalchemy.orm.sync", "sqlalchemy.orm.unitofwork", "sqlalchemy.orm.util", "sqlalchemy.orm.interfaces", "sqlalchemy.orm", "sqlalchemy.exc", "sqlalchemy.sql", "sqlalchemy.util", "__future__", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "enum", "sqlalchemy.orm.base", "typing"], "hash": "22fe789d34c0d5462a0d87fb4df22ed4ce4a7daf402700f08a89dd2f3c32c835", "id": "sqlalchemy.orm.dependency", "ignore_all": true, "interface_hash": "601eb60de2c7ba490bc1f2f6730d78619778a14ad47b58cc0b8a059ef037f48d", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\dependency.py", "plugin_data": null, "size": 48921, "suppressed": [], "version_id": "1.8.0"}