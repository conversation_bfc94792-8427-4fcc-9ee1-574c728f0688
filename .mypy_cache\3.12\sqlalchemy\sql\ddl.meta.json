{"data_mtime": 1759504547, "dep_lines": [29, 30, 33, 36, 37, 41, 44, 51, 52, 53, 29, 34, 35, 14, 16, 17, 34, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 10, 5, 25, 25, 25, 25, 25, 20, 10, 10, 5, 10, 5, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.roles", "sqlalchemy.sql.base", "sqlalchemy.sql.elements", "sqlalchemy.util.topological", "sqlalchemy.util.typing", "sqlalchemy.sql.compiler", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.engine.base", "sqlalchemy.engine.interfaces", "sqlalchemy.sql", "sqlalchemy.exc", "sqlalchemy.util", "__future__", "contextlib", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "abc", "enum", "sqlalchemy.engine", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.operators", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "types"], "hash": "aac546ebc5b7e3c3362170985f20a47ea0cd9bc8ffb4046e073de2ced82ccf64", "id": "sqlalchemy.sql.ddl", "ignore_all": true, "interface_hash": "614382dc70c4311d7988b1acb38efe60b5e38badfc7c5628494b95027753f720", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\ddl.py", "plugin_data": null, "size": 49437, "suppressed": [], "version_id": "1.8.0"}