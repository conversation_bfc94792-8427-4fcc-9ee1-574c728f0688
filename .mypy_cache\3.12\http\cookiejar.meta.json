{"data_mtime": 1759503830, "dep_lines": [3, 4, 7, 1, 2, 5, 6, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 5, 5, 20, 20, 30, 30, 30], "dependencies": ["collections.abc", "http.client", "urllib.request", "sys", "_typeshed", "re", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "io", "urllib"], "hash": "30bafc8db398a3a96eb9f447cbd26cd3bb7b9cfb461999e2222caf6224d3078e", "id": "http.cookiejar", "ignore_all": true, "interface_hash": "b87159b8ef9c3510b1a947eb1f24be71991804a201bf61248fcffe45ff58cda7", "mtime": 1757092231, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\http\\cookiejar.pyi", "plugin_data": null, "size": 7628, "suppressed": [], "version_id": "1.8.0"}