{"data_mtime": 1759504546, "dep_lines": [27, 28, 29, 35, 36, 12, 27, 34, 9, 11, 13, 15, 16, 34, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 10, 5, 10, 20, 10, 5, 5, 5, 10, 5, 20, 5, 20, 20, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.operators", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.visitors", "sqlalchemy.util.langhelpers", "sqlalchemy.util.typing", "collections.abc", "sqlalchemy.sql", "sqlalchemy.util", "__future__", "collections", "itertools", "operator", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "enum", "sqlalchemy.util.preloaded", "types"], "hash": "d7906394bb315a6efe4bf6423ad9c0c9ba36c7ddfc107ed38436cd504de92dc1", "id": "sqlalchemy.sql.traversals", "ignore_all": true, "interface_hash": "bf4eec93f95d9105598aef83ff8feae8d691ea7ad6865d64397dd2dd83392a67", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\traversals.py", "plugin_data": null, "size": 34688, "suppressed": [], "version_id": "1.8.0"}