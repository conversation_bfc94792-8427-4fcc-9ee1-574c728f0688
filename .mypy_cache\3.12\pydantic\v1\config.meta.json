{"data_mtime": 1759503828, "dep_lines": [7, 8, 9, 14, 15, 1, 2, 3, 5, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 25, 25, 10, 5, 5, 5, 5, 20, 20, 30, 30, 30, 30, 30], "dependencies": ["pydantic.v1.typing", "pydantic.v1.utils", "pydantic.v1.version", "pydantic.v1.fields", "pydantic.v1.main", "json", "enum", "typing", "typing_extensions", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "_typeshed", "abc", "json.decoder", "json.encoder"], "hash": "6ba3f459eafdc7871bc0a5bb5eff29a1252a33858ff912d6c17e9832962af400", "id": "pydantic.v1.config", "ignore_all": true, "interface_hash": "51f505e7f56789879c0837d0ede265bc835e539620e79745f742b5106d380164", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\v1\\config.py", "plugin_data": null, "size": 6532, "suppressed": [], "version_id": "1.8.0"}