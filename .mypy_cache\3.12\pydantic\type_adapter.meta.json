{"data_mtime": 1759504514, "dep_lines": [25, 25, 25, 25, 25, 25, 25, 35, 6, 22, 23, 25, 26, 28, 3, 5, 7, 8, 9, 19, 20, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 20, 5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._config", "pydantic._internal._generate_schema", "pydantic._internal._mock_val_ser", "pydantic._internal._namespace_utils", "pydantic._internal._repr", "pydantic._internal._typing_extra", "pydantic._internal._utils", "pydantic.plugin._schema_validator", "collections.abc", "pydantic.errors", "pydantic.main", "pydantic._internal", "pydantic.config", "pydantic.json_schema", "__future__", "sys", "dataclasses", "types", "typing", "pydantic_core", "typing_extensions", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "abc", "pydantic.aliases", "pydantic.fields", "pydantic.plugin", "pydantic_core._pydantic_core", "pydantic_core.core_schema", "re"], "hash": "637344d18845c31c28aab614f5c696ca62d6a7502fab8b1151d6f9b34d51a099", "id": "pydantic.type_adapter", "ignore_all": true, "interface_hash": "e03ec3f6682b80c1711bd4952fdcf38792d95b1819c6ab5eb8ae7d954cf8b1d5", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\type_adapter.py", "plugin_data": null, "size": 31171, "suppressed": [], "version_id": "1.8.0"}