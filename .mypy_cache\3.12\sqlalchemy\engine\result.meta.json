{"data_mtime": 1759504547, "dep_lines": [36, 40, 45, 46, 50, 55, 56, 38, 39, 10, 12, 13, 14, 15, 16, 38, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 25, 25, 25, 10, 5, 5, 5, 10, 10, 10, 5, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.engine.row", "sqlalchemy.sql.base", "sqlalchemy.util._has_cy", "sqlalchemy.util.typing", "sqlalchemy.engine._py_row", "sqlalchemy.sql.elements", "sqlalchemy.sql.type_api", "sqlalchemy.exc", "sqlalchemy.util", "__future__", "enum", "functools", "itertools", "operator", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "_operator", "_typeshed", "abc", "sqlalchemy.sql", "sqlalchemy.sql.operators", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers"], "hash": "aa5f10db15017610794969398ce820b7fcdbd830459ae95d32ad58e3984816f6", "id": "sqlalchemy.engine.result", "ignore_all": true, "interface_hash": "d804f942c80908a19b900693fdd88e706c963f13f058156489e163ec6e186758", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\result.py", "plugin_data": null, "size": 80192, "suppressed": [], "version_id": "1.8.0"}