{"data_mtime": 1759503829, "dep_lines": [10, 5, 8, 9, 11, 12, 3, 6, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 5, 5, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["markdown_it.rules_inline.state_inline", "collections.abc", "markdown_it.rules_inline", "markdown_it.ruler", "markdown_it.token", "markdown_it.utils", "__future__", "typing", "markdown_it", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "markdown_it.main", "markdown_it.rules_inline.autolink", "markdown_it.rules_inline.backticks", "markdown_it.rules_inline.balance_pairs", "markdown_it.rules_inline.emphasis", "markdown_it.rules_inline.entity", "markdown_it.rules_inline.escape", "markdown_it.rules_inline.fragments_join", "markdown_it.rules_inline.html_inline", "markdown_it.rules_inline.image", "markdown_it.rules_inline.link", "markdown_it.rules_inline.linkify", "markdown_it.rules_inline.newline", "markdown_it.rules_inline.strikethrough", "markdown_it.rules_inline.text"], "hash": "cb48c28a0f0227140eee1073d19637b06bcf940293a213b02206aa9e549a4b90", "id": "markdown_it.parser_inline", "ignore_all": true, "interface_hash": "feef11ffb6e579eea1e2aaf0cf6662f99916b9144afcbf575484c868e32b28c3", "mtime": 1757092234, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\markdown_it\\parser_inline.py", "plugin_data": null, "size": 5024, "suppressed": [], "version_id": "1.8.0"}