{"data_mtime": 1759503828, "dep_lines": [5, 1, 2, 3, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 20, 20, 30, 30, 30], "dependencies": ["pydantic.v1.typing", "decimal", "pathlib", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "_decimal", "abc", "os"], "hash": "988c0f103e6f18ce50e6fe02e19d493e5753447fa89af132947ea4b0c84e98fc", "id": "pydantic.v1.errors", "ignore_all": true, "interface_hash": "136c8a72fabd56971eaeec91388a0bd0f216f504b1f1b3a80cbfe6adb6aa3668", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\v1\\errors.py", "plugin_data": null, "size": 17726, "suppressed": [], "version_id": "1.8.0"}