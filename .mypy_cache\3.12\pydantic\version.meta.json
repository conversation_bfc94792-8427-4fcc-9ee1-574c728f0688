{"data_mtime": 1759504508, "dep_lines": [3, 5, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 20, 20, 30, 30, 30, 30], "dependencies": ["__future__", "pydantic_core", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "pydantic.v1", "pydantic.v1.version", "typing"], "hash": "e1f992271cd974e944677bb2dcba2deac6b6c53a9585dec643e88facc9dfa4c9", "id": "pydantic.version", "ignore_all": true, "interface_hash": "056167301f54b8cdf1249fd3766d2c80a5ec2670d785532d006eea0d45547112", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\version.py", "plugin_data": null, "size": 2827, "suppressed": [], "version_id": "1.8.0"}