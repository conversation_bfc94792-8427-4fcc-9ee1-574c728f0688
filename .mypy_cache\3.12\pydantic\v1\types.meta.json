{"data_mtime": 1759503828, "dep_lines": [31, 32, 33, 34, 126, 127, 128, 31, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 28, 29, 124, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 25, 25, 25, 20, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 25, 5, 20, 20, 30, 30, 30, 30, 30], "dependencies": ["pydantic.v1.errors", "pydantic.v1.datetime_parse", "pydantic.v1.utils", "pydantic.v1.validators", "pydantic.v1.dataclasses", "pydantic.v1.main", "pydantic.v1.typing", "pydantic.v1", "abc", "math", "re", "warnings", "datetime", "decimal", "enum", "pathlib", "types", "typing", "uuid", "weakref", "typing_extensions", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "_decimal", "_typeshed", "_weakrefset", "os"], "hash": "165b71e46a0ffea69498092d946cfb9c57896b5df2372dc5635f9170ccc456df", "id": "pydantic.v1.types", "ignore_all": true, "interface_hash": "62ab5c93aa0b2e5a6dfebc803792a7d9aad1be1c44b398e7f25571a7bde91cc0", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\v1\\types.py", "plugin_data": null, "size": 35455, "suppressed": [], "version_id": "1.8.0"}