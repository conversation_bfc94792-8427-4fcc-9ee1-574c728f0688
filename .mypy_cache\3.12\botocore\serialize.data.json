{".class": "MypyFile", "_fullname": "botocore.serialize", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseRestSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.serialize.Serializer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.serialize.BaseRestSerializer", "name": "BaseRestSerializer", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.serialize.BaseRestSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.serialize", "mro": ["botocore.serialize.BaseRestSerializer", "botocore.serialize.Serializer", "builtins.object"], "names": {".class": "SymbolTable", "HEADER_TIMESTAMP_FORMAT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.serialize.BaseRestSerializer.HEADER_TIMESTAMP_FORMAT", "name": "HEADER_TIMESTAMP_FORMAT", "type": "builtins.str"}}, "KNOWN_LOCATIONS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.serialize.BaseRestSerializer.KNOWN_LOCATIONS", "name": "KNOWN_LOCATIONS", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}, "QUERY_STRING_TIMESTAMP_FORMAT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.serialize.BaseRestSerializer.QUERY_STRING_TIMESTAMP_FORMAT", "name": "QUERY_STRING_TIMESTAMP_FORMAT", "type": "builtins.str"}}, "serialize_to_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parameters", "operation_model"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.serialize.BaseRestSerializer.serialize_to_request", "name": "serialize_to_request", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "parameters", "operation_model"], "arg_types": ["botocore.serialize.BaseRestSerializer", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, "botocore.model.OperationModel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "serialize_to_request of BaseRestSerializer", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.serialize.BaseRestSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.serialize.BaseRestSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BaseRpcV2Serializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.serialize.Serializer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.serialize.BaseRpcV2Serializer", "name": "BaseRpcV2Serializer", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.serialize.BaseRpcV2Serializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.serialize", "mro": ["botocore.serialize.BaseRpcV2Serializer", "botocore.serialize.Serializer", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.serialize.BaseRpcV2Serializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.serialize.BaseRpcV2Serializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CBORSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.serialize.Serializer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.serialize.CBORSerializer", "name": "CBORSerializer", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.serialize.CBORSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.serialize", "mro": ["botocore.serialize.CBORSerializer", "botocore.serialize.Serializer", "builtins.object"], "names": {".class": "SymbolTable", "BLOB_MAJOR_TYPE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.serialize.CBORSerializer.BLOB_MAJOR_TYPE", "name": "BLOB_MAJOR_TYPE", "type": "builtins.int"}}, "FLOAT_AND_SIMPLE_MAJOR_TYPE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.serialize.CBORSerializer.FLOAT_AND_SIMPLE_MAJOR_TYPE", "name": "FLOAT_AND_SIMPLE_MAJOR_TYPE", "type": "builtins.int"}}, "LIST_MAJOR_TYPE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.serialize.CBORSerializer.LIST_MAJOR_TYPE", "name": "LIST_MAJOR_TYPE", "type": "builtins.int"}}, "MAP_MAJOR_TYPE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.serialize.CBORSerializer.MAP_MAJOR_TYPE", "name": "MAP_MAJOR_TYPE", "type": "builtins.int"}}, "NEGATIVE_INT_MAJOR_TYPE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.serialize.CBORSerializer.NEGATIVE_INT_MAJOR_TYPE", "name": "NEGATIVE_INT_MAJOR_TYPE", "type": "builtins.int"}}, "STRING_MAJOR_TYPE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.serialize.CBORSerializer.STRING_MAJOR_TYPE", "name": "STRING_MAJOR_TYPE", "type": "builtins.int"}}, "TAG_MAJOR_TYPE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.serialize.CBORSerializer.TAG_MAJOR_TYPE", "name": "TAG_MAJOR_TYPE", "type": "builtins.int"}}, "UNSIGNED_INT_MAJOR_TYPE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.serialize.CBORSerializer.UNSIGNED_INT_MAJOR_TYPE", "name": "UNSIGNED_INT_MAJOR_TYPE", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.serialize.CBORSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.serialize.CBORSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DEFAULT_TIMESTAMP_FORMAT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.serialize.DEFAULT_TIMESTAMP_FORMAT", "name": "DEFAULT_TIMESTAMP_FORMAT", "type": "builtins.str"}}, "EC2Serializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.serialize.QuerySerializer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.serialize.EC2Serializer", "name": "EC2Serializer", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.serialize.EC2Serializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.serialize", "mro": ["botocore.serialize.EC2Serializer", "botocore.serialize.QuerySerializer", "botocore.serialize.Serializer", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.serialize.EC2Serializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.serialize.EC2Serializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HOST_PREFIX_RE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.serialize.HOST_PREFIX_RE", "name": "HOST_PREFIX_RE", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "<PERSON><PERSON>"}}}, "ISO8601": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.serialize.ISO8601", "name": "ISO8601", "type": "builtins.str"}}, "ISO8601_MICRO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.serialize.ISO8601_MICRO", "name": "ISO8601_MICRO", "type": "builtins.str"}}, "JSONSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.serialize.Serializer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.serialize.JSONSerializer", "name": "JSONSerializer", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.serialize.JSONSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.serialize", "mro": ["botocore.serialize.JSONSerializer", "botocore.serialize.Serializer", "builtins.object"], "names": {".class": "SymbolTable", "TIMESTAMP_FORMAT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.serialize.JSONSerializer.TIMESTAMP_FORMAT", "name": "TIMESTAMP_FORMAT", "type": "builtins.str"}}, "serialize_to_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parameters", "operation_model"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.serialize.JSONSerializer.serialize_to_request", "name": "serialize_to_request", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "parameters", "operation_model"], "arg_types": ["botocore.serialize.JSONSerializer", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, "botocore.model.OperationModel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "serialize_to_request of JSONSerializer", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.serialize.JSONSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.serialize.JSONSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "OperationModel": {".class": "SymbolTableNode", "cross_ref": "botocore.model.OperationModel", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Pattern": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "QuerySerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.serialize.Serializer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.serialize.QuerySerializer", "name": "QuerySerializer", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.serialize.QuerySerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.serialize", "mro": ["botocore.serialize.QuerySerializer", "botocore.serialize.Serializer", "builtins.object"], "names": {".class": "SymbolTable", "TIMESTAMP_FORMAT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.serialize.QuerySerializer.TIMESTAMP_FORMAT", "name": "TIMESTAMP_FORMAT", "type": "builtins.str"}}, "serialize_to_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parameters", "operation_model"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.serialize.QuerySerializer.serialize_to_request", "name": "serialize_to_request", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "parameters", "operation_model"], "arg_types": ["botocore.serialize.QuerySerializer", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, "botocore.model.OperationModel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "serialize_to_request of QuerySerializer", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.serialize.QuerySerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.serialize.QuerySerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RestJSONSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.serialize.BaseRestSerializer", "botocore.serialize.JSONSerializer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.serialize.RestJSONSerializer", "name": "RestJSONSerializer", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.serialize.RestJSONSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.serialize", "mro": ["botocore.serialize.RestJSONSerializer", "botocore.serialize.BaseRestSerializer", "botocore.serialize.JSONSerializer", "botocore.serialize.Serializer", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.serialize.RestJSONSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.serialize.RestJSONSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RestXMLSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.serialize.BaseRestSerializer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.serialize.RestXMLSerializer", "name": "RestXMLSerializer", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.serialize.RestXMLSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.serialize", "mro": ["botocore.serialize.RestXMLSerializer", "botocore.serialize.BaseRestSerializer", "botocore.serialize.Serializer", "builtins.object"], "names": {".class": "SymbolTable", "TIMESTAMP_FORMAT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.serialize.RestXMLSerializer.TIMESTAMP_FORMAT", "name": "TIMESTAMP_FORMAT", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.serialize.RestXMLSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.serialize.RestXMLSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RpcV2CBORSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.serialize.BaseRpcV2Serializer", "botocore.serialize.CBORSerializer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.serialize.RpcV2CBORSerializer", "name": "RpcV2CBORSerializer", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.serialize.RpcV2CBORSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.serialize", "mro": ["botocore.serialize.RpcV2CBORSerializer", "botocore.serialize.BaseRpcV2Serializer", "botocore.serialize.CBORSerializer", "botocore.serialize.Serializer", "builtins.object"], "names": {".class": "SymbolTable", "TIMESTAMP_FORMAT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.serialize.RpcV2CBORSerializer.TIMESTAMP_FORMAT", "name": "TIMESTAMP_FORMAT", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.serialize.RpcV2CBORSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.serialize.RpcV2CBORSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SERIALIZERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.serialize.SERIALIZERS", "name": "SERIALIZERS", "type": {".class": "Instance", "args": ["builtins.str", "botocore.serialize.Serializer"], "type_ref": "builtins.dict"}}}, "Serializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.serialize.Serializer", "name": "Serializer", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.serialize.Serializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.serialize", "mro": ["botocore.serialize.Serializer", "builtins.object"], "names": {".class": "SymbolTable", "DEFAULT_ENCODING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.serialize.Serializer.DEFAULT_ENCODING", "name": "DEFAULT_ENCODING", "type": "builtins.str"}}, "DEFAULT_METHOD": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.serialize.Serializer.DEFAULT_METHOD", "name": "DEFAULT_METHOD", "type": "builtins.str"}}, "MAP_TYPE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.serialize.Serializer.MAP_TYPE", "name": "MAP_TYPE", "type": {".class": "TypeType", "item": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}}}}, "serialize_to_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parameters", "operation_model"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.serialize.Serializer.serialize_to_request", "name": "serialize_to_request", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "parameters", "operation_model"], "arg_types": ["botocore.serialize.Serializer", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, "botocore.model.OperationModel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "serialize_to_request of Serializer", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.serialize.Serializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.serialize.Serializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.serialize.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.serialize.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.serialize.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.serialize.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.serialize.__package__", "name": "__package__", "type": "builtins.str"}}, "create_serializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["protocol_name", "include_validation"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.serialize.create_serializer", "name": "create_serializer", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["protocol_name", "include_validation"], "arg_types": ["builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_serializer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "formatdate": {".class": "SymbolTableNode", "cross_ref": "email.utils.formatdate", "kind": "Gdef"}, "is_json_value_header": {".class": "SymbolTableNode", "cross_ref": "botocore.utils.is_json_value_header", "kind": "Gdef"}, "parse_to_aware_datetime": {".class": "SymbolTableNode", "cross_ref": "botocore.utils.parse_to_aware_datetime", "kind": "Gdef"}, "percent_encode": {".class": "SymbolTableNode", "cross_ref": "botocore.utils.percent_encode", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\botocore-stubs\\serialize.pyi"}