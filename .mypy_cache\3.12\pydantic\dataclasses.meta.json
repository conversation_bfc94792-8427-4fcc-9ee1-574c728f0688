{"data_mtime": 1759504514, "dep_lines": [13, 13, 13, 13, 14, 13, 15, 16, 17, 18, 3, 5, 6, 7, 8, 9, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 20, 5, 5, 5, 5, 5, 10, 10, 10, 5, 5, 5, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._config", "pydantic._internal._decorators", "pydantic._internal._namespace_utils", "pydantic._internal._typing_extra", "pydantic._internal._dataclasses", "pydantic._internal", "pydantic._migration", "pydantic.config", "pydantic.errors", "pydantic.fields", "__future__", "dataclasses", "sys", "types", "typing", "warnings", "typing_extensions", "builtins", "pyexpat.model", "pyexpat.errors", "_typeshed", "abc", "annotated_types", "pydantic._internal._generate_schema", "pydantic._internal._repr", "pydantic.aliases", "pydantic.types", "re"], "hash": "6faaabbc91dc5913cf8b67cdc0af0d377ec2ae1ac979b55a90e38ba6e16e9216", "id": "pydantic.dataclasses", "ignore_all": true, "interface_hash": "79137694b2ddf82e0c5fc05d0f7213a2e153d8fbd4ebd7e5e85d64a777adde7f", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\dataclasses.py", "plugin_data": null, "size": 16215, "suppressed": [], "version_id": "1.8.0"}