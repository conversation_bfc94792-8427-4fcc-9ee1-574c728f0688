{".class": "MypyFile", "_fullname": "botocore.utils", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ArgumentGenerator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.utils.ArgumentGenerator", "name": "ArgumentGenerator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.utils.ArgumentGenerator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.utils", "mro": ["botocore.utils.ArgumentGenerator", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "use_member_names"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.ArgumentGenerator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "use_member_names"], "arg_types": ["botocore.utils.ArgumentGenerator", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ArgumentGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "generate_skeleton": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "shape"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.ArgumentGenerator.generate_skeleton", "name": "generate_skeleton", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "shape"], "arg_types": ["botocore.utils.ArgumentGenerator", "botocore.model.Shape"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_skeleton of ArgumentGenerator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.utils.ArgumentGenerator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.utils.ArgumentGenerator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ArnParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.utils.ArnParser", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.utils.ArnParser", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.utils", "mro": ["botocore.utils.ArnParser", "builtins.object"], "names": {".class": "SymbolTable", "is_arn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["value"], "dataclass_transform_spec": null, "flags": ["is_static", "is_decorated"], "fullname": "botocore.utils.ArnParser.is_arn", "name": "is_arn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_arn of Arn<PERSON><PERSON><PERSON>", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "botocore.utils.ArnParser.is_arn", "name": "is_arn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_arn of Arn<PERSON><PERSON><PERSON>", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "parse_arn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "arn"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.ArnParser.parse_arn", "name": "parse_arn", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "arn"], "arg_types": ["botocore.utils.ArnParser", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_arn of Arn<PERSON><PERSON><PERSON>", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.utils.ArnParser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.utils.ArnParser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BadIMDSRequestError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.utils.BadIMDSRequestError", "name": "BadIMDSRequestError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.utils.BadIMDSRequestError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.utils", "mro": ["botocore.utils.BadIMDSRequestError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.BadIMDSRequestError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["botocore.utils.BadIMDSRequestError", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BadIMDSRequestError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "request": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "botocore.utils.BadIMDSRequestError.request", "name": "request", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.utils.BadIMDSRequestError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.utils.BadIMDSRequestError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BaseClient": {".class": "SymbolTableNode", "cross_ref": "botocore.client.BaseClient", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseEndpointResolver": {".class": "SymbolTableNode", "cross_ref": "botocore.regions.BaseEndpointResolver", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseEventHooks": {".class": "SymbolTableNode", "cross_ref": "botocore.hooks.BaseEventHooks", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CHECKSUM_HEADER_PATTERN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.utils.CHECKSUM_HEADER_PATTERN", "name": "CHECKSUM_HEADER_PATTERN", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "<PERSON><PERSON>"}}}, "CLIENT_NAME_TO_HYPHENIZED_SERVICE_ID_OVERRIDES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "botocore.utils.CLIENT_NAME_TO_HYPHENIZED_SERVICE_ID_OVERRIDES", "name": "CLIENT_NAME_TO_HYPHENIZED_SERVICE_ID_OVERRIDES", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "builtins.dict"}}}, "CachedProperty": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.utils.CachedProperty", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.utils._V", "id": 1, "name": "_V", "namespace": "botocore.utils.CachedProperty", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "botocore.utils.CachedProperty", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.utils", "mro": ["botocore.utils.CachedProperty", "builtins.object"], "names": {".class": "SymbolTable", "__get__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "cls"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.CachedProperty.__get__", "name": "__get__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "cls"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.utils._V", "id": 1, "name": "_V", "namespace": "botocore.utils.CachedProperty", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "botocore.utils.CachedProperty"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of CachedProperty", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.utils._V", "id": 1, "name": "_V", "namespace": "botocore.utils.CachedProperty", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fget"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.CachedProperty.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fget"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.utils._V", "id": 1, "name": "_V", "namespace": "botocore.utils.CachedProperty", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "botocore.utils.CachedProperty"}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.utils._V", "id": 1, "name": "_V", "namespace": "botocore.utils.CachedProperty", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CachedProperty", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.utils.CachedProperty.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.utils._V", "id": 1, "name": "_V", "namespace": "botocore.utils.CachedProperty", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "botocore.utils.CachedProperty"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_V"], "typeddict_type": null}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ClientError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.ClientError", "kind": "Gdef"}, "ConfigNotFound": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.ConfigNotFound", "kind": "Gdef"}, "ConnectTimeoutError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.ConnectTimeoutError", "kind": "Gdef"}, "ConnectionClosedError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.ConnectionClosedError", "kind": "Gdef"}, "ContainerMetadataFetcher": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.utils.ContainerMetadataFetcher", "name": "ContainerMetadataFetcher", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.utils.ContainerMetadataFetcher", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.utils", "mro": ["botocore.utils.ContainerMetadataFetcher", "builtins.object"], "names": {".class": "SymbolTable", "IP_ADDRESS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "botocore.utils.ContainerMetadataFetcher.IP_ADDRESS", "name": "IP_ADDRESS", "type": "builtins.str"}}, "RETRY_ATTEMPTS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "botocore.utils.ContainerMetadataFetcher.RETRY_ATTEMPTS", "name": "RETRY_ATTEMPTS", "type": "builtins.int"}}, "SLEEP_TIME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "botocore.utils.ContainerMetadataFetcher.SLEEP_TIME", "name": "SLEEP_TIME", "type": "builtins.int"}}, "TIMEOUT_SECONDS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "botocore.utils.ContainerMetadataFetcher.TIMEOUT_SECONDS", "name": "TIMEOUT_SECONDS", "type": "builtins.int"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "session", "sleep"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.ContainerMetadataFetcher.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "session", "sleep"], "arg_types": ["botocore.utils.ContainerMetadataFetcher", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ContainerMetadataFetcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "full_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "relative_uri"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.ContainerMetadataFetcher.full_url", "name": "full_url", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "relative_uri"], "arg_types": ["botocore.utils.ContainerMetadataFetcher", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "full_url of ContainerMetadataFetcher", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "retrieve_full_uri": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "full_url", "headers"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.ContainerMetadataFetcher.retrieve_full_uri", "name": "retrieve_full_uri", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "full_url", "headers"], "arg_types": ["botocore.utils.ContainerMetadataFetcher", "builtins.str", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "retrieve_full_uri of ContainerMetadataFetcher", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "retrieve_uri": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "relative_uri"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.ContainerMetadataFetcher.retrieve_uri", "name": "retrieve_uri", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "relative_uri"], "arg_types": ["botocore.utils.ContainerMetadataFetcher", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "retrieve_uri of ContainerMetadataFetcher", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.utils.ContainerMetadataFetcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.utils.ContainerMetadataFetcher", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Credentials": {".class": "SymbolTableNode", "cross_ref": "botocore.credentials.Credentials", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DEFAULT_METADATA_SERVICE_TIMEOUT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.utils.DEFAULT_METADATA_SERVICE_TIMEOUT", "name": "DEFAULT_METADATA_SERVICE_TIMEOUT", "type": "builtins.int"}}, "EVENT_ALIASES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.utils.EVENT_ALIASES", "name": "EVENT_ALIASES", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "builtins.dict"}}}, "EndpointConnectionError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.EndpointConnectionError", "kind": "Gdef"}, "EventbridgeSignerSetter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.utils.EventbridgeSignerSetter", "name": "EventbridgeSignerSetter", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.utils.EventbridgeSignerSetter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.utils", "mro": ["botocore.utils.EventbridgeSignerSetter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "endpoint_resolver", "region", "endpoint_url"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.EventbridgeSignerSetter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "endpoint_resolver", "region", "endpoint_url"], "arg_types": ["botocore.utils.EventbridgeSignerSetter", "botocore.regions.BaseEndpointResolver", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of EventbridgeSignerSetter", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "check_for_global_endpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "params", "context", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.EventbridgeSignerSetter.check_for_global_endpoint", "name": "check_for_global_endpoint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "params", "context", "kwargs"], "arg_types": ["botocore.utils.EventbridgeSignerSetter", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_for_global_endpoint of EventbridgeSignerSetter", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "register": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event_emitter"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.EventbridgeSignerSetter.register", "name": "register", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event_emitter"], "arg_types": ["botocore.utils.EventbridgeSignerSetter", "botocore.hooks.BaseEventHooks"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register of EventbridgeSignerSetter", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "set_endpoint_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "params", "context", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.EventbridgeSignerSetter.set_endpoint_url", "name": "set_endpoint_url", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "params", "context", "kwargs"], "arg_types": ["botocore.utils.EventbridgeSignerSetter", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_endpoint_url of EventbridgeSignerSetter", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.utils.EventbridgeSignerSetter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.utils.EventbridgeSignerSetter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FileWebIdentityTokenLoader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.utils.FileWebIdentityTokenLoader", "name": "FileWebIdentityTokenLoader", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.utils.FileWebIdentityTokenLoader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.utils", "mro": ["botocore.utils.FileWebIdentityTokenLoader", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.FileWebIdentityTokenLoader.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.utils.FileWebIdentityTokenLoader"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of FileWebIdentityTokenLoader", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "web_identity_token_path", "_open"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.FileWebIdentityTokenLoader.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "web_identity_token_path", "_open"], "arg_types": ["botocore.utils.FileWebIdentityTokenLoader", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FileWebIdentityTokenLoader", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.utils.FileWebIdentityTokenLoader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.utils.FileWebIdentityTokenLoader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef", "module_hidden": true, "module_public": false}, "HAS_CRT": {".class": "SymbolTableNode", "cross_ref": "botocore.compat.HAS_CRT", "kind": "Gdef"}, "HEX_PAT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.utils.HEX_PAT", "name": "HEX_PAT", "type": "builtins.str"}}, "HTTPClientError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.HTTPClientError", "kind": "Gdef"}, "IMDSFetcher": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.utils.IMDSFetcher", "name": "IMDSFetcher", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.utils.IMDSFetcher", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.utils", "mro": ["botocore.utils.IMDSFetcher", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "timeout", "num_attempts", "base_url", "env", "user_agent", "config"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.IMDSFetcher.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "timeout", "num_attempts", "base_url", "env", "user_agent", "config"], "arg_types": ["botocore.utils.IMDSFetcher", "builtins.int", "builtins.int", "builtins.str", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of IMDSFetcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_base_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.IMDSFetcher.get_base_url", "name": "get_base_url", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.utils.IMDSFetcher"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_base_url of IMDSFetcher", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.utils.IMDSFetcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.utils.IMDSFetcher", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IMDSRegionProvider": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.utils.IMDSRegionProvider", "name": "IMDSRegionProvider", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.utils.IMDSRegionProvider", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.utils", "mro": ["botocore.utils.IMDSRegionProvider", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "session", "environ", "fetcher"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.IMDSRegionProvider.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "session", "environ", "fetcher"], "arg_types": ["botocore.utils.IMDSRegionProvider", "botocore.session.Session", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["botocore.utils.IMDSFetcher", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of IMDSRegionProvider", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "provide": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.IMDSRegionProvider.provide", "name": "provide", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.utils.IMDSRegionProvider"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "provide of IMDSRegionProvider", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.utils.IMDSRegionProvider.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.utils.IMDSRegionProvider", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IPV4_PAT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.utils.IPV4_PAT", "name": "IPV4_PAT", "type": "builtins.str"}}, "IPV6_ADDRZ_PAT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.utils.IPV6_ADDRZ_PAT", "name": "IPV6_ADDRZ_PAT", "type": "builtins.str"}}, "IPV6_ADDRZ_RE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.utils.IPV6_ADDRZ_RE", "name": "IPV6_ADDRZ_RE", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "<PERSON><PERSON>"}}}, "IPV6_PAT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.utils.IPV6_PAT", "name": "IPV6_PAT", "type": "builtins.str"}}, "IdentityCache": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.utils.IdentityCache", "name": "IdentityCache", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.utils.IdentityCache", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.utils", "mro": ["botocore.utils.IdentityCache", "builtins.object"], "names": {".class": "SymbolTable", "METHOD": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.utils.IdentityCache.METHOD", "name": "METHOD", "type": "builtins.str"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "client", "credential_cls"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.IdentityCache.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "client", "credential_cls"], "arg_types": ["botocore.utils.IdentityCache", "botocore.client.BaseClient", {".class": "TypeType", "item": "botocore.credentials.Credentials"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of IdentityCache", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "build_refresh_callback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [4], "arg_names": ["kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.IdentityCache.build_refresh_callback", "name": "build_refresh_callback", "type": {".class": "CallableType", "arg_kinds": [4], "arg_names": ["kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "kwargs"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_refresh_callback of IdentityCache", "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_credentials": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.IdentityCache.get_credentials", "name": "get_credentials", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["botocore.utils.IdentityCache", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_credentials of IdentityCache", "ret_type": "botocore.credentials.Credentials", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.utils.IdentityCache.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.utils.IdentityCache", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InstanceMetadataFetcher": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.utils.IMDSFetcher"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.utils.InstanceMetadataFetcher", "name": "InstanceMetadataFetcher", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.utils.InstanceMetadataFetcher", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.utils", "mro": ["botocore.utils.InstanceMetadataFetcher", "botocore.utils.IMDSFetcher", "builtins.object"], "names": {".class": "SymbolTable", "retrieve_iam_role_credentials": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.InstanceMetadataFetcher.retrieve_iam_role_credentials", "name": "retrieve_iam_role_credentials", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.utils.InstanceMetadataFetcher"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "retrieve_iam_role_credentials of InstanceMetadataFetcher", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.utils.InstanceMetadataFetcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.utils.InstanceMetadataFetcher", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InstanceMetadataRegionFetcher": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.utils.IMDSFetcher"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.utils.InstanceMetadataRegionFetcher", "name": "InstanceMetadataRegionFetcher", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.utils.InstanceMetadataRegionFetcher", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.utils", "mro": ["botocore.utils.InstanceMetadataRegionFetcher", "botocore.utils.IMDSFetcher", "builtins.object"], "names": {".class": "SymbolTable", "retrieve_region": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.InstanceMetadataRegionFetcher.retrieve_region", "name": "retrieve_region", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.utils.InstanceMetadataRegionFetcher"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "retrieve_region of InstanceMetadataRegionFetcher", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.utils.InstanceMetadataRegionFetcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.utils.InstanceMetadataRegionFetcher", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidArnException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.ValueError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.utils.InvalidArnException", "name": "InvalidArnException", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.utils.InvalidArnException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.utils", "mro": ["botocore.utils.InvalidArnException", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.utils.InvalidArnException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.utils.InvalidArnException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidDNSNameError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.InvalidDNSNameError", "kind": "Gdef"}, "InvalidExpressionError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.InvalidExpressionError", "kind": "Gdef"}, "InvalidHostLabelError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.InvalidHostLabelError", "kind": "Gdef"}, "InvalidIMDSEndpointError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.InvalidIMDSEndpointError", "kind": "Gdef"}, "InvalidRegionError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.InvalidRegionError", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "JSONFileCache": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.utils.JSONFileCache", "name": "JSONFileCache", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.utils.JSONFileCache", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.utils", "mro": ["botocore.utils.JSONFileCache", "builtins.object"], "names": {".class": "SymbolTable", "CACHE_DIR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.utils.JSONFileCache.CACHE_DIR", "name": "CACHE_DIR", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "__contains__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.JSONFileCache.__contains__", "name": "__contains__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["botocore.utils.JSONFileCache", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__contains__ of JSONFileCache", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__delitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.JSONFileCache.__delitem__", "name": "__delitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["botocore.utils.JSONFileCache", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__delitem__ of JSONFileCache", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.JSONFileCache.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["botocore.utils.JSONFileCache", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of JSONFileCache", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "working_dir", "dumps_func"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.JSONFileCache.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "working_dir", "dumps_func"], "arg_types": ["botocore.utils.JSONFileCache", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of JSONFileCache", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__setitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.JSONFileCache.__setitem__", "name": "__setitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["botocore.utils.JSONFileCache", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setitem__ of JSONFileCache", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.utils.JSONFileCache.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.utils.JSONFileCache", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LABEL_RE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.utils.LABEL_RE", "name": "LABEL_RE", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "<PERSON><PERSON>"}}}, "LS32_PAT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.utils.LS32_PAT", "name": "LS32_PAT", "type": "builtins.str"}}, "Logger": {".class": "SymbolTableNode", "cross_ref": "logging.Logger", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MD5_AVAILABLE": {".class": "SymbolTableNode", "cross_ref": "botocore.compat.MD5_AVAILABLE", "kind": "Gdef"}, "METADATA_BASE_URL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.utils.METADATA_BASE_URL", "name": "METADATA_BASE_URL", "type": "builtins.str"}}, "METADATA_BASE_URL_IPv6": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.utils.METADATA_BASE_URL_IPv6", "name": "METADATA_BASE_URL_IPv6", "type": "builtins.str"}}, "METADATA_ENDPOINT_MODES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.utils.METADATA_ENDPOINT_MODES", "name": "METADATA_ENDPOINT_MODES", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}}}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MetadataRetrievalError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.MetadataRetrievalError", "kind": "Gdef"}, "OperationModel": {".class": "SymbolTableNode", "cross_ref": "botocore.model.OperationModel", "kind": "Gdef", "module_hidden": true, "module_public": false}, "OrderedDict": {".class": "SymbolTableNode", "cross_ref": "collections.OrderedDict", "kind": "Gdef"}, "PRIORITY_ORDERED_SUPPORTED_PROTOCOLS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.utils.PRIORITY_ORDERED_SUPPORTED_PROTOCOLS", "name": "PRIORITY_ORDERED_SUPPORTED_PROTOCOLS", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}}}, "Pattern": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RETRYABLE_HTTP_ERRORS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.utils.RETRYABLE_HTTP_ERRORS", "name": "RETRYABLE_HTTP_ERRORS", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}}}, "ReadTimeoutError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.ReadTimeoutError", "kind": "Gdef"}, "Response": {".class": "SymbolTableNode", "cross_ref": "requests.models.Response", "kind": "Gdef", "module_hidden": true, "module_public": false}, "S3ArnParamHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.utils.S3ArnParamHandler", "name": "S3ArnParamHandler", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.utils.S3ArnParamHandler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.utils", "mro": ["botocore.utils.S3ArnParamHandler", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "arn_parser"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.S3ArnParamHandler.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "arn_parser"], "arg_types": ["botocore.utils.S3ArnParamHandler", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of S3ArnParamHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "handle_arn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "params", "model", "context", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.S3ArnParamHandler.handle_arn", "name": "handle_arn", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "params", "model", "context", "kwargs"], "arg_types": ["botocore.utils.S3ArnParamHandler", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_arn of S3ArnParamHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "register": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event_emitter"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.S3ArnParamHandler.register", "name": "register", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event_emitter"], "arg_types": ["botocore.utils.S3ArnParamHandler", "botocore.hooks.BaseEventHooks"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register of S3ArnParamHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.utils.S3ArnParamHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.utils.S3ArnParamHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "S3ControlArnParamHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.utils.S3ControlArnParamHandler", "name": "S3ControlArnParamHandler", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.utils.S3ControlArnParamHandler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.utils", "mro": ["botocore.utils.S3ControlArnParamHandler", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "arn_parser"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.S3ControlArnParamHandler.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "arn_parser"], "arg_types": ["botocore.utils.S3ControlArnParamHandler", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of S3ControlArnParamHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "handle_arn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "params", "model", "context", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.S3ControlArnParamHandler.handle_arn", "name": "handle_arn", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "params", "model", "context", "kwargs"], "arg_types": ["botocore.utils.S3ControlArnParamHandler", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_arn of S3ControlArnParamHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "register": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event_emitter"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.S3ControlArnParamHandler.register", "name": "register", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event_emitter"], "arg_types": ["botocore.utils.S3ControlArnParamHandler", "botocore.hooks.BaseEventHooks"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register of S3ControlArnParamHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.utils.S3ControlArnParamHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.utils.S3ControlArnParamHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "S3ControlArnParamHandlerv2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.utils.S3ControlArnParamHandler"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.utils.S3ControlArnParamHandlerv2", "name": "S3ControlArnParamHandlerv2", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.utils.S3ControlArnParamHandlerv2", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.utils", "mro": ["botocore.utils.S3ControlArnParamHandlerv2", "botocore.utils.S3ControlArnParamHandler", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.utils.S3ControlArnParamHandlerv2.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.utils.S3ControlArnParamHandlerv2", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "S3ControlEndpointSetter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.utils.S3ControlEndpointSetter", "name": "S3ControlEndpointSetter", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.utils.S3ControlEndpointSetter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.utils", "mro": ["botocore.utils.S3ControlEndpointSetter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "endpoint_resolver", "region", "s3_config", "endpoint_url", "partition", "use_fips_endpoint"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.S3ControlEndpointSetter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "endpoint_resolver", "region", "s3_config", "endpoint_url", "partition", "use_fips_endpoint"], "arg_types": ["botocore.utils.S3ControlEndpointSetter", "botocore.regions.BaseEndpointResolver", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of S3ControlEndpointSetter", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "register": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event_emitter"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.S3ControlEndpointSetter.register", "name": "register", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event_emitter"], "arg_types": ["botocore.utils.S3ControlEndpointSetter", "botocore.hooks.BaseEventHooks"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register of S3ControlEndpointSetter", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "set_endpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "request", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.S3ControlEndpointSetter.set_endpoint", "name": "set_endpoint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "request", "kwargs"], "arg_types": ["botocore.utils.S3ControlEndpointSetter", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_endpoint of S3ControlEndpointSetter", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.utils.S3ControlEndpointSetter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.utils.S3ControlEndpointSetter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "S3EndpointSetter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.utils.S3EndpointSetter", "name": "S3EndpointSetter", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.utils.S3EndpointSetter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.utils", "mro": ["botocore.utils.S3EndpointSetter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "endpoint_resolver", "region", "s3_config", "endpoint_url", "partition", "use_fips_endpoint"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.S3EndpointSetter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "endpoint_resolver", "region", "s3_config", "endpoint_url", "partition", "use_fips_endpoint"], "arg_types": ["botocore.utils.S3EndpointSetter", "botocore.regions.BaseEndpointResolver", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of S3EndpointSetter", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "register": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event_emitter"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.S3EndpointSetter.register", "name": "register", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event_emitter"], "arg_types": ["botocore.utils.S3EndpointSetter", "botocore.hooks.BaseEventHooks"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register of S3EndpointSetter", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "set_endpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "request", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.S3EndpointSetter.set_endpoint", "name": "set_endpoint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "request", "kwargs"], "arg_types": ["botocore.utils.S3EndpointSetter", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_endpoint of S3EndpointSetter", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "set_signer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "context", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.S3EndpointSetter.set_signer", "name": "set_signer", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "context", "kwargs"], "arg_types": ["botocore.utils.S3EndpointSetter", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_signer of S3EndpointSetter", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "update_endpoint_to_s3_object_lambda": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "params", "context", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.S3EndpointSetter.update_endpoint_to_s3_object_lambda", "name": "update_endpoint_to_s3_object_lambda", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "params", "context", "kwargs"], "arg_types": ["botocore.utils.S3EndpointSetter", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_endpoint_to_s3_object_lambda of S3EndpointSetter", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.utils.S3EndpointSetter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.utils.S3EndpointSetter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "S3ExpressIdentityCache": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.utils.IdentityCache"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.utils.S3ExpressIdentityCache", "name": "S3ExpressIdentityCache", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.utils.S3ExpressIdentityCache", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.utils", "mro": ["botocore.utils.S3ExpressIdentityCache", "botocore.utils.IdentityCache", "builtins.object"], "names": {".class": "SymbolTable", "build_refresh_callback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "bucket"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.S3ExpressIdentityCache.build_refresh_callback", "name": "build_refresh_callback", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "bucket"], "arg_types": ["botocore.utils.S3ExpressIdentityCache", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_refresh_callback of S3ExpressIdentityCache", "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_credentials": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "bucket"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.S3ExpressIdentityCache.get_credentials", "name": "get_credentials", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "bucket"], "arg_types": ["botocore.utils.S3ExpressIdentityCache", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_credentials of S3ExpressIdentityCache", "ret_type": "botocore.credentials.Credentials", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.utils.S3ExpressIdentityCache.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.utils.S3ExpressIdentityCache", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "S3ExpressIdentityResolver": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.utils.S3ExpressIdentityResolver", "name": "S3ExpressIdentityResolver", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.utils.S3ExpressIdentityResolver", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.utils", "mro": ["botocore.utils.S3ExpressIdentityResolver", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "client", "credential_cls", "cache"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.S3ExpressIdentityResolver.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "client", "credential_cls", "cache"], "arg_types": ["botocore.utils.S3ExpressIdentityResolver", "botocore.client.BaseClient", {".class": "TypeType", "item": "botocore.credentials.Credentials"}, {".class": "UnionType", "items": ["botocore.utils.S3ExpressIdentityCache", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of S3ExpressIdentityResolver", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "apply_signing_cache_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "params", "context", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.S3ExpressIdentityResolver.apply_signing_cache_key", "name": "apply_signing_cache_key", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "params", "context", "kwargs"], "arg_types": ["botocore.utils.S3ExpressIdentityResolver", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply_signing_cache_key of S3ExpressIdentityResolver", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "register": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "event_emitter"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.S3ExpressIdentityResolver.register", "name": "register", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "event_emitter"], "arg_types": ["botocore.utils.S3ExpressIdentityResolver", {".class": "UnionType", "items": ["botocore.hooks.BaseEventHooks", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register of S3ExpressIdentityResolver", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "resolve_s3express_identity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "request", "signing_name", "region_name", "signature_version", "request_signer", "operation_name", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.S3ExpressIdentityResolver.resolve_s3express_identity", "name": "resolve_s3express_identity", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "request", "signing_name", "region_name", "signature_version", "request_signer", "operation_name", "kwargs"], "arg_types": ["botocore.utils.S3ExpressIdentityResolver", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolve_s3express_identity of S3ExpressIdentityResolver", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.utils.S3ExpressIdentityResolver.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.utils.S3ExpressIdentityResolver", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "S3RegionRedirector": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.utils.S3RegionRedirector", "name": "S3RegionRedirector", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.utils.S3RegionRedirector", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.utils", "mro": ["botocore.utils.S3RegionRedirector", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "endpoint_bridge", "client", "cache"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.S3RegionRedirector.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "endpoint_bridge", "client", "cache"], "arg_types": ["botocore.utils.S3RegionRedirector", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "botocore.client.BaseClient", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of S3RegionRedirector", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_bucket_region": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "bucket", "response"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.S3RegionRedirector.get_bucket_region", "name": "get_bucket_region", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "bucket", "response"], "arg_types": ["botocore.utils.S3RegionRedirector", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "requests.models.Response"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_bucket_region of S3RegionRedirector", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "redirect_from_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "params", "context", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.S3RegionRedirector.redirect_from_cache", "name": "redirect_from_cache", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "params", "context", "kwargs"], "arg_types": ["botocore.utils.S3RegionRedirector", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "redirect_from_cache of S3RegionRedirector", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "redirect_from_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "request_dict", "response", "operation", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.S3RegionRedirector.redirect_from_error", "name": "redirect_from_error", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "request_dict", "response", "operation", "kwargs"], "arg_types": ["botocore.utils.S3RegionRedirector", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, "requests.models.Response", "botocore.model.OperationModel", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "redirect_from_error of S3RegionRedirector", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "register": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "event_emitter"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.S3RegionRedirector.register", "name": "register", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "event_emitter"], "arg_types": ["botocore.utils.S3RegionRedirector", {".class": "UnionType", "items": ["botocore.hooks.BaseEventHooks", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register of S3RegionRedirector", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "set_request_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "params", "context", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.S3RegionRedirector.set_request_url", "name": "set_request_url", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "params", "context", "kwargs"], "arg_types": ["botocore.utils.S3RegionRedirector", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_request_url of S3RegionRedirector", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.utils.S3RegionRedirector.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.utils.S3RegionRedirector", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "S3RegionRedirectorv2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.utils.S3RegionRedirectorv2", "name": "S3RegionRedirectorv2", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.utils.S3RegionRedirectorv2", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.utils", "mro": ["botocore.utils.S3RegionRedirectorv2", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "endpoint_bridge", "client", "cache"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.S3RegionRedirectorv2.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "endpoint_bridge", "client", "cache"], "arg_types": ["botocore.utils.S3RegionRedirectorv2", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "botocore.client.BaseClient", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of S3RegionRedirectorv2", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "annotate_request_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "params", "context", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.S3RegionRedirectorv2.annotate_request_context", "name": "annotate_request_context", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "params", "context", "kwargs"], "arg_types": ["botocore.utils.S3RegionRedirectorv2", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "annotate_request_context of S3RegionRedirectorv2", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_bucket_region": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "bucket", "response"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.S3RegionRedirectorv2.get_bucket_region", "name": "get_bucket_region", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "bucket", "response"], "arg_types": ["botocore.utils.S3RegionRedirectorv2", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "requests.models.Response"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_bucket_region of S3RegionRedirectorv2", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "redirect_from_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "builtins", "params", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.S3RegionRedirectorv2.redirect_from_cache", "name": "redirect_from_cache", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "builtins", "params", "kwargs"], "arg_types": ["botocore.utils.S3RegionRedirectorv2", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "redirect_from_cache of S3RegionRedirectorv2", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "redirect_from_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "request_dict", "response", "operation", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.S3RegionRedirectorv2.redirect_from_error", "name": "redirect_from_error", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "request_dict", "response", "operation", "kwargs"], "arg_types": ["botocore.utils.S3RegionRedirectorv2", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, "requests.models.Response", "botocore.model.OperationModel", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "redirect_from_error of S3RegionRedirectorv2", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "register": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "event_emitter"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.S3RegionRedirectorv2.register", "name": "register", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "event_emitter"], "arg_types": ["botocore.utils.S3RegionRedirectorv2", {".class": "UnionType", "items": ["botocore.hooks.BaseEventHooks", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register of S3RegionRedirectorv2", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "set_request_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "old_url", "new_endpoint", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.S3RegionRedirectorv2.set_request_url", "name": "set_request_url", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "old_url", "new_endpoint", "kwargs"], "arg_types": ["botocore.utils.S3RegionRedirectorv2", "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_request_url of S3RegionRedirectorv2", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.utils.S3RegionRedirectorv2.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.utils.S3RegionRedirectorv2", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "S3_ACCELERATE_WHITELIST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.utils.S3_ACCELERATE_WHITELIST", "name": "S3_ACCELERATE_WHITELIST", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}, "SAFE_CHARS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.utils.SAFE_CHARS", "name": "SAFE_CHARS", "type": "builtins.str"}}, "SERVICE_NAME_ALIASES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "botocore.utils.SERVICE_NAME_ALIASES", "name": "SERVICE_NAME_ALIASES", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "builtins.dict"}}}, "SSOTokenLoadError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.SSOTokenLoadError", "kind": "Gdef"}, "SSOTokenLoader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.utils.SSOTokenLoader", "name": "SSOTokenLoader", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.utils.SSOTokenLoader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.utils", "mro": ["botocore.utils.SSOTokenLoader", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "start_url", "session_name"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.SSOTokenLoader.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "start_url", "session_name"], "arg_types": ["botocore.utils.SSOTokenLoader", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of SSOTokenLoader", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "cache"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.SSOTokenLoader.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "cache"], "arg_types": ["botocore.utils.SSOTokenLoader", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SSOTokenLoader", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "save_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "start_url", "token", "session_name"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.SSOTokenLoader.save_token", "name": "save_token", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "start_url", "token", "session_name"], "arg_types": ["botocore.utils.SSOTokenLoader", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "save_token of SSOTokenLoader", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.utils.SSOTokenLoader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.utils.SSOTokenLoader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ServiceModel": {".class": "SymbolTableNode", "cross_ref": "botocore.model.ServiceModel", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Session": {".class": "SymbolTableNode", "cross_ref": "botocore.session.Session", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Shape": {".class": "SymbolTableNode", "cross_ref": "botocore.model.Shape", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "UNRESERVED_PAT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.utils.UNRESERVED_PAT", "name": "UNRESERVED_PAT", "type": "builtins.str"}}, "UNSAFE_URL_CHARS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.utils.UNSAFE_URL_CHARS", "name": "UNSAFE_URL_CHARS", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.frozenset"}}}, "UnsupportedOutpostResourceError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.UnsupportedOutpostResourceError", "kind": "Gdef"}, "UnsupportedS3AccesspointConfigurationError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.UnsupportedS3AccesspointConfigurationError", "kind": "Gdef"}, "UnsupportedS3ArnError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.UnsupportedS3ArnError", "kind": "Gdef"}, "UnsupportedS3ControlArnError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.UnsupportedS3ControlArnError", "kind": "Gdef"}, "UnsupportedS3ControlConfigurationError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.UnsupportedS3ControlConfigurationError", "kind": "Gdef"}, "ZONE_ID_PAT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.utils.ZONE_ID_PAT", "name": "ZONE_ID_PAT", "type": "builtins.str"}}, "_RetriesExceededError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.utils._RetriesExceededError", "name": "_RetriesExceededError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.utils._RetriesExceededError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.utils", "mro": ["botocore.utils._RetriesExceededError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.utils._RetriesExceededError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.utils._RetriesExceededError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_V": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.utils._V", "name": "_V", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.utils.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.utils.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.utils.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.utils.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.utils.__package__", "name": "__package__", "type": "builtins.str"}}, "calculate_md5": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["body", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.calculate_md5", "name": "calculate_md5", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["body", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "calculate_md5", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "calculate_sha256": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["body", "as_hex"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.calculate_sha256", "name": "calculate_sha256", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["body", "as_hex"], "arg_types": ["builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "calculate_sha256", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "calculate_tree_hash": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["body"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.calculate_tree_hash", "name": "calculate_tree_hash", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["body"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "calculate_tree_hash", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "check_dns_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["bucket_name"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.check_dns_name", "name": "check_dns_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["bucket_name"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_dns_name", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "conditionally_calculate_checksum": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["params", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.conditionally_calculate_checksum", "name": "conditionally_calculate_checksum", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["params", "kwargs"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "conditionally_calculate_checksum", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "conditionally_calculate_md5": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["params", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.conditionally_calculate_md5", "name": "conditionally_calculate_md5", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["params", "kwargs"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "conditionally_calculate_md5", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "conditionally_enable_crc32": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["params", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.conditionally_enable_crc32", "name": "conditionally_enable_crc32", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["params", "kwargs"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "conditionally_enable_crc32", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "create_nested_client": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["session", "service_name", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.create_nested_client", "name": "create_nested_client", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["session", "service_name", "kwargs"], "arg_types": ["botocore.session.Session", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_nested_client", "ret_type": "botocore.client.BaseClient", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime", "kind": "Gdef", "module_hidden": true, "module_public": false}, "datetime2timestamp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["dt", "default_timezone"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.datetime2timestamp", "name": "datetime2timestamp", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["dt", "default_timezone"], "arg_types": ["datetime.datetime", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "datetime2timestamp", "ret_type": "builtins.int", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "deep_merge": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["base", "extra"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.deep_merge", "name": "deep_merge", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["base", "extra"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "deep_merge", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "determine_content_length": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["body"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.determine_content_length", "name": "determine_content_length", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "determine_content_length", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "ensure_boolean": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["val"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.ensure_boolean", "name": "ensure_boolean", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ensure_boolean", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "fix_s3_host": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["request", "signature_version", "region_name", "default_endpoint_url", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.fix_s3_host", "name": "fix_s3_host", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["request", "signature_version", "region_name", "default_endpoint_url", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fix_s3_host", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_encoding_from_headers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["headers", "default"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.get_encoding_from_headers", "name": "get_encoding_from_headers", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["headers", "default"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_encoding_from_headers", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_environ_proxies": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["url"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.get_environ_proxies", "name": "get_environ_proxies", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["url"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_environ_proxies", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_md5": {".class": "SymbolTableNode", "cross_ref": "botocore.compat.get_md5", "kind": "Gdef"}, "get_service_module_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["service_model"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.get_service_module_name", "name": "get_service_module_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["service_model"], "arg_types": ["botocore.model.ServiceModel"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_service_module_name", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_token_from_environment": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["signing_name", "environ"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.get_token_from_environment", "name": "get_token_from_environment", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["signing_name", "environ"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_token_from_environment", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_tzinfo_options": {".class": "SymbolTableNode", "cross_ref": "botocore.compat.get_tzinfo_options", "kind": "Gdef"}, "has_checksum_header": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["params"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.has_checksum_header", "name": "has_checksum_header", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["params"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_checksum_header", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "has_header": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["header_name", "headers"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.has_header", "name": "has_header", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["header_name", "headers"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_header", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "hyphenize_service_id": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["service_id"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.hyphenize_service_id", "name": "hyphenize_service_id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["service_id"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hyphenize_service_id", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "instance_cache": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["func"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.instance_cache", "name": "instance_cache", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["func"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "instance_cache", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "is_global_accesspoint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["context"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.is_global_accesspoint", "name": "is_global_accesspoint", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["context"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_global_accesspoint", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "is_json_value_header": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["shape"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.is_json_value_header", "name": "is_json_value_header", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["shape"], "arg_types": ["botocore.model.Shape"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_json_value_header", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "is_s3_accelerate_url": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["url"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.is_s3_accelerate_url", "name": "is_s3_accelerate_url", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["url"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_s3_accelerate_url", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "is_s3express_bucket": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["bucket"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.is_s3express_bucket", "name": "is_s3express_bucket", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["bucket"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_s3express_bucket", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "is_valid_endpoint_url": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["endpoint_url"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.is_valid_endpoint_url", "name": "is_valid_endpoint_url", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["endpoint_url"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_valid_endpoint_url", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "is_valid_ipv4_endpoint_url": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["endpoint_url"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.is_valid_ipv4_endpoint_url", "name": "is_valid_ipv4_endpoint_url", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["endpoint_url"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_valid_ipv4_endpoint_url", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "is_valid_ipv6_endpoint_url": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["endpoint_url"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.is_valid_ipv6_endpoint_url", "name": "is_valid_ipv6_endpoint_url", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["endpoint_url"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_valid_ipv6_endpoint_url", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "is_valid_uri": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["endpoint_url"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.is_valid_uri", "name": "is_valid_uri", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["endpoint_url"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_valid_uri", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "botocore.utils.logger", "name": "logger", "type": "logging.Logger"}}, "lowercase_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["original"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.lowercase_dict", "name": "lowercase_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["original"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lowercase_dict", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "lru_cache_weakref": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2, 4], "arg_names": ["cache_args", "cache_kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.lru_cache_weakref", "name": "lru_cache_weakref", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["cache_args", "cache_kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lru_cache_weakref", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "merge_dicts": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["dict1", "dict2", "append_lists"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.merge_dicts", "name": "merge_dicts", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["dict1", "dict2", "append_lists"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "merge_dicts", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "normalize_boolean": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["val"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.normalize_boolean", "name": "normalize_boolean", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "normalize_boolean", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "normalize_url_path": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.normalize_url_path", "name": "normalize_url_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "normalize_url_path", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "parse_key_val_file": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["filename", "_open"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.parse_key_val_file", "name": "parse_key_val_file", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["filename", "_open"], "arg_types": ["builtins.str", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_key_val_file", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "parse_key_val_file_contents": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["contents"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.parse_key_val_file_contents", "name": "parse_key_val_file_contents", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["contents"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_key_val_file_contents", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "parse_timestamp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["value"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.parse_timestamp", "name": "parse_timestamp", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_timestamp", "ret_type": "datetime.datetime", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "parse_to_aware_datetime": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["value"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.parse_to_aware_datetime", "name": "parse_to_aware_datetime", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_to_aware_datetime", "ret_type": "datetime.datetime", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "percent_encode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["input_str", "safe"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.percent_encode", "name": "percent_encode", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["input_str", "safe"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "percent_encode", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "percent_encode_sequence": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["mapping", "safe"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.percent_encode_sequence", "name": "percent_encode_sequence", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["mapping", "safe"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "percent_encode_sequence", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "quote": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.quote", "kind": "Gdef"}, "remove_dot_segments": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["url"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.remove_dot_segments", "name": "remove_dot_segments", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["url"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_dot_segments", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "resolve_imds_endpoint_mode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["session"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.resolve_imds_endpoint_mode", "name": "resolve_imds_endpoint_mode", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["session"], "arg_types": ["botocore.session.Session"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolve_imds_endpoint_mode", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "set_value_from_jmespath": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["source", "expression", "value", "is_first"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.set_value_from_jmespath", "name": "set_value_from_jmespath", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["source", "expression", "value", "is_first"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_value_from_jmespath", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "should_bypass_proxies": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["url"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.should_bypass_proxies", "name": "should_bypass_proxies", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["url"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "should_bypass_proxies", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "switch_host_s3_accelerate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["request", "operation_name", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.switch_host_s3_accelerate", "name": "switch_host_s3_accelerate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["request", "operation_name", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "switch_host_s3_accelerate", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "switch_host_with_param": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["request", "param_name"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.switch_host_with_param", "name": "switch_host_with_param", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["request", "param_name"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "switch_host_with_param", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "switch_to_virtual_host_style": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["request", "signature_version", "default_endpoint_url", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.switch_to_virtual_host_style", "name": "switch_to_virtual_host_style", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["request", "signature_version", "default_endpoint_url", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "switch_to_virtual_host_style", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "urlparse": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urlparse", "kind": "Gdef"}, "urlsplit": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urlsplit", "kind": "Gdef"}, "urlunsplit": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urlunsplit", "kind": "Gdef"}, "validate_jmespath_for_set": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["expression"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.validate_jmespath_for_set", "name": "validate_jmespath_for_set", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["expression"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate_jmespath_for_set", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "validate_region_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["region_name"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.utils.validate_region_name", "name": "validate_region_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["region_name"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate_region_name", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "zip_longest": {".class": "SymbolTableNode", "cross_ref": "itertools.zip_longest", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\botocore-stubs\\utils.pyi"}