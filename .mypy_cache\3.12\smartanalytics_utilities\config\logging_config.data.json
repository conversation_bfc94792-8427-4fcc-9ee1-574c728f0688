{".class": "MypyFile", "_fullname": "smartanalytics_utilities.config.logging_config", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BaseSettings": {".class": "SymbolTableNode", "cross_ref": "pydantic_settings.main.BaseSettings", "kind": "Gdef"}, "Field": {".class": "SymbolTableNode", "cross_ref": "pydantic.fields.Field", "kind": "Gdef"}, "LoggingConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic_settings.main.BaseSettings"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "smartanalytics_utilities.config.logging_config.LoggingConfig", "name": "LoggingConfig", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "smartanalytics_utilities.config.logging_config.LoggingConfig", "has_param_spec_type": false, "metaclass_type": "pydantic._internal._model_construction.ModelMetaclass", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 204, "name": "__pydantic_extra__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 207, "name": "__pydantic_fields_set__", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 210, "name": "__pydantic_private__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 13, "name": "log_level", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 15, "name": "root_log_level", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 18, "name": "powertools_service_name", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 22, "name": "powertools_log_level", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 26, "name": "powertools_logger_sample_rate", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 30, "name": "powertools_logger_stream", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 35, "name": "json_logging", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 39, "name": "include_timestamp", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 43, "name": "include_caller_info", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 48, "name": "correlation_id_header", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 52, "name": "auto_correlation_id", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 57, "name": "log_performance_metrics", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 61, "name": "slow_query_threshold_ms", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 68, "name": "cloudwatch_log_group", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 72, "name": "cloudwatch_log_stream", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "smartanalytics_utilities.config.logging_config", "mro": ["smartanalytics_utilities.config.logging_config.LoggingConfig", "pydantic_settings.main.BaseSettings", "pydantic.main.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "smartanalytics_utilities.config.logging_config.LoggingConfig.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "log_level", "root_log_level", "powertools_service_name", "powertools_log_level", "powertools_logger_sample_rate", "powertools_logger_stream", "json_logging", "include_timestamp", "include_caller_info", "correlation_id_header", "auto_correlation_id", "log_performance_metrics", "slow_query_threshold_ms", "cloudwatch_log_group", "cloudwatch_log_stream"], "dataclass_transform_spec": null, "flags": [], "fullname": "smartanalytics_utilities.config.logging_config.LoggingConfig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "log_level", "root_log_level", "powertools_service_name", "powertools_log_level", "powertools_logger_sample_rate", "powertools_logger_stream", "json_logging", "include_timestamp", "include_caller_info", "correlation_id_header", "auto_correlation_id", "log_performance_metrics", "slow_query_threshold_ms", "cloudwatch_log_group", "cloudwatch_log_stream"], "arg_types": ["smartanalytics_utilities.config.logging_config.LoggingConfig", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.float", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.str", "builtins.bool", "builtins.bool", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LoggingConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "smartanalytics_utilities.config.logging_config.LoggingConfig.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "log_level"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "root_log_level"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "powertools_service_name"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "powertools_log_level"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "powertools_logger_sample_rate"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "powertools_logger_stream"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "json_logging"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "include_timestamp"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "include_caller_info"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "correlation_id_header"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto_correlation_id"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "log_performance_metrics"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "slow_query_threshold_ms"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cloudwatch_log_group"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cloudwatch_log_stream"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "log_level", "root_log_level", "powertools_service_name", "powertools_log_level", "powertools_logger_sample_rate", "powertools_logger_stream", "json_logging", "include_timestamp", "include_caller_info", "correlation_id_header", "auto_correlation_id", "log_performance_metrics", "slow_query_threshold_ms", "cloudwatch_log_group", "cloudwatch_log_stream"], "dataclass_transform_spec": null, "flags": ["is_static", "is_decorated"], "fullname": "smartanalytics_utilities.config.logging_config.LoggingConfig.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "log_level", "root_log_level", "powertools_service_name", "powertools_log_level", "powertools_logger_sample_rate", "powertools_logger_stream", "json_logging", "include_timestamp", "include_caller_info", "correlation_id_header", "auto_correlation_id", "log_performance_metrics", "slow_query_threshold_ms", "cloudwatch_log_group", "cloudwatch_log_stream"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}, "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.float", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.str", "builtins.bool", "builtins.bool", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of LoggingConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "smartanalytics_utilities.config.logging_config.LoggingConfig.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "log_level", "root_log_level", "powertools_service_name", "powertools_log_level", "powertools_logger_sample_rate", "powertools_logger_stream", "json_logging", "include_timestamp", "include_caller_info", "correlation_id_header", "auto_correlation_id", "log_performance_metrics", "slow_query_threshold_ms", "cloudwatch_log_group", "cloudwatch_log_stream"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}, "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.float", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.str", "builtins.bool", "builtins.bool", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of LoggingConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "auto_correlation_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "smartanalytics_utilities.config.logging_config.LoggingConfig.auto_correlation_id", "name": "auto_correlation_id", "type": "builtins.bool"}}, "cloudwatch_log_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "smartanalytics_utilities.config.logging_config.LoggingConfig.cloudwatch_log_group", "name": "cloudwatch_log_group", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "cloudwatch_log_stream": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "smartanalytics_utilities.config.logging_config.LoggingConfig.cloudwatch_log_stream", "name": "cloudwatch_log_stream", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "correlation_id_header": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "smartanalytics_utilities.config.logging_config.LoggingConfig.correlation_id_header", "name": "correlation_id_header", "type": "builtins.str"}}, "get_log_format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "smartanalytics_utilities.config.logging_config.LoggingConfig.get_log_format", "name": "get_log_format", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["smartanalytics_utilities.config.logging_config.LoggingConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_log_format of LoggingConfig", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_powertools_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "smartanalytics_utilities.config.logging_config.LoggingConfig.get_powertools_config", "name": "get_powertools_config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["smartanalytics_utilities.config.logging_config.LoggingConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_powertools_config of LoggingConfig", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "include_caller_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "smartanalytics_utilities.config.logging_config.LoggingConfig.include_caller_info", "name": "include_caller_info", "type": "builtins.bool"}}, "include_timestamp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "smartanalytics_utilities.config.logging_config.LoggingConfig.include_timestamp", "name": "include_timestamp", "type": "builtins.bool"}}, "json_logging": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "smartanalytics_utilities.config.logging_config.LoggingConfig.json_logging", "name": "json_logging", "type": "builtins.bool"}}, "log_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "smartanalytics_utilities.config.logging_config.LoggingConfig.log_level", "name": "log_level", "type": "builtins.str"}}, "log_performance_metrics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "smartanalytics_utilities.config.logging_config.LoggingConfig.log_performance_metrics", "name": "log_performance_metrics", "type": "builtins.bool"}}, "model_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "smartanalytics_utilities.config.logging_config.LoggingConfig.model_config", "name": "model_config", "type": {".class": "TypedDictType", "fallback": "pydantic_settings.main.SettingsConfigDict", "items": [["title", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], ["model_title_generator", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.type"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}], ["field_title_generator", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["pydantic.fields.FieldInfo", "pydantic.fields.ComputedFieldInfo"]}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}], ["str_to_lower", "builtins.bool"], ["str_to_upper", "builtins.bool"], ["str_strip_whitespace", "builtins.bool"], ["str_min_length", "builtins.int"], ["str_max_length", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], ["extra", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.ExtraValues"}, {".class": "NoneType"}]}], ["frozen", "builtins.bool"], ["populate_by_name", "builtins.bool"], ["use_enum_values", "builtins.bool"], ["validate_assignment", "builtins.bool"], ["arbitrary_types_allowed", "builtins.bool"], ["from_attributes", "builtins.bool"], ["loc_by_alias", "builtins.bool"], ["alias_generator", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}, "pydantic.aliases.AliasGenerator", {".class": "NoneType"}]}], ["ignored_types", {".class": "Instance", "args": ["builtins.type"], "type_ref": "builtins.tuple"}], ["allow_inf_nan", "builtins.bool"], ["json_schema_extra", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.JsonDict"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.JsonSchemaExtraCallable"}, {".class": "NoneType"}]}], ["json_encoders", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeType", "item": "builtins.object"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.JsonEncoder"}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}], ["strict", "builtins.bool"], ["revalidate_instances", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "always"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "never"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "subclass-instances"}]}], ["ser_j<PERSON>_<PERSON><PERSON><PERSON>", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "iso8601"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "float"}]}], ["ser_json_bytes", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "utf8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "base64"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hex"}]}], ["val_json_bytes", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "utf8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "base64"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hex"}]}], ["ser_json_inf_nan", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "null"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "constants"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "strings"}]}], ["validate_default", "builtins.bool"], ["validate_return", "builtins.bool"], ["protected_namespaces", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "<PERSON><PERSON>"}]}], "type_ref": "builtins.tuple"}], ["hide_input_in_errors", "builtins.bool"], ["defer_build", "builtins.bool"], ["plugin_settings", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.object"], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}], ["schema_generator", {".class": "UnionType", "items": [{".class": "TypeType", "item": "pydantic._internal._generate_schema.GenerateSchema"}, {".class": "NoneType"}]}], ["json_schema_serialization_defaults_required", "builtins.bool"], ["json_schema_mode_override", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "validation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "serialization"}, {".class": "NoneType"}]}], ["coerce_numbers_to_str", "builtins.bool"], ["regex_engine", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "rust-regex"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "python-re"}]}], ["validation_error_cause", "builtins.bool"], ["use_attribute_docstrings", "builtins.bool"], ["cache_strings", {".class": "UnionType", "items": ["builtins.bool", {".class": "LiteralType", "fallback": "builtins.str", "value": "all"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "keys"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "none"}]}], ["validate_by_alias", "builtins.bool"], ["validate_by_name", "builtins.bool"], ["serialize_by_alias", "builtins.bool"], ["case_sensitive", "builtins.bool"], ["nested_model_default_partial_update", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}], ["env_prefix", "builtins.str"], ["env_file", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_settings.sources.types.DotenvType"}, {".class": "NoneType"}]}], ["env_file_encoding", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], ["env_ignore_empty", "builtins.bool"], ["env_nested_delimiter", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], ["env_nested_max_split", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], ["env_parse_none_str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], ["env_parse_enums", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}], ["cli_prog_name", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], ["cli_parse_args", {".class": "UnionType", "items": ["builtins.bool", {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, {".class": "NoneType"}]}], ["cli_parse_none_str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], ["cli_hide_none_type", "builtins.bool"], ["cli_avoid_json", "builtins.bool"], ["cli_enforce_required", "builtins.bool"], ["cli_use_class_docs_for_groups", "builtins.bool"], ["cli_exit_on_error", "builtins.bool"], ["cli_prefix", "builtins.str"], ["cli_flag_prefix_char", "builtins.str"], ["cli_implicit_flags", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}], ["cli_ignore_unknown_args", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}], ["cli_kebab_case", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}], ["cli_shortcuts", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}]}], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}], ["secrets_dir", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_settings.sources.types.PathType"}, {".class": "NoneType"}]}], ["json_file", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_settings.sources.types.PathType"}, {".class": "NoneType"}]}], ["json_file_encoding", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], ["yaml_file", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_settings.sources.types.PathType"}, {".class": "NoneType"}]}], ["yaml_file_encoding", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], ["yaml_config_section", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], ["pyproject_toml_depth", "builtins.int"], ["pyproject_toml_table_header", {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}], ["toml_file", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_settings.sources.types.PathType"}, {".class": "NoneType"}]}], ["enable_decoding", "builtins.bool"]], "required_keys": []}}}, "powertools_log_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "smartanalytics_utilities.config.logging_config.LoggingConfig.powertools_log_level", "name": "powertools_log_level", "type": "builtins.str"}}, "powertools_logger_sample_rate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "smartanalytics_utilities.config.logging_config.LoggingConfig.powertools_logger_sample_rate", "name": "powertools_logger_sample_rate", "type": "builtins.float"}}, "powertools_logger_stream": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "smartanalytics_utilities.config.logging_config.LoggingConfig.powertools_logger_stream", "name": "powertools_logger_stream", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "powertools_service_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "smartanalytics_utilities.config.logging_config.LoggingConfig.powertools_service_name", "name": "powertools_service_name", "type": "builtins.str"}}, "root_log_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "smartanalytics_utilities.config.logging_config.LoggingConfig.root_log_level", "name": "root_log_level", "type": "builtins.str"}}, "slow_query_threshold_ms": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "smartanalytics_utilities.config.logging_config.LoggingConfig.slow_query_threshold_ms", "name": "slow_query_threshold_ms", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "smartanalytics_utilities.config.logging_config.LoggingConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "smartanalytics_utilities.config.logging_config.LoggingConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SettingsConfigDict": {".class": "SymbolTableNode", "cross_ref": "pydantic_settings.main.SettingsConfigDict", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "smartanalytics_utilities.config.logging_config.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "smartanalytics_utilities.config.logging_config.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "smartanalytics_utilities.config.logging_config.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "smartanalytics_utilities.config.logging_config.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "smartanalytics_utilities.config.logging_config.__package__", "name": "__package__", "type": "builtins.str"}}}, "path": "layers\\utilities\\python\\smartanalytics_utilities\\config\\logging_config.py"}