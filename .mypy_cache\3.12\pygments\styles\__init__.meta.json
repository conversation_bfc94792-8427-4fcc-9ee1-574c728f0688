{"data_mtime": 1759503826, "dep_lines": [1, 3, 4, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 30, 30], "dependencies": ["collections.abc", "pygments.style", "pygments.util", "builtins", "abc", "typing"], "hash": "b4588da0cf7a0b10af5ba0c6aa978e4ebc65b02d9f357f69582a2ed5c555c30e", "id": "pygments.styles", "ignore_all": true, "interface_hash": "95e27863545d36cc888b8e59712b47c3e335d675d146c4fa004ba3df54d33d86", "mtime": 1757424623, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pygments-stubs\\styles\\__init__.pyi", "plugin_data": null, "size": 373, "suppressed": [], "version_id": "1.8.0"}