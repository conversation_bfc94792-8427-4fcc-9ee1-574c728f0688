{"data_mtime": 1759504546, "dep_lines": [38, 39, 44, 10, 12, 13, 14, 15, 36, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 25, 5, 10, 10, 10, 5, 10, 5, 20, 20, 30, 30, 30], "dependencies": ["sqlalchemy.util._has_cy", "sqlalchemy.util.typing", "sqlalchemy.util._py_collections", "__future__", "operator", "threading", "types", "typing", "weakref", "builtins", "pyexpat.model", "pyexpat.errors", "_typeshed", "_weakref", "abc"], "hash": "3137ad364764df6f7d8e444ec42e9e10835a46ee201fa75cbc6de65c6f21ae2b", "id": "sqlalchemy.util._collections", "ignore_all": true, "interface_hash": "d5536511f43280ad39dc7eb2ef204219e366aba18d9d03a44c781b68a04033ba", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\_collections.py", "plugin_data": null, "size": 20868, "suppressed": [], "version_id": "1.8.0"}