{"data_mtime": 1759503829, "dep_lines": [4, 5, 1, 2, 4, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 5, 20, 5, 20, 20, 30], "dependencies": ["multiprocessing.popen_fork", "multiprocessing.util", "sys", "typing", "multiprocessing", "builtins", "pyexpat.model", "pyexpat.errors", "abc"], "hash": "92e299989c70e2277c4797534e9f81ec4fb9a8359349ec403a40aa4ad10ca0aa", "id": "multiprocessing.popen_spawn_posix", "ignore_all": true, "interface_hash": "3a81d303a984afa565d7ad760c71ddb8428af40ca41887a9144de4a007b7150e", "mtime": 1757092231, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\multiprocessing\\popen_spawn_posix.pyi", "plugin_data": null, "size": 524, "suppressed": [], "version_id": "1.8.0"}