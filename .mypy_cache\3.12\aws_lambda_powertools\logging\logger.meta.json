{"data_mtime": 1759503833, "dep_lines": [13, 16, 17, 18, 23, 24, 25, 30, 33, 24, 30, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 5, 10, 25, 20, 20, 5, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 20, 20, 30, 30, 30], "dependencies": ["aws_lambda_powertools.logging.constants", "aws_lambda_powertools.logging.exceptions", "aws_lambda_powertools.logging.filters", "aws_lambda_powertools.logging.formatter", "aws_lambda_powertools.logging.lambda_context", "aws_lambda_powertools.shared.constants", "aws_lambda_powertools.shared.functions", "aws_lambda_powertools.utilities.jmespath_utils", "aws_lambda_powertools.shared.types", "aws_lambda_powertools.shared", "aws_lambda_powertools.utilities", "__future__", "functools", "inspect", "logging", "os", "random", "sys", "warnings", "contextlib", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "_typeshed", "abc", "types"], "hash": "1a212b55711b581c921c647f8b17665b180bf183724636569222129a86b79cbf", "id": "aws_lambda_powertools.logging.logger", "ignore_all": true, "interface_hash": "039071c93fdb2184b46e7d47ea1a3b8270a9111e20e326671e8edb3ed699d13c", "mtime": 1757091873, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\aws_lambda_powertools\\logging\\logger.py", "plugin_data": null, "size": 33035, "suppressed": [], "version_id": "1.8.0"}