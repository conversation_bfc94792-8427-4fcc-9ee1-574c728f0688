{"data_mtime": 1759503833, "dep_lines": [7, 6, 12, 1, 3, 4, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 25, 5, 10, 10, 5, 20, 20, 30, 30, 30], "dependencies": ["urllib3.util.timeout", "urllib3.exceptions", "urllib3._base_connection", "__future__", "socket", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "_socket", "abc", "enum"], "hash": "2633bbdb69731e5ccb5cf4e4afd65605d86c7979cc5633126f50c92d5ad74a74", "id": "urllib3.util.connection", "ignore_all": true, "interface_hash": "b275a0f80fbbe07866c93d6220e246bbea595b807edd876a2f03c82fbcbf540a", "mtime": 1757091871, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\urllib3\\util\\connection.py", "plugin_data": null, "size": 4444, "suppressed": [], "version_id": "1.8.0"}