{"data_mtime": 1759503828, "dep_lines": [50, 51, 52, 53, 54, 55, 56, 60, 34, 35, 36, 37, 38, 46, 48, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 25, 10, 10, 10, 5, 5, 5, 5, 5, 20, 20, 30, 30, 30], "dependencies": ["pydantic.v1.class_validators", "pydantic.v1.config", "pydantic.v1.error_wrappers", "pydantic.v1.errors", "pydantic.v1.fields", "pydantic.v1.main", "pydantic.v1.utils", "pydantic.v1.typing", "copy", "dataclasses", "sys", "contextlib", "functools", "typing", "typing_extensions", "builtins", "pyexpat.model", "pyexpat.errors", "_typeshed", "abc", "enum"], "hash": "efce1caaf2276f020f5abf6eb1fa57ddc87bcf8b77276b532ba374ebbff0935a", "id": "pydantic.v1.dataclasses", "ignore_all": true, "interface_hash": "ebeb8ba536e9f4c5e428f669f52384554a02287315a130f4a46628f8eeedc5e5", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\v1\\dataclasses.py", "plugin_data": null, "size": 18172, "suppressed": [], "version_id": "1.8.0"}