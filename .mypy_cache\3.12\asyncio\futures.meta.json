{"data_mtime": 1759503833, "dep_lines": [3, 2, 7, 1, 4, 5, 14, 17, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 5, 5, 5, 20, 20, 30, 30, 30, 30], "dependencies": ["concurrent.futures._base", "collections.abc", "asyncio.events", "sys", "typing", "typing_extensions", "<PERSON><PERSON><PERSON>", "types", "builtins", "pyexpat.model", "pyexpat.errors", "_typeshed", "abc", "concurrent", "concurrent.futures"], "hash": "beb19f7fcbc8aef1aad2dd7a69276c55312611872d6da11402dfcefbf4a1fb54", "id": "asyncio.futures", "ignore_all": true, "interface_hash": "728c082ef12d996f664ef418fd6fffdfed5496c77be6c5f919ac3497b03bc3e0", "mtime": 1757092230, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\asyncio\\futures.pyi", "plugin_data": null, "size": 2651, "suppressed": [], "version_id": "1.8.0"}