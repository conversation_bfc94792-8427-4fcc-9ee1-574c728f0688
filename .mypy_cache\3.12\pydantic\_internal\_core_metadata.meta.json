{"data_mtime": 1759503836, "dep_lines": [8, 7, 1, 3, 4, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 25, 5, 5, 5, 5, 20, 20, 30, 30, 30, 30], "dependencies": ["pydantic._internal._schema_generation_shared", "pydantic.config", "__future__", "typing", "warnings", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "pydantic.annotated_handlers", "pydantic_core", "pydantic_core.core_schema"], "hash": "63f836b778bbba5b8afb05c266fcc97d114c3d4336dda0582df69ee05cc13f2d", "id": "pydantic._internal._core_metadata", "ignore_all": true, "interface_hash": "2bddb7e6d912535bc6da0e71f7adf6f98bdd83af4d93acff1cc3dd601b2aa316", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\_internal\\_core_metadata.py", "plugin_data": null, "size": 5162, "suppressed": [], "version_id": "1.8.0"}