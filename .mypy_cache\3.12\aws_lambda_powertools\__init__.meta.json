{"data_mtime": 1759503870, "dep_lines": [8, 9, 5, 6, 7, 10, 3, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 30, 30, 30, 30], "dependencies": ["aws_lambda_powertools.shared.user_agent", "aws_lambda_powertools.shared.version", "aws_lambda_powertools.logging", "aws_lambda_powertools.metrics", "aws_lambda_powertools.package_logger", "aws_lambda_powertools.tracing", "pathlib", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "aws_lambda_powertools.shared", "os", "typing"], "hash": "a388841d4d0c7d60b4fd37d59a2b318b454e014c39b907ea2d05abd2ddbd65a1", "id": "aws_lambda_powertools", "ignore_all": true, "interface_hash": "25271c654bec1d59369f373e3373b7e3df7c21a7953031e7d1d22b9942ee7e69", "mtime": 1757091873, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\aws_lambda_powertools\\__init__.py", "plugin_data": null, "size": 676, "suppressed": [], "version_id": "1.8.0"}