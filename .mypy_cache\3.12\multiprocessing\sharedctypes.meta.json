{"data_mtime": 1759503829, "dep_lines": [2, 4, 5, 1, 6, 7, 8, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 30, 30, 30], "dependencies": ["collections.abc", "multiprocessing.context", "multiprocessing.synchronize", "ctypes", "types", "typing", "typing_extensions", "builtins", "pyexpat.model", "pyexpat.errors", "_ctypes", "abc", "contextlib"], "hash": "4fe0d41d933951ac9e78ff1bef67b8ed5029b27c8ff11be84a0f9b5084b9b866", "id": "multiprocessing.sharedctypes", "ignore_all": true, "interface_hash": "9b9e15142ec4b45aac42c7a73f23a3e0ba67296e196c96b13908390350aea19f", "mtime": 1757092231, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\multiprocessing\\sharedctypes.pyi", "plugin_data": null, "size": 4052, "suppressed": [], "version_id": "1.8.0"}