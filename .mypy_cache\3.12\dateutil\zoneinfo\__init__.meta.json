{"data_mtime": 1759504508, "dep_lines": [2, 8, 1, 3, 4, 5, 6, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 30, 30, 30, 30], "dependencies": ["collections.abc", "dateutil.tz", "_typeshed", "io", "tarfile", "typing", "typing_extensions", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "datetime", "dateutil.tz._common", "dateutil.tz.tz"], "hash": "87df416817c1f2fda92b736d2725baff2a86b6e77b2a579ed1582092cedf4c18", "id": "dateutil.zoneinfo", "ignore_all": true, "interface_hash": "947db9c0d9422115e2e5947adc344c490d67e81f5c0b65e01f8dc77fa12abb30", "mtime": 1759341510, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\dateutil-stubs\\zoneinfo\\__init__.pyi", "plugin_data": null, "size": 1577, "suppressed": [], "version_id": "1.8.0"}