{"data_mtime": 1759503829, "dep_lines": [5, 6, 7, 1, 3, 10, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 25, 5, 20, 20, 30, 30], "dependencies": ["markdown_it.ruler", "markdown_it.token", "markdown_it.utils", "__future__", "typing", "markdown_it", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "markdown_it.main"], "hash": "1ea599094af97d6ef11ba8de4190dd3b4844f61c71ca5dfff9b6b080ecb9ec76", "id": "markdown_it.rules_core.state_core", "ignore_all": true, "interface_hash": "2fa996caf0fa177c083e794d598221efe3f431b90e9d94c336359079d9504a66", "mtime": 1757092234, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\markdown_it\\rules_core\\state_core.py", "plugin_data": null, "size": 570, "suppressed": [], "version_id": "1.8.0"}