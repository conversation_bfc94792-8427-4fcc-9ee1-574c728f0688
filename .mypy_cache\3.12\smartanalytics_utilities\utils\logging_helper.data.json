{".class": "MypyFile", "_fullname": "smartanalytics_utilities.utils.logging_helper", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Logger": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.logging.logger.Logger", "kind": "Gdef"}, "LoggingConfig": {".class": "SymbolTableNode", "cross_ref": "smartanalytics_utilities.config.logging_config.LoggingConfig", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "smartanalytics_utilities.utils.logging_helper.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "smartanalytics_utilities.utils.logging_helper.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "smartanalytics_utilities.utils.logging_helper.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "smartanalytics_utilities.utils.logging_helper.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "smartanalytics_utilities.utils.logging_helper.__package__", "name": "__package__", "type": "builtins.str"}}, "get_logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1], "arg_names": ["service_name", "log_level", "correlation_id_path"], "dataclass_transform_spec": null, "flags": [], "fullname": "smartanalytics_utilities.utils.logging_helper.get_logger", "name": "get_logger", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["service_name", "log_level", "correlation_id_path"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_logger", "ret_type": "aws_lambda_powertools.logging.logger.Logger", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "setup_logging": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["config"], "dataclass_transform_spec": null, "flags": [], "fullname": "smartanalytics_utilities.utils.logging_helper.setup_logging", "name": "setup_logging", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["config"], "arg_types": [{".class": "UnionType", "items": ["smartanalytics_utilities.config.logging_config.LoggingConfig", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setup_logging", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}}, "path": "layers\\utilities\\python\\smartanalytics_utilities\\utils\\logging_helper.py"}