{"data_mtime": 1759503866, "dep_lines": [25, 36, 37, 40, 33, 34, 10, 12, 13, 14, 23, 33, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 25, 10, 5, 5, 10, 10, 5, 10, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.pool.base", "sqlalchemy.util.queue", "sqlalchemy.util.typing", "sqlalchemy.engine.interfaces", "sqlalchemy.exc", "sqlalchemy.util", "__future__", "threading", "traceback", "typing", "weakref", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "_weakref", "abc", "enum", "logging", "re", "sqlalchemy.engine", "sqlalchemy.event", "sqlalchemy.event.base", "sqlalchemy.event.registry", "sqlalchemy.log", "sqlalchemy.util.langhelpers", "types"], "hash": "db7a791d6fc312cae08011f6777161c3e6adfc1d9cf31f4602c5a449cb3ceef3", "id": "sqlalchemy.pool.impl", "ignore_all": true, "interface_hash": "ff45bcc46110a0095d929369290174050192fcb2af9b54cf61de1d815c496146", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\pool\\impl.py", "plugin_data": null, "size": 19478, "suppressed": [], "version_id": "1.8.0"}