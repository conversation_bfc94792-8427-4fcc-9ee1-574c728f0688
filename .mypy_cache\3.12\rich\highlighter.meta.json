{"data_mtime": 1759503832, "dep_lines": [5, 202, 1, 2, 3, 230, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 10, 5, 20, 20, 30, 30, 30, 30, 30, 30], "dependencies": ["rich.text", "rich.console", "re", "abc", "typing", "json", "builtins", "pyexpat.model", "pyexpat.errors", "_random", "datetime", "json.encoder", "rich.jupyter", "rich.style", "rich.theme"], "hash": "1bfb27fbc0ca8ccd6c1232c6fe8738a2f9169a25295af8fc6d78b4c9e7762e76", "id": "rich.highlighter", "ignore_all": true, "interface_hash": "4d1aeaf45cc7b2abe9a153be464e83c337ce18eefc7641bc93423554f0b8d131", "mtime": 1757092238, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\rich\\highlighter.py", "plugin_data": null, "size": 9586, "suppressed": [], "version_id": "1.8.0"}