{"data_mtime": 1759503866, "dep_lines": [31, 32, 33, 34, 35, 36, 37, 41, 45, 11, 31, 42, 43, 44, 9, 11, 12, 13, 14, 15, 16, 18, 29, 42, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 10, 10, 5, 5, 5, 10, 20, 10, 10, 10, 5, 20, 10, 10, 10, 10, 5, 5, 10, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.cache_key", "sqlalchemy.sql.coercions", "sqlalchemy.sql.elements", "sqlalchemy.sql.roles", "sqlalchemy.sql.schema", "sqlalchemy.sql.visitors", "sqlalchemy.sql.base", "sqlalchemy.sql.operators", "sqlalchemy.util.typing", "collections.abc", "sqlalchemy.sql", "sqlalchemy.exc", "sqlalchemy.inspection", "sqlalchemy.util", "__future__", "collections", "inspect", "itertools", "operator", "threading", "types", "typing", "weakref", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "_typeshed", "abc", "enum", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.sql._py_util", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.util._collections", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "typing_extensions"], "hash": "972046324991ac6e31de560f8d9d3e3754467879996b8793868805f67bf65e7d", "id": "sqlalchemy.sql.lambdas", "ignore_all": true, "interface_hash": "86e0aa9d6e64d86b5e9bb8ea7bd2f7a434487b7426f79f0a7b8b860454b5e10c", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\lambdas.py", "plugin_data": null, "size": 50843, "suppressed": [], "version_id": "1.8.0"}