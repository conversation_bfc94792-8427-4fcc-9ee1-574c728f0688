"""SmartAnalytics Domain Core Layer.

This package contains the core business logic and domain models for SmartAnalytics
applications. It follows Domain-Driven Design (DDD) principles with:

- Pure domain models without infrastructure dependencies
- Domain services for business logic
- Command objects for operations
- Domain events for communication
- Value objects for data integrity
- Ports (interfaces) for external dependencies

The domain layer is the heart of the hexagonal architecture and should remain
independent of infrastructure concerns.
"""
__version__ = "1.0.0"

__all__ = ["__version__"]
