{"tests/unit/test_event_processor.py::TestEventProcessor": true, "layers/infrastructure/python/tests/test_repositories.py::TestRepositories::test_ring_group_repository_get_or_create": true, "layers/infrastructure/python/tests/test_repositories.py::TestRepositories::test_agent_event_repository_add": true, "layers/infrastructure/python/tests/test_repositories.py::TestRepositories::test_agent_event_repository_exists_by_hash": true, "layers/infrastructure/python/tests/test_models.py::TestDatabaseModels::test_dim_tenant_creation": true, "layers/infrastructure/python/tests/test_models.py::TestDatabaseModels::test_dim_agent_creation": true, "layers/infrastructure/python/tests/test_models.py::TestDatabaseModels::test_dim_ring_group_creation": true, "layers/infrastructure/python/tests/test_models.py::TestDatabaseModels::test_dim_agent_event_creation": true, "layers/infrastructure/python/tests/test_models.py::TestDatabaseModels::test_model_relationships": true, "layers/infrastructure/python/tests/test_models.py::TestDatabaseModels::test_model_constraints": true, "layers/infrastructure/python/tests/test_models.py::TestDatabaseModels::test_model_defaults": true, "layers/utilities/python/tests/test_config.py::TestSettings": true, "layers/utilities/python/tests/test_config.py::TestDatabaseConfig": true, "layers/utilities/python/tests/test_config.py::TestLoggingConfig": true, "layers/utilities/python/tests/test_utils.py::TestHashHelper": true, "layers/utilities/python/tests/test_config.py::TestSettings::test_settings_with_minimal_config": true, "layers/utilities/python/tests/test_config.py::TestSettings::test_settings_with_environment_override": true, "layers/utilities/python/tests/test_config.py::TestSettings::test_settings_feature_flags": true, "layers/utilities/python/tests/test_config.py::TestDatabaseConfig::test_database_config_with_secrets_arn": true, "layers/utilities/python/tests/test_config.py::TestDatabaseConfig::test_database_config_validation_error": true, "layers/utilities/python/tests/test_config.py::TestDatabaseConfig::test_database_url_generation": true, "layers/utilities/python/tests/test_config.py::TestDatabaseConfig::test_database_url_without_password": true, "layers/utilities/python/tests/test_config.py::TestLoggingConfig::test_logging_config_defaults": true, "layers/utilities/python/tests/test_config.py::TestLoggingConfig::test_logging_config_custom_values": true, "layers/utilities/python/tests/test_config.py::TestLoggingConfig::test_log_format_generation": true, "layers/utilities/python/tests/test_config.py::TestLoggingConfig::test_third_party_logger_levels": true, "layers/utilities/python/tests/test_utils.py::TestHashHelper::test_generate_event_hash_string": true, "layers/infrastructure/python/tests/test_models.py": true, "tests/infrastructure/test_models.py": true, "layers/infrastructure/python/tests/test_repositories.py::TestTenantRepository": true, "layers/infrastructure/python/tests/test_repositories.py::TestAgentRepository": true, "layers/infrastructure/python/tests/test_repositories.py::TestRingGroupRepository": true, "layers/infrastructure/python/tests/test_repositories.py::TestAgentEventRepository": true, "layers/infrastructure/python/tests/test_database_connection.py::TestDatabaseConnection::test_database_connection_connect": true, "layers/infrastructure/python/tests/test_database_models.py::TestDatabaseModels::test_metadata_schema_configuration": true, "layers/infrastructure/python/tests/test_database_models.py::TestDatabaseModels::test_dim_tenant_model_creation": true, "layers/infrastructure/python/tests/test_database_models.py::TestDatabaseModels::test_dim_tenant_unique_constraint": true, "layers/infrastructure/python/tests/test_database_models.py::TestDatabaseModels::test_dim_agent_model_creation": true, "layers/infrastructure/python/tests/test_database_models.py::TestDatabaseModels::test_dim_ring_group_model_creation": true, "layers/infrastructure/python/tests/test_database_models.py::TestDatabaseModels::test_dim_agent_event_model_creation": true, "layers/infrastructure/python/tests/test_database_models.py::TestDatabaseModels::test_dim_agent_event_with_ring_group": true, "layers/infrastructure/python/tests/test_database_models.py::TestDatabaseModels::test_scd_type2_pattern_support": true, "layers/infrastructure/python/tests/test_repositories.py::TestTenantRepository::test_get_by_name_found": true, "layers/infrastructure/python/tests/test_repositories.py::TestTenantRepository::test_get_by_name_not_found": true, "layers/infrastructure/python/tests/test_repositories.py::TestTenantRepository::test_create_tenant": true, "layers/infrastructure/python/tests/test_repositories.py::TestTenantRepository::test_get_or_create_existing_tenant": true, "layers/infrastructure/python/tests/test_repositories.py::TestTenantRepository::test_get_or_create_new_tenant": true, "layers/infrastructure/python/tests/test_repositories.py::TestAgentRepository::test_get_by_name_and_tenant_found": true, "layers/infrastructure/python/tests/test_repositories.py::TestAgentRepository::test_create_agent": true, "layers/infrastructure/python/tests/test_repositories.py::TestAgentRepository::test_get_or_create_new_agent": true, "layers/infrastructure/python/tests/test_repositories.py::TestRingGroupRepository::test_get_by_name_and_tenant_found": true, "layers/infrastructure/python/tests/test_repositories.py::TestRingGroupRepository::test_create_ring_group": true, "layers/infrastructure/python/tests/test_repositories.py::TestRingGroupRepository::test_get_or_create_with_uri_extraction": true, "layers/infrastructure/python/tests/test_repositories.py::TestAgentEventRepository::test_get_by_hash_found": true, "layers/infrastructure/python/tests/test_repositories.py::TestAgentEventRepository::test_get_by_hash_not_found": true, "layers/infrastructure/python/tests/test_repositories.py::TestAgentEventRepository::test_create_event_without_ring_group": true, "layers/infrastructure/python/tests/test_repositories.py::TestAgentEventRepository::test_create_event_with_ring_group": true, "layers/infrastructure/python/tests/test_repositories.py::TestAgentEventRepository::test_create_batch_events": true, "layers/infrastructure/python/tests/test_unit_of_work.py::TestSqlAlchemyUnitOfWork::test_unit_of_work_context_manager_success": true}