{".class": "MypyFile", "_fullname": "aws_lambda_powertools.logging.logger", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AnyCallableT": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.shared.types.AnyCallableT", "kind": "Gdef"}, "BasePowertoolsFormatter": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.logging.formatter.BasePowertoolsFormatter", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef"}, "IO": {".class": "SymbolTableNode", "cross_ref": "typing.IO", "kind": "Gdef"}, "InvalidLoggerSamplingRateError": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.logging.exceptions.InvalidLoggerSamplingRateError", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "LOGGER_ATTRIBUTE_PRECONFIGURED": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.logging.constants.LOGGER_ATTRIBUTE_PRECONFIGURED", "kind": "Gdef"}, "LambdaPowertoolsFormatter": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.logging.formatter.LambdaPowertoolsFormatter", "kind": "Gdef"}, "Logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aws_lambda_powertools.logging.logger.Logger", "name": "<PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "aws_lambda_powertools.logging.logger.Logger", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aws_lambda_powertools.logging.logger", "mro": ["aws_lambda_powertools.logging.logger.Logger", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "service", "level", "child", "sampling_rate", "stream", "logger_formatter", "logger_handler", "log_uncaught_exceptions", "json_serializer", "json_deserializer", "json_default", "datefmt", "use_datetime_directive", "log_record_order", "utc", "use_rfc3339", "serialize_stacktrace", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "aws_lambda_powertools.logging.logger.Logger.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "service", "level", "child", "sampling_rate", "stream", "logger_formatter", "logger_handler", "log_uncaught_exceptions", "json_serializer", "json_deserializer", "json_default", "datefmt", "use_datetime_directive", "log_record_order", "utc", "use_rfc3339", "serialize_stacktrace", "kwargs"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", "builtins.int", {".class": "NoneType"}]}, "builtins.bool", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.IO"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aws_lambda_powertools.logging.logger.PowertoolsFormatter", "id": -1, "name": "PowertoolsFormatter", "namespace": "", "upper_bound": "aws_lambda_powertools.logging.formatter.BasePowertoolsFormatter", "values": [], "variance": 0}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["logging.Handler", {".class": "NoneType"}]}, "builtins.bool", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "builtins.dict"}, "builtins.str", "builtins.bool", "builtins.int", "builtins.float"]}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, {".class": "NoneType"}]}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of <PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aws_lambda_powertools.logging.logger.PowertoolsFormatter", "id": -1, "name": "PowertoolsFormatter", "namespace": "", "upper_bound": "aws_lambda_powertools.logging.formatter.BasePowertoolsFormatter", "values": [], "variance": 0}]}}}, "_configure_sampling": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "aws_lambda_powertools.logging.logger.Logger._configure_sampling", "name": "_configure_sampling", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_configure_sampling of Logger", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_default_log_keys": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aws_lambda_powertools.logging.logger.Logger._default_log_keys", "name": "_default_log_keys", "type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.float", "builtins.str"]}], "type_ref": "builtins.dict"}}}, "_determine_log_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "level"], "dataclass_transform_spec": null, "flags": [], "fullname": "aws_lambda_powertools.logging.logger.Logger._determine_log_level", "name": "_determine_log_level", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "level"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger", {".class": "UnionType", "items": ["builtins.str", "builtins.int", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_determine_log_level of Logger", "ret_type": {".class": "UnionType", "items": ["builtins.str", "builtins.int"]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_get_aws_lambda_log_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "aws_lambda_powertools.logging.logger.Logger._get_aws_lambda_log_level", "name": "_get_aws_lambda_log_level", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_aws_lambda_log_level of Logger", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_get_logger": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "aws_lambda_powertools.logging.logger.Logger._get_logger", "name": "_get_logger", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_logger of Logger", "ret_type": "logging.Logger", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_get_powertools_log_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "level"], "dataclass_transform_spec": null, "flags": [], "fullname": "aws_lambda_powertools.logging.logger.Logger._get_powertools_log_level", "name": "_get_powertools_log_level", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "level"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger", {".class": "UnionType", "items": ["builtins.str", "builtins.int", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_powertools_log_level of Logger", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_init_logger": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "formatter_options", "log_level", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "aws_lambda_powertools.logging.logger.Logger._init_logger", "name": "_init_logger", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "formatter_options", "log_level", "kwargs"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", "builtins.int", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_init_logger of Logger", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_is_deduplication_disabled": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aws_lambda_powertools.logging.logger.Logger._is_deduplication_disabled", "name": "_is_deduplication_disabled", "type": "builtins.bool"}}, "_logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aws_lambda_powertools.logging.logger.Logger._logger", "name": "_logger", "type": "logging.Logger"}}, "_stream": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aws_lambda_powertools.logging.logger.Logger._stream", "name": "_stream", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.IO"}}}, "addFilter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "filter"], "dataclass_transform_spec": null, "flags": [], "fullname": "aws_lambda_powertools.logging.logger.Logger.addFilter", "name": "addFilter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "filter"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger", {".class": "TypeAliasType", "args": [], "type_ref": "logging._FilterType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "addFilter of Logger", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "addHandler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "handler"], "dataclass_transform_spec": null, "flags": [], "fullname": "aws_lambda_powertools.logging.logger.Logger.addHandler", "name": "add<PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "handler"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger", "logging.Handler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON><PERSON><PERSON> of Logger", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "append_context_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "additional_keys"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "aws_lambda_powertools.logging.logger.Logger.append_context_keys", "name": "append_context_keys", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "additional_keys"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "append_context_keys of Logger", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "type_ref": "typing.Generator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aws_lambda_powertools.logging.logger.Logger.append_context_keys", "name": "append_context_keys", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "additional_keys"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "append_context_keys of Logger", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "append_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "additional_keys"], "dataclass_transform_spec": null, "flags": [], "fullname": "aws_lambda_powertools.logging.logger.Logger.append_keys", "name": "append_keys", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "additional_keys"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "append_keys of Logger", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "child": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aws_lambda_powertools.logging.logger.Logger.child", "name": "child", "type": "builtins.bool"}}, "critical": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 5, 5, 5, 5, 4], "arg_names": ["self", "msg", "args", "exc_info", "stack_info", "stacklevel", "extra", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "aws_lambda_powertools.logging.logger.Logger.critical", "name": "critical", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5, 5, 5, 5, 4], "arg_names": ["self", "msg", "args", "exc_info", "stack_info", "stacklevel", "extra", "kwargs"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger", "builtins.object", "builtins.object", {".class": "TypeAliasType", "args": [], "type_ref": "logging._ExcInfoType"}, "builtins.bool", "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.object"], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}, "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "critical of <PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "debug": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 5, 5, 5, 5, 4], "arg_names": ["self", "msg", "args", "exc_info", "stack_info", "stacklevel", "extra", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "aws_lambda_powertools.logging.logger.Logger.debug", "name": "debug", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5, 5, 5, 5, 4], "arg_names": ["self", "msg", "args", "exc_info", "stack_info", "stacklevel", "extra", "kwargs"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger", "builtins.object", "builtins.object", {".class": "TypeAliasType", "args": [], "type_ref": "logging._ExcInfoType"}, "builtins.bool", "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.object"], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}, "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "debug of Logger", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 5, 5, 5, 5, 4], "arg_names": ["self", "msg", "args", "exc_info", "stack_info", "stacklevel", "extra", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "aws_lambda_powertools.logging.logger.Logger.error", "name": "error", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5, 5, 5, 5, 4], "arg_names": ["self", "msg", "args", "exc_info", "stack_info", "stacklevel", "extra", "kwargs"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger", "builtins.object", "builtins.object", {".class": "TypeAliasType", "args": [], "type_ref": "logging._ExcInfoType"}, "builtins.bool", "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.object"], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}, "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "error of Logger", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 5, 5, 5, 5, 4], "arg_names": ["self", "msg", "args", "exc_info", "stack_info", "stacklevel", "extra", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "aws_lambda_powertools.logging.logger.Logger.exception", "name": "exception", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5, 5, 5, 5, 4], "arg_names": ["self", "msg", "args", "exc_info", "stack_info", "stacklevel", "extra", "kwargs"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger", "builtins.object", "builtins.object", {".class": "TypeAliasType", "args": [], "type_ref": "logging._ExcInfoType"}, "builtins.bool", "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.object"], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}, "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exception of <PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_correlation_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "aws_lambda_powertools.logging.logger.Logger.get_correlation_id", "name": "get_correlation_id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_correlation_id of Logger", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_current_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "aws_lambda_powertools.logging.logger.Logger.get_current_keys", "name": "get_current_keys", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_current_keys of Logger", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "handlers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "aws_lambda_powertools.logging.logger.Logger.handlers", "name": "handlers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handlers of Logger", "ret_type": {".class": "Instance", "args": ["logging.Handler"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aws_lambda_powertools.logging.logger.Logger.handlers", "name": "handlers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handlers of Logger", "ret_type": {".class": "Instance", "args": ["logging.Handler"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 5, 5, 5, 5, 4], "arg_names": ["self", "msg", "args", "exc_info", "stack_info", "stacklevel", "extra", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "aws_lambda_powertools.logging.logger.Logger.info", "name": "info", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5, 5, 5, 5, 4], "arg_names": ["self", "msg", "args", "exc_info", "stack_info", "stacklevel", "extra", "kwargs"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger", "builtins.object", "builtins.object", {".class": "TypeAliasType", "args": [], "type_ref": "logging._ExcInfoType"}, "builtins.bool", "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.object"], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}, "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "info of Logger", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "inject_lambda_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "aws_lambda_powertools.logging.logger.Logger.inject_lambda_context", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "lambda_handler", "log_event", "correlation_id_path", "clear_state"], "dataclass_transform_spec": null, "flags": ["is_overload"], "fullname": "aws_lambda_powertools.logging.logger.Logger.inject_lambda_context", "name": "inject_lambda_context", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "lambda_handler", "log_event", "correlation_id_path", "clear_state"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger", {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aws_lambda_powertools.shared.types.AnyCallableT", "id": -1, "name": "AnyCallableT", "namespace": "", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inject_lambda_context of Logger", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aws_lambda_powertools.shared.types.AnyCallableT", "id": -1, "name": "AnyCallableT", "namespace": "", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}]}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "lambda_handler", "log_event", "correlation_id_path", "clear_state"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "aws_lambda_powertools.logging.logger.Logger.inject_lambda_context", "name": "inject_lambda_context", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "lambda_handler", "log_event", "correlation_id_path", "clear_state"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aws_lambda_powertools.shared.types.AnyCallableT", "id": -1, "name": "AnyCallableT", "namespace": "", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inject_lambda_context of Logger", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aws_lambda_powertools.shared.types.AnyCallableT", "id": -1, "name": "AnyCallableT", "namespace": "", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aws_lambda_powertools.shared.types.AnyCallableT", "id": -1, "name": "AnyCallableT", "namespace": "", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "aws_lambda_powertools.logging.logger.Logger.inject_lambda_context", "name": "inject_lambda_context", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "lambda_handler", "log_event", "correlation_id_path", "clear_state"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aws_lambda_powertools.shared.types.AnyCallableT", "id": -1, "name": "AnyCallableT", "namespace": "", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inject_lambda_context of Logger", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aws_lambda_powertools.shared.types.AnyCallableT", "id": -1, "name": "AnyCallableT", "namespace": "", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aws_lambda_powertools.shared.types.AnyCallableT", "id": -1, "name": "AnyCallableT", "namespace": "", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "lambda_handler", "log_event", "correlation_id_path", "clear_state"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "aws_lambda_powertools.logging.logger.Logger.inject_lambda_context", "name": "inject_lambda_context", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "lambda_handler", "log_event", "correlation_id_path", "clear_state"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger", {".class": "NoneType"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inject_lambda_context of Logger", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aws_lambda_powertools.shared.types.AnyCallableT", "id": -1, "name": "AnyCallableT", "namespace": "", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aws_lambda_powertools.shared.types.AnyCallableT", "id": -1, "name": "AnyCallableT", "namespace": "", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aws_lambda_powertools.shared.types.AnyCallableT", "id": -1, "name": "AnyCallableT", "namespace": "", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "aws_lambda_powertools.logging.logger.Logger.inject_lambda_context", "name": "inject_lambda_context", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "lambda_handler", "log_event", "correlation_id_path", "clear_state"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger", {".class": "NoneType"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inject_lambda_context of Logger", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aws_lambda_powertools.shared.types.AnyCallableT", "id": -1, "name": "AnyCallableT", "namespace": "", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aws_lambda_powertools.shared.types.AnyCallableT", "id": -1, "name": "AnyCallableT", "namespace": "", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aws_lambda_powertools.shared.types.AnyCallableT", "id": -1, "name": "AnyCallableT", "namespace": "", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "lambda_handler", "log_event", "correlation_id_path", "clear_state"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aws_lambda_powertools.shared.types.AnyCallableT", "id": -1, "name": "AnyCallableT", "namespace": "", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inject_lambda_context of Logger", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aws_lambda_powertools.shared.types.AnyCallableT", "id": -1, "name": "AnyCallableT", "namespace": "", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aws_lambda_powertools.shared.types.AnyCallableT", "id": -1, "name": "AnyCallableT", "namespace": "", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "lambda_handler", "log_event", "correlation_id_path", "clear_state"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger", {".class": "NoneType"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inject_lambda_context of Logger", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aws_lambda_powertools.shared.types.AnyCallableT", "id": -1, "name": "AnyCallableT", "namespace": "", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aws_lambda_powertools.shared.types.AnyCallableT", "id": -1, "name": "AnyCallableT", "namespace": "", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aws_lambda_powertools.shared.types.AnyCallableT", "id": -1, "name": "AnyCallableT", "namespace": "", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}]}}}, "log_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "aws_lambda_powertools.logging.logger.Logger.log_level", "name": "log_level", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "log_level of Logger", "ret_type": "builtins.int", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aws_lambda_powertools.logging.logger.Logger.log_level", "name": "log_level", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "log_level of Logger", "ret_type": "builtins.int", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "log_uncaught_exceptions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aws_lambda_powertools.logging.logger.Logger.log_uncaught_exceptions", "name": "log_uncaught_exceptions", "type": "builtins.bool"}}, "logger_formatter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aws_lambda_powertools.logging.logger.Logger.logger_formatter", "name": "logger_formatter", "type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aws_lambda_powertools.logging.logger.PowertoolsFormatter", "id": -1, "name": "PowertoolsFormatter", "namespace": "", "upper_bound": "aws_lambda_powertools.logging.formatter.BasePowertoolsFormatter", "values": [], "variance": 0}, {".class": "NoneType"}]}}}, "logger_handler": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aws_lambda_powertools.logging.logger.Logger.logger_handler", "name": "logger_handler", "type": "logging.Handler"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "aws_lambda_powertools.logging.logger.Logger.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of <PERSON><PERSON>", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aws_lambda_powertools.logging.logger.Logger.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of <PERSON><PERSON>", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "registered_formatter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "aws_lambda_powertools.logging.logger.Logger.registered_formatter", "name": "registered_formatter", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "registered_formatter of Logger", "ret_type": "aws_lambda_powertools.logging.formatter.BasePowertoolsFormatter", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aws_lambda_powertools.logging.logger.Logger.registered_formatter", "name": "registered_formatter", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "registered_formatter of Logger", "ret_type": "aws_lambda_powertools.logging.formatter.BasePowertoolsFormatter", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "registered_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "aws_lambda_powertools.logging.logger.Logger.registered_handler", "name": "registered_handler", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "registered_handler of Logger", "ret_type": "logging.Handler", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aws_lambda_powertools.logging.logger.Logger.registered_handler", "name": "registered_handler", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "registered_handler of Logger", "ret_type": "logging.Handler", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "removeFilter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "filter"], "dataclass_transform_spec": null, "flags": [], "fullname": "aws_lambda_powertools.logging.logger.Logger.removeFilter", "name": "removeFilter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "filter"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger", {".class": "TypeAliasType", "args": [], "type_ref": "logging._FilterType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "removeFilter of Logger", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "remove_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "keys"], "dataclass_transform_spec": null, "flags": [], "fullname": "aws_lambda_powertools.logging.logger.Logger.remove_keys", "name": "remove_keys", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "keys"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_keys of Logger", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "sampling_rate": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aws_lambda_powertools.logging.logger.Logger.sampling_rate", "name": "sampling_rate", "type": {".class": "UnionType", "items": ["builtins.float", "builtins.str"]}}}, "service": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aws_lambda_powertools.logging.logger.Logger.service", "name": "service", "type": "builtins.str"}}, "setLevel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "level"], "dataclass_transform_spec": null, "flags": [], "fullname": "aws_lambda_powertools.logging.logger.Logger.setLevel", "name": "setLevel", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "level"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger", {".class": "UnionType", "items": ["builtins.str", "builtins.int", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setLevel of Logger", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "set_correlation_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "flags": [], "fullname": "aws_lambda_powertools.logging.logger.Logger.set_correlation_id", "name": "set_correlation_id", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_correlation_id of <PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "structure_logs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "append", "formatter_options", "keys"], "dataclass_transform_spec": null, "flags": [], "fullname": "aws_lambda_powertools.logging.logger.Logger.structure_logs", "name": "structure_logs", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "append", "formatter_options", "keys"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "structure_logs of Logger", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "thread_safe_append_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "additional_keys"], "dataclass_transform_spec": null, "flags": [], "fullname": "aws_lambda_powertools.logging.logger.Logger.thread_safe_append_keys", "name": "thread_safe_append_keys", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "additional_keys"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "thread_safe_append_keys of <PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "thread_safe_clear_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "aws_lambda_powertools.logging.logger.Logger.thread_safe_clear_keys", "name": "thread_safe_clear_keys", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "thread_safe_clear_keys of Logger", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "thread_safe_get_current_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "aws_lambda_powertools.logging.logger.Logger.thread_safe_get_current_keys", "name": "thread_safe_get_current_keys", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "thread_safe_get_current_keys of Logger", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "thread_safe_remove_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "keys"], "dataclass_transform_spec": null, "flags": [], "fullname": "aws_lambda_powertools.logging.logger.Logger.thread_safe_remove_keys", "name": "thread_safe_remove_keys", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "keys"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "thread_safe_remove_keys of Logger", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "warning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 5, 5, 5, 5, 4], "arg_names": ["self", "msg", "args", "exc_info", "stack_info", "stacklevel", "extra", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "aws_lambda_powertools.logging.logger.Logger.warning", "name": "warning", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5, 5, 5, 5, 4], "arg_names": ["self", "msg", "args", "exc_info", "stack_info", "stacklevel", "extra", "kwargs"], "arg_types": ["aws_lambda_powertools.logging.logger.Logger", "builtins.object", "builtins.object", {".class": "TypeAliasType", "args": [], "type_ref": "logging._ExcInfoType"}, "builtins.bool", "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.object"], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}, "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "warning of <PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aws_lambda_powertools.logging.logger.Logger.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aws_lambda_powertools.logging.logger.Logger", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "PowertoolsFormatter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aws_lambda_powertools.logging.logger.PowertoolsFormatter", "name": "PowertoolsFormatter", "upper_bound": "aws_lambda_powertools.logging.formatter.BasePowertoolsFormatter", "values": [], "variance": 0}}, "RESERVED_FORMATTER_CUSTOM_KEYS": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.logging.formatter.RESERVED_FORMATTER_CUSTOM_KEYS", "kind": "Gdef"}, "SuppressFilter": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.logging.filters.SuppressFilter", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aws_lambda_powertools.logging.logger.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aws_lambda_powertools.logging.logger.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aws_lambda_powertools.logging.logger.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aws_lambda_powertools.logging.logger.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aws_lambda_powertools.logging.logger.__package__", "name": "__package__", "type": "builtins.str"}}, "_get_caller_filename": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "flags": [], "fullname": "aws_lambda_powertools.logging.logger._get_caller_filename", "name": "_get_caller_filename", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_caller_filename", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_is_cold_start": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "flags": [], "fullname": "aws_lambda_powertools.logging.logger._is_cold_start", "name": "_is_cold_start", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_cold_start", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "build_lambda_context_model": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.logging.lambda_context.build_lambda_context_model", "kind": "Gdef"}, "constants": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.shared.constants", "kind": "Gdef"}, "contextmanager": {".class": "SymbolTableNode", "cross_ref": "contextlib.contextmanager", "kind": "Gdef"}, "extract_event_from_common_models": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.shared.functions.extract_event_from_common_models", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef"}, "is_cold_start": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "aws_lambda_powertools.logging.logger.is_cold_start", "name": "is_cold_start", "type": "builtins.bool"}}, "jmespath_utils": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.utilities.jmespath_utils", "kind": "Gdef"}, "log_uncaught_exception_hook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["exc_type", "exc_value", "exc_traceback", "logger"], "dataclass_transform_spec": null, "flags": [], "fullname": "aws_lambda_powertools.logging.logger.log_uncaught_exception_hook", "name": "log_uncaught_exception_hook", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["exc_type", "exc_value", "exc_traceback", "logger"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "aws_lambda_powertools.logging.logger.Logger"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "log_uncaught_exception_hook", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "aws_lambda_powertools.logging.logger.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "random": {".class": "SymbolTableNode", "cross_ref": "random", "kind": "Gdef"}, "resolve_env_var_choice": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.shared.functions.resolve_env_var_choice", "kind": "Gdef"}, "resolve_truthy_env_var_choice": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.shared.functions.resolve_truthy_env_var_choice", "kind": "Gdef"}, "set_package_logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1], "arg_names": ["level", "stream", "formatter"], "dataclass_transform_spec": null, "flags": [], "fullname": "aws_lambda_powertools.logging.logger.set_package_logger", "name": "set_package_logger", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["level", "stream", "formatter"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.IO"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["logging.Formatter", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_package_logger", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\aws_lambda_powertools\\logging\\logger.py"}