{"data_mtime": 1759504514, "dep_lines": [36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 55, 65, 1486, 1549, 14, 36, 49, 50, 51, 52, 53, 54, 66, 1351, 7, 9, 10, 11, 12, 13, 15, 16, 30, 31, 35, 58, 59, 1362, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 20, 20, 20, 5, 20, 5, 5, 5, 5, 5, 5, 25, 20, 5, 10, 10, 10, 5, 10, 5, 5, 5, 5, 5, 25, 25, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._config", "pydantic._internal._decorators", "pydantic._internal._fields", "pydantic._internal._forward_ref", "pydantic._internal._generics", "pydantic._internal._mock_val_ser", "pydantic._internal._model_construction", "pydantic._internal._namespace_utils", "pydantic._internal._repr", "pydantic._internal._typing_extra", "pydantic._internal._utils", "pydantic.plugin._schema_validator", "pydantic.deprecated.parse", "pydantic.deprecated.copy_internals", "pydantic.deprecated.json", "collections.abc", "pydantic._internal", "pydantic._migration", "pydantic.aliases", "pydantic.annotated_handlers", "pydantic.config", "pydantic.errors", "pydantic.json_schema", "pydantic.fields", "pydantic.deprecated", "__future__", "operator", "sys", "types", "typing", "warnings", "copy", "functools", "pydantic_core", "typing_extensions", "pydantic", "inspect", "pathlib", "json", "builtins", "pyexpat.model", "pyexpat.errors", "_typeshed", "abc", "enum", "os", "pydantic._internal._generate_schema", "pydantic.plugin", "pydantic_core._pydantic_core", "re"], "hash": "a2bab34576f137d12a2ea810345827f6cb0cc6e9ca9d9f7e224dfccda2b5779b", "id": "pydantic.main", "ignore_all": true, "interface_hash": "781ed4ce652fa4582e9212959ff90133253cb70321705b566aa578435800241a", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\main.py", "plugin_data": null, "size": 80829, "suppressed": [], "version_id": "1.8.0"}