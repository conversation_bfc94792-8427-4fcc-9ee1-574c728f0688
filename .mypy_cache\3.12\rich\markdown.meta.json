{"data_mtime": 1759504511, "dep_lines": [7, 9, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 1, 3, 4, 6, 11, 685, 765, 766, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 20, 10, 10, 10, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["markdown_it.token", "rich.table", "rich.box", "rich._loop", "rich._stack", "rich.console", "rich.containers", "rich.jupyter", "rich.panel", "rich.rule", "rich.segment", "rich.style", "rich.syntax", "rich.text", "__future__", "sys", "typing", "markdown_it", "rich", "<PERSON><PERSON><PERSON><PERSON>", "io", "pydoc", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "_random", "_typeshed", "abc", "datetime", "enum", "markdown_it.main", "markdown_it.renderer", "markdown_it.utils", "pygments", "pygments.lexer", "rich.theme", "types", "typing_extensions"], "hash": "47a5ffd53331532de3de9d1f91b98fdc0623f2fb7d43bda3af8473ead76d494f", "id": "rich.markdown", "ignore_all": true, "interface_hash": "f3d93e0dee4fe2412e394fa8b3b463015ae8b14f276adce60ed57c38838baecb", "mtime": 1757092238, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\rich\\markdown.py", "plugin_data": null, "size": 25846, "suppressed": [], "version_id": "1.8.0"}