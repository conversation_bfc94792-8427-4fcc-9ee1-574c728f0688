{"data_mtime": 1759504547, "dep_lines": [26, 24, 25, 10, 12, 13, 14, 24, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 10, 5, 5, 20, 5, 20, 20, 30], "dependencies": ["sqlalchemy.util.typing", "sqlalchemy.exc", "sqlalchemy.util", "__future__", "contextlib", "enum", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "abc"], "hash": "e094fd9d4695d20f77b8697a523e7272caac049dd88fbc803922f1e44f7a63f7", "id": "sqlalchemy.orm.state_changes", "ignore_all": true, "interface_hash": "82b898f44be4cbe203bb11683b5977d9273782961309d1d966119ad3db1f9a90", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\state_changes.py", "plugin_data": null, "size": 7009, "suppressed": [], "version_id": "1.8.0"}