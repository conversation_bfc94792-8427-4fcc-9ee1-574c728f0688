{"data_mtime": 1759504547, "dep_lines": [60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 80, 84, 90, 94, 95, 96, 128, 133, 134, 29, 60, 81, 82, 26, 28, 30, 31, 32, 33, 34, 35, 36, 37, 81, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 10, 10, 10, 10, 10, 5, 10, 5, 5, 5, 25, 25, 25, 25, 25, 25, 25, 10, 20, 10, 5, 5, 10, 10, 5, 10, 10, 10, 10, 5, 5, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.base", "sqlalchemy.sql.coercions", "sqlalchemy.sql.crud", "sqlalchemy.sql.elements", "sqlalchemy.sql.functions", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.util", "sqlalchemy.sql._typing", "sqlalchemy.sql.visitors", "sqlalchemy.util.typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.ddl", "sqlalchemy.sql.dml", "sqlalchemy.sql.type_api", "sqlalchemy.engine.cursor", "sqlalchemy.engine.interfaces", "collections.abc", "sqlalchemy.sql", "sqlalchemy.exc", "sqlalchemy.util", "__future__", "collections", "contextlib", "enum", "functools", "itertools", "operator", "re", "time", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "_operator", "_typeshed", "abc", "sqlalchemy.engine", "sqlalchemy.engine.result", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql._py_util", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.traversals", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "sqlalchemy.util.preloaded", "types", "typing_extensions"], "hash": "102f321156b516784656c03752c9e12bf5b808816d89a0cb160598e93509d522", "id": "sqlalchemy.sql.compiler", "ignore_all": true, "interface_hash": "753095d02336c7ec75f75c951e4c110f7acbdc2552b6cc4d86628b4f497920a0", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\compiler.py", "plugin_data": null, "size": 291318, "suppressed": [], "version_id": "1.8.0"}