{"data_mtime": 1759504551, "dep_lines": [4, 1, 2, 10, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 5, 20, 20, 30, 30], "dependencies": ["aws_lambda_powertools.shared.version", "logging", "os", "botocore", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "typing"], "hash": "0eb08c150b93e1ae2222ba5c5a90088d8dfb105a91f7e4319710c60fe367f468", "id": "aws_lambda_powertools.shared.user_agent", "ignore_all": true, "interface_hash": "77e5eb1352616ad1e6f31d3080ad4fd8a58a4799eb1b0a2ad71b84ee5a6e1cb2", "mtime": 1757091873, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\aws_lambda_powertools\\shared\\user_agent.py", "plugin_data": null, "size": 7081, "suppressed": [], "version_id": "1.8.0"}