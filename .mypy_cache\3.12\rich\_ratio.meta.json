{"data_mtime": 1759503829, "dep_lines": [1, 2, 3, 144, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 20, 20, 30, 30], "dependencies": ["fractions", "math", "typing", "dataclasses", "builtins", "pyexpat.model", "pyexpat.errors", "_typeshed", "abc"], "hash": "20eb65efcb1009866c987cb185ee3992b91bebcbbdc55cfbcc5b175490fe6f66", "id": "rich._ratio", "ignore_all": true, "interface_hash": "052715746cf71222b337ede3f9c4a8abf3ec06408871f4edfb719f7e6ff12566", "mtime": 1757092238, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\rich\\_ratio.py", "plugin_data": null, "size": 5325, "suppressed": [], "version_id": "1.8.0"}