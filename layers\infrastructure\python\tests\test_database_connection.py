"""Tests for database connection and session management."""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from sqlalchemy.ext.asyncio import AsyncEngine

from smartanalytics_infrastructure.database.connection import (
    DatabaseConnection,
    get_database_connection
)


class TestDatabaseConnection:
    """Test database connection functionality."""

    @pytest.fixture
    def mock_database_config(self):
        """Mock database configuration."""
        config = Mock()
        config.host = "test-host.amazonaws.com"
        config.port = 5432
        config.database_name = "testdb"
        config.schema_name = "memo"
        config.username = "testuser"
        config.password = "testpass"
        config.use_iam_auth = False
        config.ssl_mode = "require"
        config.connection_timeout = 30
        config.query_timeout = 300
        return config

    def test_database_connection_initialization(self, mock_database_config):
        """Test DatabaseConnection initialization."""
        db_conn = DatabaseConnection(mock_database_config, "us-east-1")

        assert db_conn.config == mock_database_config
        assert db_conn.region == "us-east-1"
        assert db_conn._engine is None

    def test_get_database_connection_singleton(self):
        """Test that get_database_connection returns singleton instance."""
        conn1 = get_database_connection()
        conn2 = get_database_connection()

        # Should return the same instance (singleton pattern)
        assert conn1 is conn2
        assert isinstance(conn1, DatabaseConnection)

    @pytest.mark.asyncio
    @patch.object(DatabaseConnection, 'connect')
    async def test_database_connection_connect(self, mock_connect, mock_database_config):
        """Test database connection connect method."""
        # Setup mock
        mock_connect.return_value = None

        db_conn = DatabaseConnection(mock_database_config, "us-east-1")

        # Test connect
        await db_conn.connect()

        # Verify connect was called
        mock_connect.assert_called_once()

    @pytest.mark.asyncio
    @patch.object(DatabaseConnection, 'test_connection')
    async def test_database_connection_test(self, mock_test, mock_database_config):
        """Test database connection test method."""
        # Setup mock
        mock_test.return_value = True

        db_conn = DatabaseConnection(mock_database_config, "us-east-1")

        # Test connection test
        result = await db_conn.test_connection()

        # Verify test was called and returned True
        assert result is True
        mock_test.assert_called_once()

    @pytest.mark.asyncio
    @patch.object(DatabaseConnection, 'close')
    async def test_database_connection_close(self, mock_close, mock_database_config):
        """Test database connection close method."""
        # Setup mock
        mock_close.return_value = None

        db_conn = DatabaseConnection(mock_database_config, "us-east-1")

        # Test close
        await db_conn.close()

        # Verify close was called
        mock_close.assert_called_once()
