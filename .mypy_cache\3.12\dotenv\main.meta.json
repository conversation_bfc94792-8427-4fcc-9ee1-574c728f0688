{"data_mtime": 1759504508, "dep_lines": [12, 13, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 20, 20, 30, 30], "dependencies": ["dotenv.parser", "dotenv.variables", "io", "logging", "os", "pathlib", "shutil", "sys", "tempfile", "collections", "contextlib", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "_typeshed", "abc"], "hash": "1c98244b45d971dd1fd9565a546c6551cace121a8172643a2f3f6142b31f6aeb", "id": "dotenv.main", "ignore_all": true, "interface_hash": "36b0ea3e189d93e4c9a1250d137f97d28a0800a28ac77c4b2a9a1dca7a87265d", "mtime": 1757091871, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\dotenv\\main.py", "plugin_data": null, "size": 12467, "suppressed": [], "version_id": "1.8.0"}