{"data_mtime": 1759504508, "dep_lines": [18, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 30, 30], "dependencies": ["markdown_it.rules_inline.emphasis", "markdown_it.rules_inline.strikethrough", "markdown_it.rules_inline.autolink", "markdown_it.rules_inline.backticks", "markdown_it.rules_inline.balance_pairs", "markdown_it.rules_inline.entity", "markdown_it.rules_inline.escape", "markdown_it.rules_inline.fragments_join", "markdown_it.rules_inline.html_inline", "markdown_it.rules_inline.image", "markdown_it.rules_inline.link", "markdown_it.rules_inline.linkify", "markdown_it.rules_inline.newline", "markdown_it.rules_inline.state_inline", "markdown_it.rules_inline.text", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "typing"], "hash": "aaa1d993af9813c45cd76aba3f1bd52816b1bf6c2665e7a8e391f55cc47f571b", "id": "markdown_it.rules_inline", "ignore_all": true, "interface_hash": "3cbb49dea354d0819609a08c4eb313cf3fc15a26b633b763b3a717f3e71a4865", "mtime": 1757092234, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\markdown_it\\rules_inline\\__init__.py", "plugin_data": null, "size": 696, "suppressed": [], "version_id": "1.8.0"}