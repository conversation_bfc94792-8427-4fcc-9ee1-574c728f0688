{"data_mtime": 1759503866, "dep_lines": [43, 44, 45, 46, 47, 48, 52, 54, 64, 69, 79, 96, 98, 100, 104, 108, 115, 120, 43, 74, 75, 76, 113, 14, 16, 17, 18, 19, 20, 21, 74, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 10, 5, 5, 5, 5, 5, 5, 25, 25, 25, 25, 25, 25, 25, 20, 10, 10, 5, 25, 5, 5, 5, 10, 10, 10, 5, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.coercions", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.visitors", "sqlalchemy.util.typing", "sqlalchemy.sql.compiler", "sqlalchemy.sql.functions", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.sql.sqltypes", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.result", "sqlalchemy.sql", "sqlalchemy.exc", "sqlalchemy.inspection", "sqlalchemy.util", "sqlalchemy.engine", "__future__", "decimal", "enum", "itertools", "operator", "re", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "_decimal", "_typeshed", "abc", "sqlalchemy.engine.base", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.sql._py_util", "sqlalchemy.sql.lambdas", "sqlalchemy.util._collections", "sqlalchemy.util._py_collections", "sqlalchemy.util.deprecations", "sqlalchemy.util.langhelpers", "sqlalchemy.util.preloaded", "types", "typing_extensions"], "hash": "180251f2f17ec67639d5500ea17d521eddc9ab1ad75a6a20ffad2d8f5c525abe", "id": "sqlalchemy.sql.elements", "ignore_all": true, "interface_hash": "ba8897c5027a98472f567b9933e705253866e10cac9bd9b90217ef614d39a9dd", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\elements.py", "plugin_data": null, "size": 183870, "suppressed": [], "version_id": "1.8.0"}