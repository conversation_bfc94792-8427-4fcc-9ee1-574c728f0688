{"data_mtime": 1759503830, "dep_lines": [5, 17, 1, 2, 3, 4, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 20, 20, 30, 30], "dependencies": ["urllib.parse", "urllib.request", "builtins", "collections", "typing", "typing_extensions", "pyexpat.model", "pyexpat.errors", "_collections_abc", "abc"], "hash": "2b42ea53dceb790f35aca1bb0847973163d51194abc751467d527495a4bdbc63", "id": "requests.compat", "ignore_all": true, "interface_hash": "24649194bb038a354a0819ad80a32cb6b44e74dbe103bf46ad6cdfc967493077", "mtime": 1757363991, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\requests-stubs\\compat.pyi", "plugin_data": null, "size": 806, "suppressed": [], "version_id": "1.8.0"}