{"data_mtime": 1759503828, "dep_lines": [5, 6, 7, 8, 9, 4, 1, 2, 62, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic.v1.config", "pydantic.v1.errors", "pydantic.v1.main", "pydantic.v1.typing", "pydantic.v1.utils", "pydantic.v1", "functools", "typing", "inspect", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "_typeshed", "abc", "enum", "pydantic.v1.class_validators", "types"], "hash": "cda6b1c71a163c29bcd7c0f56ecd3285aa518d79b7d95f06d191d609d335b970", "id": "pydantic.v1.decorator", "ignore_all": true, "interface_hash": "d4ffe1b8a3b30ce255bcf97a405ee32d5c3423cff0f19db923eb82e3f64f4d1a", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\v1\\decorator.py", "plugin_data": null, "size": 10339, "suppressed": [], "version_id": "1.8.0"}