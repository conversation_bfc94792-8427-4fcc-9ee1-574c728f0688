{"data_mtime": 1759504511, "dep_lines": [5, 8, 21, 22, 23, 79, 80, 1, 2, 3, 76, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 20, 5, 5, 20, 5, 5, 5, 5, 5, 10, 5, 20, 20, 30, 30, 30, 30, 30, 30], "dependencies": ["rich.color_triplet", "rich.table", "rich.color", "rich.style", "rich.text", "rich.console", "rich.segment", "math", "functools", "typing", "colorsys", "builtins", "pyexpat.model", "pyexpat.errors", "_random", "abc", "datetime", "enum", "rich.jupyter", "rich.theme"], "hash": "02be9952b607885b7af91af693e93d17c57b87180960735daa3936bd55ec2e07", "id": "rich.palette", "ignore_all": true, "interface_hash": "38147e55619cbb0f9b8ede760a27c01d454e9e23b96710790955ba9e5439da5f", "mtime": 1757092238, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\rich\\palette.py", "plugin_data": null, "size": 3288, "suppressed": [], "version_id": "1.8.0"}