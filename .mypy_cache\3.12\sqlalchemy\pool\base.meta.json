{"data_mtime": 1759504547, "dep_lines": [35, 39, 45, 31, 32, 33, 34, 11, 13, 14, 15, 16, 17, 18, 29, 31, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 25, 25, 10, 10, 10, 10, 5, 5, 10, 5, 10, 10, 5, 10, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.util.typing", "sqlalchemy.engine.interfaces", "sqlalchemy.sql._typing", "sqlalchemy.event", "sqlalchemy.exc", "sqlalchemy.log", "sqlalchemy.util", "__future__", "collections", "dataclasses", "enum", "threading", "time", "typing", "weakref", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "_weakref", "abc", "logging", "sqlalchemy.engine", "sqlalchemy.event.api", "sqlalchemy.event.attr", "sqlalchemy.event.base", "sqlalchemy.event.registry", "sqlalchemy.util.deprecations", "sqlalchemy.util.langhelpers"], "hash": "07e2998fd0c73c85a89b150b7f4842a3be25d3e26e8b3c7af0cb9202b2edbb48", "id": "sqlalchemy.pool.base", "ignore_all": true, "interface_hash": "d4c2d694b6ed03bf86c4373952f0fb9013ab6a1bd57bb3dbf1028db264a99a29", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\pool\\base.py", "plugin_data": null, "size": 53899, "suppressed": [], "version_id": "1.8.0"}