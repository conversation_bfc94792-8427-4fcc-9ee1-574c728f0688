# ACD Event Processor Lambda

AWS Lambda function for processing ACD (Automatic Call Distribution) agent events from SQS queues using a clean command/handler architecture.

## Overview

This Lambda function processes XML agent events from SQS queues and stores them in a PostgreSQL database using the Command/Handler pattern (CQRS-lite). It features dimensional modeling with SCD Type 2 patterns, duplicate detection, and comprehensive error handling.

## Features

### Core Functionality
- **Command/Handler Pattern**: Clean separation of concerns with CQRS-lite architecture
- **XML Processing**: Parses XML events using xmltodict with security features
- **Strong Typing**: Full type safety with mypy and frozen dataclasses
- **Dimensional Modeling**: Tenant, Agent, and Ring Group dimensions with SCD Type 2
- **Duplicate Detection**: SHA-256 hash-based deduplication using database unique constraints
- **Batch Processing**: Processes multiple SQS messages efficiently
- **Nested Data Extraction**: Extracts event-specific data from nested XML elements

### Quality & Monitoring
- **Comprehensive Testing**: Unit tests with mocks and integration tests with real database
- **AWS Lambda Powertools**: Structured logging, tracing, and metrics
- **Error Handling**: Graceful error handling with detailed logging
- **Type Safety**: 100% mypy coverage with strict type checking
- **Code Quality**: Ruff formatting and linting, Bandit security scanning

_## Architecture

### Lambda Layers
This Lambda uses **3 Lambda layers** for shared code and dependencies:

1. **Utilities Layer** (~15 MB)
   - `pydantic`, `pydantic-settings` - Configuration management
   - `aws-lambda-powertools` - Logging, tracing, metrics
   - `xmltodict` - XML parsing
   - `pytz`, `python-dateutil` - Timezone handling
   - Custom helpers: `datetime_helper`, `hash_helper`

2. **Domain Layer** (~50 KB - pure Python)
   - Business logic (commands, handlers, services)
   - Domain models and value objects
   - Repository interfaces (ports)
   - Domain exceptions

3. **Infrastructure Layer** (~4 MB)
   - `asyncpg` - Async PostgreSQL driver
   - `sqlalchemy[asyncio]` - ORM with async support
   - `psycopg2-binary` - PostgreSQL adapter
   - `tenacity` - Retry logic
   - Repository implementations
   - Database connection management

**Lambda Package**: Only contains `lambda_function.py` (~10 KB) - all dependencies come from layers!

### High-Level Flow
```
SQS Queue → Lambda Handler → Command → Command Handler → Database
                                ↓
                         Sub-Commands (Dimensions)
```

### Command/Handler Pattern
```
ProcessAgentEventCommand
    ↓
ProcessAgentEventCommandHandler (orchestrator)
    ├─→ GetOrCreateTenantCommandHandler
    ├─→ GetOrCreateAgentCommandHandler
    └─→ GetOrCreateRingGroupCommandHandler
```

### Event Types Supported
1. **Login** - Agent workstation login
2. **Logout** - Agent workstation logout
3. **ACDLogin** - Agent joins ACD queue
4. **ACDLogout** - Agent leaves ACD queue
5. **AgentAvailable** - Agent becomes available
6. **AgentBusiedOut** - Agent becomes busy

## 🚀 Quick Start

### Prerequisites

- Python 3.12+
- AWS CLI configured
- Docker (for local testing)

### Installation

1. **Install dependencies**:
```bash
cd lambdas/acd-event-processor
pip install -r requirements.txt
pip install -r requirements-dev.txt
```

2. **Install layer dependencies for local development**:
```bash
# Install layer dependencies locally for development/testing
pip install -r ../../layers/utilities/requirements.txt
pip install -r ../../layers/infrastructure/requirements.txt
# Domain layer has no external dependencies
```

**Note**: In production, dependencies come from Lambda layers automatically.

### Local Testing

1. **Run unit tests**:
```bash
pytest tests/ -v --cov=src
```

2. **Type checking**:
```bash
mypy src/
```

3. **Code formatting**:
```bash
ruff format src/ tests/
ruff check src/ tests/ --fix
```

4. **Security scanning**:
```bash
bandit -r src/
```

5. **Test database connection**:
```bash
# Test both password and IAM authentication
python test_rds_proxy_connection.py
```

6. **Run integration tests** (requires database):
```bash
# Test all event types
python integration_test.py --all

# Test specific event
python integration_test.py --event AgentAvailable

# List available events
python integration_test.py --list
```

## 📋 Configuration

The Lambda uses environment variables for configuration:

### Database Configuration

#### Option 1: Password Authentication
```bash
DATABASE_HOST=your-aurora-endpoint.cluster-xxxxx.us-east-1.rds.amazonaws.com
DATABASE_PORT=5432
DATABASE_DATABASE_NAME=postgres
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=your-password
DATABASE_USE_IAM_AUTH=false
```

#### Option 2: RDS Proxy with IAM Authentication (Recommended)
```bash
DATABASE_HOST=your-rds-proxy-endpoint.proxy-xxxxx.us-east-1.rds.amazonaws.com
DATABASE_PORT=5432
DATABASE_DATABASE_NAME=postgres
DATABASE_USERNAME=postgres
DATABASE_USE_IAM_AUTH=true
AWS_REGION=us-east-1
DATABASE_SCHEMA_NAME=memo
DATABASE_SSL_MODE=require
```

**RDS Proxy Benefits**:
- ✅ **Connection pooling**: Reduces database connection overhead
- ✅ **IAM authentication**: No password management required
- ✅ **Automatic failover**: Built-in high availability
- ✅ **Security**: Encrypted connections with IAM-based access
- ✅ **Lambda optimization**: Designed for serverless workloads

### Application Configuration
_```bash
ENVIRONMENT=staging  # dev, staging, production
CLIENT_TIMEZONE=America/Chicago
LOG_LEVEL=INFO  # DEBUG, INFO, WARNING, ERROR
```

### AWS Configuration (auto-set by Lambda)
```bash
AWS_REGION=us-east-1
```

## 📊 Event Format

The Lambda expects SQS messages with XML payloads in the following format:

```xml
<LogEvent xmlns="http://solacom.com/Logging">
    <timestamp>2025-10-02T14:30:00Z</timestamp>
    <agencyOrElement>TestTenant</agencyOrElement>
    <agent>agent1</agent>
    <eventType>AgentAvailable</eventType>
    <agentAvailable>
        <mediaLabel>_ML_12345@TestTenant</mediaLabel>
    </agentAvailable>
</LogEvent>
```

### Supported Event Types
- **Login** - Agent workstation login
- **Logout** - Agent workstation logout
- **ACDLogin** - Agent joins ACD queue (includes ring group)
- **ACDLogout** - Agent leaves ACD queue
- **AgentAvailable** - Agent becomes available (includes media label)
- **AgentBusiedOut** - Agent becomes busy (includes action, duration, reason code)

## 🔄 Processing Flow

1. **Event Validation**: Validate SQS event structure and payload format
2. **Transformation**: Convert SQS payload to domain events
3. **Dimension Upsert**: Get or create tenant, agent, ring group records
4. **Event Persistence**: Insert event with foreign key references
5. **Deduplication**: Handle duplicate events gracefully
6. **Response**: Return processing results with metrics

## 📈 Monitoring

The Lambda emits CloudWatch metrics:

- `EventsProcessed` - Total events processed
- `EventsSuccessful` - Successfully processed events
- `EventsFailed` - Failed events
- `EventsDuplicate` - Duplicate events detected
- `ProcessingTime` - Processing time in milliseconds
- `SuccessRate` - Success rate percentage

## 🧪 Testing Strategy

### Unit Tests (82 tests, 74% coverage)
- **Domain Layer**: Command handlers, entities, exceptions
- **Utilities Layer**: Hash helpers, datetime helpers, XML parsing
- **Lambda Handler**: Event processing with mocked dependencies

### Integration Tests
- **Professional Test Script**: `integration_test.py`
  - Tests all 6 event types
  - Dynamic event generation (avoids duplicates)
  - Command-line interface
  - Database verification
  - Schema recreation option

### Test Coverage
```bash
# Run all tests with coverage
pytest layers/domain/python/tests/ layers/utilities/python/tests/ lambdas/acd-event-processor/tests/ \
  --cov=layers/domain/python/smartanalytics_domain \
  --cov=layers/utilities/python/smartanalytics_utilities \
  --cov=lambdas/acd-event-processor/src \
  --cov-report=term-missing -v
```

## 🚀 Deployment

### Using AWS SAM
```bash
sam build
sam deploy --guided
```

### Using Terraform
```hcl
resource "aws_lambda_function" "acd_event_processor" {
  filename         = "acd-event-processor.zip"
  function_name    = "acd-event-processor"
  role            = aws_iam_role.lambda_role.arn
  handler         = "lambda_function.lambda_handler"
  runtime         = "python3.12"
  timeout         = 300
  memory_size     = 512
  
  environment {
    variables = {
      DB_HOST = var.database_host
      LOG_LEVEL = "INFO"
    }
  }
}
```

### Environment-Specific Deployments
- **Dev**: Lower memory, debug logging
- **Staging**: Production-like configuration
- **Prod**: Optimized memory, error-only logging

## 🔧 Development

### Pre-commit Hooks
```bash
pre-commit install
pre-commit run --all-files
```

### Adding New Event Types
1. Update `EventType` enum in domain layer
2. Add validation in `AgentEventPayload.validate_event_type()`
3. Update tests and documentation

### Performance Optimization
- **Connection Pooling**: Reuse database connections
- **Batch Processing**: Process multiple events per invocation
- **Async Operations**: Non-blocking database operations

## 📚 Related Documentation

- [Clean Architecture Guide](../../CLEAN_ARCHITECTURE.md)
- [Layer Usage Guide](../../LAYER_USAGE_GUIDE.md)
- [Domain Models](../../layers/domain/python/README.md)
- [Infrastructure Layer](../../layers/infrastructure/python/README.md)
