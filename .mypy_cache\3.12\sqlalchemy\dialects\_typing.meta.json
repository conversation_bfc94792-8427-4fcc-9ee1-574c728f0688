{"data_mtime": 1759504547, "dep_lines": [15, 16, 17, 15, 7, 9, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 20, 5, 5, 5, 20, 20, 30], "dependencies": ["sqlalchemy.sql.roles", "sqlalchemy.sql.base", "sqlalchemy.sql.schema", "sqlalchemy.sql", "__future__", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "abc"], "hash": "754f01d9a65c07133dceaeed7e2e1923ea35ddda1a81f80bf88b365c3aca5deb", "id": "sqlalchemy.dialects._typing", "ignore_all": true, "interface_hash": "bf8975f6bd06d93ad7e1cc5627336db9cd6a2d929b0fa65104323bb04ef8a52c", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\_typing.py", "plugin_data": null, "size": 1001, "suppressed": [], "version_id": "1.8.0"}