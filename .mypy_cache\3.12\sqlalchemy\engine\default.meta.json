{"data_mtime": 1759503866, "dep_lines": [42, 43, 44, 45, 51, 57, 58, 59, 60, 61, 62, 63, 67, 68, 88, 89, 100, 42, 53, 54, 55, 56, 57, 17, 19, 20, 21, 22, 23, 24, 40, 53, 72, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 5, 5, 10, 10, 10, 10, 5, 5, 5, 5, 25, 25, 25, 20, 10, 10, 10, 10, 20, 5, 10, 10, 10, 10, 5, 5, 10, 20, 25, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.engine.characteristics", "sqlalchemy.engine.cursor", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.base", "sqlalchemy.engine.reflection", "sqlalchemy.sql.compiler", "sqlalchemy.sql.dml", "sqlalchemy.sql.expression", "sqlalchemy.sql.type_api", "sqlalchemy.sql.util", "sqlalchemy.sql._typing", "sqlalchemy.sql.base", "sqlalchemy.sql.elements", "sqlalchemy.util.typing", "sqlalchemy.engine.row", "sqlalchemy.engine.url", "sqlalchemy.sql.schema", "sqlalchemy.engine", "sqlalchemy.event", "sqlalchemy.exc", "sqlalchemy.pool", "sqlalchemy.util", "sqlalchemy.sql", "__future__", "functools", "operator", "random", "re", "time", "typing", "weakref", "sqlalchemy", "types", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "_operator", "_typeshed", "abc", "enum", "sqlalchemy.engine._py_row", "sqlalchemy.engine.result", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.pool.base", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.ddl", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.selectable", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util._py_collections", "sqlalchemy.util.deprecations", "sqlalchemy.util.langhelpers", "typing_extensions"], "hash": "badc8850e1cd87e369bf5e30d403cf123f91d6ea28aff22607a892c429ec31e8", "id": "sqlalchemy.engine.default", "ignore_all": true, "interface_hash": "10effa2ab2f423086eb276489cabb6659f52df36b06d7c214b57a2781aed7ef5", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\default.py", "plugin_data": null, "size": 88110, "suppressed": [], "version_id": "1.8.0"}