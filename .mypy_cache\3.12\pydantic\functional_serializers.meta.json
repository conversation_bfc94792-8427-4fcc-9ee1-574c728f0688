{"data_mtime": 1759503836, "dep_lines": [14, 14, 9, 14, 15, 3, 5, 6, 7, 9, 11, 13, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 20, 5, 5, 10, 5, 5, 5, 5, 5, 5, 20, 20, 30, 30], "dependencies": ["pydantic._internal._decorators", "pydantic._internal._internal_dataclass", "pydantic_core.core_schema", "pydantic._internal", "pydantic.annotated_handlers", "__future__", "dataclasses", "functools", "typing", "pydantic_core", "typing_extensions", "pydantic", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "pydantic_core._pydantic_core"], "hash": "de6f35ba71f7958a2f74c8b4d28672c251e19f5283cfd5f608ede24ed0726ba0", "id": "pydantic.functional_serializers", "ignore_all": true, "interface_hash": "13e6b6e4897ab4eca71d78475237fd94a23aae31a268fd9f52d38d73287d89a7", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\functional_serializers.py", "plugin_data": null, "size": 17102, "suppressed": [], "version_id": "1.8.0"}