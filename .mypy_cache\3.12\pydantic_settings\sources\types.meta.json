{"data_mtime": 1759503836, "dep_lines": [10, 5, 11, 3, 6, 7, 1, 1, 1, 1, 1], "dep_prios": [25, 5, 25, 5, 5, 5, 5, 20, 20, 30, 30], "dependencies": ["pydantic._internal._dataclasses", "collections.abc", "pydantic.main", "__future__", "pathlib", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "os"], "hash": "874140f133143028f684f31c03a56a65d7487dfa0b6d7c5a0822a9703a398973", "id": "pydantic_settings.sources.types", "ignore_all": true, "interface_hash": "3cf8c46e6c93730aa37c2fea846c179da47d8549c741ebffe8509c1bcaf3a98e", "mtime": 1757091892, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic_settings\\sources\\types.py", "plugin_data": null, "size": 1554, "suppressed": [], "version_id": "1.8.0"}