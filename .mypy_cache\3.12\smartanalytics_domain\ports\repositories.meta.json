{"data_mtime": 1759503830, "dep_lines": [5, 6, 7, 8, 3, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 20, 20, 30, 30], "dependencies": ["smartanalytics_domain.models.agent", "smartanalytics_domain.models.agent_event", "smartanalytics_domain.models.ring_group", "smartanalytics_domain.models.tenant", "abc", "builtins", "pyexpat.model", "pyexpat.errors", "smartanalytics_domain.models", "typing"], "hash": "01c8d6942a21944263e8cf1e293c17a1550b5ff702daf5b8650f33f3044016a1", "id": "smartanalytics_domain.ports.repositories", "ignore_all": false, "interface_hash": "e27c6b660cd95687f9a740f75c00f17b4ca2479b2680d1f01f9453a15c320484", "mtime": 1759502092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "layers\\domain\\python\\smartanalytics_domain\\ports\\repositories.py", "plugin_data": null, "size": 4138, "suppressed": [], "version_id": "1.8.0"}