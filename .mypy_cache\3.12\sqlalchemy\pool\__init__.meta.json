{"data_mtime": 1759503866, "dep_lines": [20, 21, 36, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 20, 20, 30, 30], "dependencies": ["sqlalchemy.pool.events", "sqlalchemy.pool.base", "sqlalchemy.pool.impl", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "typing"], "hash": "56a968ada41a3f6cadd8c31f7348493b9d6c33b2ae1c41f2029b83be1d854442", "id": "sqlalchemy.pool", "ignore_all": true, "interface_hash": "9712f0a724dd7b0100d2b1412411155a37d44ab04e9bcd6ce81ba929bc77609d", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\pool\\__init__.py", "plugin_data": null, "size": 1848, "suppressed": [], "version_id": "1.8.0"}