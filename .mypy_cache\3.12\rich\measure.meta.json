{"data_mtime": 1759504511, "dep_lines": [4, 5, 8, 1, 2, 4, 1, 1, 1, 1], "dep_prios": [10, 5, 25, 5, 5, 20, 5, 20, 20, 30], "dependencies": ["rich.errors", "rich.protocol", "rich.console", "operator", "typing", "rich", "builtins", "pyexpat.model", "pyexpat.errors", "abc"], "hash": "1e6ac8257f2c5914c76e087c33111acbff37564a8d5bfef4b3c68a3f965c608f", "id": "rich.measure", "ignore_all": true, "interface_hash": "5891d631ba2ee98efafcc2efe04c6705c1abb88337acbf27e6cbe384256b1345", "mtime": 1757092238, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\rich\\measure.py", "plugin_data": null, "size": 5305, "suppressed": [], "version_id": "1.8.0"}