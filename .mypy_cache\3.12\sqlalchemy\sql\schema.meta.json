{"data_mtime": 1759504547, "dep_lines": [57, 58, 59, 60, 61, 62, 73, 78, 86, 94, 102, 105, 110, 113, 57, 81, 82, 83, 84, 108, 31, 33, 34, 35, 36, 37, 81, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 5, 5, 5, 5, 5, 25, 25, 25, 25, 25, 20, 10, 10, 10, 5, 25, 5, 5, 10, 5, 10, 5, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.coercions", "sqlalchemy.sql.ddl", "sqlalchemy.sql.roles", "sqlalchemy.sql.type_api", "sqlalchemy.sql.visitors", "sqlalchemy.sql.base", "sqlalchemy.sql.elements", "sqlalchemy.sql.selectable", "sqlalchemy.util.typing", "sqlalchemy.sql._typing", "sqlalchemy.sql.compiler", "sqlalchemy.sql.functions", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.reflection", "sqlalchemy.sql", "sqlalchemy.event", "sqlalchemy.exc", "sqlalchemy.inspection", "sqlalchemy.util", "sqlalchemy.engine", "__future__", "abc", "collections", "enum", "operator", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "_typeshed", "sqlalchemy.engine.base", "sqlalchemy.engine.mock", "sqlalchemy.event.api", "sqlalchemy.event.attr", "sqlalchemy.event.base", "sqlalchemy.event.registry", "sqlalchemy.log", "sqlalchemy.sql._py_util", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.operators", "sqlalchemy.sql.traversals", "sqlalchemy.util._collections", "sqlalchemy.util._py_collections", "sqlalchemy.util.deprecations", "sqlalchemy.util.langhelpers", "sqlalchemy.util.preloaded", "types", "typing_extensions"], "hash": "4362cfa30eaaee268540e1e26c166298327e380fd1ae28284612bdeff24ee3d5", "id": "sqlalchemy.sql.schema", "ignore_all": true, "interface_hash": "9134a9f4918f67686025d9b7cac184f595171b3e155c6aff82ff8ba7e4b97748", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\schema.py", "plugin_data": null, "size": 236619, "suppressed": [], "version_id": "1.8.0"}