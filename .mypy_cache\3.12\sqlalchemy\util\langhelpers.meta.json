{"data_mtime": 1759503866, "dep_lines": [48, 49, 50, 51, 48, 52, 13, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 27, 46, 52, 1, 1, 1, 1, 1, 1, 1, 1, 67], "dep_prios": [10, 10, 5, 5, 20, 10, 5, 10, 10, 5, 10, 10, 10, 10, 10, 10, 10, 5, 5, 10, 20, 5, 20, 20, 30, 30, 30, 30, 30, 5], "dependencies": ["sqlalchemy.util._collections", "sqlalchemy.util.compat", "sqlalchemy.util._has_cy", "sqlalchemy.util.typing", "sqlalchemy.util", "sqlalchemy.exc", "__future__", "collections", "enum", "functools", "inspect", "itertools", "operator", "re", "sys", "textwrap", "threading", "types", "typing", "warnings", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "_typeshed", "abc", "sqlalchemy.util._py_collections", "typing_extensions"], "hash": "d2e2915ad7a53ff55002b4b96ba2de8bd8965d22d16853bf54983f5a67df7e29", "id": "sqlalchemy.util.langhelpers", "ignore_all": true, "interface_hash": "608af151d69e4f14c774b965f7d8290afdac2a6d4701fb828c48ad90d6b18cc3", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\langhelpers.py", "plugin_data": null, "size": 70674, "suppressed": ["annotationlib"], "version_id": "1.8.0"}