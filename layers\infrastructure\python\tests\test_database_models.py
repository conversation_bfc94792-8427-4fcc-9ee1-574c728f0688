"""Tests for database models and schema definitions."""

import pytest
from datetime import datetime, timezone
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import IntegrityError

from smartanalytics_infrastructure.database.models import (
    Base,
    DimTenant,
    DimAgent,
    DimRingGroup,
    DimAgentEvent,
    metadata
)


class TestDatabaseModels:
    """Test database model definitions and relationships."""

    @pytest.fixture
    def engine(self):
        """Create in-memory SQLite engine for testing."""
        engine = create_engine("sqlite:///:memory:")
        Base.metadata.create_all(engine)
        return engine

    @pytest.fixture
    def session(self, engine):
        """Create database session for testing."""
        Session = sessionmaker(bind=engine)
        session = Session()
        yield session
        session.close()

    def test_metadata_schema_configuration(self):
        """Test that metadata has correct schema configuration."""
        assert metadata.schema == "memo"

    def test_all_expected_tables_exist(self):
        """Test that all expected tables are defined in metadata."""
        table_names = {table.name for table in Base.metadata.tables.values()}
        expected_tables = {
            "dim_tenant",
            "dim_agent", 
            "dim_ring_group",
            "dim_agent_event"
        }
        assert expected_tables.issubset(table_names)

    def test_dim_tenant_model_creation(self, session):
        """Test DimTenant model creation and field validation."""
        now = datetime.now(timezone.utc)
        future = datetime(9999, 12, 31, 23, 59, 59, tzinfo=timezone.utc)
        
        tenant = DimTenant(
            tenant_name="TestTenant",
            effective_date=now,
            expiry_date=future,
            is_current=True
        )
        session.add(tenant)
        session.commit()
        
        # Verify auto-generated fields
        assert tenant.tenant_key is not None
        assert tenant.created_at_utc is not None
        assert tenant.updated_at_utc is not None
        
        # Verify field values
        assert tenant.tenant_name == "TestTenant"
        assert tenant.effective_date == now
        assert tenant.expiry_date == future
        assert tenant.is_current is True

    def test_dim_tenant_unique_constraint(self, session):
        """Test that tenant names must be unique for current records."""
        now = datetime.now(timezone.utc)
        future = datetime(9999, 12, 31, 23, 59, 59, tzinfo=timezone.utc)
        
        # Create first tenant
        tenant1 = DimTenant(
            tenant_name="DuplicateTenant",
            effective_date=now,
            expiry_date=future,
            is_current=True
        )
        session.add(tenant1)
        session.commit()
        
        # Try to create duplicate current tenant (should fail)
        tenant2 = DimTenant(
            tenant_name="DuplicateTenant",
            effective_date=now,
            expiry_date=future,
            is_current=True
        )
        session.add(tenant2)
        
        with pytest.raises(IntegrityError):
            session.commit()

    def test_dim_agent_model_creation(self, session):
        """Test DimAgent model creation with foreign key relationship."""
        # Create tenant first
        tenant = DimTenant(
            tenant_name="TestTenant",
            effective_date=datetime.now(timezone.utc),
            expiry_date=datetime(9999, 12, 31, 23, 59, 59, tzinfo=timezone.utc),
            is_current=True
        )
        session.add(tenant)
        session.flush()

        # Create agent
        agent = DimAgent(
            tenant_key=tenant.tenant_key,
            agent_name="TestAgent001",
            effective_date=datetime.now(timezone.utc),
            expiry_date=datetime(9999, 12, 31, 23, 59, 59, tzinfo=timezone.utc),
            is_current=True
        )
        session.add(agent)
        session.commit()
        
        # Verify relationships and fields
        assert agent.agent_key is not None
        assert agent.tenant_key == tenant.tenant_key
        assert agent.agent_name == "TestAgent001"
        assert agent.is_current is True
        assert agent.created_at_utc is not None

    def test_dim_ring_group_model_creation(self, session):
        """Test DimRingGroup model creation with URI validation."""
        # Create tenant first
        tenant = DimTenant(
            tenant_name="TestTenant",
            effective_date=datetime.now(timezone.utc),
            expiry_date=datetime(9999, 12, 31, 23, 59, 59, tzinfo=timezone.utc),
            is_current=True
        )
        session.add(tenant)
        session.flush()

        # Create ring group
        ring_group = DimRingGroup(
            tenant_key=tenant.tenant_key,
            ring_group_name="Support",
            ring_group_uri="sip:<EMAIL>",
            effective_date=datetime.now(timezone.utc),
            expiry_date=datetime(9999, 12, 31, 23, 59, 59, tzinfo=timezone.utc),
            is_current=True
        )
        session.add(ring_group)
        session.commit()
        
        # Verify fields
        assert ring_group.ring_group_key is not None
        assert ring_group.tenant_key == tenant.tenant_key
        assert ring_group.ring_group_name == "Support"
        assert ring_group.ring_group_uri == "sip:<EMAIL>"
        assert ring_group.is_current is True

    def test_dim_agent_event_model_creation(self, session):
        """Test DimAgentEvent model creation with JSON data storage."""
        # Create tenant
        tenant = DimTenant(
            tenant_name="TestTenant",
            effective_date=datetime.now(timezone.utc),
            expiry_date=datetime(9999, 12, 31, 23, 59, 59, tzinfo=timezone.utc),
            is_current=True
        )
        session.add(tenant)
        session.flush()

        # Create agent
        agent = DimAgent(
            tenant_key=tenant.tenant_key,
            agent_name="TestAgent",
            effective_date=datetime.now(timezone.utc),
            expiry_date=datetime(9999, 12, 31, 23, 59, 59, tzinfo=timezone.utc),
            is_current=True
        )
        session.add(agent)
        session.flush()

        # Create event
        event_data = {
            "eventType": "Login",
            "workstation": "WS001",
            "operatorId": "OP123"
        }
        
        event = DimAgentEvent(
            tenant_key=tenant.tenant_key,
            agent_key=agent.agent_key,
            event_type="Login",
            event_timestamp=datetime.now(timezone.utc),
            event_hash="test_hash_12345",
            event_data=event_data,
            sqs_message_id="test-message-id-123"
        )
        session.add(event)
        session.commit()
        
        # Verify fields and JSON storage
        assert event.event_key is not None
        assert event.tenant_key == tenant.tenant_key
        assert event.agent_key == agent.agent_key
        assert event.event_type == "Login"
        assert event.event_hash == "test_hash_12345"
        assert event.event_data == event_data
        assert event.sqs_message_id == "test-message-id-123"
        assert event.created_at_utc is not None

    def test_dim_agent_event_with_ring_group(self, session):
        """Test DimAgentEvent with optional ring group relationship."""
        # Create tenant
        tenant = DimTenant(
            tenant_name="TestTenant",
            effective_date=datetime.now(timezone.utc),
            expiry_date=datetime(9999, 12, 31, 23, 59, 59, tzinfo=timezone.utc),
            is_current=True
        )
        session.add(tenant)
        session.flush()

        # Create agent and ring group
        agent = DimAgent(
            tenant_key=tenant.tenant_key,
            agent_name="TestAgent",
            effective_date=datetime.now(timezone.utc),
            expiry_date=datetime(9999, 12, 31, 23, 59, 59, tzinfo=timezone.utc),
            is_current=True
        )
        
        ring_group = DimRingGroup(
            tenant_key=tenant.tenant_key,
            ring_group_name="Support",
            ring_group_uri="sip:<EMAIL>",
            effective_date=datetime.now(timezone.utc),
            expiry_date=datetime(9999, 12, 31, 23, 59, 59, tzinfo=timezone.utc),
            is_current=True
        )
        
        session.add_all([agent, ring_group])
        session.flush()

        # Create event with ring group
        event = DimAgentEvent(
            tenant_key=tenant.tenant_key,
            agent_key=agent.agent_key,
            ring_group_key=ring_group.ring_group_key,
            event_type="ACDLogin",
            event_timestamp=datetime.now(timezone.utc),
            event_hash="test_hash_acd_login",
            event_data={"ringGroupName": "Support"},
            sqs_message_id="test-message-acd-123"
        )
        session.add(event)
        session.commit()
        
        # Verify ring group relationship
        assert event.ring_group_key == ring_group.ring_group_key

    def test_scd_type2_pattern_support(self, session):
        """Test that models support SCD Type 2 (Slowly Changing Dimension) patterns."""
        now = datetime.now(timezone.utc)
        future = datetime(9999, 12, 31, 23, 59, 59, tzinfo=timezone.utc)
        
        # Create initial tenant record
        tenant_v1 = DimTenant(
            tenant_name="EvolvingTenant",
            effective_date=now,
            expiry_date=future,
            is_current=True
        )
        session.add(tenant_v1)
        session.commit()
        
        # Simulate SCD Type 2 update - expire old record and create new one
        tenant_v1.expiry_date = now
        tenant_v1.is_current = False
        
        tenant_v2 = DimTenant(
            tenant_name="EvolvingTenant_Updated",
            effective_date=now,
            expiry_date=future,
            is_current=True
        )
        session.add(tenant_v2)
        session.commit()
        
        # Verify both records exist with proper SCD Type 2 structure
        all_tenants = session.query(DimTenant).filter(
            DimTenant.tenant_name.like("EvolvingTenant%")
        ).all()
        
        assert len(all_tenants) == 2
        current_tenant = [t for t in all_tenants if t.is_current][0]
        expired_tenant = [t for t in all_tenants if not t.is_current][0]
        
        assert current_tenant.tenant_name == "EvolvingTenant_Updated"
        assert expired_tenant.tenant_name == "EvolvingTenant"
