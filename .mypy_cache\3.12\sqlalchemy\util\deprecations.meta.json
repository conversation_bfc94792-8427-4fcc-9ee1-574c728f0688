{"data_mtime": 1759503866, "dep_lines": [27, 28, 27, 33, 12, 14, 15, 33, 1, 1, 1, 1], "dep_prios": [10, 5, 20, 10, 5, 10, 5, 20, 5, 20, 20, 30], "dependencies": ["sqlalchemy.util.compat", "sqlalchemy.util.langhelpers", "sqlalchemy.util", "sqlalchemy.exc", "__future__", "re", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "abc"], "hash": "7ec6eded15a14ce1a680aa36cd9a0561269a8379bd1cdf6bd47e2f10b15f72fe", "id": "sqlalchemy.util.deprecations", "ignore_all": true, "interface_hash": "bdf7ff5c2ce3b9ed2fa8e14eddc1eff85a7a3df1d74540fa5e2cb16751b3c4e6", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\deprecations.py", "plugin_data": null, "size": 12413, "suppressed": [], "version_id": "1.8.0"}