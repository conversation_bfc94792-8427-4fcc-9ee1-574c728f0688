{"data_mtime": **********, "dep_lines": [6, 10, 9, 11, 2, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 25, 25, 25, 5, 5, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch", "aws_lambda_powertools.metrics.provider.cloudwatch_emf.types", "aws_lambda_powertools.metrics.base", "aws_lambda_powertools.shared.types", "__future__", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "_typeshed", "abc", "aws_lambda_powertools.metrics.provider", "aws_lambda_powertools.metrics.provider.base", "aws_lambda_powertools.metrics.provider.cloudwatch_emf", "aws_lambda_powertools.metrics.provider.cloudwatch_emf.metric_properties", "enum"], "hash": "320c638d91a00ab248de09520924a12d658ba8b2eacd60427ebba8920b3f53fc", "id": "aws_lambda_powertools.metrics.metrics", "ignore_all": true, "interface_hash": "5ffa6e213853be4a421e65bd6dabfa58061fce86738b272569db4183997499e7", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\aws_lambda_powertools\\metrics\\metrics.py", "plugin_data": null, "size": 8005, "suppressed": [], "version_id": "1.8.0"}