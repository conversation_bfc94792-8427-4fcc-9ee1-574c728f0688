{"data_mtime": 1759504512, "dep_lines": [3, 15, 16, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 20, 20, 30, 30, 30], "dependencies": ["concurrent.futures._base", "concurrent.futures.process", "concurrent.futures.thread", "sys", "builtins", "pyexpat.model", "pyexpat.errors", "_typeshed", "abc", "typing"], "hash": "f0eb3066e0fa6f4f5c5abb5d218457c20f869ebfb6771ff8a5da2619f36604b4", "id": "concurrent.futures", "ignore_all": true, "interface_hash": "0536462aa870a08510cf934034b00468d456cb744cd916fa70714e6895683e3f", "mtime": 1757092231, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\concurrent\\futures\\__init__.pyi", "plugin_data": null, "size": 880, "suppressed": [], "version_id": "1.8.0"}