{"data_mtime": 1759504508, "dep_lines": [7, 2, 4, 5, 8, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 20, 20, 30, 30, 30], "dependencies": ["pydantic_core.core_schema", "__future__", "dataclasses", "typing", "typing_extensions", "builtins", "pyexpat.model", "pyexpat.errors", "_typeshed", "abc", "pydantic_core"], "hash": "b044fe4ef7a086bcd998f5ab690124ac38c15ffbb206a1017f412fa3f889af41", "id": "pydantic._internal._schema_gather", "ignore_all": true, "interface_hash": "b08876165713e3234499daae2fa909a80077d4e97318131f7d4ae4e9f5c686b4", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\_internal\\_schema_gather.py", "plugin_data": null, "size": 8863, "suppressed": [], "version_id": "1.8.0"}