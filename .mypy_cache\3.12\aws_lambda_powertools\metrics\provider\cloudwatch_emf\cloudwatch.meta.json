{"data_mtime": **********, "dep_lines": [21, 22, 28, 20, 12, 13, 14, 23, 24, 29, 30, 31, 23, 25, 1, 3, 4, 5, 6, 7, 8, 9, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 25, 5, 5, 5, 5, 10, 5, 25, 25, 25, 20, 5, 5, 10, 10, 10, 10, 10, 10, 5, 5, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["aws_lambda_powertools.metrics.provider.cloudwatch_emf.constants", "aws_lambda_powertools.metrics.provider.cloudwatch_emf.metric_properties", "aws_lambda_powertools.metrics.provider.cloudwatch_emf.types", "aws_lambda_powertools.metrics.provider.base", "aws_lambda_powertools.metrics.base", "aws_lambda_powertools.metrics.exceptions", "aws_lambda_powertools.metrics.functions", "aws_lambda_powertools.shared.constants", "aws_lambda_powertools.shared.functions", "aws_lambda_powertools.metrics.types", "aws_lambda_powertools.shared.types", "aws_lambda_powertools.utilities.typing", "aws_lambda_powertools.shared", "aws_lambda_powertools.warnings", "__future__", "datetime", "json", "logging", "numbers", "os", "warnings", "collections", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "_typeshed", "_warnings", "abc", "aws_lambda_powertools.utilities", "aws_lambda_powertools.utilities.typing.lambda_context", "enum", "types"], "hash": "c8de8e213db57f4af984ab9673435a44a2da9337196bc931e9b4396924f17457", "id": "aws_lambda_powertools.metrics.provider.cloudwatch_emf.cloudwatch", "ignore_all": true, "interface_hash": "02b68412ce6faa1cf4c3f2ca5dda4591f0da3eb3dd76992aa575f535110927d2", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\aws_lambda_powertools\\metrics\\provider\\cloudwatch_emf\\cloudwatch.py", "plugin_data": null, "size": 18124, "suppressed": [], "version_id": "1.8.0"}