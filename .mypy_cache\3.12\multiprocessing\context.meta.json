{"data_mtime": 1759503830, "dep_lines": [3, 6, 6, 6, 6, 6, 6, 7, 8, 9, 10, 17, 1, 2, 5, 6, 11, 12, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 10, 5, 20, 5, 5, 5, 20, 20, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "multiprocessing.popen_fork", "multiprocessing.popen_forkserver", "multiprocessing.popen_spawn_posix", "multiprocessing.popen_spawn_win32", "multiprocessing.queues", "multiprocessing.synchronize", "multiprocessing.managers", "multiprocessing.pool", "multiprocessing.process", "multiprocessing.sharedctypes", "multiprocessing.connection", "ctypes", "sys", "logging", "multiprocessing", "typing", "typing_extensions", "builtins", "pyexpat.model", "pyexpat.errors", "_ctypes", "_typeshed", "abc", "contextlib", "threading"], "hash": "f8a5f59e6d8703404cf7e646183deabc2ec7d101dddd8b01f0c706f373be3530", "id": "multiprocessing.context", "ignore_all": true, "interface_hash": "7a4da76556c11e5eacb13f70164780e36c4ce1ef984158ff6e5c2cd58e0f8acd", "mtime": 1757092231, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\multiprocessing\\context.pyi", "plugin_data": null, "size": 7970, "suppressed": [], "version_id": "1.8.0"}