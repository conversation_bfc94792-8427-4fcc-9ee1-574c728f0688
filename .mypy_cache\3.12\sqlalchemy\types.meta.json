{"data_mtime": 1759503866, "dep_lines": [13, 68, 11, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 20, 20, 30, 30], "dependencies": ["sqlalchemy.sql.sqltypes", "sqlalchemy.sql.type_api", "__future__", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "typing"], "hash": "a31d82de68be329420c99103f043e6a3204a9403b21f26f09bf7392bd66fdede", "id": "sqlalchemy.types", "ignore_all": true, "interface_hash": "d89fbc0149dee7ceeeb2ba8bb7d3a8fbdf3b2b549d6ea544404a3f9e85048c73", "mtime": 1759106563, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\types.py", "plugin_data": null, "size": 3240, "suppressed": [], "version_id": "1.8.0"}