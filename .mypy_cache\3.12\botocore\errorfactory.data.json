{".class": "MypyFile", "_fullname": "botocore.errorfactory", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "BaseClientExceptions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.errorfactory.BaseClientExceptions", "name": "BaseClientExceptions", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.errorfactory.BaseClientExceptions", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.errorfactory", "mro": ["botocore.errorfactory.BaseClientExceptions", "builtins.object"], "names": {".class": "SymbolTable", "ClientError": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.errorfactory.BaseClientExceptions.ClientError", "name": "ClientError", "type": {".class": "TypeType", "item": "botocore.exceptions.ClientError"}}}, "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.errorfactory.BaseClientExceptions.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["botocore.errorfactory.BaseClientExceptions", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattr__ of BaseClientExceptions", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "code_to_exception"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.errorfactory.BaseClientExceptions.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "code_to_exception"], "arg_types": ["botocore.errorfactory.BaseClientExceptions", {".class": "Instance", "args": ["builtins.str", {".class": "TypeType", "item": "botocore.exceptions.ClientError"}], "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BaseClientExceptions", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "from_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "error_code"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.errorfactory.BaseClientExceptions.from_code", "name": "from_code", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "error_code"], "arg_types": ["botocore.errorfactory.BaseClientExceptions", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_code of BaseClientExceptions", "ret_type": "botocore.exceptions.ClientError", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.errorfactory.BaseClientExceptions.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.errorfactory.BaseClientExceptions", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClientError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.ClientError", "kind": "Gdef"}, "ClientExceptionsFactory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.errorfactory.ClientExceptionsFactory", "name": "ClientExceptionsFactory", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.errorfactory.ClientExceptionsFactory", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.errorfactory", "mro": ["botocore.errorfactory.ClientExceptionsFactory", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.errorfactory.ClientExceptionsFactory.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.errorfactory.ClientExceptionsFactory"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ClientExceptionsFactory", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "create_client_exceptions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "service_model"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.errorfactory.ClientExceptionsFactory.create_client_exceptions", "name": "create_client_exceptions", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "service_model"], "arg_types": ["botocore.errorfactory.ClientExceptionsFactory", "botocore.model.ServiceModel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_client_exceptions of ClientExceptionsFactory", "ret_type": "botocore.errorfactory.BaseClientExceptions", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.errorfactory.ClientExceptionsFactory.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.errorfactory.ClientExceptionsFactory", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ServiceModel": {".class": "SymbolTableNode", "cross_ref": "botocore.model.ServiceModel", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ClientError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.ClientError", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.errorfactory.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.errorfactory.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.errorfactory.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.errorfactory.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.errorfactory.__package__", "name": "__package__", "type": "builtins.str"}}, "get_service_module_name": {".class": "SymbolTableNode", "cross_ref": "botocore.utils.get_service_module_name", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\botocore-stubs\\errorfactory.pyi"}