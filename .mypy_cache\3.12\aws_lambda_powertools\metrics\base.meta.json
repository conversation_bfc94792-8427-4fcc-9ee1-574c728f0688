{"data_mtime": **********, "dep_lines": [22, 23, 21, 14, 20, 21, 27, 28, 31, 27, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 20, 10, 5, 25, 20, 5, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 20, 20, 30, 30, 30, 30, 30], "dependencies": ["aws_lambda_powertools.metrics.provider.cloudwatch_emf.constants", "aws_lambda_powertools.metrics.provider.cloudwatch_emf.metric_properties", "aws_lambda_powertools.metrics.provider.cold_start", "aws_lambda_powertools.metrics.exceptions", "aws_lambda_powertools.metrics.functions", "aws_lambda_powertools.metrics.provider", "aws_lambda_powertools.shared.constants", "aws_lambda_powertools.shared.functions", "aws_lambda_powertools.metrics.types", "aws_lambda_powertools.shared", "__future__", "datetime", "functools", "json", "logging", "numbers", "os", "warnings", "collections", "contextlib", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "_warnings", "abc", "aws_lambda_powertools.metrics.provider.cloudwatch_emf", "enum", "types"], "hash": "38c61a651fc2044f5de0c3c41ef0177e47fc61e5bcfc20faca939ae6e36aee0b", "id": "aws_lambda_powertools.metrics.base", "ignore_all": true, "interface_hash": "d88ce71b1551c0ff2d183a33ae89959d0fba8a2511ad245ab073159822f2725c", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\aws_lambda_powertools\\metrics\\base.py", "plugin_data": null, "size": 23675, "suppressed": [], "version_id": "1.8.0"}