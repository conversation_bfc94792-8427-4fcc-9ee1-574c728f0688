{"data_mtime": 1759503866, "dep_lines": [26, 34, 38, 42, 31, 32, 8, 10, 11, 12, 31, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 25, 25, 5, 5, 5, 10, 5, 5, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.visitors", "sqlalchemy.util.typing", "sqlalchemy.sql.elements", "sqlalchemy.engine.interfaces", "sqlalchemy.util", "sqlalchemy.inspection", "__future__", "enum", "itertools", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "sqlalchemy.sql._py_util", "sqlalchemy.sql.annotation", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.util.langhelpers", "sqlalchemy.util.preloaded", "types"], "hash": "0f3326fe6f53d17677e1b04983d6252e07139b9682f600b9ee58b5fd5c4a1c38", "id": "sqlalchemy.sql.cache_key", "ignore_all": true, "interface_hash": "beafd95efac0242c8fbe7ab9b468ae862e04f8ea29e987b1416876ea5a848a30", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\cache_key.py", "plugin_data": null, "size": 34710, "suppressed": [], "version_id": "1.8.0"}