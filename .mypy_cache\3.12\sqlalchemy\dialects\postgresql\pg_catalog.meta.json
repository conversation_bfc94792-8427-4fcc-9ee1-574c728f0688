{"data_mtime": 1759503868, "dep_lines": [15, 16, 33, 34, 22, 8, 10, 18, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 25, 25, 5, 5, 5, 5, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.dialects.postgresql.array", "sqlalchemy.dialects.postgresql.types", "sqlalchemy.engine.interfaces", "sqlalchemy.sql.type_api", "sqlalchemy.types", "__future__", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "_decimal", "abc", "enum", "sqlalchemy.engine", "sqlalchemy.engine.base", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.sql", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.elements", "sqlalchemy.sql.functions", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util", "sqlalchemy.util.langhelpers"], "hash": "85fd2d067175417f5ff813f6bc2194f4faa8f1d0d9bd259776b0a0959bdbf778", "id": "sqlalchemy.dialects.postgresql.pg_catalog", "ignore_all": true, "interface_hash": "684179fc084aaed29587749e284b8889d6a3f50b959dfad5ae7dfa81a14eac3a", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\pg_catalog.py", "plugin_data": null, "size": 10325, "suppressed": [], "version_id": "1.8.0"}