{"data_mtime": 1759503828, "dep_lines": [12, 13, 14, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic.v1.color", "pydantic.v1.networks", "pydantic.v1.types", "datetime", "collections", "decimal", "enum", "ipaddress", "pathlib", "re", "types", "typing", "uuid", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "_decimal", "abc", "os", "pydantic.v1.utils", "typing_extensions"], "hash": "590e47cbf848a5f751dd84bc93a37613a28c273b1d6c18bf95d58e3c9695f353", "id": "pydantic.v1.json", "ignore_all": true, "interface_hash": "b24f2c59a55ea14610eb559cef2e09bbfae81da725b9b1f2032c06bc3c0c68bd", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\v1\\json.py", "plugin_data": null, "size": 3390, "suppressed": [], "version_id": "1.8.0"}