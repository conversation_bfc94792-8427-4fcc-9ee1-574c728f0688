{"data_mtime": 1759504547, "dep_lines": [12, 13, 41, 42, 10, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 20, 20, 30, 30], "dependencies": ["sqlalchemy.sql.base", "sqlalchemy.sql.ddl", "sqlalchemy.sql.naming", "sqlalchemy.sql.schema", "__future__", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "typing"], "hash": "2dcbfd9b8ad95fca5c209611635d08e8a2142e1d4531f9a77a0488c1f13fb340", "id": "sqlalchemy.schema", "ignore_all": true, "interface_hash": "a1ec85d6783e42d690849f33dc00983c2ca4fd640088c4d28bf82497bbef928f", "mtime": 1759106563, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\schema.py", "plugin_data": null, "size": 3320, "suppressed": [], "version_id": "1.8.0"}