{"data_mtime": 1759503866, "dep_lines": [24, 28, 29, 24, 25, 8, 10, 22, 25, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 25, 25, 20, 10, 5, 5, 10, 20, 5, 20, 20, 30, 30, 30], "dependencies": ["sqlalchemy.orm.util", "sqlalchemy.orm._typing", "sqlalchemy.orm.state", "sqlalchemy.orm", "sqlalchemy.exc", "__future__", "typing", "weakref", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "_weakref", "abc", "sqlalchemy.orm.base"], "hash": "3de3bdc2c77ba26111182d869a25207021e6a68b422546d9dceda0db72469cc1", "id": "sqlalchemy.orm.identity", "ignore_all": true, "interface_hash": "e69c0f600cc18cc34c378ea56734c635960454f8df672b3b0b0421594ca0e8dc", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\identity.py", "plugin_data": null, "size": 9551, "suppressed": [], "version_id": "1.8.0"}