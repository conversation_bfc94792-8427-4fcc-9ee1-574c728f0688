{"data_mtime": 1759504512, "dep_lines": [3, 4, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 20, 20, 30, 30, 30], "dependencies": ["rich._win32_console", "rich.segment", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "enum", "rich.style"], "hash": "77bf7dc4e9f12db0820b31aef7e53b60b988424c6dc5020115043a8aee2f7927", "id": "rich._windows_renderer", "ignore_all": true, "interface_hash": "84e1ab87e357665e35cfc7effde45a0e288a96beba5b8630d36b695d88bb85f3", "mtime": 1757092238, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\rich\\_windows_renderer.py", "plugin_data": null, "size": 2759, "suppressed": [], "version_id": "1.8.0"}