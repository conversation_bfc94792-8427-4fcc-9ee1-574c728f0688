{"data_mtime": 1759503866, "dep_lines": [35, 41, 44, 45, 46, 47, 48, 53, 54, 55, 60, 61, 62, 42, 43, 44, 13, 15, 16, 17, 18, 42, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 10, 5, 5, 5, 10, 5, 25, 25, 25, 10, 10, 20, 5, 10, 10, 10, 5, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.engine.result", "sqlalchemy.engine.row", "sqlalchemy.sql.elements", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.util", "sqlalchemy.sql.base", "sqlalchemy.sql.compiler", "sqlalchemy.sql.type_api", "sqlalchemy.util.compat", "sqlalchemy.util.typing", "sqlalchemy.engine.base", "sqlalchemy.engine.default", "sqlalchemy.engine.interfaces", "sqlalchemy.exc", "sqlalchemy.util", "sqlalchemy.sql", "__future__", "collections", "functools", "operator", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "_operator", "_typeshed", "abc", "sqlalchemy.engine._py_row", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util.langhelpers", "types", "typing_extensions"], "hash": "e39235274c06aa1f6b6e73d0b360fc5475bd1e594a8ca1688f9df2ebf47a94de", "id": "sqlalchemy.engine.cursor", "ignore_all": true, "interface_hash": "a2b626f806719450fb5a7f58e8ce31b8166fa11ff700abe1edcc38756d64332a", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\cursor.py", "plugin_data": null, "size": 78748, "suppressed": [], "version_id": "1.8.0"}