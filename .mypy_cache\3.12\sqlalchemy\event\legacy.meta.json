{"data_mtime": 1759504546, "dep_lines": [22, 25, 28, 29, 24, 12, 14, 24, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 25, 25, 10, 5, 5, 20, 5, 20, 20, 30, 30], "dependencies": ["sqlalchemy.event.registry", "sqlalchemy.util.compat", "sqlalchemy.event.attr", "sqlalchemy.event.base", "sqlalchemy.util", "__future__", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "sqlalchemy.util.langhelpers"], "hash": "eba97e35de1ab6e080b5f7a03aff25eb9a842fcd5957c99cd2763f3969c246d5", "id": "sqlalchemy.event.legacy", "ignore_all": true, "interface_hash": "e7416613c1d1e8197ea3c811a4b29c4bd45174f51ed0a4636127f81eba3b7b30", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\event\\legacy.py", "plugin_data": null, "size": 8473, "suppressed": [], "version_id": "1.8.0"}