{"data_mtime": 1759504547, "dep_lines": [10, 16, 18, 22, 23, 8, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 20, 20, 30, 30], "dependencies": ["sqlalchemy.event.api", "sqlalchemy.event.attr", "sqlalchemy.event.base", "sqlalchemy.event.legacy", "sqlalchemy.event.registry", "__future__", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "typing"], "hash": "941181d6c418f7132a7e4a24a52801e4325e5afbcd123c1419579fa2c9e5101c", "id": "sqlalchemy.event", "ignore_all": true, "interface_hash": "b098ea242f1134f9d200fa2347a9b5e5ab8be7b1ea853682ee4341415ca615a6", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\event\\__init__.py", "plugin_data": null, "size": 1022, "suppressed": [], "version_id": "1.8.0"}