{"data_mtime": 1759503867, "dep_lines": [28, 29, 31, 28, 39, 40, 41, 55, 58, 61, 62, 63, 68, 69, 70, 71, 72, 34, 35, 48, 57, 7, 9, 10, 34, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 20, 10, 5, 5, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 10, 5, 25, 25, 5, 10, 5, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.ext.asyncio.engine", "sqlalchemy.ext.asyncio.base", "sqlalchemy.ext.asyncio.result", "sqlalchemy.ext.asyncio", "sqlalchemy.orm.state", "sqlalchemy.util.concurrency", "sqlalchemy.util.typing", "sqlalchemy.engine.interfaces", "sqlalchemy.orm._typing", "sqlalchemy.orm.identity", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.session", "sqlalchemy.sql._typing", "sqlalchemy.sql.base", "sqlalchemy.sql.dml", "sqlalchemy.sql.elements", "sqlalchemy.sql.selectable", "sqlalchemy.util", "sqlalchemy.orm", "sqlalchemy.engine", "sqlalchemy.event", "__future__", "asyncio", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "abc", "enum", "sqlalchemy.engine._py_row", "sqlalchemy.engine.base", "sqlalchemy.engine.cursor", "sqlalchemy.engine.result", "sqlalchemy.engine.row", "sqlalchemy.engine.util", "sqlalchemy.event.base", "sqlalchemy.event.registry", "sqlalchemy.exc", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.orm.base", "sqlalchemy.orm.mapper", "sqlalchemy.orm.query", "sqlalchemy.orm.state_changes", "sqlalchemy.sql", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.compiler", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util._concurrency_py3k", "sqlalchemy.util._py_collections", "sqlalchemy.util.deprecations", "sqlalchemy.util.langhelpers", "types"], "hash": "fe04a760dfa41900047e0b72f9f1d21343ac6895960f55811742a27a1c9f2b68", "id": "sqlalchemy.ext.asyncio.session", "ignore_all": true, "interface_hash": "0b250a69e5d237057c6eb319721f3c8337491e2d32ebe5fa2595c435c2d8e9d5", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\session.py", "plugin_data": null, "size": 65704, "suppressed": [], "version_id": "1.8.0"}