{"data_mtime": 1759503828, "dep_lines": [30, 31, 32, 38, 39, 40, 30, 1, 2, 12, 36, 1, 1, 1, 1, 1, 35], "dep_prios": [10, 5, 5, 25, 25, 25, 20, 10, 5, 5, 25, 5, 20, 20, 30, 30, 25], "dependencies": ["pydantic.v1.errors", "pydantic.v1.utils", "pydantic.v1.validators", "pydantic.v1.config", "pydantic.v1.fields", "pydantic.v1.typing", "pydantic.v1", "re", "ipaddress", "typing", "typing_extensions", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "abc"], "hash": "1d836d2807ce98e9ca269b0383583a488923f563e153ffa2f25e5ed89281a46e", "id": "pydantic.v1.networks", "ignore_all": true, "interface_hash": "bac3eeef986708e8116b6b16e12ac9c51bb279a63afc2f3fecc95cbc9fe3c448", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\v1\\networks.py", "plugin_data": null, "size": 22124, "suppressed": ["email_validator"], "version_id": "1.8.0"}