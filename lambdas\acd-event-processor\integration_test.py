#!/usr/bin/env python3
"""
Professional Integration Test for ACD Event Processor Lambda.

This script tests the Lambda function against a real Aurora PostgreSQL database.
It supports testing individual event types or all events at once.

Usage:
    python integration_test.py --all                    # Test all event types
    python integration_test.py --event AgentAvailable   # Test specific event
    python integration_test.py --list                   # List available events
    python integration_test.py --help                   # Show help

Requirements:
    - Aurora PostgreSQL database accessible
    - Environment variables set (or use .env file)
    - Database schema 'memo' must exist
"""

import argparse
import asyncio
import json
import os
import sys
from datetime import datetime, timezone
from pathlib import Path
from typing import Any

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("⚠️  python-dotenv not installed. Install with: pip install python-dotenv")
    print("   Using existing environment variables only.")

from smartanalytics_infrastructure.database.connection import DatabaseConnection
from smartanalytics_infrastructure.database.models import Base
from sqlalchemy import text
from sqlalchemy.exc import ProgrammingError

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.lambda_function import lambda_handler


class MockLambdaContext:
    """Mock Lambda context for testing with all required AWS Lambda Powertools attributes."""

    def __init__(self):
        self.function_name = "acd-event-processor-test"
        self.function_version = "$LATEST"
        self.invoked_function_arn = (
            "arn:aws:lambda:us-east-1:123456789012:function:acd-event-processor-test"
        )
        self.memory_limit_in_mb = 512
        self.request_id = "test-request-id-12345"
        self.aws_request_id = "test-request-id-12345"
        self.log_group_name = "/aws/lambda/acd-event-processor-test"
        self.log_stream_name = "2025/01/15/[$LATEST]test-stream"
        self.identity = None
        self.client_context = None

    def get_remaining_time_in_millis(self):
        return 300000  # 5 minutes


# Event templates with placeholders for dynamic data
EVENT_TEMPLATES = {
    "Login": """<LogEvent xmlns="http://solacom.com/Logging">
    <timestamp>{timestamp}</timestamp>
    <agencyOrElement>{tenant}</agencyOrElement>
    <agent>{agent}</agent>
    <eventType>Login</eventType>
    <login>
        <mediaLabel>_ML_{unique_id}@{tenant}</mediaLabel>
        <uri>tel:+{phone}</uri>
        <agentRole>{agent_role}</agentRole>
        <tenantGroup>{tenant}</tenantGroup>
        <operatorId>{operator_id}</operatorId>
        <workstation>{workstation}</workstation>
        <deviceName>Headset</deviceName>
        <reason>normal</reason>
    </login>
</LogEvent>""",
    "Logout": """<LogEvent xmlns="http://solacom.com/Logging">
    <timestamp>{timestamp}</timestamp>
    <agencyOrElement>{tenant}</agencyOrElement>
    <agent>{agent}</agent>
    <eventType>Logout</eventType>
    <logout>
        <mediaLabel>_ML_{unique_id}@{tenant}</mediaLabel>
        <uri>tel:+{phone}</uri>
        <agentRole>{agent_role}</agentRole>
        <tenantGroup>{tenant}</tenantGroup>
        <operatorId>{operator_id}</operatorId>
        <workstation>{workstation}</workstation>
        <deviceName>Headset</deviceName>
        <reason>normal</reason>
        <responseCode>16</responseCode>
        <voiceQOS>
            <mediaIpSourceAddr>*************</mediaIpSourceAddr>
            <mediaIpDestAddr>**************</mediaIpDestAddr>
            <mediaUdpRtpSourcePort>21464</mediaUdpRtpSourcePort>
            <mediaUdpRtpDestPort>4000</mediaUdpRtpDestPort>
            <mediaNumOfIpPktRxed>65535</mediaNumOfIpPktRxed>
            <mediaNumOfIpPktTxed>65535</mediaNumOfIpPktTxed>
            <mediaNumOfIpErroredPktRxed>65535</mediaNumOfIpErroredPktRxed>
            <mediaNumOfRtpPktRxed>2018682</mediaNumOfRtpPktRxed>
            <mediaNumOfRtpPktTxed>2171890</mediaNumOfRtpPktTxed>
            <mediaNumOfRtpPktLost>0</mediaNumOfRtpPktLost>
            <mediaNumOfRtpPktDiscarded>0</mediaNumOfRtpPktDiscarded>
            <mediaRtpJitter>6</mediaRtpJitter>
            <mediaRtpLatency>1</mediaRtpLatency>
            <mediaNumOfRtcpPktRxed>9444</mediaNumOfRtcpPktRxed>
            <mediaNumOfRtcpPktTxed>17370</mediaNumOfRtcpPktTxed>
            <mediaFarEndPacketLostPercentage>0</mediaFarEndPacketLostPercentage>
            <mediaFarEndCumulativePacketLost>0</mediaFarEndCumulativePacketLost>
            <mediaFarEndInterarrivalJitter>2</mediaFarEndInterarrivalJitter>
        </voiceQOS>
    </logout>
</LogEvent>""",
    "AgentAvailable": """<LogEvent xmlns="http://solacom.com/Logging">
    <timestamp>{timestamp}</timestamp>
    <agencyOrElement>{tenant}</agencyOrElement>
    <agent>{agent}</agent>
    <eventType>AgentAvailable</eventType>
    <agentAvailable>
        <mediaLabel>_ML_{unique_id}@{tenant}</mediaLabel>
        <uri>tel:+{phone}</uri>
        <agentRole>{agent_role}</agentRole>
        <tenantGroup>{tenant}</tenantGroup>
        <operatorId>{operator_id}</operatorId>
        <workstation>{workstation}</workstation>
        <busiedOutAction>Manual</busiedOutAction>
    </agentAvailable>
</LogEvent>""",
    "AgentBusiedOut": """<LogEvent xmlns="http://solacom.com/Logging">
    <timestamp>{timestamp}</timestamp>
    <agencyOrElement>{tenant}</agencyOrElement>
    <agent>{agent}</agent>
    <eventType>AgentBusiedOut</eventType>
    <busiedOut>
        <mediaLabel>_ML_{unique_id}@{tenant}</mediaLabel>
        <uri>tel:+{phone}</uri>
        <agentRole>{agent_role}</agentRole>
        <tenantGroup>{tenant}</tenantGroup>
        <operatorId>{operator_id}</operatorId>
        <workstation>{workstation}</workstation>
        <busiedOutAction>Break</busiedOutAction>
    </busiedOut>
</LogEvent>""",
    "ACDLogin": """<LogEvent xmlns="http://solacom.com/Logging">
    <timestamp>{timestamp}</timestamp>
    <agencyOrElement>{tenant}</agencyOrElement>
    <agent>{agent}</agent>
    <eventType>ACDLogin</eventType>
    <acdLogin>
        <mediaLabel>_ML_{unique_id}@{tenant}</mediaLabel>
        <agentUri>tel:+{phone}</agentUri>
        <agentRole>{agent_role}</agentRole>
        <tenantGroup>{tenant}</tenantGroup>
        <operatorId>{operator_id}</operatorId>
        <workstation>{workstation}</workstation>
        <deviceName>Headset</deviceName>
        <ringGroupName>{ring_group}</ringGroupName>
        <ringGroupUri>tel:+{ring_group_phone}</ringGroupUri>
    </acdLogin>
</LogEvent>""",
    "ACDLogout": """<LogEvent xmlns="http://solacom.com/Logging">
    <timestamp>{timestamp}</timestamp>
    <agencyOrElement>{tenant}</agencyOrElement>
    <agent>{agent}</agent>
    <eventType>ACDLogout</eventType>
    <acdLogout>
        <mediaLabel>_ML_{unique_id}@{tenant}</mediaLabel>
        <agentUri>tel:+{phone}</agentUri>
        <agentRole>{agent_role}</agentRole>
        <tenantGroup>{tenant}</tenantGroup>
        <operatorId>{operator_id}</operatorId>
        <workstation>{workstation}</workstation>
        <deviceName>Headset</deviceName>
        <ringGroupName>{ring_group}</ringGroupName>
        <ringGroupUri>tel:+{ring_group_phone}</ringGroupUri>
    </acdLogout>
</LogEvent>""",
}


def generate_event_xml(event_type: str, sequence: int = 1) -> str:
    """Generate XML for an event with unique data to avoid duplicates."""
    template = EVENT_TEMPLATES.get(event_type)
    if not template:
        raise ValueError(f"Unknown event type: {event_type}")

    now = datetime.now(timezone.utc)
    timestamp = now.isoformat().replace("+00:00", "Z")
    unique_id = f"{int(now.timestamp() * 1000)}{sequence:04d}"

    # Fill in template with production-like data
    return template.format(
        timestamp=timestamp,
        tenant=f"TestTenant{sequence}",
        agent=f"agent{sequence:03d}",
        agent_role=f"TestRole{sequence}",
        ring_group=f"RingGroup{sequence}",
        unique_id=unique_id,
        operator_id=str(sequence),
        workstation=f"WS{sequence:02d}",
        phone=f"204555{3000 + sequence}",
        ring_group_phone=f"204555{4000 + sequence}",
        duration=str(300 + sequence),
        reason_code=str(100 + sequence),
    )


def create_sqs_event(xml_content: str, message_id: str | None = None) -> dict[str, Any]:
    """Create a mock SQS event for testing."""
    now = datetime.now(timezone.utc)
    timestamp_ms = int(now.timestamp() * 1000)

    if message_id is None:
        message_id = f"msg-{timestamp_ms}"

    return {
        "Records": [
            {
                "messageId": message_id,
                "receiptHandle": "test-receipt-handle",
                "body": xml_content,
                "attributes": {
                    "ApproximateReceiveCount": "1",
                    "SentTimestamp": str(timestamp_ms),
                    "SenderId": "test-sender",
                    "ApproximateFirstReceiveTimestamp": str(timestamp_ms),
                },
                "messageAttributes": {},
                "md5OfBody": "test-md5",
                "eventSource": "aws:sqs",
                "eventSourceARN": "arn:aws:sqs:us-east-1:123456789012:test-queue",
                "awsRegion": "us-east-1",
            }
        ]
    }


async def recreate_schema():
    """Recreate database schema for testing."""
    print("\n" + "=" * 80)
    print("RECREATING DATABASE SCHEMA")
    print("=" * 80)

    db_conn = DatabaseConnection()
    engine = db_conn.get_engine()

    try:
        async with engine.begin() as conn:
            # Drop and recreate schema
            print("Dropping existing schema...")
            await conn.execute(text("DROP SCHEMA IF EXISTS memo CASCADE"))

            print("Creating schema...")
            await conn.execute(text("CREATE SCHEMA memo"))

            # Create all tables
            print("Creating tables...")
            await conn.run_sync(Base.metadata.create_all)

        print("✅ Schema recreated successfully\n")
    finally:
        await engine.dispose()


async def verify_database_content():
    """Verify database content (optional - only if tables exist)."""
    print("\n" + "=" * 80)
    print("VERIFYING DATABASE CONTENT")
    print("=" * 80)

    db_conn = DatabaseConnection()
    engine = db_conn.get_engine()

    try:
        async with engine.begin() as conn:
            # Check if table exists first
            table_check = await conn.execute(
                text(
                    "SELECT EXISTS ("
                    "SELECT FROM information_schema.tables "
                    "WHERE table_schema = 'memo' "
                    "AND table_name = 'dim_agent_event'"
                    ")"
                )
            )
            table_exists = table_check.scalar()

            if not table_exists:
                # Table doesn't exist - skip verification silently
                return

            # Check events
            result = await conn.execute(
                text(
                    "SELECT event_key, event_type, agent_key, ring_group_key, tenant_key, "
                    "operator_id, workstation, media_label, uri, device_name, "
                    "busied_out_action, busied_out_duration, reason_code, "
                    "event_data->>'eventType' as event_type_from_json "
                    "FROM memo.dim_agent_event ORDER BY event_key"
                )
            )
            events = result.fetchall()

            print(f"\nFound {len(events)} events in database:")
            for event in events:
                print(f"\n  Event {event[0]}: {event[1]}")
                print(
                    f"    - agent_key: {event[2]}, ring_group_key: {event[3]}, tenant_key: {event[4]}"
                )
                print(f"    - operator_id: {event[5]}, workstation: {event[6]}")
                print(f"    - media_label: {event[7]}")
                print(f"    - uri: {event[8]}, device_name: {event[9]}")
                print(
                    f"    - busied_out_action: {event[10]}, busied_out_duration: {event[11]}"
                )
                print(f"    - reason_code: {event[12]}")
                print(f"    - event_data contains: {event[13]}")

    except ProgrammingError as e:
        print(f"\n⚠️  Could not verify database content: {e}")
        print("   This is expected if tables don't exist yet.")
    finally:
        await engine.dispose()


def test_event(event_type: str, sequence: int = 1) -> bool:
    """Test a single event type."""
    print(f"\n{sequence}. Testing {event_type}...")

    # Generate unique XML
    xml_content = generate_event_xml(event_type, sequence)

    # Create SQS event
    sqs_event = create_sqs_event(xml_content)

    # Create context
    context = MockLambdaContext()

    # Call Lambda handler
    try:
        result = lambda_handler(sqs_event, context) # type: ignore

        # Check result
        if result.get("statusCode") == 200:
            body = json.loads(result["body"])
            print(f"   Success - Processed: {body.get('processed_events', 0)}")
            return True
        else:
            print(f"   Failed - Status: {result.get('statusCode')}")
            print(f"      Error: {result.get('body')}")
            return False

    except Exception as e:
        print(f"   Exception: {e}")
        return False


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Integration test for ACD Event Processor Lambda",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__,
    )
    parser.add_argument(
        "--all", action="store_true", help="Test all event types"
    )
    parser.add_argument(
        "--event", type=str, help="Test specific event type"
    )
    parser.add_argument(
        "--list", action="store_true", help="List available event types"
    )
    parser.add_argument(
        "--no-recreate", action="store_true", help="Skip schema recreation"
    )
    parser.add_argument(
        "--no-verify", action="store_true", help="Skip database verification"
    )

    args = parser.parse_args()

    # List events
    if args.list:
        print("\nAvailable event types:")
        for event_type in EVENT_TEMPLATES.keys():
            print(f"  - {event_type}")
        return 0

    # Validate arguments
    if not args.all and not args.event:
        parser.print_help()
        return 1

    if args.event and args.event not in EVENT_TEMPLATES:
        print(f"\nError: Unknown event type '{args.event}'")
        print("\nAvailable event types:")
        for event_type in EVENT_TEMPLATES.keys():
            print(f"  - {event_type}")
        return 1

    # Check required environment variables
    required_vars = [
        "DATABASE_HOST",
        "DATABASE_DATABASE_NAME",
        "DATABASE_USERNAME",
        "DATABASE_SCHEMA_NAME"
    ]

    missing_vars = [var for var in required_vars if not os.environ.get(var)]
    if missing_vars:
        print(f"\n❌ Missing required environment variables: {', '.join(missing_vars)}")
        print("   Please set them in your .env file or environment.")
        print("   See .env file for reference.")
        return 1

    # Check database password or IAM auth
    password = os.environ.get("DATABASE_PASSWORD")
    use_iam = os.environ.get("DATABASE_USE_IAM_AUTH", "false").lower() == "true"

    if not use_iam and (not password or password == "your-password-here"):
        print("\n❌ No valid database password provided and IAM auth not enabled.")
        print("   Either set DATABASE_PASSWORD in .env file or enable DATABASE_USE_IAM_AUTH=true")
        return 1

    print("\n✅ Database configuration:")
    print(f"   Host: {os.environ.get('DATABASE_HOST')}")
    print(f"   Database: {os.environ.get('DATABASE_DATABASE_NAME')}")
    print(f"   Schema: {os.environ.get('DATABASE_SCHEMA_NAME')}")
    print(f"   Username: {os.environ.get('DATABASE_USERNAME')}")
    print(f"   Auth Method: {'IAM' if use_iam else 'Password'}")
    print(f"   SSL Mode: {os.environ.get('DATABASE_SSL_MODE', 'require')}")

    # Recreate schema
    if not args.no_recreate:
        asyncio.run(recreate_schema())

    # Run tests
    print("\n" + "=" * 80)
    print("RUNNING TESTS")
    print("=" * 80)

    if args.all:
        # Test all events
        results = {}
        for i, event_type in enumerate(EVENT_TEMPLATES.keys(), 1):
            results[event_type] = test_event(event_type, i)

        # Summary
        print("\n" + "=" * 80)
        print("TEST SUMMARY")
        print("=" * 80)
        passed = sum(1 for v in results.values() if v)
        total = len(results)
        print(f"\nPassed: {passed}/{total}")
        for event_type, success in results.items():
            status = "✅" if success else "❌"
            print(f"  {status} {event_type}")

        success = all(results.values())
    else:
        # Test single event
        success = test_event(args.event, 1)

    # Verify database
    if not args.no_verify:
        asyncio.run(verify_database_content())

    # Final result
    print("\n" + "=" * 80)
    if success:
        print("✅ ALL TESTS PASSED")
    else:
        print("❌ SOME TESTS FAILED")
    print("=" * 80 + "\n")

    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
