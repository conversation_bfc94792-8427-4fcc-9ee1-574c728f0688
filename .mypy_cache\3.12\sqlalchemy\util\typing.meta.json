{"data_mtime": 1759503866, "dep_lines": [37, 13, 37, 9, 11, 12, 14, 15, 16, 35, 72, 1, 1, 1], "dep_prios": [10, 10, 20, 5, 10, 5, 10, 10, 5, 5, 5, 20, 20, 30], "dependencies": ["sqlalchemy.util.compat", "collections.abc", "sqlalchemy.util", "__future__", "builtins", "collections", "re", "sys", "typing", "typing_extensions", "types", "pyexpat.model", "pyexpat.errors", "abc"], "hash": "1fc8f1efd11bbde123637f76cc78b3c2094c7e7d43532fcfd752c9a783c9e6fe", "id": "sqlalchemy.util.typing", "ignore_all": true, "interface_hash": "e32e705d0cddae5e0b25713f9a59a122c1c13e88f11e2c6985397b5053d6cbae", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\typing.py", "plugin_data": null, "size": 23199, "suppressed": [], "version_id": "1.8.0"}