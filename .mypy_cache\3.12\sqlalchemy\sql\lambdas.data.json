{".class": "MypyFile", "_fullname": "sqlalchemy.sql.lambdas", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AnalyzedCode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.lambdas.AnalyzedCode", "name": "AnalyzedCode", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.lambdas.AnalyzedCode", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.lambdas", "mro": ["sqlalchemy.sql.lambdas.AnalyzedCode", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "fn", "lambda_element", "opts"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.AnalyzedCode.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.lambdas.AnalyzedCode.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_bound_parameter_getter_func_closure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "closure_index"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.AnalyzedCode._bound_parameter_getter_func_closure", "name": "_bound_parameter_getter_func_closure", "type": null}}, "_bound_parameter_getter_func_globals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.AnalyzedCode._bound_parameter_getter_func_globals", "name": "_bound_parameter_getter_func_globals", "type": null}}, "_cache_key_getter_closure_variable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1], "arg_names": ["self", "fn", "variable_name", "idx", "cell_contents", "use_clause_element", "use_inspect"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.AnalyzedCode._cache_key_getter_closure_variable", "name": "_cache_key_getter_closure_variable", "type": null}}, "_cache_key_getter_track_on": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "idx", "elem"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.AnalyzedCode._cache_key_getter_track_on", "name": "_cache_key_getter_track_on", "type": null}}, "_cache_key_getter_tracked_literal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "fn", "pytracker"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.AnalyzedCode._cache_key_getter_tracked_literal", "name": "_cache_key_getter_tracked_literal", "type": null}}, "_fns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.lambdas.AnalyzedCode._fns", "name": "_fns", "type": {".class": "Instance", "args": ["types.CodeType", "sqlalchemy.sql.lambdas.AnalyzedCode"], "type_ref": "weakref.<PERSON>eyDictionary"}}}, "_generation_mutex": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.lambdas.AnalyzedCode._generation_mutex", "name": "_generation_mutex", "type": "threading._RLock"}}, "_init_closure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fn"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.AnalyzedCode._init_closure", "name": "_init_closure", "type": null}}, "_init_globals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fn"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.AnalyzedCode._init_globals", "name": "_init_globals", "type": null}}, "_init_track_on": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "track_on"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.AnalyzedCode._init_track_on", "name": "_init_track_on", "type": null}}, "_raise_for_uncacheable_closure_variable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "variable_name", "fn", "from_"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.AnalyzedCode._raise_for_uncacheable_closure_variable", "name": "_raise_for_uncacheable_closure_variable", "type": null}}, "_roll_down_to_literal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "element"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.sql.lambdas.AnalyzedCode._roll_down_to_literal", "name": "_roll_down_to_literal", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.lambdas.AnalyzedCode._roll_down_to_literal", "name": "_roll_down_to_literal", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "element"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.sql.lambdas.AnalyzedCode"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_roll_down_to_literal of AnalyzedCode", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_setup_additional_closure_trackers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "fn", "lambda_element", "opts"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.AnalyzedCode._setup_additional_closure_trackers", "name": "_setup_additional_closure_trackers", "type": null}}, "bindparam_trackers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type"], "fullname": "sqlalchemy.sql.lambdas.AnalyzedCode.bindparam_trackers", "name": "bindparam_trackers", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "type_ref": "builtins.list"}}}, "build_py_wrappers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type"], "fullname": "sqlalchemy.sql.lambdas.AnalyzedCode.build_py_wrappers", "name": "build_py_wrappers", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "type_ref": "builtins.list"}}}, "closure_trackers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type"], "fullname": "sqlalchemy.sql.lambdas.AnalyzedCode.closure_trackers", "name": "closure_trackers", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "type_ref": "builtins.list"}}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["cls", "fn", "lambda_element", "lambda_kw", "kw"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.sql.lambdas.AnalyzedCode.get", "name": "get", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.lambdas.AnalyzedCode.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["cls", "fn", "lambda_element", "lambda_kw", "kw"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.sql.lambdas.AnalyzedCode"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of AnalyzedCode", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "track_bound_values": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.lambdas.AnalyzedCode.track_bound_values", "name": "track_bound_values", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "track_closure_variables": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.lambdas.AnalyzedCode.track_closure_variables", "name": "track_closure_variables", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "builtins.bool"]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.lambdas.AnalyzedCode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.lambdas.AnalyzedCode", "values": [], "variance": 0}, "slots": ["bindparam_trackers", "build_py_wrappers", "closure_trackers", "track_bound_values", "track_closure_variables"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AnalyzedFunction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.lambdas.AnalyzedFunction", "name": "AnalyzedFunction", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.lambdas.AnalyzedFunction", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.lambdas", "mro": ["sqlalchemy.sql.lambdas.AnalyzedFunction", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "analyzed_code", "lambda_element", "apply_propagate_attrs", "fn"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.AnalyzedFunction.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.lambdas.AnalyzedFunction.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_coerce_expression": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "lambda_element", "apply_propagate_attrs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.AnalyzedFunction._coerce_expression", "name": "_coerce_expression", "type": null}}, "_instrument_and_run_function": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "lambda_element"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.AnalyzedFunction._instrument_and_run_function", "name": "_instrument_and_run_function", "type": null}}, "_rewrite_code_obj": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "f", "cell_values", "globals_"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.AnalyzedFunction._rewrite_code_obj", "name": "_rewrite_code_obj", "type": null}}, "analyzed_code": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.lambdas.AnalyzedFunction.analyzed_code", "name": "analyzed_code", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "bindparam_trackers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.lambdas.AnalyzedFunction.bindparam_trackers", "name": "bindparam_trackers", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.lambdas._BoundParameterGetter"}], "type_ref": "builtins.list"}, {".class": "NoneType"}]}}}, "closure_bindparams": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.lambdas.AnalyzedFunction.closure_bindparams", "name": "closure_bindparams", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.elements.BindParameter"}], "type_ref": "builtins.list"}, {".class": "NoneType"}]}}}, "closure_pywrappers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type"], "fullname": "sqlalchemy.sql.lambdas.AnalyzedFunction.closure_pywrappers", "name": "closure_pywrappers", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "type_ref": "builtins.list"}}}, "expected_expr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.lambdas.AnalyzedFunction.expected_expr", "name": "expected_expr", "type": {".class": "UnionType", "items": ["sqlalchemy.sql.elements.ClauseElement", {".class": "Instance", "args": ["sqlalchemy.sql.elements.ClauseElement"], "type_ref": "builtins.list"}]}}}, "expr": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.lambdas.AnalyzedFunction.expr", "name": "expr", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "fn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.lambdas.AnalyzedFunction.fn", "name": "fn", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "is_sequence": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.lambdas.AnalyzedFunction.is_sequence", "name": "is_sequence", "type": "builtins.bool"}}, "propagate_attrs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.lambdas.AnalyzedFunction.propagate_attrs", "name": "propagate_attrs", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "tracker_instrumented_fn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.lambdas.AnalyzedFunction.tracker_instrumented_fn", "name": "tracker_instrumented_fn", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.lambdas.AnalyzedFunction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.lambdas.AnalyzedFunction", "values": [], "variance": 0}, "slots": ["analyzed_code", "bindparam_trackers", "closure_bindparams", "closure_pywrappers", "expected_expr", "expr", "fn", "is_sequence", "propagate_attrs", "tracker_instrumented_fn"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BindParameter": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.BindParameter", "kind": "Gdef"}, "CacheConst": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.cache_key.CacheConst", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "ClauseElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ClauseElement", "kind": "Gdef"}, "CodeType": {".class": "SymbolTableNode", "cross_ref": "types.CodeType", "kind": "Gdef"}, "ColumnOperators": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.operators.ColumnOperators", "kind": "Gdef"}, "DeferredLambdaElement": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.lambdas.LambdaElement"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.lambdas.DeferredLambdaElement", "name": "DeferredLambdaElement", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.lambdas.DeferredLambdaElement", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.lambdas", "mro": ["sqlalchemy.sql.lambdas.DeferredLambdaElement", "sqlalchemy.sql.lambdas.LambdaElement", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "fn", "role", "opts", "lambda_args"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.DeferredLambdaElement.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "fn", "role", "opts", "lambda_args"], "arg_types": ["sqlalchemy.sql.lambdas.DeferredLambdaElement", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.lambdas._AnyLambdaType"}, {".class": "TypeType", "item": "sqlalchemy.sql.roles.SQLRole"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "sqlalchemy.sql.lambdas.LambdaOptions"}, "sqlalchemy.sql.lambdas.LambdaOptions"]}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DeferredLambdaElement", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_copy_internals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "clone", "deferred_copy_internals", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.DeferredLambdaElement._copy_internals", "name": "_copy_internals", "type": null}}, "_invoke_user_fn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["self", "fn", "arg"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.DeferredLambdaElement._invoke_user_fn", "name": "_invoke_user_fn", "type": null}}, "_resolve_with_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "lambda_args"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.DeferredLambdaElement._resolve_with_args", "name": "_resolve_with_args", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "lambda_args"], "arg_types": ["sqlalchemy.sql.lambdas.DeferredLambdaElement", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_resolve_with_args of DeferredLambdaElement", "ret_type": "sqlalchemy.sql.elements.ClauseElement", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "lambda_args": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.lambdas.DeferredLambdaElement.lambda_args", "name": "lambda_args", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.lambdas.DeferredLambdaElement.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.lambdas.DeferredLambdaElement", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Executable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.Executable", "kind": "Gdef"}, "LambdaElement": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.elements.ClauseElement"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.lambdas.LambdaElement", "name": "LambdaElement", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.lambdas.LambdaElement", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.lambdas", "mro": ["sqlalchemy.sql.lambdas.LambdaElement", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.LambdaElement.__getattr__", "name": "__getattr__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "fn", "role", "opts", "apply_propagate_attrs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.LambdaElement.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "fn", "role", "opts", "apply_propagate_attrs"], "arg_types": ["sqlalchemy.sql.lambdas.LambdaElement", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.lambdas._LambdaType"}, {".class": "TypeType", "item": "sqlalchemy.sql.roles.SQLRole"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "sqlalchemy.sql.lambdas.LambdaOptions"}, "sqlalchemy.sql.lambdas.LambdaOptions"]}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.ClauseElement", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LambdaElement", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.LambdaElement.__repr__", "name": "__repr__", "type": null}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.lambdas.LambdaElement.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "_copy_internals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "clone", "deferred_copy_internals", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.LambdaElement._copy_internals", "name": "_copy_internals", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "clone", "deferred_copy_internals", "kw"], "arg_types": ["sqlalchemy.sql.lambdas.LambdaElement", "sqlalchemy.sql.visitors._CloneCallableType", {".class": "UnionType", "items": ["sqlalchemy.sql.visitors._CloneCallableType", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_copy_internals of LambdaElement", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_from_objects": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.lambdas.LambdaElement._from_objects", "name": "_from_objects", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.lambdas.LambdaElement._from_objects", "name": "_from_objects", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.lambdas.LambdaElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_from_objects of LambdaElement", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_gen_cache_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "anon_map", "<PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.LambdaElement._gen_cache_key", "name": "_gen_cache_key", "type": null}}, "_invoke_user_fn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["self", "fn", "arg"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.LambdaElement._invoke_user_fn", "name": "_invoke_user_fn", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["self", "fn", "arg"], "arg_types": ["sqlalchemy.sql.lambdas.LambdaElement", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.lambdas._AnyLambdaType"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_invoke_user_fn of LambdaElement", "ret_type": "sqlalchemy.sql.elements.ClauseElement", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_is_lambda_element": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.lambdas.LambdaElement._is_lambda_element", "name": "_is_lambda_element", "type": "builtins.bool"}}, "_is_sequence": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.lambdas.LambdaElement._is_sequence", "name": "_is_sequence", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.lambdas.LambdaElement._is_sequence", "name": "_is_sequence", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.lambdas.LambdaElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_sequence of LambdaElement", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_param_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.LambdaElement._param_dict", "name": "_param_dict", "type": null}}, "_rec": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.lambdas.LambdaElement._rec", "name": "_rec", "type": {".class": "UnionType", "items": ["sqlalchemy.sql.lambdas.AnalyzedFunction", "sqlalchemy.sql.lambdas.NonAnalyzedFunction"]}}}, "_resolved": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.sql.lambdas.LambdaElement._resolved", "name": "_resolved", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.lambdas.LambdaElement._resolved", "name": "_resolved", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "_resolved_bindparams": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.lambdas.LambdaElement._resolved_bindparams", "name": "_resolved_bindparams", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.elements.BindParameter"}], "type_ref": "builtins.list"}}}, "_retrieve_tracker_rec": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "fn", "apply_propagate_attrs", "opts"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.LambdaElement._retrieve_tracker_rec", "name": "_retrieve_tracker_rec", "type": null}}, "_select_iterable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.lambdas.LambdaElement._select_iterable", "name": "_select_iterable", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.lambdas.LambdaElement._select_iterable", "name": "_select_iterable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.lambdas.LambdaElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_select_iterable of LambdaElement", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_setup_binds_for_tracked_expr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "expr"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.LambdaElement._setup_binds_for_tracked_expr", "name": "_setup_binds_for_tracked_expr", "type": null}}, "_transforms": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.lambdas.LambdaElement._transforms", "name": "_transforms", "type": {".class": "Instance", "args": ["sqlalchemy.sql.visitors._CloneCallableType"], "type_ref": "builtins.tuple"}}}, "_traverse_internals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.lambdas.LambdaElement._traverse_internals", "name": "_traverse_internals", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "sqlalchemy.sql.visitors.InternalTraversal"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}}}, "closure_cache_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.lambdas.LambdaElement.closure_cache_key", "name": "closure_cache_key", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}, {".class": "LiteralType", "fallback": "sqlalchemy.sql.cache_key.CacheConst", "value": "NO_CACHE"}]}}}, "fn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.lambdas.LambdaElement.fn", "name": "fn", "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.lambdas._AnyLambdaType"}}}, "opts": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.lambdas.LambdaElement.opts", "name": "opts", "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": "sqlalchemy.sql.lambdas.LambdaOptions"}, "sqlalchemy.sql.lambdas.LambdaOptions"]}}}, "parent_lambda": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.lambdas.LambdaElement.parent_lambda", "name": "parent_lambda", "type": {".class": "UnionType", "items": ["sqlalchemy.sql.lambdas.StatementLambdaElement", {".class": "NoneType"}]}}}, "role": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.lambdas.LambdaElement.role", "name": "role", "type": {".class": "TypeType", "item": "sqlalchemy.sql.roles.SQLRole"}}}, "tracker_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.lambdas.LambdaElement.tracker_key", "name": "tracker_key", "type": {".class": "Instance", "args": ["types.CodeType"], "type_ref": "builtins.tuple"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.lambdas.LambdaElement.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.lambdas.LambdaElement", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LambdaOptions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.base.Options"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.lambdas.LambdaOptions", "name": "LambdaOptions", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.lambdas.LambdaOptions", "has_param_spec_type": false, "metaclass_type": "sqlalchemy.sql.base._MetaOptions", "metadata": {}, "module_name": "sqlalchemy.sql.lambdas", "mro": ["sqlalchemy.sql.lambdas.LambdaOptions", "sqlalchemy.sql.base.Options", "builtins.object"], "names": {".class": "SymbolTable", "enable_tracking": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.lambdas.LambdaOptions.enable_tracking", "name": "enable_tracking", "type": "builtins.bool"}}, "global_track_bound_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.lambdas.LambdaOptions.global_track_bound_values", "name": "global_track_bound_values", "type": "builtins.bool"}}, "lambda_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.lambdas.LambdaOptions.lambda_cache", "name": "lambda_cache", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.lambdas._LambdaCacheType"}, {".class": "NoneType"}]}}}, "track_bound_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.lambdas.LambdaOptions.track_bound_values", "name": "track_bound_values", "type": "builtins.bool"}}, "track_closure_variables": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.lambdas.LambdaOptions.track_closure_variables", "name": "track_closure_variables", "type": "builtins.bool"}}, "track_on": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.lambdas.LambdaOptions.track_on", "name": "track_on", "type": {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.lambdas.LambdaOptions.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.lambdas.LambdaOptions", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LinkedLambdaElement": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.lambdas.StatementLambdaElement"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.lambdas.LinkedLambdaElement", "name": "LinkedLambdaElement", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.lambdas.LinkedLambdaElement", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.lambdas", "mro": ["sqlalchemy.sql.lambdas.LinkedLambdaElement", "sqlalchemy.sql.lambdas.StatementLambdaElement", "sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.lambdas.LambdaElement", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "sqlalchemy.sql.base.Executable", "sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "fn", "parent_lambda", "opts"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.LinkedLambdaElement.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "fn", "parent_lambda", "opts"], "arg_types": ["sqlalchemy.sql.lambdas.LinkedLambdaElement", {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.lambdas._StmtLambdaElementType"}, "sqlalchemy.sql.lambdas.StatementLambdaElement", {".class": "UnionType", "items": [{".class": "TypeType", "item": "sqlalchemy.sql.lambdas.LambdaOptions"}, "sqlalchemy.sql.lambdas.LambdaOptions"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LinkedLambdaElement", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_invoke_user_fn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["self", "fn", "arg"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.LinkedLambdaElement._invoke_user_fn", "name": "_invoke_user_fn", "type": null}}, "parent_lambda": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.lambdas.LinkedLambdaElement.parent_lambda", "name": "parent_lambda", "type": "sqlalchemy.sql.lambdas.StatementLambdaElement"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.lambdas.LinkedLambdaElement.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.lambdas.LinkedLambdaElement", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef"}, "MutableMapping": {".class": "SymbolTableNode", "cross_ref": "typing.MutableMapping", "kind": "Gdef"}, "NonAnalyzedFunction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.lambdas.NonAnalyzedFunction", "name": "NonAnalyzedFunction", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.lambdas.NonAnalyzedFunction", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.lambdas", "mro": ["sqlalchemy.sql.lambdas.NonAnalyzedFunction", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "expr"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.NonAnalyzedFunction.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "expr"], "arg_types": ["sqlalchemy.sql.lambdas.NonAnalyzedFunction", "sqlalchemy.sql.elements.ClauseElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NonAnalyzedFunction", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.lambdas.NonAnalyzedFunction.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "bindparam_trackers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.lambdas.NonAnalyzedFunction.bindparam_trackers", "name": "bindparam_trackers", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.lambdas._BoundParameterGetter"}], "type_ref": "builtins.list"}, {".class": "NoneType"}]}}}, "closure_bindparams": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.lambdas.NonAnalyzedFunction.closure_bindparams", "name": "closure_bindparams", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.elements.BindParameter"}], "type_ref": "builtins.list"}, {".class": "NoneType"}]}}}, "expected_expr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.lambdas.NonAnalyzedFunction.expected_expr", "name": "expected_expr", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.lambdas.NonAnalyzedFunction"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "expected_expr of NonAnalyzedFunction", "ret_type": "sqlalchemy.sql.elements.ClauseElement", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.lambdas.NonAnalyzedFunction.expected_expr", "name": "expected_expr", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.lambdas.NonAnalyzedFunction"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "expected_expr of NonAnalyzedFunction", "ret_type": "sqlalchemy.sql.elements.ClauseElement", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "expr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.lambdas.NonAnalyzedFunction.expr", "name": "expr", "type": "sqlalchemy.sql.elements.ClauseElement"}}, "is_sequence": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.lambdas.NonAnalyzedFunction.is_sequence", "name": "is_sequence", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.lambdas.NonAnalyzedFunction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.lambdas.NonAnalyzedFunction", "values": [], "variance": 0}, "slots": ["expr"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NullLambdaStatement": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.elements.ClauseElement"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.lambdas.NullLambdaStatement", "name": "NullLambdaStatement", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.lambdas.NullLambdaStatement", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.lambdas", "mro": ["sqlalchemy.sql.lambdas.NullLambdaStatement", "sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__add__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.NullLambdaStatement.__add__", "name": "__add__", "type": null}}, "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.NullLambdaStatement.__getattr__", "name": "__getattr__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "statement"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.NullLambdaStatement.__init__", "name": "__init__", "type": null}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.lambdas.NullLambdaStatement.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "_execute_on_connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "connection", "distilled_params", "execution_options"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.NullLambdaStatement._execute_on_connection", "name": "_execute_on_connection", "type": null}}, "_is_lambda_element": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.lambdas.NullLambdaStatement._is_lambda_element", "name": "_is_lambda_element", "type": "builtins.bool"}}, "_resolved": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.lambdas.NullLambdaStatement._resolved", "name": "_resolved", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_traverse_internals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.lambdas.NullLambdaStatement._traverse_internals", "name": "_traverse_internals", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "sqlalchemy.sql.visitors.InternalTraversal"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}}}, "add_criteria": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "other", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.NullLambdaStatement.add_criteria", "name": "add_criteria", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.lambdas.NullLambdaStatement.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.lambdas.NullLambdaStatement", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Options": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.Options", "kind": "Gdef"}, "PyWrapper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.operators.ColumnOperators"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.lambdas.PyWrapper", "name": "PyWrapper", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.lambdas.PyWrapper", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.lambdas", "mro": ["sqlalchemy.sql.lambdas.PyWrapper", "sqlalchemy.sql.operators.ColumnOperators", "sqlalchemy.sql.operators.Operators", "builtins.object"], "names": {".class": "SymbolTable", "__bool__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.PyWrapper.__bool__", "name": "__bool__", "type": null}}, "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "arg", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.PyWrapper.__call__", "name": "__call__", "type": null}}, "__getattribute__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.PyWrapper.__getattribute__", "name": "__getattribute__", "type": null}}, "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.PyWrapper.__getitem__", "name": "__getitem__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "fn", "name", "to_evaluate", "closure_index", "getter", "track_bound_values"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.PyWrapper.__init__", "name": "__init__", "type": null}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.PyWrapper.__iter__", "name": "__iter__", "type": null}}, "_add_getter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "getter_fn"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.PyWrapper._add_getter", "name": "_add_getter", "type": null}}, "_bind_paths": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type"], "fullname": "sqlalchemy.sql.lambdas.PyWrapper._bind_paths", "name": "_bind_paths", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "type_ref": "builtins.dict"}}}, "_closure_index": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.lambdas.PyWrapper._closure_index", "name": "_closure_index", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_extract_bound_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "starting_point", "result_list"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.PyWrapper._extract_bound_parameters", "name": "_extract_bound_parameters", "type": null}}, "_getter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.lambdas.PyWrapper._getter", "name": "_getter", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_has_param": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.lambdas.PyWrapper._has_param", "name": "_has_param", "type": "builtins.bool"}}, "_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.lambdas.PyWrapper._name", "name": "_name", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_param": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type"], "fullname": "sqlalchemy.sql.lambdas.PyWrapper._param", "name": "_param", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "NoneType"}]}}}, "_py_wrapper_literal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "expr", "operator", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.PyWrapper._py_wrapper_literal", "name": "_py_wrapper_literal", "type": null}}, "_to_evaluate": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.lambdas.PyWrapper._to_evaluate", "name": "_to_evaluate", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "fn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.lambdas.PyWrapper.fn", "name": "fn", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "operate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "op", "other", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.PyWrapper.operate", "name": "operate", "type": null}}, "reverse_operate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "op", "other", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.PyWrapper.reverse_operate", "name": "reverse_operate", "type": null}}, "track_bound_values": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.lambdas.PyWrapper.track_bound_values", "name": "track_bound_values", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.lambdas.PyWrapper.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.lambdas.PyWrapper", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SQLRole": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.roles.SQLRole", "kind": "Gdef"}, "StatementLambdaElement": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.lambdas.LambdaElement", "sqlalchemy.sql.base.Executable"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.lambdas.StatementLambdaElement", "name": "StatementLambdaElement", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.lambdas.StatementLambdaElement", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.lambdas", "mro": ["sqlalchemy.sql.lambdas.StatementLambdaElement", "sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.lambdas.LambdaElement", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "sqlalchemy.sql.base.Executable", "sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__add__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.StatementLambdaElement.__add__", "name": "__add__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.sql.lambdas.StatementLambdaElement", {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.lambdas._StmtLambdaElementType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__add__ of StatementLambdaElement", "ret_type": "sqlalchemy.sql.lambdas.StatementLambdaElement", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "fn", "role", "opts", "apply_propagate_attrs"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.sql.lambdas.StatementLambdaElement.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "fn", "role", "opts", "apply_propagate_attrs"], "arg_types": ["sqlalchemy.sql.lambdas.StatementLambdaElement", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.lambdas._StmtLambdaType"}, {".class": "TypeType", "item": "sqlalchemy.sql.roles.SQLRole"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "sqlalchemy.sql.lambdas.LambdaOptions"}, "sqlalchemy.sql.lambdas.LambdaOptions"]}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.ClauseElement", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of StatementLambdaElement", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_all_selected_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.lambdas.StatementLambdaElement._all_selected_columns", "name": "_all_selected_columns", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.lambdas.StatementLambdaElement._all_selected_columns", "name": "_all_selected_columns", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.lambdas.StatementLambdaElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_all_selected_columns of StatementLambdaElement", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_effective_plugin_target": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.lambdas.StatementLambdaElement._effective_plugin_target", "name": "_effective_plugin_target", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.lambdas.StatementLambdaElement._effective_plugin_target", "name": "_effective_plugin_target", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.lambdas.StatementLambdaElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_effective_plugin_target of StatementLambdaElement", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_execute_on_connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "connection", "distilled_params", "execution_options"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.StatementLambdaElement._execute_on_connection", "name": "_execute_on_connection", "type": null}}, "_execution_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.lambdas.StatementLambdaElement._execution_options", "name": "_execution_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.lambdas.StatementLambdaElement._execution_options", "name": "_execution_options", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.lambdas.StatementLambdaElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_execution_options of StatementLambdaElement", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_proxied": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.lambdas.StatementLambdaElement._proxied", "name": "_proxied", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.lambdas.StatementLambdaElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_proxied of StatementLambdaElement", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.lambdas.StatementLambdaElement._proxied", "name": "_proxied", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.lambdas.StatementLambdaElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_proxied of StatementLambdaElement", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_with_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.lambdas.StatementLambdaElement._with_options", "name": "_with_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.lambdas.StatementLambdaElement._with_options", "name": "_with_options", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.lambdas.StatementLambdaElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_with_options of StatementLambdaElement", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "add_criteria": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "other", "enable_tracking", "track_on", "track_closure_variables", "track_bound_values"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.StatementLambdaElement.add_criteria", "name": "add_criteria", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "other", "enable_tracking", "track_on", "track_closure_variables", "track_bound_values"], "arg_types": ["sqlalchemy.sql.lambdas.StatementLambdaElement", {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.lambdas._StmtLambdaElementType"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_criteria of StatementLambdaElement", "ret_type": "sqlalchemy.sql.lambdas.StatementLambdaElement", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "is_delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.lambdas.StatementLambdaElement.is_delete", "name": "is_delete", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.lambdas.StatementLambdaElement.is_delete", "name": "is_delete", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.lambdas.StatementLambdaElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_delete of StatementLambdaElement", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "is_dml": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.lambdas.StatementLambdaElement.is_dml", "name": "is_dml", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.lambdas.StatementLambdaElement.is_dml", "name": "is_dml", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.lambdas.StatementLambdaElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_dml of StatementLambdaElement", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "is_insert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.lambdas.StatementLambdaElement.is_insert", "name": "is_insert", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.lambdas.StatementLambdaElement.is_insert", "name": "is_insert", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.lambdas.StatementLambdaElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_insert of StatementLambdaElement", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "is_select": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.lambdas.StatementLambdaElement.is_select", "name": "is_select", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.lambdas.StatementLambdaElement.is_select", "name": "is_select", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.lambdas.StatementLambdaElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_select of StatementLambdaElement", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "is_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.lambdas.StatementLambdaElement.is_text", "name": "is_text", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.lambdas.StatementLambdaElement.is_text", "name": "is_text", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.lambdas.StatementLambdaElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_text of StatementLambdaElement", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "is_update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.lambdas.StatementLambdaElement.is_update", "name": "is_update", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.lambdas.StatementLambdaElement.is_update", "name": "is_update", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.lambdas.StatementLambdaElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_update of StatementLambdaElement", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "spoil": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.StatementLambdaElement.spoil", "name": "spoil", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.lambdas.StatementLambdaElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "spoil of StatementLambdaElement", "ret_type": "sqlalchemy.sql.lambdas.NullLambdaStatement", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.lambdas.StatementLambdaElement.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.lambdas.StatementLambdaElement", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_AnyLambdaType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.sql.lambdas._AnyLambdaType", "line": 64, "no_args": false, "normalized": false, "target": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_BoundParameterGetter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.sql.lambdas._BoundParameterGetter", "line": 57, "no_args": false, "normalized": false, "target": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_CloneCallableType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.visitors._CloneCallableType", "kind": "Gdef"}, "_E": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.lambdas._E", "name": "_E", "upper_bound": "sqlalchemy.sql.base.Executable", "values": [], "variance": 0}}, "_LambdaCacheType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.sql.lambdas._LambdaCacheType", "line": 54, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": ["sqlalchemy.sql.lambdas.NonAnalyzedFunction", "sqlalchemy.sql.lambdas.AnalyzedFunction"]}], "type_ref": "typing.MutableMapping"}}}, "_LambdaType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.sql.lambdas._LambdaType", "line": 62, "no_args": false, "normalized": false, "target": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_StmtLambdaElementType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.lambdas._E", "id": 1, "name": "_E", "namespace": "sqlalchemy.sql.lambdas._StmtLambdaElementType", "upper_bound": "sqlalchemy.sql.base.Executable", "values": [], "variance": 0}], "column": 0, "fullname": "sqlalchemy.sql.lambdas._StmtLambdaElementType", "line": 69, "no_args": false, "normalized": false, "target": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.lambdas._E", "id": 1, "name": "_E", "namespace": "sqlalchemy.sql.lambdas._StmtLambdaElementType", "upper_bound": "sqlalchemy.sql.base.Executable", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_StmtLambdaType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.sql.lambdas._StmtLambdaType", "line": 66, "no_args": false, "normalized": false, "target": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.lambdas.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.lambdas.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.lambdas.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.lambdas.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.lambdas.__package__", "name": "__package__", "type": "builtins.str"}}, "_cache_key": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.cache_key", "kind": "Gdef"}, "_clone": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base._clone", "kind": "Gdef"}, "_closure_per_cache_key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.lambdas._closure_per_cache_key", "name": "_closure_per_cache_key", "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.lambdas._LambdaCacheType"}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "coercions": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.coercions", "kind": "Gdef"}, "collections_abc": {".class": "SymbolTableNode", "cross_ref": "collections.abc", "kind": "Gdef"}, "elements": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements", "kind": "Gdef"}, "exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc", "kind": "Gdef"}, "insp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["lmb"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.sql.lambdas.insp", "name": "insp", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.lambdas.insp", "name": "insp", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["lmb"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "insp", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef"}, "inspection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.inspection", "kind": "Gdef"}, "itertools": {".class": "SymbolTableNode", "cross_ref": "itertools", "kind": "Gdef"}, "lambda_stmt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["lmb", "enable_tracking", "track_closure_variables", "track_on", "global_track_bound_values", "track_bound_values", "lambda_cache"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.lambdas.lambda_stmt", "name": "lambda_stmt", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["lmb", "enable_tracking", "track_closure_variables", "track_on", "global_track_bound_values", "track_bound_values", "lambda_cache"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.lambdas._StmtLambdaType"}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}]}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.lambdas._LambdaCacheType"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lambda_stmt", "ret_type": "sqlalchemy.sql.lambdas.StatementLambdaElement", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "operator": {".class": "SymbolTableNode", "cross_ref": "operator", "kind": "Gdef"}, "roles": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.roles", "kind": "Gdef"}, "schema": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema", "kind": "Gdef"}, "threading": {".class": "SymbolTableNode", "cross_ref": "threading", "kind": "Gdef"}, "types": {".class": "SymbolTableNode", "cross_ref": "types", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}, "visitors": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.visitors", "kind": "Gdef"}, "weakref": {".class": "SymbolTableNode", "cross_ref": "weakref", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\lambdas.py"}