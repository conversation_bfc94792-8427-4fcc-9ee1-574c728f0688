{"data_mtime": 1759503829, "dep_lines": [21, 26, 19, 22, 23, 24, 29, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 25, 5, 20, 20, 30, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "markdown_it.utils", "__future__", "dataclasses", "typing", "warnings", "markdown_it", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "_warnings", "abc", "markdown_it.main", "types", "typing_extensions"], "hash": "78c02d5864407d2337dda88979dd24e7ddb704492e315b0cab572df2f53eaa5e", "id": "markdown_it.ruler", "ignore_all": true, "interface_hash": "8d3542da793d849a21c4acb753e1a5b587c1d5d6f1964f31f151a3ff71a4684f", "mtime": 1757092234, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\markdown_it\\ruler.py", "plugin_data": null, "size": 9142, "suppressed": [], "version_id": "1.8.0"}