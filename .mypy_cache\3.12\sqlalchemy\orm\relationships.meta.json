{"data_mtime": 1759504547, "dep_lines": [46, 47, 48, 50, 58, 66, 76, 77, 78, 79, 80, 81, 83, 84, 86, 94, 107, 109, 110, 111, 112, 113, 114, 115, 126, 127, 19, 46, 69, 71, 72, 73, 74, 75, 16, 18, 20, 21, 22, 23, 24, 44, 69, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 5, 5, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 10, 20, 10, 10, 10, 10, 10, 5, 5, 10, 10, 10, 10, 10, 5, 10, 5, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.attributes", "sqlalchemy.orm.strategy_options", "sqlalchemy.orm._typing", "sqlalchemy.orm.base", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.util", "sqlalchemy.sql.coercions", "sqlalchemy.sql.expression", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.visitors", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.elements", "sqlalchemy.sql.util", "sqlalchemy.util.typing", "sqlalchemy.orm.clsregistry", "sqlalchemy.orm.decl_base", "sqlalchemy.orm.dependency", "sqlalchemy.orm.mapper", "sqlalchemy.orm.query", "sqlalchemy.orm.session", "sqlalchemy.orm.state", "sqlalchemy.orm.strategies", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "collections.abc", "sqlalchemy.orm", "sqlalchemy.exc", "sqlalchemy.log", "sqlalchemy.schema", "sqlalchemy.sql", "sqlalchemy.util", "sqlalchemy.inspection", "__future__", "collections", "dataclasses", "inspect", "itertools", "re", "typing", "weakref", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "_typeshed", "abc", "enum", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.orm.decl_api", "sqlalchemy.sql._elements_constructors", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.traversals", "sqlalchemy.util._collections", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "sqlalchemy.util.preloaded", "types"], "hash": "f8546447c8154686679367941ae7b2fffb9a0006ebd371943fb6c9dc8d35cada", "id": "sqlalchemy.orm.relationships", "ignore_all": true, "interface_hash": "a4b75c7f6265eb211c7ed48f2899e92c93564a486d80ff861b6eb8286b869d0c", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\relationships.py", "plugin_data": null, "size": 132271, "suppressed": [], "version_id": "1.8.0"}