{".class": "MypyFile", "_fullname": "pydantic.annotated_handlers", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "CoreSchemaOrField": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "pydantic.annotated_handlers.CoreSchemaOrField", "line": 13, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ModelField"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.DataclassField"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.TypedDictField"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ComputedField"}]}}}, "GetCoreSchemaHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.annotated_handlers.GetCoreSchemaHandler", "name": "GetCoreSchemaHandler", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic.annotated_handlers.GetCoreSchemaHandler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.annotated_handlers", "mro": ["pydantic.annotated_handlers.GetCoreSchemaHandler", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.annotated_handlers.GetCoreSchemaHandler.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pydantic.annotated_handlers.GetCoreSchemaHandler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of GetCoreSchemaHandler", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_get_types_namespace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.annotated_handlers.GetCoreSchemaHandler._get_types_namespace", "name": "_get_types_namespace", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.annotated_handlers.GetCoreSchemaHandler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_types_namespace of GetCoreSchemaHandler", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.NamespacesTuple"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "field_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "pydantic.annotated_handlers.GetCoreSchemaHandler.field_name", "name": "field_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.annotated_handlers.GetCoreSchemaHandler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "field_name of GetCoreSchemaHandler", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic.annotated_handlers.GetCoreSchemaHandler.field_name", "name": "field_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.annotated_handlers.GetCoreSchemaHandler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "field_name of GetCoreSchemaHandler", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "generate_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.annotated_handlers.GetCoreSchemaHandler.generate_schema", "name": "generate_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pydantic.annotated_handlers.GetCoreSchemaHandler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_schema of GetCoreSchemaHandler", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "resolve_ref_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.annotated_handlers.GetCoreSchemaHandler.resolve_ref_schema", "name": "resolve_ref_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pydantic.annotated_handlers.GetCoreSchemaHandler", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolve_ref_schema of GetCoreSchemaHandler", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.annotated_handlers.GetCoreSchemaHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.annotated_handlers.GetCoreSchemaHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GetJsonSchemaHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.annotated_handlers.GetJsonSchemaHandler", "name": "GetJsonSchemaHandler", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic.annotated_handlers.GetJsonSchemaHandler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.annotated_handlers", "mro": ["pydantic.annotated_handlers.GetJsonSchemaHandler", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.annotated_handlers.GetJsonSchemaHandler.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pydantic.annotated_handlers.GetJsonSchemaHandler", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.annotated_handlers.CoreSchemaOrField"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of GetJsonSchemaHandler", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic.annotated_handlers.GetJsonSchemaHandler.mode", "name": "mode", "type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaMode"}}}, "resolve_ref_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.annotated_handlers.GetJsonSchemaHandler.resolve_ref_schema", "name": "resolve_ref_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pydantic.annotated_handlers.GetJsonSchemaHandler", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolve_ref_schema of GetJsonSchemaHandler", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.annotated_handlers.GetJsonSchemaHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.annotated_handlers.GetJsonSchemaHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "JsonSchemaMode": {".class": "SymbolTableNode", "cross_ref": "pydantic.json_schema.JsonSchemaMode", "kind": "Gdef", "module_public": false}, "JsonSchemaValue": {".class": "SymbolTableNode", "cross_ref": "pydantic.json_schema.JsonSchemaValue", "kind": "Gdef", "module_public": false}, "NamespacesTuple": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._namespace_utils.NamespacesTuple", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic.annotated_handlers.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.annotated_handlers.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.annotated_handlers.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.annotated_handlers.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.annotated_handlers.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.annotated_handlers.__package__", "name": "__package__", "type": "builtins.str"}}, "_annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "core_schema": {".class": "SymbolTableNode", "cross_ref": "pydantic_core.core_schema", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\annotated_handlers.py"}