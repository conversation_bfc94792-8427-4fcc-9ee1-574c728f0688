{".class": "MypyFile", "_fullname": "botocore.httpsession", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "AWSPreparedRequest": {".class": "SymbolTableNode", "cross_ref": "botocore.awsrequest.AWSPreparedRequest", "kind": "Gdef", "module_hidden": true, "module_public": false}, "AWSRequest": {".class": "SymbolTableNode", "cross_ref": "botocore.awsrequest.AWSRequest", "kind": "Gdef", "module_hidden": true, "module_public": false}, "AWSResponse": {".class": "SymbolTableNode", "cross_ref": "botocore.awsrequest.AWSResponse", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ConnectTimeoutError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.ConnectTimeoutError", "kind": "Gdef"}, "ConnectionClosedError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.ConnectionClosedError", "kind": "Gdef"}, "DEFAULT_CA_BUNDLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "botocore.httpsession.DEFAULT_CA_BUNDLE", "name": "DEFAULT_CA_BUNDLE", "type": "builtins.str"}}, "DEFAULT_CIPHERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "botocore.httpsession.DEFAULT_CIPHERS", "name": "DEFAULT_CIPHERS", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "DEFAULT_TIMEOUT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "botocore.httpsession.DEFAULT_TIMEOUT", "name": "DEFAULT_TIMEOUT", "type": "builtins.int"}}, "EndpointConnectionError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.EndpointConnectionError", "kind": "Gdef"}, "HTTPClientError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.HTTPClientError", "kind": "Gdef"}, "IPV6_ADDRZ_RE": {".class": "SymbolTableNode", "cross_ref": "botocore.compat.IPV6_ADDRZ_RE", "kind": "Gdef"}, "InvalidProxiesConfigError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.InvalidProxiesConfigError", "kind": "Gdef"}, "Logger": {".class": "SymbolTableNode", "cross_ref": "logging.Logger", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MAX_POOL_CONNECTIONS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "botocore.httpsession.MAX_POOL_CONNECTIONS", "name": "MAX_POOL_CONNECTIONS", "type": "builtins.int"}}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ProxyConfiguration": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.httpsession.ProxyConfiguration", "name": "ProxyConfiguration", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.httpsession.ProxyConfiguration", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.httpsession", "mro": ["botocore.httpsession.ProxyConfiguration", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "proxies", "proxies_settings"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.httpsession.ProxyConfiguration.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "proxies", "proxies_settings"], "arg_types": ["botocore.httpsession.ProxyConfiguration", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ProxyConfiguration", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "proxy_headers_for": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "proxy_url"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.httpsession.ProxyConfiguration.proxy_headers_for", "name": "proxy_headers_for", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "proxy_url"], "arg_types": ["botocore.httpsession.ProxyConfiguration", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "proxy_headers_for of ProxyConfiguration", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "proxy_url_for": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "url"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.httpsession.ProxyConfiguration.proxy_url_for", "name": "proxy_url_for", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "url"], "arg_types": ["botocore.httpsession.ProxyConfiguration", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "proxy_url_for of ProxyConfiguration", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "settings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "botocore.httpsession.ProxyConfiguration.settings", "name": "settings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.httpsession.ProxyConfiguration"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "settings of ProxyConfiguration", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "botocore.httpsession.ProxyConfiguration.settings", "name": "settings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.httpsession.ProxyConfiguration"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "settings of ProxyConfiguration", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.httpsession.ProxyConfiguration.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.httpsession.ProxyConfiguration", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ProxyConnectionError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.ProxyConnectionError", "kind": "Gdef"}, "ReadTimeoutError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.ReadTimeoutError", "kind": "Gdef"}, "SSLError": {".class": "SymbolTableNode", "cross_ref": "botocore.exceptions.SSLError", "kind": "Gdef"}, "URLLib3Session": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.httpsession.URLLib3Session", "name": "URLLib3Session", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.httpsession.URLLib3Session", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.httpsession", "mro": ["botocore.httpsession.URLLib3Session", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "verify", "proxies", "timeout", "max_pool_connections", "socket_options", "client_cert", "proxies_config"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.httpsession.URLLib3Session.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "verify", "proxies", "timeout", "max_pool_connections", "socket_options", "client_cert", "proxies_config"], "arg_types": ["botocore.httpsession.URLLib3Session", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of URLLib3Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.httpsession.URLLib3Session.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.httpsession.URLLib3Session"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of URLLib3Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "send": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.httpsession.URLLib3Session.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["botocore.httpsession.URLLib3Session", {".class": "UnionType", "items": ["botocore.awsrequest.AWSRequest", "botocore.awsrequest.AWSPreparedRequest"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send of URLLib3Session", "ret_type": "botocore.awsrequest.AWSResponse", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.httpsession.URLLib3Session.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.httpsession.URLLib3Session", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.httpsession.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.httpsession.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.httpsession.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.httpsession.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.httpsession.__package__", "name": "__package__", "type": "builtins.str"}}, "create_urllib3_context": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1, 1], "arg_names": ["ssl_version", "cert_reqs", "options", "ciphers"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.httpsession.create_urllib3_context", "name": "create_urllib3_context", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1], "arg_names": ["ssl_version", "cert_reqs", "options", "ciphers"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_urllib3_context", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "ensure_boolean": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["val"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.httpsession.ensure_boolean", "name": "ensure_boolean", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ensure_boolean", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "filter_ssl_warnings": {".class": "SymbolTableNode", "cross_ref": "botocore.compat.filter_ssl_warnings", "kind": "Gdef"}, "get_cert_path": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["verify"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.httpsession.get_cert_path", "name": "get_cert_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["verify"], "arg_types": ["builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_cert_path", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "botocore.httpsession.logger", "name": "logger", "type": "logging.Logger"}}, "mask_proxy_url": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["proxy_url"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.httpsession.mask_proxy_url", "name": "mask_proxy_url", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["proxy_url"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mask_proxy_url", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "urlparse": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urlparse", "kind": "Gdef"}, "where": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.httpsession.where", "name": "where", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "where", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\botocore-stubs\\httpsession.pyi"}