{"data_mtime": 1759504547, "dep_lines": [14, 15, 18, 19, 9, 11, 18, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 5, 5, 20, 5, 20, 20, 30, 30], "dependencies": ["sqlalchemy.event.base", "sqlalchemy.event.registry", "sqlalchemy.exc", "sqlalchemy.util", "__future__", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "sqlalchemy.util.langhelpers"], "hash": "21e4f0a7ef0154e83b77ee4a335de6d3ef18b2a7f6799832d5c2a06142a71f6e", "id": "sqlalchemy.event.api", "ignore_all": true, "interface_hash": "a4e397566d66f8d6f7cd3df895b64ac8b2e9ca85a35c2de129fa93bf012fad5f", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\event\\api.py", "plugin_data": null, "size": 8329, "suppressed": [], "version_id": "1.8.0"}