{"data_mtime": 1759503866, "dep_lines": [44, 45, 46, 48, 58, 65, 66, 67, 68, 70, 72, 80, 82, 88, 44, 53, 54, 55, 86, 12, 14, 15, 16, 18, 19, 20, 53, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 25, 25, 25, 25, 25, 25, 25, 25, 25, 20, 10, 10, 5, 25, 5, 10, 5, 5, 10, 10, 5, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.roles", "sqlalchemy.sql.visitors", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.traversals", "sqlalchemy.util.typing", "sqlalchemy.sql.coercions", "sqlalchemy.sql.elements", "sqlalchemy.sql.type_api", "sqlalchemy.sql._orm_types", "sqlalchemy.sql._typing", "sqlalchemy.sql.compiler", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.engine.interfaces", "sqlalchemy.sql", "sqlalchemy.event", "sqlalchemy.exc", "sqlalchemy.util", "sqlalchemy.engine", "__future__", "collections", "enum", "itertools", "operator", "re", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "_operator", "_typeshed", "abc", "sqlalchemy.engine.base", "sqlalchemy.engine.cursor", "sqlalchemy.engine.result", "sqlalchemy.event.base", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql._py_util", "sqlalchemy.sql.annotation", "sqlalchemy.sql.operators", "sqlalchemy.util._collections", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "sqlalchemy.util.preloaded", "types", "typing_extensions"], "hash": "8dcb3a14850da8429c3d692a3b7e394943c5b2cb8845226e2bd625f1050eddc2", "id": "sqlalchemy.sql.base", "ignore_all": true, "interface_hash": "3a4ea3add24a6f59c10dbd6120144f0765bc1f861d019b486eb48f03cc988525", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\base.py", "plugin_data": null, "size": 78078, "suppressed": [], "version_id": "1.8.0"}