{"data_mtime": 1759504547, "dep_lines": [18, 19, 20, 26, 29, 31, 42, 43, 46, 56, 59, 62, 62, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 20, 5, 20, 20, 30, 30], "dependencies": ["sqlalchemy.engine.events", "sqlalchemy.engine.util", "sqlalchemy.engine.base", "sqlalchemy.engine.create", "sqlalchemy.engine.cursor", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.mock", "sqlalchemy.engine.reflection", "sqlalchemy.engine.result", "sqlalchemy.engine.row", "sqlalchemy.engine.url", "sqlalchemy.sql.ddl", "sqlalchemy.sql", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "typing"], "hash": "4025497d299a70cc2b4f7b8a3a3184820c369cfe9ea1a61e08f3e187fbc065e2", "id": "sqlalchemy.engine", "ignore_all": true, "interface_hash": "339c2dc33bc62e7b7e1c6256fab2eaf5b16eceec7fe39561d354586e0704beea", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\__init__.py", "plugin_data": null, "size": 2880, "suppressed": [], "version_id": "1.8.0"}