{"data_mtime": 1759504515, "dep_lines": [5, 10, 11, 14, 1, 2, 3, 4, 6, 7, 8, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 10, 5, 5, 5, 5, 5, 5, 20, 20, 30], "dependencies": ["collections.abc", "asyncio.events", "asyncio.futures", "asyncio.mixins", "enum", "sys", "_typeshed", "collections", "types", "typing", "typing_extensions", "builtins", "pyexpat.model", "pyexpat.errors", "abc"], "hash": "64e232873ebf25459f59274654df68db03c448572a81250891efc6d8dced357a", "id": "asyncio.locks", "ignore_all": true, "interface_hash": "1627b1e7554770135aa417207f2b440e39520f32772bb7ac5d0ef5e923fe4f51", "mtime": 1757092230, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\asyncio\\locks.pyi", "plugin_data": null, "size": 4072, "suppressed": [], "version_id": "1.8.0"}