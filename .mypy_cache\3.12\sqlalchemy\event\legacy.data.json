{".class": "MypyFile", "_fullname": "sqlalchemy.event.legacy", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "FullArgSpec": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.compat.FullArgSpec", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "_ClsLevelDispatch": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.attr._ClsLevelDispatch", "kind": "Gdef"}, "_ET": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.registry._ET", "kind": "Gdef"}, "_HasEventsDispatch": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.base._HasEventsDispatch", "kind": "Gdef"}, "_LegacySignatureType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.event.legacy._LegacySignatureType", "line": 32, "no_args": false, "normalized": false, "target": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_ListenerFnType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.registry._ListenerFnType", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.event.legacy.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.event.legacy.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.event.legacy.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.event.legacy.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.event.legacy.__package__", "name": "__package__", "type": "builtins.str"}}, "_augment_fn_docs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["dispatch_collection", "parent_dispatch_cls", "fn"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.legacy._augment_fn_docs", "name": "_augment_fn_docs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["dispatch_collection", "parent_dispatch_cls", "fn"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": -1, "name": "_ET", "namespace": "", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._ClsLevelDispatch"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": -1, "name": "_ET", "namespace": "", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.base._HasEventsDispatch"}}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_augment_fn_docs", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": -1, "name": "_ET", "namespace": "", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}]}}}, "_indent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["text", "indent"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.legacy._indent", "name": "_indent", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["text", "indent"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_indent", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_legacy_listen_examples": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["dispatch_collection", "sample_target", "fn"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.legacy._legacy_listen_examples", "name": "_legacy_listen_examples", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["dispatch_collection", "sample_target", "fn"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": -1, "name": "_ET", "namespace": "", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._ClsLevelDispatch"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_legacy_listen_examples", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": -1, "name": "_ET", "namespace": "", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}]}}}, "_legacy_signature": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["since", "argnames", "converter"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.legacy._legacy_signature", "name": "_legacy_signature", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["since", "argnames", "converter"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_legacy_signature", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "after_bulk_delete of SessionEvents", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_standard_listen_example": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["dispatch_collection", "sample_target", "fn"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.legacy._standard_listen_example", "name": "_standard_listen_example", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["dispatch_collection", "sample_target", "fn"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": -1, "name": "_ET", "namespace": "", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._ClsLevelDispatch"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_standard_listen_example", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": -1, "name": "_ET", "namespace": "", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}]}}}, "_version_signature_changes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["parent_dispatch_cls", "dispatch_collection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.legacy._version_signature_changes", "name": "_version_signature_changes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["parent_dispatch_cls", "dispatch_collection"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": -1, "name": "_ET", "namespace": "", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.base._HasEventsDispatch"}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": -1, "name": "_ET", "namespace": "", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._ClsLevelDispatch"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_version_signature_changes", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": -1, "name": "_ET", "namespace": "", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}]}}}, "_wrap_fn_for_legacy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["dispatch_collection", "fn", "argspec"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.legacy._wrap_fn_for_legacy", "name": "_wrap_fn_for_legacy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["dispatch_collection", "fn", "argspec"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": -1, "name": "_ET", "namespace": "", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._ClsLevelDispatch"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.compat.FullArgSpec"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_wrap_fn_for_legacy", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": -1, "name": "_ET", "namespace": "", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}]}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\event\\legacy.py"}