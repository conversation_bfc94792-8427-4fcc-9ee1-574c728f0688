{"data_mtime": 1759503829, "dep_lines": [1, 7, 1, 2, 3, 4, 5, 6, 8, 9, 10, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 20, 10, 10, 10, 10, 5, 5, 5, 5, 5, 20, 20, 30, 30, 30], "dependencies": ["email.message", "collections.abc", "email", "io", "ssl", "sys", "types", "_typeshed", "socket", "typing", "typing_extensions", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "_socket", "abc"], "hash": "2dcdd5eaf91861da72a4a9f270f4885eb823672ab56f1abbea5e07d719dc35df", "id": "http.client", "ignore_all": true, "interface_hash": "3b55731444decfedab39fc7743ad21fcfa025cc5a2ff06a23910825a85823740", "mtime": 1757092231, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\http\\client.pyi", "plugin_data": null, "size": 7347, "suppressed": [], "version_id": "1.8.0"}