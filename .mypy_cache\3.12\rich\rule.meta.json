{"data_mtime": 1759503832, "dep_lines": [3, 4, 5, 6, 7, 8, 9, 1, 118, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["rich.align", "rich.cells", "rich.console", "rich.jupyter", "rich.measure", "rich.style", "rich.text", "typing", "sys", "builtins", "pyexpat.model", "pyexpat.errors", "_random", "_typeshed", "abc", "datetime", "enum", "rich.segment", "rich.theme", "types", "typing_extensions"], "hash": "ba63b6d568f0d0571801e4c1dd4ba634b0ac0d685e8f3c678e57f65708978832", "id": "rich.rule", "ignore_all": true, "interface_hash": "82d52c59a121d967700d9821f05984d42a40682f1546f2c71bec80e51e862706", "mtime": 1757092239, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\rich\\rule.py", "plugin_data": null, "size": 4590, "suppressed": [], "version_id": "1.8.0"}