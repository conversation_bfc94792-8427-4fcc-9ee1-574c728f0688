{"data_mtime": 1759504547, "dep_lines": [24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 41, 47, 48, 49, 58, 59, 60, 62, 65, 67, 24, 52, 53, 55, 56, 57, 13, 15, 16, 17, 52, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 25, 25, 20, 10, 10, 10, 10, 10, 5, 10, 10, 5, 5, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.attributes", "sqlalchemy.orm.exc", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.loading", "sqlalchemy.orm.path_registry", "sqlalchemy.orm.properties", "sqlalchemy.orm.query", "sqlalchemy.orm.relationships", "sqlalchemy.orm.unitofwork", "sqlalchemy.orm.util", "sqlalchemy.orm.base", "sqlalchemy.orm.context", "sqlalchemy.orm.session", "sqlalchemy.orm.state", "sqlalchemy.orm.strategy_options", "sqlalchemy.sql.util", "sqlalchemy.sql.visitors", "sqlalchemy.sql.selectable", "sqlalchemy.util.typing", "sqlalchemy.orm.mapper", "sqlalchemy.sql.elements", "sqlalchemy.orm", "sqlalchemy.event", "sqlalchemy.exc", "sqlalchemy.log", "sqlalchemy.sql", "sqlalchemy.util", "__future__", "collections", "itertools", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "_typeshed", "abc", "enum", "logging", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.orm.instrumentation", "sqlalchemy.sql._elements_constructors", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.coercions", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.schema", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.util._collections", "sqlalchemy.util._py_collections", "sqlalchemy.util.deprecations", "sqlalchemy.util.langhelpers", "sqlalchemy.util.preloaded", "types"], "hash": "bf93637362dd0ffce7c2047ebfd9aebb33a369cdce5dca8d8903aee6ca87a129", "id": "sqlalchemy.orm.strategies", "ignore_all": true, "interface_hash": "b9d6537c3f20cd57cffdb40c968cfb75cf12ef0093ad4bf973f81ac2aec0621e", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\strategies.py", "plugin_data": null, "size": 123273, "suppressed": [], "version_id": "1.8.0"}