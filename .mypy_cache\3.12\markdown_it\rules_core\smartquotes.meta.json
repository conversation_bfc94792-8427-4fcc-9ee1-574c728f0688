{"data_mtime": 1759504508, "dep_lines": [8, 10, 9, 3, 5, 6, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 20, 20, 30, 30], "dependencies": ["markdown_it.common.utils", "markdown_it.rules_core.state_core", "markdown_it.token", "__future__", "re", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "markdown_it.ruler"], "hash": "8b32bd7d2cae4f303ecc0506911933f4ac300905a8e34891a9c08aa8e8456c41", "id": "markdown_it.rules_core.smartquotes", "ignore_all": true, "interface_hash": "08b5fd1edded8a8b84bf7c2161b1aee8c602449c1375a3140aa25fb4e5bf9ee6", "mtime": 1757092234, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\markdown_it\\rules_core\\smartquotes.py", "plugin_data": null, "size": 7443, "suppressed": [], "version_id": "1.8.0"}