{"data_mtime": 1759503866, "dep_lines": [17, 18, 19, 17, 15, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 20, 5, 5, 20, 20, 30, 30], "dependencies": ["sqlalchemy.orm.exc", "sqlalchemy.orm.util", "sqlalchemy.orm.base", "sqlalchemy.orm", "__future__", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "typing"], "hash": "ac642c2863e8af6b1a3020549ee759b19194d1329b190748a9889182cf058632", "id": "sqlalchemy.orm.sync", "ignore_all": true, "interface_hash": "312e61a4de1eacf8aa596923212ae02d7fa3ee5ee9cd5538d753dfd59c271d26", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\sync.py", "plugin_data": null, "size": 5943, "suppressed": [], "version_id": "1.8.0"}