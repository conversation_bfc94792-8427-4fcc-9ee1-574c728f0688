{"data_mtime": 1759503868, "dep_lines": [20, 25, 26, 27, 30, 31, 33, 37, 38, 43, 23, 24, 25, 9, 11, 12, 23, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 25, 25, 25, 25, 25, 25, 10, 10, 20, 5, 10, 5, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.dialects.postgresql.operators", "sqlalchemy.sql.expression", "sqlalchemy.sql.operators", "sqlalchemy.sql.visitors", "sqlalchemy.engine.interfaces", "sqlalchemy.sql._typing", "sqlalchemy.sql.elements", "sqlalchemy.sql.selectable", "sqlalchemy.sql.type_api", "sqlalchemy.util.typing", "sqlalchemy.types", "sqlalchemy.util", "sqlalchemy.sql", "__future__", "re", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "enum", "sqlalchemy.engine", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.roles", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.traversals", "sqlalchemy.util.langhelpers", "types"], "hash": "3f9a4da5be052984dd4b89306dfe03156907f295c4db41cfbe02c908f16aeab3", "id": "sqlalchemy.dialects.postgresql.array", "ignore_all": true, "interface_hash": "462a9670a13e7243722fda0127cc5bce44ea13e3d643c687b68d3b8228b828b5", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\array.py", "plugin_data": null, "size": 17465, "suppressed": [], "version_id": "1.8.0"}