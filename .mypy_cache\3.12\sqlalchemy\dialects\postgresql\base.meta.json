{"data_mtime": 1759504551, "dep_lines": [1558, 1559, 1560, 1561, 1563, 1564, 1571, 1557, 1601, 1602, 1603, 1606, 1610, 1611, 1612, 1613, 1614, 1615, 1616, 1618, 1632, 1596, 1597, 1599, 1600, 1601, 1619, 1543, 1545, 1546, 1547, 1548, 1596, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 5, 5, 5, 5, 10, 10, 10, 5, 10, 5, 10, 10, 10, 10, 10, 5, 5, 10, 10, 5, 10, 5, 5, 5, 5, 5, 10, 5, 5, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.dialects.postgresql.json", "sqlalchemy.dialects.postgresql.pg_catalog", "sqlalchemy.dialects.postgresql.ranges", "sqlalchemy.dialects.postgresql.ext", "sqlalchemy.dialects.postgresql.hstore", "sqlalchemy.dialects.postgresql.named_types", "sqlalchemy.dialects.postgresql.types", "sqlalchemy.dialects.postgresql", "sqlalchemy.engine.characteristics", "sqlalchemy.engine.default", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.reflection", "sqlalchemy.sql.coercions", "sqlalchemy.sql.compiler", "sqlalchemy.sql.elements", "sqlalchemy.sql.expression", "sqlalchemy.sql.roles", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.util", "sqlalchemy.sql.visitors", "sqlalchemy.util.typing", "sqlalchemy.exc", "sqlalchemy.schema", "sqlalchemy.sql", "sqlalchemy.util", "sqlalchemy.engine", "sqlalchemy.types", "__future__", "collections", "functools", "re", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "_decimal", "_typeshed", "abc", "enum", "sqlalchemy.dialects.postgresql.array", "sqlalchemy.engine.base", "sqlalchemy.engine.url", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.ddl", "sqlalchemy.sql.operators", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "types", "uuid"], "hash": "36f4068edf0aa180dd635dbbd6b163da99fe19eb31755b23666e90a3d60273ce", "id": "sqlalchemy.dialects.postgresql.base", "ignore_all": true, "interface_hash": "3d04238aa66fd38682bdab7cf4b250d948a301236dc1b039837110729c4889d0", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\base.py", "plugin_data": null, "size": 191647, "suppressed": [], "version_id": "1.8.0"}