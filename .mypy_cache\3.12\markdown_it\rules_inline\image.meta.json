{"data_mtime": 1759503829, "dep_lines": [4, 6, 5, 2, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 20, 20, 30, 30, 30], "dependencies": ["markdown_it.common.utils", "markdown_it.rules_inline.state_inline", "markdown_it.token", "__future__", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "markdown_it.ruler", "typing"], "hash": "59bb20ee38273ad2972305c63493a51bb3914e190c90156897122d0b4a61e82d", "id": "markdown_it.rules_inline.image", "ignore_all": true, "interface_hash": "c4eabf0bb7491005dac98524db60a1bea8e6e7fc82696ce8419be73445664c75", "mtime": 1757092234, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\markdown_it\\rules_inline\\image.py", "plugin_data": null, "size": 4141, "suppressed": [], "version_id": "1.8.0"}