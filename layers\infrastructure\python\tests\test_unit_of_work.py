"""Tests for Unit of Work pattern implementation."""

import pytest
from unittest.mock import Mo<PERSON>, AsyncMock
from sqlalchemy.ext.asyncio import AsyncSession

from smartanalytics_infrastructure.unit_of_work import SqlAlchemyUnitOfWork


class TestSqlAlchemyUnitOfWork:
    """Test SqlAlchemyUnitOfWork implementation."""

    @pytest.fixture
    def mock_session_factory(self):
        """Mock session factory."""
        session = Mock(spec=AsyncSession)
        session.commit = AsyncMock()
        session.rollback = AsyncMock()
        session.close = AsyncMock()
        session.__aenter__ = AsyncMock(return_value=session)
        session.__aexit__ = AsyncMock(return_value=None)

        factory = Mock(return_value=session)
        return factory

    @pytest.fixture
    def uow(self, mock_session_factory):
        """Create SqlAlchemyUnitOfWork instance with mock session factory."""
        return SqlAlchemyUnitOfWork(mock_session_factory)

    @pytest.mark.asyncio
    async def test_unit_of_work_context_manager_success(self, uow, mock_session_factory):
        """Test UnitOfWork context manager with successful transaction."""
        mock_session = mock_session_factory.return_value

        # Test context manager
        async with uow:
            # Verify repositories are initialized
            assert uow.tenants is not None
            assert uow.agents is not None
            assert uow.ring_groups is not None
            assert uow.agent_events is not None

        # Verify commit was called (successful transaction)
        mock_session.commit.assert_called_once()
        mock_session.rollback.assert_not_called()

    @pytest.mark.asyncio
    async def test_unit_of_work_context_manager_exception(self, uow, mock_session_factory):
        """Test UnitOfWork context manager with exception handling."""
        mock_session = mock_session_factory.return_value

        # Test context manager with exception
        with pytest.raises(ValueError):
            async with uow:
                # Simulate an error during transaction
                raise ValueError("Test error")

        # Verify rollback was called (failed transaction)
        mock_session.rollback.assert_called_once()
        mock_session.commit.assert_not_called()

    def test_unit_of_work_initialization(self, mock_session_factory):
        """Test UnitOfWork initialization."""
        uow = SqlAlchemyUnitOfWork(mock_session_factory)

        # Verify session factory is stored
        assert uow.session_factory == mock_session_factory
