{"data_mtime": 1759504548, "dep_lines": [12, 13, 14, 7, 15, 16, 18, 19, 1, 3, 4, 5, 6, 8, 9, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 5, 5, 5, 5, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._config", "pydantic._internal._signature", "pydantic._internal._utils", "collections.abc", "pydantic.dataclasses", "pydantic.main", "pydantic_settings.exceptions", "pydantic_settings.sources", "__future__", "asyncio", "inspect", "threading", "<PERSON><PERSON><PERSON><PERSON>", "types", "typing", "pydantic", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "_typeshed", "abc", "os", "pathlib", "pydantic._internal", "pydantic._internal._dataclasses", "pydantic._internal._generate_schema", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.fields", "pydantic_settings.sources.base", "pydantic_settings.sources.providers", "pydantic_settings.sources.providers.cli", "pydantic_settings.sources.providers.env", "pydantic_settings.sources.types", "re"], "hash": "61f623a655f7a9e5f8c31de7fadedfcc6fbae679d94ba768988e83ed1e55cf69", "id": "pydantic_settings.main", "ignore_all": true, "interface_hash": "865500ead53a5ef2d2be3d5c174fe25b00bafd0c889e4f6ee4effb8a907abafc", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic_settings\\main.py", "plugin_data": null, "size": 29176, "suppressed": [], "version_id": "1.8.0"}