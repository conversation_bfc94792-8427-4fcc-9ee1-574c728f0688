{"data_mtime": 1759503828, "dep_lines": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 30, 30], "dependencies": ["pydantic.v1.dataclasses", "pydantic.v1.annotated_types", "pydantic.v1.class_validators", "pydantic.v1.config", "pydantic.v1.decorator", "pydantic.v1.env_settings", "pydantic.v1.error_wrappers", "pydantic.v1.errors", "pydantic.v1.fields", "pydantic.v1.main", "pydantic.v1.networks", "pydantic.v1.parse", "pydantic.v1.tools", "pydantic.v1.types", "pydantic.v1.version", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "typing"], "hash": "4b140f925801b385c727013a059f6aa1ec18a0688dc98527987cc414265b7e72", "id": "pydantic.v1", "ignore_all": true, "interface_hash": "21ac188fbfc7ab1bd4a75cc26b982e62948431dcaeeec767dee329a44c3a3fa1", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\v1\\__init__.py", "plugin_data": null, "size": 2946, "suppressed": [], "version_id": "1.8.0"}