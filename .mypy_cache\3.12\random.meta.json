{"data_mtime": 1759503830, "dep_lines": [4, 1, 2, 3, 5, 6, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 5, 5, 5, 20, 20, 30, 30], "dependencies": ["collections.abc", "_random", "sys", "_typeshed", "fractions", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "numbers"], "hash": "b64e597eaaf92ecd3606f1cf16c13b1e3bc336aaf7adc45cf5d751f7d6a90a92", "id": "random", "ignore_all": true, "interface_hash": "f54e1c926d7c8a6a9b9dd3fbe595fac7349ee7d6d60449fb7356ba7c43490ed9", "mtime": 1757092230, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\random.pyi", "plugin_data": null, "size": 5012, "suppressed": [], "version_id": "1.8.0"}