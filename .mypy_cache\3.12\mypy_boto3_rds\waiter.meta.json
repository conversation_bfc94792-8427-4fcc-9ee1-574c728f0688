{"data_mtime": 1759504551, "dep_lines": [49, 51, 45, 47, 66, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 20, 20, 30, 30, 30], "dependencies": ["botocore.waiter", "mypy_boto3_rds.type_defs", "__future__", "sys", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "_typeshed", "abc", "botocore"], "hash": "f7aefeae5c23ff8c9873a052e97e6a610f38c0548faf7c3a6272a21daa46d79d", "id": "mypy_boto3_rds.waiter", "ignore_all": true, "interface_hash": "d5030fff41e494c47d6683a919ee5a14df91ae8dea206d93a7defa901d3a1f24", "mtime": 1759495924, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\mypy_boto3_rds\\waiter.pyi", "plugin_data": null, "size": 12387, "suppressed": [], "version_id": "1.8.0"}