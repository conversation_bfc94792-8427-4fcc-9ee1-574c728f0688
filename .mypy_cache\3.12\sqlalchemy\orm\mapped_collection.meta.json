{"data_mtime": 1759503866, "dep_lines": [24, 25, 29, 30, 31, 32, 34, 40, 24, 27, 28, 29, 8, 10, 11, 27, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 10, 10, 5, 5, 25, 20, 10, 10, 20, 5, 10, 5, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.base", "sqlalchemy.orm.collections", "sqlalchemy.sql.coercions", "sqlalchemy.sql.expression", "sqlalchemy.sql.roles", "sqlalchemy.util.langhelpers", "sqlalchemy.util.typing", "sqlalchemy.sql.elements", "sqlalchemy.orm", "sqlalchemy.exc", "sqlalchemy.util", "sqlalchemy.sql", "__future__", "operator", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "_operator", "_typeshed", "abc", "enum", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.orm.attributes", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.mapper", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.operators", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util.preloaded"], "hash": "c15e4ad66d0bdd49c2f841c8f41be250be55a5bfa460beba4be5142b73df9d07", "id": "sqlalchemy.orm.mapped_collection", "ignore_all": true, "interface_hash": "4496d9315e073a5098fe3b68a84822128c3e72b7078f0755696850a641abaf3a", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\mapped_collection.py", "plugin_data": null, "size": 20239, "suppressed": [], "version_id": "1.8.0"}