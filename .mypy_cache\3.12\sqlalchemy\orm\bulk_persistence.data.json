{".class": "MypyFile", "_fullname": "sqlalchemy.orm.bulk_persistence", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AbstractORMCompileState": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.context.AbstractORMCompileState", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BulkORMDelete": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.bulk_persistence.BulkUDCompileState", "sqlalchemy.sql.dml.DeleteDMLState"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMDelete", "name": "BulkORMDelete", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMDelete", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.bulk_persistence", "mro": ["sqlalchemy.orm.bulk_persistence.BulkORMDelete", "sqlalchemy.orm.bulk_persistence.BulkUDCompileState", "sqlalchemy.orm.bulk_persistence.ORMDMLState", "sqlalchemy.orm.context.AbstractORMCompileState", "sqlalchemy.sql.dml.DeleteDMLState", "sqlalchemy.sql.dml.DMLState", "sqlalchemy.sql.base.CompileState", "builtins.object"], "names": {".class": "SymbolTable", "_do_post_synchronize_evaluate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "result", "update_options"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMDelete._do_post_synchronize_evaluate", "name": "_do_post_synchronize_evaluate", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMDelete._do_post_synchronize_evaluate", "name": "_do_post_synchronize_evaluate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "result", "update_options"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.BulkORMDelete"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_do_post_synchronize_evaluate of BulkORMDelete", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_do_post_synchronize_fetch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "result", "update_options"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMDelete._do_post_synchronize_fetch", "name": "_do_post_synchronize_fetch", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMDelete._do_post_synchronize_fetch", "name": "_do_post_synchronize_fetch", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "result", "update_options"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.BulkORMDelete"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_do_post_synchronize_fetch of BulkORMDelete", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "can_use_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5, 5], "arg_names": ["cls", "dialect", "mapper", "is_multitable", "is_update_from", "is_delete_using", "is_executemany"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMDelete.can_use_returning", "name": "can_use_returning", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5], "arg_names": ["cls", "dialect", "mapper", "is_multitable", "is_update_from", "is_delete_using", "is_executemany"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.BulkORMDelete"}, "sqlalchemy.engine.interfaces.Dialect", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "can_use_returning of BulkORMDelete", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMDelete.can_use_returning", "name": "can_use_returning", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5], "arg_names": ["cls", "dialect", "mapper", "is_multitable", "is_update_from", "is_delete_using", "is_executemany"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.BulkORMDelete"}, "sqlalchemy.engine.interfaces.Dialect", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "can_use_returning of BulkORMDelete", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "create_for_statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["cls", "statement", "compiler", "kw"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMDelete.create_for_statement", "name": "create_for_statement", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMDelete.create_for_statement", "name": "create_for_statement", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["cls", "statement", "compiler", "kw"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.BulkORMDelete"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_for_statement of BulkORMDelete", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "orm_execute_statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "params", "execution_options", "bind_arguments", "conn"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMDelete.orm_execute_statement", "name": "orm_execute_statement", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "params", "execution_options", "bind_arguments", "conn"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.BulkORMDelete"}, "sqlalchemy.orm.session.Session", "sqlalchemy.sql.dml.Delete", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, "sqlalchemy.engine.base.Connection"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "orm_execute_statement of BulkORMDelete", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.engine.result.Result"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMDelete.orm_execute_statement", "name": "orm_execute_statement", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "params", "execution_options", "bind_arguments", "conn"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.BulkORMDelete"}, "sqlalchemy.orm.session.Session", "sqlalchemy.sql.dml.Delete", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, "sqlalchemy.engine.base.Connection"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "orm_execute_statement of BulkORMDelete", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.engine.result.Result"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMDelete.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.bulk_persistence.BulkORMDelete", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BulkORMInsert": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.bulk_persistence.ORMDMLState", "sqlalchemy.sql.dml.InsertDMLState"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMInsert", "name": "BulkORMInsert", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMInsert", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.bulk_persistence", "mro": ["sqlalchemy.orm.bulk_persistence.BulkORMInsert", "sqlalchemy.orm.bulk_persistence.ORMDMLState", "sqlalchemy.orm.context.AbstractORMCompileState", "sqlalchemy.sql.dml.InsertDMLState", "sqlalchemy.sql.dml.DMLState", "sqlalchemy.sql.base.CompileState", "builtins.object"], "names": {".class": "SymbolTable", "_resolved_keys_as_col_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "mapper", "resolved_value_dict"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMInsert._resolved_keys_as_col_keys", "name": "_resolved_keys_as_col_keys", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMInsert._resolved_keys_as_col_keys", "name": "_resolved_keys_as_col_keys", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "mapper", "resolved_value_dict"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.BulkORMInsert"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_resolved_keys_as_col_keys of BulkORMInsert", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_setup_for_bulk_insert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "compiler"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMInsert._setup_for_bulk_insert", "name": "_setup_for_bulk_insert", "type": null}}, "_setup_for_orm_insert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "compiler", "mapper"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMInsert._setup_for_orm_insert", "name": "_setup_for_orm_insert", "type": null}}, "create_for_statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["cls", "statement", "compiler", "kw"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMInsert.create_for_statement", "name": "create_for_statement", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["cls", "statement", "compiler", "kw"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.BulkORMInsert"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_for_statement of BulkORMInsert", "ret_type": "sqlalchemy.orm.bulk_persistence.BulkORMInsert", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMInsert.create_for_statement", "name": "create_for_statement", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["cls", "statement", "compiler", "kw"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.BulkORMInsert"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_for_statement of BulkORMInsert", "ret_type": "sqlalchemy.orm.bulk_persistence.BulkORMInsert", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "default_insert_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.base.Options"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMInsert.default_insert_options", "name": "default_insert_options", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMInsert.default_insert_options", "has_param_spec_type": false, "metaclass_type": "sqlalchemy.sql.base._MetaOptions", "metadata": {}, "module_name": "sqlalchemy.orm.bulk_persistence", "mro": ["sqlalchemy.orm.bulk_persistence.BulkORMInsert.default_insert_options", "sqlalchemy.sql.base.Options", "builtins.object"], "names": {".class": "SymbolTable", "_autoflush": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMInsert.default_insert_options._autoflush", "name": "_autoflush", "type": "builtins.bool"}}, "_dml_strategy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMInsert.default_insert_options._dml_strategy", "name": "_dml_strategy", "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._orm_types.DMLStrategyArgument"}}}, "_populate_existing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMInsert.default_insert_options._populate_existing", "name": "_populate_existing", "type": "builtins.bool"}}, "_render_nulls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMInsert.default_insert_options._render_nulls", "name": "_render_nulls", "type": "builtins.bool"}}, "_return_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMInsert.default_insert_options._return_defaults", "name": "_return_defaults", "type": "builtins.bool"}}, "_subject_mapper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMInsert.default_insert_options._subject_mapper", "name": "_subject_mapper", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "NoneType"}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMInsert.default_insert_options.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.bulk_persistence.BulkORMInsert.default_insert_options", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "orm_execute_statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "params", "execution_options", "bind_arguments", "conn"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMInsert.orm_execute_statement", "name": "orm_execute_statement", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "params", "execution_options", "bind_arguments", "conn"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.BulkORMInsert"}, "sqlalchemy.orm.session.Session", "sqlalchemy.sql.dml.Insert", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, "sqlalchemy.engine.base.Connection"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "orm_execute_statement of BulkORMInsert", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.engine.result.Result"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMInsert.orm_execute_statement", "name": "orm_execute_statement", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "params", "execution_options", "bind_arguments", "conn"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.BulkORMInsert"}, "sqlalchemy.orm.session.Session", "sqlalchemy.sql.dml.Insert", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, "sqlalchemy.engine.base.Connection"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "orm_execute_statement of BulkORMInsert", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.engine.result.Result"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "orm_pre_session_exec": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "params", "execution_options", "bind_arguments", "is_pre_event"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMInsert.orm_pre_session_exec", "name": "orm_pre_session_exec", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMInsert.orm_pre_session_exec", "name": "orm_pre_session_exec", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "params", "execution_options", "bind_arguments", "is_pre_event"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.BulkORMInsert"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "orm_pre_session_exec of BulkORMInsert", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "select_statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMInsert.select_statement", "name": "select_statement", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.orm.context.FromStatement"}, {".class": "NoneType"}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMInsert.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.bulk_persistence.BulkORMInsert", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BulkORMUpdate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.bulk_persistence.BulkUDCompileState", "sqlalchemy.sql.dml.UpdateDMLState"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMUpdate", "name": "BulkORMUpdate", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMUpdate", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.bulk_persistence", "mro": ["sqlalchemy.orm.bulk_persistence.BulkORMUpdate", "sqlalchemy.orm.bulk_persistence.BulkUDCompileState", "sqlalchemy.orm.bulk_persistence.ORMDMLState", "sqlalchemy.orm.context.AbstractORMCompileState", "sqlalchemy.sql.dml.UpdateDMLState", "sqlalchemy.sql.dml.DMLState", "sqlalchemy.sql.base.CompileState", "builtins.object"], "names": {".class": "SymbolTable", "_apply_update_set_values_to_objects": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "session", "update_options", "statement", "effective_params", "matched_objects", "prefetch_cols", "postfetch_cols"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMUpdate._apply_update_set_values_to_objects", "name": "_apply_update_set_values_to_objects", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMUpdate._apply_update_set_values_to_objects", "name": "_apply_update_set_values_to_objects", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "session", "update_options", "statement", "effective_params", "matched_objects", "prefetch_cols", "postfetch_cols"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.BulkORMUpdate"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_apply_update_set_values_to_objects of BulkORMUpdate", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_do_post_synchronize_bulk_evaluate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "session", "params", "result", "update_options"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMUpdate._do_post_synchronize_bulk_evaluate", "name": "_do_post_synchronize_bulk_evaluate", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMUpdate._do_post_synchronize_bulk_evaluate", "name": "_do_post_synchronize_bulk_evaluate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "session", "params", "result", "update_options"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.BulkORMUpdate"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_do_post_synchronize_bulk_evaluate of BulkORMUpdate", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_do_post_synchronize_evaluate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "result", "update_options"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMUpdate._do_post_synchronize_evaluate", "name": "_do_post_synchronize_evaluate", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMUpdate._do_post_synchronize_evaluate", "name": "_do_post_synchronize_evaluate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "result", "update_options"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.BulkORMUpdate"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_do_post_synchronize_evaluate of BulkORMUpdate", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_do_post_synchronize_fetch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "result", "update_options"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMUpdate._do_post_synchronize_fetch", "name": "_do_post_synchronize_fetch", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMUpdate._do_post_synchronize_fetch", "name": "_do_post_synchronize_fetch", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "result", "update_options"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.BulkORMUpdate"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_do_post_synchronize_fetch of BulkORMUpdate", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_resolved_values": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMUpdate._resolved_values", "name": "_resolved_values", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_setup_for_bulk_update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "statement", "compiler", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMUpdate._setup_for_bulk_update", "name": "_setup_for_bulk_update", "type": null}}, "_setup_for_orm_update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "statement", "compiler", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMUpdate._setup_for_orm_update", "name": "_setup_for_orm_update", "type": null}}, "can_use_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5, 5], "arg_names": ["cls", "dialect", "mapper", "is_multitable", "is_update_from", "is_delete_using", "is_executemany"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMUpdate.can_use_returning", "name": "can_use_returning", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5], "arg_names": ["cls", "dialect", "mapper", "is_multitable", "is_update_from", "is_delete_using", "is_executemany"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.BulkORMUpdate"}, "sqlalchemy.engine.interfaces.Dialect", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "can_use_returning of BulkORMUpdate", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMUpdate.can_use_returning", "name": "can_use_returning", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5], "arg_names": ["cls", "dialect", "mapper", "is_multitable", "is_update_from", "is_delete_using", "is_executemany"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.BulkORMUpdate"}, "sqlalchemy.engine.interfaces.Dialect", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "can_use_returning of BulkORMUpdate", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "create_for_statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["cls", "statement", "compiler", "kw"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMUpdate.create_for_statement", "name": "create_for_statement", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMUpdate.create_for_statement", "name": "create_for_statement", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["cls", "statement", "compiler", "kw"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.BulkORMUpdate"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_for_statement of BulkORMUpdate", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "mapper": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMUpdate.mapper", "name": "mapper", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "orm_execute_statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "params", "execution_options", "bind_arguments", "conn"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMUpdate.orm_execute_statement", "name": "orm_execute_statement", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "params", "execution_options", "bind_arguments", "conn"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.BulkORMUpdate"}, "sqlalchemy.orm.session.Session", "sqlalchemy.sql.dml.Update", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, "sqlalchemy.engine.base.Connection"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "orm_execute_statement of BulkORMUpdate", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.engine.result.Result"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMUpdate.orm_execute_statement", "name": "orm_execute_statement", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "params", "execution_options", "bind_arguments", "conn"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.BulkORMUpdate"}, "sqlalchemy.orm.session.Session", "sqlalchemy.sql.dml.Update", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, "sqlalchemy.engine.base.Connection"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "orm_execute_statement of BulkORMUpdate", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.engine.result.Result"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.bulk_persistence.BulkORMUpdate.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.bulk_persistence.BulkORMUpdate", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BulkUDCompileState": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.bulk_persistence.ORMDMLState"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState", "name": "BulkUDCompileState", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.bulk_persistence", "mro": ["sqlalchemy.orm.bulk_persistence.BulkUDCompileState", "sqlalchemy.orm.bulk_persistence.ORMDMLState", "sqlalchemy.orm.context.AbstractORMCompileState", "sqlalchemy.sql.base.CompileState", "builtins.object"], "names": {".class": "SymbolTable", "_adjust_for_extra_criteria": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "global_attributes", "ext_info"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState._adjust_for_extra_criteria", "name": "_adjust_for_extra_criteria", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState._adjust_for_extra_criteria", "name": "_adjust_for_extra_criteria", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "global_attributes", "ext_info"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_adjust_for_extra_criteria of BulkUDCompileState", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_do_pre_synchronize_auto": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "params", "execution_options", "bind_arguments", "update_options"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState._do_pre_synchronize_auto", "name": "_do_pre_synchronize_auto", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState._do_pre_synchronize_auto", "name": "_do_pre_synchronize_auto", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "params", "execution_options", "bind_arguments", "update_options"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_do_pre_synchronize_auto of BulkUDCompileState", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_do_pre_synchronize_evaluate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "params", "execution_options", "bind_arguments", "update_options"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState._do_pre_synchronize_evaluate", "name": "_do_pre_synchronize_evaluate", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState._do_pre_synchronize_evaluate", "name": "_do_pre_synchronize_evaluate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "params", "execution_options", "bind_arguments", "update_options"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_do_pre_synchronize_evaluate of BulkUDCompileState", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_do_pre_synchronize_fetch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "params", "execution_options", "bind_arguments", "update_options"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState._do_pre_synchronize_fetch", "name": "_do_pre_synchronize_fetch", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState._do_pre_synchronize_fetch", "name": "_do_pre_synchronize_fetch", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "params", "execution_options", "bind_arguments", "update_options"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_do_pre_synchronize_fetch of BulkUDCompileState", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_eval_condition_from_statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "update_options", "statement"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState._eval_condition_from_statement", "name": "_eval_condition_from_statement", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState._eval_condition_from_statement", "name": "_eval_condition_from_statement", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "update_options", "statement"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_eval_condition_from_statement of BulkUDCompileState", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_get_matched_objects_on_criteria": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "update_options", "states"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState._get_matched_objects_on_criteria", "name": "_get_matched_objects_on_criteria", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState._get_matched_objects_on_criteria", "name": "_get_matched_objects_on_criteria", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "update_options", "states"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_matched_objects_on_criteria of BulkUDCompileState", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_get_resolved_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "mapper", "statement"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState._get_resolved_values", "name": "_get_resolved_values", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState._get_resolved_values", "name": "_get_resolved_values", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "mapper", "statement"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_resolved_values of BulkUDCompileState", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_interpret_returning_rows": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "result", "mapper", "rows"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState._interpret_returning_rows", "name": "_interpret_returning_rows", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState._interpret_returning_rows", "name": "_interpret_returning_rows", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "result", "mapper", "rows"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_interpret_returning_rows of BulkUDCompileState", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_resolved_keys_as_propnames": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "mapper", "resolved_values"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState._resolved_keys_as_propnames", "name": "_resolved_keys_as_propnames", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState._resolved_keys_as_propnames", "name": "_resolved_keys_as_propnames", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "mapper", "resolved_values"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_resolved_keys_as_propnames of BulkUDCompileState", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "can_use_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5, 5], "arg_names": ["cls", "dialect", "mapper", "is_multitable", "is_update_from", "is_delete_using", "is_executemany"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState.can_use_returning", "name": "can_use_returning", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5], "arg_names": ["cls", "dialect", "mapper", "is_multitable", "is_update_from", "is_delete_using", "is_executemany"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState"}, "sqlalchemy.engine.interfaces.Dialect", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "can_use_returning of BulkUDCompileState", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState.can_use_returning", "name": "can_use_returning", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5], "arg_names": ["cls", "dialect", "mapper", "is_multitable", "is_update_from", "is_delete_using", "is_executemany"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState"}, "sqlalchemy.engine.interfaces.Dialect", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "can_use_returning of BulkUDCompileState", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "default_update_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.base.Options"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState.default_update_options", "name": "default_update_options", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState.default_update_options", "has_param_spec_type": false, "metaclass_type": "sqlalchemy.sql.base._MetaOptions", "metadata": {}, "module_name": "sqlalchemy.orm.bulk_persistence", "mro": ["sqlalchemy.orm.bulk_persistence.BulkUDCompileState.default_update_options", "sqlalchemy.sql.base.Options", "builtins.object"], "names": {".class": "SymbolTable", "_autoflush": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState.default_update_options._autoflush", "name": "_autoflush", "type": "builtins.bool"}}, "_can_use_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState.default_update_options._can_use_returning", "name": "_can_use_returning", "type": "builtins.bool"}}, "_dml_strategy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState.default_update_options._dml_strategy", "name": "_dml_strategy", "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._orm_types.DMLStrategyArgument"}}}, "_eval_condition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState.default_update_options._eval_condition", "name": "_eval_condition", "type": {".class": "NoneType"}}}, "_identity_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState.default_update_options._identity_token", "name": "_identity_token", "type": {".class": "NoneType"}}}, "_is_delete_using": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState.default_update_options._is_delete_using", "name": "_is_delete_using", "type": "builtins.bool"}}, "_is_update_from": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState.default_update_options._is_update_from", "name": "_is_update_from", "type": "builtins.bool"}}, "_matched_rows": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState.default_update_options._matched_rows", "name": "_matched_rows", "type": {".class": "NoneType"}}}, "_populate_existing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState.default_update_options._populate_existing", "name": "_populate_existing", "type": "builtins.bool"}}, "_resolved_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState.default_update_options._resolved_values", "name": "_resolved_values", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}}}, "_subject_mapper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState.default_update_options._subject_mapper", "name": "_subject_mapper", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "NoneType"}]}}}, "_synchronize_session": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState.default_update_options._synchronize_session", "name": "_synchronize_session", "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._orm_types.SynchronizeSessionArgument"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState.default_update_options.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState.default_update_options", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "orm_pre_session_exec": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "params", "execution_options", "bind_arguments", "is_pre_event"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState.orm_pre_session_exec", "name": "orm_pre_session_exec", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState.orm_pre_session_exec", "name": "orm_pre_session_exec", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "params", "execution_options", "bind_arguments", "is_pre_event"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "orm_pre_session_exec of BulkUDCompileState", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "orm_setup_cursor_result": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "params", "execution_options", "bind_arguments", "result"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState.orm_setup_cursor_result", "name": "orm_setup_cursor_result", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState.orm_setup_cursor_result", "name": "orm_setup_cursor_result", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "params", "execution_options", "bind_arguments", "result"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "orm_setup_cursor_result of BulkUDCompileState", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CompileState": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.CompileState", "kind": "Gdef"}, "Connection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.base.Connection", "kind": "Gdef"}, "DMLStrategyArgument": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._orm_types.DMLStrategyArgument", "kind": "Gdef"}, "DeleteDMLState": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.dml.DeleteDMLState", "kind": "Gdef"}, "Dialect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.Dialect", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "EMPTY_DICT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._collections.EMPTY_DICT", "kind": "Gdef"}, "FromStatement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.context.FromStatement", "kind": "Gdef"}, "InsertDMLState": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.dml.InsertDMLState", "kind": "Gdef"}, "InstanceState": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.state.InstanceState", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef"}, "Mapper": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.mapper.Mapper", "kind": "Gdef"}, "NO_VALUE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.NO_VALUE", "kind": "Gdef"}, "ORMDMLState": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.context.AbstractORMCompileState"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.bulk_persistence.ORMDMLState", "name": "ORMDMLState", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.bulk_persistence.ORMDMLState", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.bulk_persistence", "mro": ["sqlalchemy.orm.bulk_persistence.ORMDMLState", "sqlalchemy.orm.context.AbstractORMCompileState", "sqlalchemy.sql.base.CompileState", "builtins.object"], "names": {".class": "SymbolTable", "_get_crud_kv_pairs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "statement", "kv_iterator", "needs_to_be_cacheable"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.bulk_persistence.ORMDMLState._get_crud_kv_pairs", "name": "_get_crud_kv_pairs", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.bulk_persistence.ORMDMLState._get_crud_kv_pairs", "name": "_get_crud_kv_pairs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "statement", "kv_iterator", "needs_to_be_cacheable"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.ORMDMLState"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_crud_kv_pairs of ORMDMLState", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_get_dml_plugin_subject": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "statement"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.bulk_persistence.ORMDMLState._get_dml_plugin_subject", "name": "_get_dml_plugin_subject", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.bulk_persistence.ORMDMLState._get_dml_plugin_subject", "name": "_get_dml_plugin_subject", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "statement"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.ORMDMLState"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_dml_plugin_subject of ORMDMLState", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_get_multi_crud_kv_pairs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "statement", "kv_iterator"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.bulk_persistence.ORMDMLState._get_multi_crud_kv_pairs", "name": "_get_multi_crud_kv_pairs", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.bulk_persistence.ORMDMLState._get_multi_crud_kv_pairs", "name": "_get_multi_crud_kv_pairs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "statement", "kv_iterator"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.ORMDMLState"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_multi_crud_kv_pairs of ORMDMLState", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_get_orm_crud_kv_pairs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "mapper", "statement", "kv_iterator", "needs_to_be_cacheable"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.bulk_persistence.ORMDMLState._get_orm_crud_kv_pairs", "name": "_get_orm_crud_kv_pairs", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.bulk_persistence.ORMDMLState._get_orm_crud_kv_pairs", "name": "_get_orm_crud_kv_pairs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "mapper", "statement", "kv_iterator", "needs_to_be_cacheable"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.ORMDMLState"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_orm_crud_kv_pairs of ORMDMLState", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_return_orm_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "params", "execution_options", "bind_arguments", "result"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.bulk_persistence.ORMDMLState._return_orm_returning", "name": "_return_orm_returning", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.bulk_persistence.ORMDMLState._return_orm_returning", "name": "_return_orm_returning", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "params", "execution_options", "bind_arguments", "result"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.ORMDMLState"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_return_orm_returning of ORMDMLState", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_setup_orm_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 5], "arg_names": ["self", "compiler", "orm_level_statement", "dml_level_statement", "dml_mapper", "use_supplemental_cols"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.bulk_persistence.ORMDMLState._setup_orm_returning", "name": "_setup_orm_returning", "type": null}}, "from_statement_ctx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.orm.bulk_persistence.ORMDMLState.from_statement_ctx", "name": "from_statement_ctx", "type": {".class": "UnionType", "items": ["sqlalchemy.orm.context.ORMFromStatementCompileState", {".class": "NoneType"}]}}}, "get_entity_description": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "statement"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.bulk_persistence.ORMDMLState.get_entity_description", "name": "get_entity_description", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.bulk_persistence.ORMDMLState.get_entity_description", "name": "get_entity_description", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "statement"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.ORMDMLState"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_entity_description of ORMDMLState", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "get_returning_column_descriptions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "statement"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.bulk_persistence.ORMDMLState.get_returning_column_descriptions", "name": "get_returning_column_descriptions", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.bulk_persistence.ORMDMLState.get_returning_column_descriptions", "name": "get_returning_column_descriptions", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "statement"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.ORMDMLState"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_returning_column_descriptions of ORMDMLState", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "is_dml_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.bulk_persistence.ORMDMLState.is_dml_returning", "name": "is_dml_returning", "type": "builtins.bool"}}, "select_statement": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.bulk_persistence.ORMDMLState.select_statement", "name": "select_statement", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "type_ref": "sqlalchemy.orm.context.FromStatement"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.bulk_persistence.ORMDMLState.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.bulk_persistence.ORMDMLState", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ORMExecuteState": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.session.ORMExecuteState", "kind": "Gdef"}, "ORMFromStatementCompileState": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.context.ORMFromStatementCompileState", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Options": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.Options", "kind": "Gdef"}, "OrmExecuteOptionsParameter": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter", "kind": "Gdef"}, "QueryContext": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.context.QueryContext", "kind": "Gdef"}, "Session": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.session.Session", "kind": "Gdef"}, "SessionTransaction": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.session.SessionTransaction", "kind": "Gdef"}, "SynchronizeSessionArgument": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._orm_types.SynchronizeSessionArgument", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "UpdateDMLState": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.dml.UpdateDMLState", "kind": "Gdef"}, "_BindArguments": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.session._BindArguments", "kind": "Gdef"}, "_CoreAnyExecuteParams": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams", "kind": "Gdef"}, "_O": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.bulk_persistence._O", "name": "_O", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.bulk_persistence.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.bulk_persistence.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.bulk_persistence.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.bulk_persistence.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.bulk_persistence.__package__", "name": "__package__", "type": "builtins.str"}}, "_bulk_insert": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "sqlalchemy.orm.bulk_persistence._bulk_insert", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 3, 3, 3, 5, 5], "arg_names": ["mapper", "mappings", "session_transaction", "isstates", "return_defaults", "render_nulls", "use_orm_insert_stmt", "execution_options"], "dataclass_transform_spec": null, "flags": ["is_overload"], "fullname": "sqlalchemy.orm.bulk_persistence._bulk_insert", "name": "_bulk_insert", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 3, 3, 3, 5, 5], "arg_names": ["mapper", "mappings", "session_transaction", "isstates", "return_defaults", "render_nulls", "use_orm_insert_stmt", "execution_options"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.bulk_persistence._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.bulk_persistence._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.state.InstanceState"}], "type_ref": "typing.Iterable"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "type_ref": "typing.Iterable"}]}, "sqlalchemy.orm.session.SessionTransaction", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["sqlalchemy.sql.dml.Insert", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing._OrmKnownExecutionOptions"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_bulk_insert", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.engine.cursor.CursorResult"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.bulk_persistence._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 3, 3, 3, 5, 5], "arg_names": ["mapper", "mappings", "session_transaction", "isstates", "return_defaults", "render_nulls", "use_orm_insert_stmt", "execution_options"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.orm.bulk_persistence._bulk_insert", "name": "_bulk_insert", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 3, 3, 3, 5, 5], "arg_names": ["mapper", "mappings", "session_transaction", "isstates", "return_defaults", "render_nulls", "use_orm_insert_stmt", "execution_options"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.bulk_persistence._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.bulk_persistence._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.state.InstanceState"}], "type_ref": "typing.Iterable"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "type_ref": "typing.Iterable"}]}, "sqlalchemy.orm.session.SessionTransaction", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "NoneType"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing._OrmKnownExecutionOptions"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_bulk_insert", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.bulk_persistence._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.bulk_persistence._bulk_insert", "name": "_bulk_insert", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 3, 3, 3, 5, 5], "arg_names": ["mapper", "mappings", "session_transaction", "isstates", "return_defaults", "render_nulls", "use_orm_insert_stmt", "execution_options"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.bulk_persistence._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.bulk_persistence._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.state.InstanceState"}], "type_ref": "typing.Iterable"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "type_ref": "typing.Iterable"}]}, "sqlalchemy.orm.session.SessionTransaction", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "NoneType"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing._OrmKnownExecutionOptions"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_bulk_insert", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.bulk_persistence._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 3, 3, 3, 5, 5], "arg_names": ["mapper", "mappings", "session_transaction", "isstates", "return_defaults", "render_nulls", "use_orm_insert_stmt", "execution_options"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.orm.bulk_persistence._bulk_insert", "name": "_bulk_insert", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 3, 3, 3, 5, 5], "arg_names": ["mapper", "mappings", "session_transaction", "isstates", "return_defaults", "render_nulls", "use_orm_insert_stmt", "execution_options"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.bulk_persistence._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.bulk_persistence._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.state.InstanceState"}], "type_ref": "typing.Iterable"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "type_ref": "typing.Iterable"}]}, "sqlalchemy.orm.session.SessionTransaction", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["sqlalchemy.sql.dml.Insert", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing._OrmKnownExecutionOptions"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_bulk_insert", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.engine.cursor.CursorResult"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.bulk_persistence._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.bulk_persistence._bulk_insert", "name": "_bulk_insert", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 3, 3, 3, 5, 5], "arg_names": ["mapper", "mappings", "session_transaction", "isstates", "return_defaults", "render_nulls", "use_orm_insert_stmt", "execution_options"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.bulk_persistence._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.bulk_persistence._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.state.InstanceState"}], "type_ref": "typing.Iterable"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "type_ref": "typing.Iterable"}]}, "sqlalchemy.orm.session.SessionTransaction", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["sqlalchemy.sql.dml.Insert", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing._OrmKnownExecutionOptions"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_bulk_insert", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.engine.cursor.CursorResult"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.bulk_persistence._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 3, 3, 3, 5, 5], "arg_names": ["mapper", "mappings", "session_transaction", "isstates", "return_defaults", "render_nulls", "use_orm_insert_stmt", "execution_options"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.bulk_persistence._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.bulk_persistence._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.state.InstanceState"}], "type_ref": "typing.Iterable"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "type_ref": "typing.Iterable"}]}, "sqlalchemy.orm.session.SessionTransaction", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "NoneType"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing._OrmKnownExecutionOptions"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_bulk_insert", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.bulk_persistence._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 3, 3, 3, 5, 5], "arg_names": ["mapper", "mappings", "session_transaction", "isstates", "return_defaults", "render_nulls", "use_orm_insert_stmt", "execution_options"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.bulk_persistence._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.bulk_persistence._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.state.InstanceState"}], "type_ref": "typing.Iterable"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "type_ref": "typing.Iterable"}]}, "sqlalchemy.orm.session.SessionTransaction", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["sqlalchemy.sql.dml.Insert", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing._OrmKnownExecutionOptions"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_bulk_insert", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.engine.cursor.CursorResult"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.bulk_persistence._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}, "_bulk_update": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "sqlalchemy.orm.bulk_persistence._bulk_update", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 3, 3, 5, 5], "arg_names": ["mapper", "mappings", "session_transaction", "isstates", "update_changed_only", "use_orm_update_stmt", "enable_check_rowcount"], "dataclass_transform_spec": null, "flags": ["is_overload"], "fullname": "sqlalchemy.orm.bulk_persistence._bulk_update", "name": "_bulk_update", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 3, 3, 5, 5], "arg_names": ["mapper", "mappings", "session_transaction", "isstates", "update_changed_only", "use_orm_update_stmt", "enable_check_rowcount"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.bulk_persistence._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.state.InstanceState"}], "type_ref": "typing.Iterable"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "type_ref": "typing.Iterable"}]}, "sqlalchemy.orm.session.SessionTransaction", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["sqlalchemy.sql.dml.Update", {".class": "NoneType"}]}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_bulk_update", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.engine.result.Result"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.bulk_persistence._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 3, 3, 5, 5], "arg_names": ["mapper", "mappings", "session_transaction", "isstates", "update_changed_only", "use_orm_update_stmt", "enable_check_rowcount"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.orm.bulk_persistence._bulk_update", "name": "_bulk_update", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 3, 3, 5, 5], "arg_names": ["mapper", "mappings", "session_transaction", "isstates", "update_changed_only", "use_orm_update_stmt", "enable_check_rowcount"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.bulk_persistence._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.state.InstanceState"}], "type_ref": "typing.Iterable"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "type_ref": "typing.Iterable"}]}, "sqlalchemy.orm.session.SessionTransaction", "builtins.bool", "builtins.bool", {".class": "NoneType"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_bulk_update", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.bulk_persistence._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.bulk_persistence._bulk_update", "name": "_bulk_update", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 3, 3, 5, 5], "arg_names": ["mapper", "mappings", "session_transaction", "isstates", "update_changed_only", "use_orm_update_stmt", "enable_check_rowcount"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.bulk_persistence._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.state.InstanceState"}], "type_ref": "typing.Iterable"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "type_ref": "typing.Iterable"}]}, "sqlalchemy.orm.session.SessionTransaction", "builtins.bool", "builtins.bool", {".class": "NoneType"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_bulk_update", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.bulk_persistence._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 3, 3, 5, 5], "arg_names": ["mapper", "mappings", "session_transaction", "isstates", "update_changed_only", "use_orm_update_stmt", "enable_check_rowcount"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.orm.bulk_persistence._bulk_update", "name": "_bulk_update", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 3, 3, 5, 5], "arg_names": ["mapper", "mappings", "session_transaction", "isstates", "update_changed_only", "use_orm_update_stmt", "enable_check_rowcount"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.bulk_persistence._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.state.InstanceState"}], "type_ref": "typing.Iterable"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "type_ref": "typing.Iterable"}]}, "sqlalchemy.orm.session.SessionTransaction", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["sqlalchemy.sql.dml.Update", {".class": "NoneType"}]}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_bulk_update", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.engine.result.Result"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.bulk_persistence._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.bulk_persistence._bulk_update", "name": "_bulk_update", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 3, 3, 5, 5], "arg_names": ["mapper", "mappings", "session_transaction", "isstates", "update_changed_only", "use_orm_update_stmt", "enable_check_rowcount"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.bulk_persistence._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.state.InstanceState"}], "type_ref": "typing.Iterable"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "type_ref": "typing.Iterable"}]}, "sqlalchemy.orm.session.SessionTransaction", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["sqlalchemy.sql.dml.Update", {".class": "NoneType"}]}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_bulk_update", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.engine.result.Result"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.bulk_persistence._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 3, 3, 5, 5], "arg_names": ["mapper", "mappings", "session_transaction", "isstates", "update_changed_only", "use_orm_update_stmt", "enable_check_rowcount"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.bulk_persistence._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.state.InstanceState"}], "type_ref": "typing.Iterable"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "type_ref": "typing.Iterable"}]}, "sqlalchemy.orm.session.SessionTransaction", "builtins.bool", "builtins.bool", {".class": "NoneType"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_bulk_update", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.bulk_persistence._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 3, 3, 5, 5], "arg_names": ["mapper", "mappings", "session_transaction", "isstates", "update_changed_only", "use_orm_update_stmt", "enable_check_rowcount"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.bulk_persistence._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.state.InstanceState"}], "type_ref": "typing.Iterable"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "type_ref": "typing.Iterable"}]}, "sqlalchemy.orm.session.SessionTransaction", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["sqlalchemy.sql.dml.Update", {".class": "NoneType"}]}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_bulk_update", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.engine.result.Result"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.bulk_persistence._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}, "_entity_namespace_key": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base._entity_namespace_key", "kind": "Gdef"}, "_expand_composites": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["mapper", "mappings"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.bulk_persistence._expand_composites", "name": "_expand_composites", "type": null}}, "_result": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "attributes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.attributes", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "coercions": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.coercions", "kind": "Gdef"}, "context": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.context", "kind": "Gdef"}, "cursor": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.cursor", "kind": "Gdef"}, "dml": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.dml", "kind": "Gdef"}, "evaluator": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.evaluator", "kind": "Gdef"}, "expression": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.expression", "kind": "Gdef"}, "loading": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.loading", "kind": "Gdef"}, "orm_exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.exc", "kind": "Gdef"}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "persistence": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.persistence", "kind": "Gdef"}, "roles": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.roles", "kind": "Gdef"}, "sa_exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc", "kind": "Gdef"}, "select": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.select", "kind": "Gdef"}, "sqltypes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\bulk_persistence.py"}