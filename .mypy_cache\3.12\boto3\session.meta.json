{"data_mtime": 1759504552, "dep_lines": [12, 10, 13, 14, 15, 17, 18, 19, 20, 350, 7, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 65, 66, 67, 68, 69, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 262, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 20, 20, 30, 30, 30, 30, 30, 30, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "dependencies": ["boto3.resources.factory", "boto3.exceptions", "botocore.config", "botocore.credentials", "botocore.exceptions", "botocore.hooks", "botocore.loaders", "botocore.model", "botocore.session", "mypy_boto3_rds.client", "sys", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "_typeshed", "abc", "boto3.resources", "botocore", "botocore.client", "mypy_boto3_rds"], "hash": "681ab47481dbabd2ff78140f70c8aec1609db5214ca64c7104e3c8fe3925475c", "id": "boto3.session", "ignore_all": true, "interface_hash": "23958101b8c83e51d3aba114f8dc0359fe08407474c51ea4e0f729cdd666a550", "mtime": 1757092242, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\boto3-stubs\\session.pyi", "plugin_data": null, "size": 286103, "suppressed": ["mypy_boto3_accessanalyzer.client", "mypy_boto3_account.client", "mypy_boto3_acm.client", "mypy_boto3_acm_pca.client", "mypy_boto3_aiops.client", "mypy_boto3_amp.client", "mypy_boto3_amplify.client", "mypy_boto3_amplifybackend.client", "mypy_boto3_amplifyuibuilder.client", "mypy_boto3_apigateway.client", "mypy_boto3_apigatewaymanagementapi.client", "mypy_boto3_apigatewayv2.client", "mypy_boto3_appconfig.client", "mypy_boto3_appconfigdata.client", "mypy_boto3_appfabric.client", "mypy_boto3_appflow.client", "mypy_boto3_appintegrations.client", "mypy_boto3_application_autoscaling.client", "mypy_boto3_application_insights.client", "mypy_boto3_application_signals.client", "mypy_boto3_applicationcostprofiler.client", "mypy_boto3_appmesh.client", "mypy_boto3_apprunner.client", "mypy_boto3_appstream.client", "mypy_boto3_appsync.client", "mypy_boto3_apptest.client", "mypy_boto3_arc_region_switch.client", "mypy_boto3_arc_zonal_shift.client", "mypy_boto3_artifact.client", "mypy_boto3_athena.client", "mypy_boto3_auditmanager.client", "mypy_boto3_autoscaling.client", "mypy_boto3_autoscaling_plans.client", "mypy_boto3_b2bi.client", "mypy_boto3_backup.client", "mypy_boto3_backup_gateway.client", "mypy_boto3_backupsearch.client", "mypy_boto3_batch.client", "mypy_boto3_bcm_dashboards.client", "mypy_boto3_bcm_data_exports.client", "mypy_boto3_bcm_pricing_calculator.client", "mypy_boto3_bcm_recommended_actions.client", "mypy_boto3_bedrock.client", "mypy_boto3_bedrock_agent.client", "mypy_boto3_bedrock_agent_runtime.client", "mypy_boto3_bedrock_agentcore.client", "mypy_boto3_bedrock_agentcore_control.client", "mypy_boto3_bedrock_data_automation.client", "mypy_boto3_bedrock_data_automation_runtime.client", "mypy_boto3_bedrock_runtime.client", "mypy_boto3_billing.client", "mypy_boto3_billingconductor.client", "mypy_boto3_braket.client", "mypy_boto3_budgets.client", "mypy_boto3_ce.client", "mypy_boto3_chatbot.client", "mypy_boto3_chime.client", "mypy_boto3_chime_sdk_identity.client", "mypy_boto3_chime_sdk_media_pipelines.client", "mypy_boto3_chime_sdk_meetings.client", "mypy_boto3_chime_sdk_messaging.client", "mypy_boto3_chime_sdk_voice.client", "mypy_boto3_cleanrooms.client", "mypy_boto3_cleanroomsml.client", "mypy_boto3_cloud9.client", "mypy_boto3_cloudcontrol.client", "mypy_boto3_clouddirectory.client", "mypy_boto3_cloudformation.client", "mypy_boto3_cloudformation.service_resource", "mypy_boto3_cloudfront.client", "mypy_boto3_cloudfront_keyvaluestore.client", "mypy_boto3_cloudhsm.client", "mypy_boto3_cloudhsmv2.client", "mypy_boto3_cloudsearch.client", "mypy_boto3_cloudsearchdomain.client", "mypy_boto3_cloudtrail.client", "mypy_boto3_cloudtrail_data.client", "mypy_boto3_cloudwatch.client", "mypy_boto3_cloudwatch.service_resource", "mypy_boto3_codeartifact.client", "mypy_boto3_codebuild.client", "mypy_boto3_codecatalyst.client", "mypy_boto3_codecommit.client", "mypy_boto3_codeconnections.client", "mypy_boto3_codedeploy.client", "mypy_boto3_codeguru_reviewer.client", "mypy_boto3_codeguru_security.client", "mypy_boto3_codeguruprofiler.client", "mypy_boto3_codepipeline.client", "mypy_boto3_codestar_connections.client", "mypy_boto3_codestar_notifications.client", "mypy_boto3_cognito_identity.client", "mypy_boto3_cognito_idp.client", "mypy_boto3_cognito_sync.client", "mypy_boto3_comprehend.client", "mypy_boto3_comprehendmedical.client", "mypy_boto3_compute_optimizer.client", "mypy_boto3_config.client", "mypy_boto3_connect.client", "mypy_boto3_connect_contact_lens.client", "mypy_boto3_connectcampaigns.client", "mypy_boto3_connectcampaignsv2.client", "mypy_boto3_connectcases.client", "mypy_boto3_connectparticipant.client", "mypy_boto3_controlcatalog.client", "mypy_boto3_controltower.client", "mypy_boto3_cost_optimization_hub.client", "mypy_boto3_cur.client", "mypy_boto3_customer_profiles.client", "mypy_boto3_databrew.client", "mypy_boto3_dataexchange.client", "mypy_boto3_datapipeline.client", "mypy_boto3_datasync.client", "mypy_boto3_datazone.client", "mypy_boto3_dax.client", "mypy_boto3_deadline.client", "mypy_boto3_detective.client", "mypy_boto3_devicefarm.client", "mypy_boto3_devops_guru.client", "mypy_boto3_directconnect.client", "mypy_boto3_discovery.client", "mypy_boto3_dlm.client", "mypy_boto3_dms.client", "mypy_boto3_docdb.client", "mypy_boto3_docdb_elastic.client", "mypy_boto3_drs.client", "mypy_boto3_ds.client", "mypy_boto3_ds_data.client", "mypy_boto3_dsql.client", "mypy_boto3_dynamodb.client", "mypy_boto3_dynamodb.service_resource", "mypy_boto3_dynamodbstreams.client", "mypy_boto3_ebs.client", "mypy_boto3_ec2.client", "mypy_boto3_ec2.service_resource", "mypy_boto3_ec2_instance_connect.client", "mypy_boto3_ecr.client", "mypy_boto3_ecr_public.client", "mypy_boto3_ecs.client", "mypy_boto3_efs.client", "mypy_boto3_eks.client", "mypy_boto3_eks_auth.client", "mypy_boto3_elasticache.client", "mypy_boto3_elasticbeanstalk.client", "mypy_boto3_elastictranscoder.client", "mypy_boto3_elb.client", "mypy_boto3_elbv2.client", "mypy_boto3_emr.client", "mypy_boto3_emr_containers.client", "mypy_boto3_emr_serverless.client", "mypy_boto3_entityresolution.client", "mypy_boto3_es.client", "mypy_boto3_events.client", "mypy_boto3_evidently.client", "mypy_boto3_evs.client", "mypy_boto3_finspace.client", "mypy_boto3_finspace_data.client", "mypy_boto3_firehose.client", "mypy_boto3_fis.client", "mypy_boto3_fms.client", "mypy_boto3_forecast.client", "mypy_boto3_forecastquery.client", "mypy_boto3_frauddetector.client", "mypy_boto3_freetier.client", "mypy_boto3_fsx.client", "mypy_boto3_gamelift.client", "mypy_boto3_gameliftstreams.client", "mypy_boto3_geo_maps.client", "mypy_boto3_geo_places.client", "mypy_boto3_geo_routes.client", "mypy_boto3_glacier.client", "mypy_boto3_glacier.service_resource", "mypy_boto3_globalaccelerator.client", "mypy_boto3_glue.client", "mypy_boto3_grafana.client", "mypy_boto3_greengrass.client", "mypy_boto3_greengrassv2.client", "mypy_boto3_groundstation.client", "mypy_boto3_guardduty.client", "mypy_boto3_health.client", "mypy_boto3_healthlake.client", "mypy_boto3_iam.client", "mypy_boto3_iam.service_resource", "mypy_boto3_identitystore.client", "mypy_boto3_imagebuilder.client", "mypy_boto3_importexport.client", "mypy_boto3_inspector.client", "mypy_boto3_inspector2.client", "mypy_boto3_inspector_scan.client", "mypy_boto3_internetmonitor.client", "mypy_boto3_invoicing.client", "mypy_boto3_iot.client", "mypy_boto3_iot_data.client", "mypy_boto3_iot_jobs_data.client", "mypy_boto3_iot_managed_integrations.client", "mypy_boto3_iotanalytics.client", "mypy_boto3_iotdeviceadvisor.client", "mypy_boto3_iotevents.client", "mypy_boto3_iotevents_data.client", "mypy_boto3_iotfleethub.client", "mypy_boto3_iotfleetwise.client", "mypy_boto3_iotsecuretunneling.client", "mypy_boto3_iotsitewise.client", "mypy_boto3_iotthingsgraph.client", "mypy_boto3_iottwinmaker.client", "mypy_boto3_iotwireless.client", "mypy_boto3_ivs.client", "mypy_boto3_ivs_realtime.client", "mypy_boto3_ivschat.client", "mypy_boto3_kafka.client", "mypy_boto3_kafkaconnect.client", "mypy_boto3_kendra.client", "mypy_boto3_kendra_ranking.client", "mypy_boto3_keyspaces.client", "mypy_boto3_keyspacesstreams.client", "mypy_boto3_kinesis.client", "mypy_boto3_kinesis_video_archived_media.client", "mypy_boto3_kinesis_video_media.client", "mypy_boto3_kinesis_video_signaling.client", "mypy_boto3_kinesis_video_webrtc_storage.client", "mypy_boto3_kinesisanalytics.client", "mypy_boto3_kinesisanalyticsv2.client", "mypy_boto3_kinesisvideo.client", "mypy_boto3_kms.client", "mypy_boto3_lakeformation.client", "mypy_boto3_lambda.client", "mypy_boto3_launch_wizard.client", "mypy_boto3_lex_models.client", "mypy_boto3_lex_runtime.client", "mypy_boto3_lexv2_models.client", "mypy_boto3_lexv2_runtime.client", "mypy_boto3_license_manager.client", "mypy_boto3_license_manager_linux_subscriptions.client", "mypy_boto3_license_manager_user_subscriptions.client", "mypy_boto3_lightsail.client", "mypy_boto3_location.client", "mypy_boto3_logs.client", "mypy_boto3_lookoutequipment.client", "mypy_boto3_lookoutmetrics.client", "mypy_boto3_lookoutvision.client", "mypy_boto3_m2.client", "mypy_boto3_machinelearning.client", "mypy_boto3_macie2.client", "mypy_boto3_mailmanager.client", "mypy_boto3_managedblockchain.client", "mypy_boto3_managedblockchain_query.client", "mypy_boto3_marketplace_agreement.client", "mypy_boto3_marketplace_catalog.client", "mypy_boto3_marketplace_deployment.client", "mypy_boto3_marketplace_entitlement.client", "mypy_boto3_marketplace_reporting.client", "mypy_boto3_marketplacecommerceanalytics.client", "mypy_boto3_mediaconnect.client", "mypy_boto3_mediaconvert.client", "mypy_boto3_medialive.client", "mypy_boto3_mediapackage.client", "mypy_boto3_mediapackage_vod.client", "mypy_boto3_mediapackagev2.client", "mypy_boto3_mediastore.client", "mypy_boto3_mediastore_data.client", "mypy_boto3_mediatailor.client", "mypy_boto3_medical_imaging.client", "mypy_boto3_memorydb.client", "mypy_boto3_meteringmarketplace.client", "mypy_boto3_mgh.client", "mypy_boto3_mgn.client", "mypy_boto3_migration_hub_refactor_spaces.client", "mypy_boto3_migrationhub_config.client", "mypy_boto3_migrationhuborchestrator.client", "mypy_boto3_migrationhubstrategy.client", "mypy_boto3_mpa.client", "mypy_boto3_mq.client", "mypy_boto3_mturk.client", "mypy_boto3_mwaa.client", "mypy_boto3_neptune.client", "mypy_boto3_neptune_graph.client", "mypy_boto3_neptunedata.client", "mypy_boto3_network_firewall.client", "mypy_boto3_networkflowmonitor.client", "mypy_boto3_networkmanager.client", "mypy_boto3_networkmonitor.client", "mypy_boto3_notifications.client", "mypy_boto3_notificationscontacts.client", "mypy_boto3_oam.client", "mypy_boto3_observabilityadmin.client", "mypy_boto3_odb.client", "mypy_boto3_omics.client", "mypy_boto3_opensearch.client", "mypy_boto3_opensearchserverless.client", "mypy_boto3_organizations.client", "mypy_boto3_osis.client", "mypy_boto3_outposts.client", "mypy_boto3_panorama.client", "mypy_boto3_partnercentral_selling.client", "mypy_boto3_payment_cryptography.client", "mypy_boto3_payment_cryptography_data.client", "mypy_boto3_pca_connector_ad.client", "mypy_boto3_pca_connector_scep.client", "mypy_boto3_pcs.client", "mypy_boto3_personalize.client", "mypy_boto3_personalize_events.client", "mypy_boto3_personalize_runtime.client", "mypy_boto3_pi.client", "mypy_boto3_pinpoint.client", "mypy_boto3_pinpoint_email.client", "mypy_boto3_pinpoint_sms_voice.client", "mypy_boto3_pinpoint_sms_voice_v2.client", "mypy_boto3_pipes.client", "mypy_boto3_polly.client", "mypy_boto3_pricing.client", "mypy_boto3_proton.client", "mypy_boto3_qapps.client", "mypy_boto3_qbusiness.client", "mypy_boto3_qconnect.client", "mypy_boto3_qldb.client", "mypy_boto3_qldb_session.client", "mypy_boto3_quicksight.client", "mypy_boto3_ram.client", "mypy_boto3_rbin.client", "mypy_boto3_rds_data.client", "mypy_boto3_redshift.client", "mypy_boto3_redshift_data.client", "mypy_boto3_redshift_serverless.client", "mypy_boto3_rekognition.client", "mypy_boto3_repostspace.client", "mypy_boto3_resiliencehub.client", "mypy_boto3_resource_explorer_2.client", "mypy_boto3_resource_groups.client", "mypy_boto3_resourcegroupstaggingapi.client", "mypy_boto3_robomaker.client", "mypy_boto3_rolesanywhere.client", "mypy_boto3_route53.client", "mypy_boto3_route53_recovery_cluster.client", "mypy_boto3_route53_recovery_control_config.client", "mypy_boto3_route53_recovery_readiness.client", "mypy_boto3_route53domains.client", "mypy_boto3_route53profiles.client", "mypy_boto3_route53resolver.client", "mypy_boto3_rum.client", "mypy_boto3_s3.client", "mypy_boto3_s3.service_resource", "mypy_boto3_s3control.client", "mypy_boto3_s3outposts.client", "mypy_boto3_s3tables.client", "mypy_boto3_s3vectors.client", "mypy_boto3_sagemaker.client", "mypy_boto3_sagemaker_a2i_runtime.client", "mypy_boto3_sagemaker_edge.client", "mypy_boto3_sagemaker_featurestore_runtime.client", "mypy_boto3_sagemaker_geospatial.client", "mypy_boto3_sagemaker_metrics.client", "mypy_boto3_sagemaker_runtime.client", "mypy_boto3_savingsplans.client", "mypy_boto3_scheduler.client", "mypy_boto3_schemas.client", "mypy_boto3_sdb.client", "mypy_boto3_secretsmanager.client", "mypy_boto3_security_ir.client", "mypy_boto3_securityhub.client", "mypy_boto3_securitylake.client", "mypy_boto3_serverlessrepo.client", "mypy_boto3_service_quotas.client", "mypy_boto3_servicecatalog.client", "mypy_boto3_servicecatalog_appregistry.client", "mypy_boto3_servicediscovery.client", "mypy_boto3_ses.client", "mypy_boto3_sesv2.client", "mypy_boto3_shield.client", "mypy_boto3_signer.client", "mypy_boto3_simspaceweaver.client", "mypy_boto3_sms.client", "mypy_boto3_snow_device_management.client", "mypy_boto3_snowball.client", "mypy_boto3_sns.client", "mypy_boto3_sns.service_resource", "mypy_boto3_socialmessaging.client", "mypy_boto3_sqs.client", "mypy_boto3_sqs.service_resource", "mypy_boto3_ssm.client", "mypy_boto3_ssm_contacts.client", "mypy_boto3_ssm_guiconnect.client", "mypy_boto3_ssm_incidents.client", "mypy_boto3_ssm_quicksetup.client", "mypy_boto3_ssm_sap.client", "mypy_boto3_sso.client", "mypy_boto3_sso_admin.client", "mypy_boto3_sso_oidc.client", "mypy_boto3_stepfunctions.client", "mypy_boto3_storagegateway.client", "mypy_boto3_sts.client", "mypy_boto3_supplychain.client", "mypy_boto3_support.client", "mypy_boto3_support_app.client", "mypy_boto3_swf.client", "mypy_boto3_synthetics.client", "mypy_boto3_taxsettings.client", "mypy_boto3_textract.client", "mypy_boto3_timestream_influxdb.client", "mypy_boto3_timestream_query.client", "mypy_boto3_timestream_write.client", "mypy_boto3_tnb.client", "mypy_boto3_transcribe.client", "mypy_boto3_transfer.client", "mypy_boto3_translate.client", "mypy_boto3_trustedadvisor.client", "mypy_boto3_verifiedpermissions.client", "mypy_boto3_voice_id.client", "mypy_boto3_vpc_lattice.client", "mypy_boto3_waf.client", "mypy_boto3_waf_regional.client", "mypy_boto3_wafv2.client", "mypy_boto3_wellarchitected.client", "mypy_boto3_wisdom.client", "mypy_boto3_workdocs.client", "mypy_boto3_workmail.client", "mypy_boto3_workmailmessageflow.client", "mypy_boto3_workspaces.client", "mypy_boto3_workspaces_instances.client", "mypy_boto3_workspaces_thin_client.client", "mypy_boto3_workspaces_web.client", "mypy_boto3_xray.client"], "version_id": "1.8.0"}