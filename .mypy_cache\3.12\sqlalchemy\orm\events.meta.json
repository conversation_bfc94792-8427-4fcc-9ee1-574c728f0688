{"data_mtime": 1759503867, "dep_lines": [26, 27, 29, 30, 34, 37, 38, 44, 45, 50, 58, 60, 63, 64, 65, 67, 68, 26, 40, 41, 42, 59, 9, 11, 24, 40, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 5, 5, 5, 5, 25, 25, 25, 25, 25, 25, 25, 25, 5, 5, 10, 10, 25, 5, 5, 10, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.instrumentation", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.attributes", "sqlalchemy.orm.base", "sqlalchemy.orm.query", "sqlalchemy.orm.scoping", "sqlalchemy.orm.session", "sqlalchemy.event.registry", "sqlalchemy.util.compat", "sqlalchemy.orm._typing", "sqlalchemy.orm.unitofwork", "sqlalchemy.event.base", "sqlalchemy.orm.collections", "sqlalchemy.orm.context", "sqlalchemy.orm.decl_api", "sqlalchemy.orm.mapper", "sqlalchemy.orm.state", "sqlalchemy.orm", "sqlalchemy.event", "sqlalchemy.exc", "sqlalchemy.util", "sqlalchemy.engine", "__future__", "typing", "weakref", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "enum", "sqlalchemy.engine.base", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.util", "sqlalchemy.event.attr", "sqlalchemy.event.legacy", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.orm.state_changes", "sqlalchemy.orm.util", "sqlalchemy.sql", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.elements", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util.langhelpers", "sqlalchemy.util.preloaded"], "hash": "478a30b19c86f9cd27cd00add810bb7f4dc4920ef5639df1cadab4145b9d1a5a", "id": "sqlalchemy.orm.events", "ignore_all": true, "interface_hash": "9118ad64c321a133e7da6ad131a79dfc9be01615cd3069780998accc6ae63930", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\events.py", "plugin_data": null, "size": 131049, "suppressed": [], "version_id": "1.8.0"}