{"data_mtime": 1759503829, "dep_lines": [2, 5, 1, 3, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 20, 20, 30], "dependencies": ["collections.abc", "psycopg2._psycopg", "_typeshed", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "abc"], "hash": "32181a2f5f30bcbcecd4057eabcc243307a36e19b5a127a022c8fa76c8d4a4e4", "id": "psycopg2.extensions", "ignore_all": true, "interface_hash": "5bf5c9a618f56d252a9f02c44f1aa89296e764a435fda87ebedefb6ccf01ce9e", "mtime": 1757092195, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\psycopg2-stubs\\extensions.pyi", "plugin_data": null, "size": 3549, "suppressed": [], "version_id": "1.8.0"}