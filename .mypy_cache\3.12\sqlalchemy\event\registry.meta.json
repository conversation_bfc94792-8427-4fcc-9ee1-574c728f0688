{"data_mtime": 1759504546, "dep_lines": [39, 40, 35, 36, 17, 19, 20, 21, 33, 35, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 25, 10, 10, 5, 10, 10, 5, 10, 20, 5, 20, 20, 30, 30, 30, 30], "dependencies": ["sqlalchemy.event.attr", "sqlalchemy.event.base", "sqlalchemy.exc", "sqlalchemy.util", "__future__", "collections", "types", "typing", "weakref", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "_typeshed", "_weakref", "abc", "sqlalchemy.util.langhelpers"], "hash": "7b1de1c11f90d21c3d0489ef8dd433b6f0baf0f8c7d648ca3d900b85ed3d45ee", "id": "sqlalchemy.event.registry", "ignore_all": true, "interface_hash": "5f8374a378794449a00dce38db6dcefaea4c7373b033108290b83228a3301d52", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\event\\registry.py", "plugin_data": null, "size": 11534, "suppressed": [], "version_id": "1.8.0"}