{"data_mtime": **********, "dep_lines": [1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 20, 20, 30, 30], "dependencies": ["aws_lambda_powertools.metrics.provider.base", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "typing"], "hash": "ec283a470cf2ea9616e8b8de586125d28a4368a95e0521ab30f7c6357087d62e", "id": "aws_lambda_powertools.metrics.provider", "ignore_all": true, "interface_hash": "499b473763bbbf13253dd5bee2adffb014a6b2b7ee1749cb063463a7b7544f24", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\aws_lambda_powertools\\metrics\\provider\\__init__.py", "plugin_data": null, "size": 104, "suppressed": [], "version_id": "1.8.0"}