{"data_mtime": 1759503832, "dep_lines": [3, 176, 177, 178, 1, 173, 174, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 10, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["rich.style", "rich.console", "rich.table", "rich.text", "typing", "<PERSON><PERSON><PERSON><PERSON>", "io", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "_random", "_typeshed", "abc", "datetime", "enum", "rich.box", "rich.color", "rich.color_triplet", "rich.jupyter", "rich.terminal_theme", "rich.theme"], "hash": "8fd7998129fb6ea9f29b1633629f21fb43864d1cb664e8fe3df63db68aa9d11c", "id": "rich.default_styles", "ignore_all": true, "interface_hash": "a3293fdb6bce62005fdd0058f3593acec894058fdb9cbe3c0ff96ed8ff9915db", "mtime": 1757092238, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\rich\\default_styles.py", "plugin_data": null, "size": 8221, "suppressed": [], "version_id": "1.8.0"}