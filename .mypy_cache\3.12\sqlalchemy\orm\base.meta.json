{"data_mtime": 1759504547, "dep_lines": [28, 29, 33, 34, 37, 38, 44, 45, 46, 47, 48, 49, 50, 51, 52, 55, 28, 30, 31, 32, 33, 10, 12, 13, 14, 30, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 5, 5, 5, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 20, 10, 10, 5, 20, 5, 5, 10, 5, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.exc", "sqlalchemy.orm._typing", "sqlalchemy.sql.roles", "sqlalchemy.sql.elements", "sqlalchemy.util.langhelpers", "sqlalchemy.util.typing", "sqlalchemy.orm.attributes", "sqlalchemy.orm.dynamic", "sqlalchemy.orm.instrumentation", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.mapper", "sqlalchemy.orm.state", "sqlalchemy.orm.util", "sqlalchemy.orm.writeonly", "sqlalchemy.sql._typing", "sqlalchemy.sql.operators", "sqlalchemy.orm", "sqlalchemy.exc", "sqlalchemy.inspection", "sqlalchemy.util", "sqlalchemy.sql", "__future__", "enum", "operator", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.log", "sqlalchemy.orm.query", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "types"], "hash": "d2f8dcf2ef16f0fb0045435f3457fb877603e9076a204a9a042cabf480ccede3", "id": "sqlalchemy.orm.base", "ignore_all": true, "interface_hash": "d527971054ea2a87dad3e8780669ebd15d540851e1f15366b210865d65174465", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\base.py", "plugin_data": null, "size": 28472, "suppressed": [], "version_id": "1.8.0"}