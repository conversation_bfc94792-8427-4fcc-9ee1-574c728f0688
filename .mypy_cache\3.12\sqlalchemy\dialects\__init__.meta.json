{"data_mtime": 1759503866, "dep_lines": [19, 16, 8, 10, 16, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 10, 5, 5, 20, 5, 20, 20, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.engine.interfaces", "sqlalchemy.util", "__future__", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "sqlalchemy.engine", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.util.langhelpers"], "hash": "1c93fb1e739303623fce0eac9654deaa5900dfe82a1476d74a7c875ac5bdb6ee", "id": "sqlalchemy.dialects", "ignore_all": true, "interface_hash": "eb1d9718451cc81768ea17aba025d516055a2d045f4fa2e3faa77c04f6c9e3c9", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\__init__.py", "plugin_data": null, "size": 1860, "suppressed": [], "version_id": "1.8.0"}