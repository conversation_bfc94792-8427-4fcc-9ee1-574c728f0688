{"data_mtime": 1759504512, "dep_lines": [1, 4, 5, 6, 7, 8, 11, 2, 71, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 25, 5, 5, 5, 20, 20, 30, 30], "dependencies": ["collections.abc", "rich.highlighter", "rich.panel", "rich.pretty", "rich.table", "rich.text", "rich.console", "typing", "rich", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "rich.jupyter"], "hash": "95fe907adfdee09398df89708584801be3415d8281718bbab7f8a0bff2681a88", "id": "rich.scope", "ignore_all": true, "interface_hash": "8415a56d8bf0c10f867d2df84ed10a40628c82da9442d41c336419bb130f3672", "mtime": 1757092239, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\rich\\scope.py", "plugin_data": null, "size": 2831, "suppressed": [], "version_id": "1.8.0"}