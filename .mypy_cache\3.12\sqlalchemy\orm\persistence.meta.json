{"data_mtime": 1759503866, "dep_lines": [25, 26, 27, 28, 29, 34, 35, 36, 37, 25, 30, 31, 32, 33, 34, 18, 20, 23, 30, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 5, 10, 10, 5, 5, 20, 10, 10, 10, 10, 20, 5, 5, 10, 20, 5, 20, 20, 30, 30], "dependencies": ["sqlalchemy.orm.attributes", "sqlalchemy.orm.exc", "sqlalchemy.orm.loading", "sqlalchemy.orm.sync", "sqlalchemy.orm.base", "sqlalchemy.engine.cursor", "sqlalchemy.sql.operators", "sqlalchemy.sql.elements", "sqlalchemy.sql.selectable", "sqlalchemy.orm", "sqlalchemy.exc", "sqlalchemy.future", "sqlalchemy.sql", "sqlalchemy.util", "sqlalchemy.engine", "__future__", "itertools", "operator", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "typing"], "hash": "9e17383ee8924b3251deafc0d4c8dd619faf8dcade19e2cacdea683c32073aea", "id": "sqlalchemy.orm.persistence", "ignore_all": true, "interface_hash": "051f1f210c897900663352d7fa7a96c008ab44e4ea6c3680e17370f09d652e92", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\persistence.py", "plugin_data": null, "size": 63600, "suppressed": [], "version_id": "1.8.0"}