{".class": "MypyFile", "_fullname": "botocore.parsers", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseCBORParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.parsers.ResponseParser"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.parsers.BaseCBORParser", "name": "BaseCBORParser", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.parsers.BaseCBORParser", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.parsers", "mro": ["botocore.parsers.BaseCBORParser", "botocore.parsers.ResponseParser", "builtins.object"], "names": {".class": "SymbolTable", "BREAK_CODE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.parsers.BaseCBORParser.BREAK_CODE", "name": "BREAK_CODE", "type": "builtins.int"}}, "INDEFINITE_ITEM_ADDITIONAL_INFO": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.parsers.BaseCBORParser.INDEFINITE_ITEM_ADDITIONAL_INFO", "name": "INDEFINITE_ITEM_ADDITIONAL_INFO", "type": "builtins.int"}}, "get_peekable_stream_from_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "bytes"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.parsers.BaseCBORParser.get_peekable_stream_from_bytes", "name": "get_peekable_stream_from_bytes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "bytes"], "arg_types": ["botocore.parsers.BaseCBORParser", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_peekable_stream_from_bytes of BaseCBORParser", "ret_type": "io.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "major_type_to_parsing_method_map": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "botocore.parsers.BaseCBORParser.major_type_to_parsing_method_map", "name": "major_type_to_parsing_method_map", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.parsers.BaseCBORParser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "major_type_to_parsing_method_map of BaseCBORParser", "ret_type": {".class": "Instance", "args": ["builtins.int", {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "type_ref": "typing.IO"}, "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "botocore.parsers.BaseCBORParser.major_type_to_parsing_method_map", "name": "major_type_to_parsing_method_map", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.int", {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "type_ref": "typing.IO"}, "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}], "type_ref": "builtins.dict"}], "type_ref": "botocore.utils.CachedProperty"}}}}, "parse_data_item": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.parsers.BaseCBORParser.parse_data_item", "name": "parse_data_item", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "arg_types": ["botocore.parsers.BaseCBORParser", {".class": "Instance", "args": ["builtins.bytes"], "type_ref": "typing.IO"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_data_item of BaseCBORParser", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.parsers.BaseCBORParser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.parsers.BaseCBORParser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BaseEventStreamParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.parsers.ResponseParser"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.parsers.BaseEventStreamParser", "name": "BaseEventStreamParser", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.parsers.BaseEventStreamParser", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.parsers", "mro": ["botocore.parsers.BaseEventStreamParser", "botocore.parsers.ResponseParser", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.parsers.BaseEventStreamParser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.parsers.BaseEventStreamParser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BaseJSONParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.parsers.ResponseParser"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.parsers.BaseJSONParser", "name": "BaseJSONParser", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.parsers.BaseJSONParser", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.parsers", "mro": ["botocore.parsers.BaseJSONParser", "botocore.parsers.ResponseParser", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.parsers.BaseJSONParser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.parsers.BaseJSONParser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BaseRestParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.parsers.ResponseParser"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.parsers.BaseRestParser", "name": "BaseRestParser", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.parsers.BaseRestParser", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.parsers", "mro": ["botocore.parsers.BaseRestParser", "botocore.parsers.ResponseParser", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.parsers.BaseRestParser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.parsers.BaseRestParser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BaseRpcV2Parser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.parsers.ResponseParser"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.parsers.BaseRpcV2Parser", "name": "BaseRpcV2Parser", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.parsers.BaseRpcV2Parser", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.parsers", "mro": ["botocore.parsers.BaseRpcV2Parser", "botocore.parsers.ResponseParser", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.parsers.BaseRpcV2Parser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.parsers.BaseRpcV2Parser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BaseXMLResponseParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.parsers.ResponseParser"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.parsers.BaseXMLResponseParser", "name": "BaseXMLResponseParser", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.parsers.BaseXMLResponseParser", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.parsers", "mro": ["botocore.parsers.BaseXMLResponseParser", "botocore.parsers.ResponseParser", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "timestamp_parser", "blob_parser"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.parsers.BaseXMLResponseParser.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "timestamp_parser", "blob_parser"], "arg_types": ["botocore.parsers.BaseXMLResponseParser", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BaseXMLResponseParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.parsers.BaseXMLResponseParser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.parsers.BaseXMLResponseParser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BufferedReader": {".class": "SymbolTableNode", "cross_ref": "io.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CachedProperty": {".class": "SymbolTableNode", "cross_ref": "botocore.utils.CachedProperty", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DEFAULT_TIMESTAMP_PARSER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "botocore.parsers.DEFAULT_TIMESTAMP_PARSER", "name": "DEFAULT_TIMESTAMP_PARSER", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "datetime.datetime", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "EC2QueryParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.parsers.QueryParser"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.parsers.EC2QueryParser", "name": "EC2QueryP<PERSON>er", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.parsers.EC2QueryParser", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.parsers", "mro": ["botocore.parsers.EC2QueryParser", "botocore.parsers.QueryParser", "botocore.parsers.BaseXMLResponseParser", "botocore.parsers.ResponseParser", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.parsers.EC2QueryParser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.parsers.EC2QueryParser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EventStream": {".class": "SymbolTableNode", "cross_ref": "botocore.eventstream.EventStream", "kind": "Gdef"}, "EventStreamCBORParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.parsers.BaseEventStreamParser", "botocore.parsers.BaseCBORParser"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.parsers.EventStreamCBORParser", "name": "EventStreamCBORParser", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.parsers.EventStreamCBORParser", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.parsers", "mro": ["botocore.parsers.EventStreamCBORParser", "botocore.parsers.BaseEventStreamParser", "botocore.parsers.BaseCBORParser", "botocore.parsers.ResponseParser", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.parsers.EventStreamCBORParser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.parsers.EventStreamCBORParser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EventStreamJSONParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.parsers.BaseEventStreamParser", "botocore.parsers.BaseJSONParser"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.parsers.EventStreamJSONParser", "name": "EventStreamJSONParser", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.parsers.EventStreamJSONParser", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.parsers", "mro": ["botocore.parsers.EventStreamJSONParser", "botocore.parsers.BaseEventStreamParser", "botocore.parsers.BaseJSONParser", "botocore.parsers.ResponseParser", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.parsers.EventStreamJSONParser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.parsers.EventStreamJSONParser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EventStreamXMLParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.parsers.BaseEventStreamParser", "botocore.parsers.BaseXMLResponseParser"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.parsers.EventStreamXMLParser", "name": "EventStreamXMLParser", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.parsers.EventStreamXMLParser", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.parsers", "mro": ["botocore.parsers.EventStreamXMLParser", "botocore.parsers.BaseEventStreamParser", "botocore.parsers.BaseXMLResponseParser", "botocore.parsers.ResponseParser", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.parsers.EventStreamXMLParser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.parsers.EventStreamXMLParser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IO": {".class": "SymbolTableNode", "cross_ref": "typing.IO", "kind": "Gdef", "module_hidden": true, "module_public": false}, "JSONParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.parsers.BaseJSONParser"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.parsers.JSONParser", "name": "JSO<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.parsers.JSONParser", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.parsers", "mro": ["botocore.parsers.JSONParser", "botocore.parsers.BaseJSONParser", "botocore.parsers.ResponseParser", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.parsers.JSONParser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.parsers.JSONParser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LOG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "botocore.parsers.LOG", "name": "LOG", "type": "logging.Logger"}}, "Logger": {".class": "SymbolTableNode", "cross_ref": "logging.Logger", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NoInitialResponseError": {".class": "SymbolTableNode", "cross_ref": "botocore.eventstream.NoInitialResponseError", "kind": "Gdef"}, "PROTOCOL_PARSERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.parsers.PROTOCOL_PARSERS", "name": "PROTOCOL_PARSERS", "type": {".class": "Instance", "args": ["builtins.str", "botocore.parsers.ResponseParser"], "type_ref": "builtins.dict"}}}, "QueryParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.parsers.BaseXMLResponseParser"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.parsers.QueryParser", "name": "Query<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.parsers.QueryParser", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.parsers", "mro": ["botocore.parsers.QueryParser", "botocore.parsers.BaseXMLResponseParser", "botocore.parsers.ResponseParser", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.parsers.QueryParser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.parsers.QueryParser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ResponseParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.parsers.ResponseParser", "name": "<PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.parsers.ResponseParser", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.parsers", "mro": ["botocore.parsers.ResponseParser", "builtins.object"], "names": {".class": "SymbolTable", "DEFAULT_ENCODING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.parsers.ResponseParser.DEFAULT_ENCODING", "name": "DEFAULT_ENCODING", "type": "builtins.str"}}, "EVENT_STREAM_PARSER_CLS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.parsers.ResponseParser.EVENT_STREAM_PARSER_CLS", "name": "EVENT_STREAM_PARSER_CLS", "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": "botocore.parsers.ResponseParser"}, {".class": "NoneType"}]}}}, "KNOWN_LOCATIONS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "botocore.parsers.ResponseParser.KNOWN_LOCATIONS", "name": "KNOWN_LOCATIONS", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "timestamp_parser", "blob_parser"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.parsers.ResponseParser.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "timestamp_parser", "blob_parser"], "arg_types": ["botocore.parsers.ResponseParser", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ResponseParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "parse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "response", "shape"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.parsers.ResponseParser.parse", "name": "parse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "response", "shape"], "arg_types": ["botocore.parsers.ResponseParser", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, "botocore.model.Shape"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse of ResponseParser", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.parsers.ResponseParser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.parsers.ResponseParser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ResponseParserError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.parsers.ResponseParserError", "name": "ResponseParserError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.parsers.ResponseParserError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.parsers", "mro": ["botocore.parsers.ResponseParserError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.parsers.ResponseParserError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.parsers.ResponseParserError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ResponseParserFactory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.parsers.ResponseParserFactory", "name": "ResponseParserFactory", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.parsers.ResponseParserFactory", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.parsers", "mro": ["botocore.parsers.ResponseParserFactory", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.parsers.ResponseParserFactory.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["botocore.parsers.ResponseParserFactory"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ResponseParserFactory", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "create_parser": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "protocol_name"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.parsers.ResponseParserFactory.create_parser", "name": "create_parser", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "protocol_name"], "arg_types": ["botocore.parsers.ResponseParserFactory", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_parser of ResponseParserFactory", "ret_type": "botocore.parsers.ResponseParser", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "set_parser_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.parsers.ResponseParserFactory.set_parser_defaults", "name": "set_parser_defaults", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["botocore.parsers.ResponseParserFactory", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_parser_defaults of ResponseParserFactory", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.parsers.ResponseParserFactory.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.parsers.ResponseParserFactory", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RestJSONParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.parsers.BaseRestParser", "botocore.parsers.BaseJSONParser"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.parsers.RestJSONParser", "name": "RestJ<PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.parsers.RestJSONParser", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.parsers", "mro": ["botocore.parsers.RestJSONParser", "botocore.parsers.BaseRestParser", "botocore.parsers.BaseJSONParser", "botocore.parsers.ResponseParser", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.parsers.RestJSONParser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.parsers.RestJSONParser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RestXMLParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.parsers.BaseRestParser", "botocore.parsers.BaseXMLResponseParser"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.parsers.RestXMLParser", "name": "RestXMLParser", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.parsers.RestXMLParser", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.parsers", "mro": ["botocore.parsers.RestXMLParser", "botocore.parsers.BaseRestParser", "botocore.parsers.BaseXMLResponseParser", "botocore.parsers.ResponseParser", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.parsers.RestXMLParser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.parsers.RestXMLParser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RpcV2CBORParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.parsers.BaseRpcV2Parser", "botocore.parsers.BaseCBORParser"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "botocore.parsers.RpcV2CBORParser", "name": "RpcV2CBORParser", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "botocore.parsers.RpcV2CBORParser", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "botocore.parsers", "mro": ["botocore.parsers.RpcV2CBORParser", "botocore.parsers.BaseRpcV2Parser", "botocore.parsers.BaseCBORParser", "botocore.parsers.ResponseParser", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "botocore.parsers.RpcV2CBORParser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "botocore.parsers.RpcV2CBORParser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Shape": {".class": "SymbolTableNode", "cross_ref": "botocore.model.Shape", "kind": "Gdef", "module_hidden": true, "module_public": false}, "XMLParseError": {".class": "SymbolTableNode", "cross_ref": "botocore.compat.XMLParseError", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.parsers.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.parsers.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.parsers.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.parsers.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "botocore.parsers.__package__", "name": "__package__", "type": "builtins.str"}}, "create_parser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["protocol"], "dataclass_transform_spec": null, "flags": [], "fullname": "botocore.parsers.create_parser", "name": "create_parser", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["protocol"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_parser", "ret_type": "botocore.parsers.ResponseParser", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "is_json_value_header": {".class": "SymbolTableNode", "cross_ref": "botocore.utils.is_json_value_header", "kind": "Gdef"}, "lowercase_dict": {".class": "SymbolTableNode", "cross_ref": "botocore.utils.lowercase_dict", "kind": "Gdef"}, "merge_dicts": {".class": "SymbolTableNode", "cross_ref": "botocore.utils.merge_dicts", "kind": "Gdef"}, "parse_timestamp": {".class": "SymbolTableNode", "cross_ref": "botocore.utils.parse_timestamp", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\botocore-stubs\\parsers.pyi"}