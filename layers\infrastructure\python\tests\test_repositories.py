"""Tests for repository implementations."""

import pytest
from datetime import datetime, timezone
from unittest.mock import <PERSON><PERSON>, Async<PERSON>ock, MagicMock
from sqlalchemy.ext.asyncio import AsyncSession

from smartanalytics_infrastructure.repositories.tenant_repository import TenantRepository
from smartanalytics_infrastructure.repositories.agent_repository import AgentRepository
from smartanalytics_infrastructure.repositories.ring_group_repository import RingGroupRepository
from smartanalytics_infrastructure.repositories.agent_event_repository import AgentEventRepository
from smartanalytics_infrastructure.database.models import (
    DimTenant,
    DimAgent,
    DimRingGroup,
    DimAgentEvent
)


class TestTenantRepository:
    """Test TenantRepository implementation."""

    @pytest.fixture
    def mock_session(self):
        """Mock async database session."""
        session = Mock(spec=AsyncSession)
        session.execute = AsyncMock()
        session.add = Mock()
        session.commit = AsyncMock()
        session.flush = AsyncMock()
        return session

    @pytest.fixture
    def repository(self, mock_session):
        """Create TenantRepository instance with mock session."""
        return TenantRepository(mock_session)

    @pytest.mark.asyncio
    async def test_get_by_name_found(self, repository, mock_session):
        """Test getting tenant by name when found."""
        # Setup mock result
        mock_tenant = DimTenant(
            tenant_key=1,
            tenant_name="TestTenant",
            effective_date=datetime.now(timezone.utc),
            expiry_date=datetime(9999, 12, 31, 23, 59, 59, tzinfo=timezone.utc),
            is_current=True
        )
        
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = mock_tenant
        mock_session.execute.return_value = mock_result

        # Test repository method
        result = await repository.get_by_name("TestTenant")
        
        # Verify result and database interaction
        assert result == mock_tenant
        mock_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_by_name_not_found(self, repository, mock_session):
        """Test getting tenant by name when not found."""
        # Setup mock to return None
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result

        # Test repository method
        result = await repository.get_by_name("NonExistentTenant")
        
        # Verify None is returned
        assert result is None
        mock_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_tenant(self, repository, mock_session):
        """Test creating a new tenant."""
        tenant_name = "NewTenant"
        
        # Test repository method
        result = await repository.create(tenant_name)
        
        # Verify tenant creation
        assert isinstance(result, DimTenant)
        assert result.tenant_name == tenant_name
        assert result.is_current is True
        assert result.effective_date is not None
        assert result.expiry_date.year == 9999
        
        # Verify database interactions
        mock_session.add.assert_called_once_with(result)
        mock_session.flush.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_or_create_existing_tenant(self, repository, mock_session):
        """Test get_or_create when tenant already exists."""
        # Setup existing tenant
        existing_tenant = DimTenant(
            tenant_key=1,
            tenant_name="ExistingTenant",
            effective_date=datetime.now(timezone.utc),
            expiry_date=datetime(9999, 12, 31, 23, 59, 59, tzinfo=timezone.utc),
            is_current=True
        )
        
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = existing_tenant
        mock_session.execute.return_value = mock_result

        # Test repository method
        result, created = await repository.get_or_create("ExistingTenant")
        
        # Verify existing tenant is returned
        assert result == existing_tenant
        assert created is False
        mock_session.add.assert_not_called()

    @pytest.mark.asyncio
    async def test_get_or_create_new_tenant(self, repository, mock_session):
        """Test get_or_create when tenant doesn't exist."""
        # Setup mock to return None (tenant doesn't exist)
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result

        # Test repository method
        result, created = await repository.get_or_create("NewTenant")
        
        # Verify new tenant is created
        assert isinstance(result, DimTenant)
        assert result.tenant_name == "NewTenant"
        assert created is True
        mock_session.add.assert_called_once()
        mock_session.flush.assert_called_once()


class TestAgentRepository:
    """Test AgentRepository implementation."""

    @pytest.fixture
    def mock_session(self):
        """Mock async database session."""
        session = Mock(spec=AsyncSession)
        session.execute = AsyncMock()
        session.add = Mock()
        session.commit = AsyncMock()
        session.flush = AsyncMock()
        return session

    @pytest.fixture
    def repository(self, mock_session):
        """Create AgentRepository instance with mock session."""
        return AgentRepository(mock_session)

    @pytest.mark.asyncio
    async def test_get_by_name_and_tenant_found(self, repository, mock_session):
        """Test getting agent by name and tenant when found."""
        # Setup mock result
        mock_agent = DimAgent(
            agent_key=1,
            tenant_key=1,
            agent_name="TestAgent001",
            effective_date=datetime.now(timezone.utc),
            expiry_date=datetime(9999, 12, 31, 23, 59, 59, tzinfo=timezone.utc),
            is_current=True
        )
        
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = mock_agent
        mock_session.execute.return_value = mock_result

        # Test repository method
        result = await repository.get_by_name_and_tenant("TestAgent001", 1)
        
        # Verify result
        assert result == mock_agent
        mock_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_agent(self, repository, mock_session):
        """Test creating a new agent."""
        agent_name = "NewAgent"
        tenant_key = 1
        
        # Test repository method
        result = await repository.create(agent_name, tenant_key)
        
        # Verify agent creation
        assert isinstance(result, DimAgent)
        assert result.agent_name == agent_name
        assert result.tenant_key == tenant_key
        assert result.is_current is True
        
        # Verify database interactions
        mock_session.add.assert_called_once_with(result)
        mock_session.flush.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_or_create_new_agent(self, repository, mock_session):
        """Test get_or_create when agent doesn't exist."""
        # Setup mock to return None (agent doesn't exist)
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result

        # Test repository method
        result, created = await repository.get_or_create("NewAgent", 1)
        
        # Verify new agent is created
        assert isinstance(result, DimAgent)
        assert result.agent_name == "NewAgent"
        assert result.tenant_key == 1
        assert created is True
        mock_session.add.assert_called_once()


class TestRingGroupRepository:
    """Test RingGroupRepository implementation."""

    @pytest.fixture
    def mock_session(self):
        """Mock async database session."""
        session = Mock(spec=AsyncSession)
        session.execute = AsyncMock()
        session.add = Mock()
        session.commit = AsyncMock()
        session.flush = AsyncMock()
        return session

    @pytest.fixture
    def repository(self, mock_session):
        """Create RingGroupRepository instance with mock session."""
        return RingGroupRepository(mock_session)

    @pytest.mark.asyncio
    async def test_get_by_name_and_tenant_found(self, repository, mock_session):
        """Test getting ring group by name and tenant when found."""
        # Setup mock result
        mock_ring_group = DimRingGroup(
            ring_group_key=1,
            tenant_key=1,
            ring_group_name="Support",
            ring_group_uri="sip:<EMAIL>",
            effective_date=datetime.now(timezone.utc),
            expiry_date=datetime(9999, 12, 31, 23, 59, 59, tzinfo=timezone.utc),
            is_current=True
        )
        
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = mock_ring_group
        mock_session.execute.return_value = mock_result

        # Test repository method
        result = await repository.get_by_name_and_tenant("Support", 1)
        
        # Verify result
        assert result == mock_ring_group
        mock_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_ring_group(self, repository, mock_session):
        """Test creating a new ring group."""
        ring_group_name = "Sales"
        ring_group_uri = "sip:<EMAIL>"
        tenant_key = 1
        
        # Test repository method
        result = await repository.create(ring_group_name, ring_group_uri, tenant_key)
        
        # Verify ring group creation
        assert isinstance(result, DimRingGroup)
        assert result.ring_group_name == ring_group_name
        assert result.ring_group_uri == ring_group_uri
        assert result.tenant_key == tenant_key
        assert result.is_current is True
        
        # Verify database interactions
        mock_session.add.assert_called_once_with(result)
        mock_session.flush.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_or_create_with_uri_extraction(self, repository, mock_session):
        """Test get_or_create with URI extraction from event data."""
        # Setup mock to return None (ring group doesn't exist)
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result

        # Test repository method with event data containing URI
        event_data = {"ringGroupUri": "sip:<EMAIL>"}
        result, created = await repository.get_or_create("Support", 1, event_data)
        
        # Verify new ring group is created with extracted URI
        assert isinstance(result, DimRingGroup)
        assert result.ring_group_name == "Support"
        assert result.ring_group_uri == "sip:<EMAIL>"
        assert result.tenant_key == 1
        assert created is True


class TestAgentEventRepository:
    """Test AgentEventRepository implementation."""

    @pytest.fixture
    def mock_session(self):
        """Mock async database session."""
        session = Mock(spec=AsyncSession)
        session.execute = AsyncMock()
        session.add = Mock()
        session.commit = AsyncMock()
        session.flush = AsyncMock()
        return session

    @pytest.fixture
    def repository(self, mock_session):
        """Create AgentEventRepository instance with mock session."""
        return AgentEventRepository(mock_session)

    @pytest.mark.asyncio
    async def test_get_by_hash_found(self, repository, mock_session):
        """Test getting event by hash when found."""
        # Setup mock result
        mock_event = DimAgentEvent(
            event_key=1,
            tenant_key=1,
            agent_key=1,
            event_type="Login",
            event_timestamp=datetime.now(timezone.utc),
            event_hash="test_hash_123",
            event_data={"test": "data"},
            sqs_message_id="test-message-id"
        )
        
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = mock_event
        mock_session.execute.return_value = mock_result

        # Test repository method
        result = await repository.get_by_hash("test_hash_123")
        
        # Verify result
        assert result == mock_event
        mock_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_by_hash_not_found(self, repository, mock_session):
        """Test getting event by hash when not found."""
        # Setup mock to return None
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result

        # Test repository method
        result = await repository.get_by_hash("nonexistent_hash")
        
        # Verify None is returned
        assert result is None
        mock_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_event_without_ring_group(self, repository, mock_session):
        """Test creating a new event without ring group."""
        event_data = {
            "tenant_key": 1,
            "agent_key": 1,
            "event_type": "Login",
            "event_timestamp": datetime.now(timezone.utc),
            "event_hash": "test_hash_456",
            "event_data": {"workstation": "WS001"},
            "sqs_message_id": "test-message-456"
        }
        
        # Test repository method
        result = await repository.create(**event_data)
        
        # Verify event creation
        assert isinstance(result, DimAgentEvent)
        assert result.tenant_key == 1
        assert result.agent_key == 1
        assert result.event_type == "Login"
        assert result.event_hash == "test_hash_456"
        assert result.ring_group_key is None
        
        # Verify database interactions
        mock_session.add.assert_called_once_with(result)
        mock_session.flush.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_event_with_ring_group(self, repository, mock_session):
        """Test creating a new event with ring group."""
        event_data = {
            "tenant_key": 1,
            "agent_key": 1,
            "ring_group_key": 1,
            "event_type": "ACDLogin",
            "event_timestamp": datetime.now(timezone.utc),
            "event_hash": "test_hash_acd",
            "event_data": {"ringGroupName": "Support"},
            "sqs_message_id": "test-message-acd"
        }
        
        # Test repository method
        result = await repository.create(**event_data)
        
        # Verify event creation with ring group
        assert isinstance(result, DimAgentEvent)
        assert result.ring_group_key == 1
        assert result.event_type == "ACDLogin"
        
        # Verify database interactions
        mock_session.add.assert_called_once_with(result)
        mock_session.flush.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_batch_events(self, repository, mock_session):
        """Test creating multiple events in batch."""
        events_data = [
            {
                "tenant_key": 1,
                "agent_key": 1,
                "event_type": "Login",
                "event_timestamp": datetime.now(timezone.utc),
                "event_hash": "hash_1",
                "event_data": {"workstation": "WS001"},
                "sqs_message_id": "msg-1"
            },
            {
                "tenant_key": 1,
                "agent_key": 1,
                "event_type": "Logout",
                "event_timestamp": datetime.now(timezone.utc),
                "event_hash": "hash_2",
                "event_data": {"workstation": "WS001"},
                "sqs_message_id": "msg-2"
            }
        ]
        
        # Test repository method
        results = await repository.create_batch(events_data)
        
        # Verify batch creation
        assert len(results) == 2
        assert all(isinstance(event, DimAgentEvent) for event in results)
        assert results[0].event_hash == "hash_1"
        assert results[1].event_hash == "hash_2"
        
        # Verify database interactions
        assert mock_session.add.call_count == 2
        mock_session.flush.assert_called_once()
