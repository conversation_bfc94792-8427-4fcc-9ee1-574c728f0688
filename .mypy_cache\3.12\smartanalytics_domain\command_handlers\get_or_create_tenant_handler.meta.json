{"data_mtime": 1759504553, "dep_lines": [3, 6, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["smartanalytics_domain.commands.get_or_create_tenant_command", "smartanalytics_domain.ports.unit_of_work", "smartanalytics_utilities.utils.logging_helper", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "aws_lambda_powertools", "aws_lambda_powertools.logging", "aws_lambda_powertools.logging.logger", "smartanalytics_domain.commands", "smartanalytics_domain.models", "smartanalytics_domain.models.tenant", "smartanalytics_domain.ports", "smartanalytics_domain.ports.repositories", "smartanalytics_utilities", "smartanalytics_utilities.utils", "types", "typing"], "hash": "b26b9aa45fb18bdf32b858823b086bc6cfb94c8c8d00e9c92f1d816ccfe7b25e", "id": "smartanalytics_domain.command_handlers.get_or_create_tenant_handler", "ignore_all": false, "interface_hash": "89ae1e83bc65331fa0e1e94e215315fb7250a41e0846e02322220d06e598b99e", "mtime": 1759502092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "layers\\domain\\python\\smartanalytics_domain\\command_handlers\\get_or_create_tenant_handler.py", "plugin_data": null, "size": 1543, "suppressed": [], "version_id": "1.8.0"}