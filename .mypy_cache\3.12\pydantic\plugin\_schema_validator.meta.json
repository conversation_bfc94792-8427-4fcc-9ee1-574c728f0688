{"data_mtime": 1759503833, "dep_lines": [6, 13, 3, 5, 7, 9, 10, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 25, 5, 10, 5, 5, 5, 5, 20, 20, 30, 30, 30], "dependencies": ["collections.abc", "pydantic.plugin", "__future__", "functools", "typing", "pydantic_core", "typing_extensions", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "pydantic_core._pydantic_core", "pydantic_core.core_schema"], "hash": "41b9aab06df730199fb4d4369cd8ae376d8b85badec5403b8a90d5bf7274d815", "id": "pydantic.plugin._schema_validator", "ignore_all": true, "interface_hash": "622bb1a6efa8873bead804bfd7018bf69029bfd735817c7b0350d4e3297832c3", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\plugin\\_schema_validator.py", "plugin_data": null, "size": 5267, "suppressed": [], "version_id": "1.8.0"}