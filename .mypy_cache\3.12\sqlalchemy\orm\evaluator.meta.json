{"data_mtime": 1759504547, "dep_lines": [24, 25, 30, 31, 24, 27, 29, 34, 20, 22, 27, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 5, 20, 10, 5, 5, 5, 5, 5, 5, 20, 20, 30], "dependencies": ["sqlalchemy.orm.exc", "sqlalchemy.orm.base", "sqlalchemy.sql.operators", "sqlalchemy.sql.sqltypes", "sqlalchemy.orm", "sqlalchemy.exc", "sqlalchemy.sql", "sqlalchemy.util", "__future__", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "abc"], "hash": "cce3fef2a6aa23d3d9eeff770444afe522bdd44c0383834e7b61bdcfefb8968f", "id": "sqlalchemy.orm.evaluator", "ignore_all": true, "interface_hash": "646ba179e0465f088aaf1093605cb71e9eb0c68abe451192fcc52da11c77eaa0", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\evaluator.py", "plugin_data": null, "size": 12732, "suppressed": [], "version_id": "1.8.0"}