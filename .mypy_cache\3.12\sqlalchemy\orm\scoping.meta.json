{"data_mtime": 1759504547, "dep_lines": [25, 34, 37, 40, 41, 42, 43, 57, 60, 61, 71, 72, 73, 74, 75, 27, 28, 51, 8, 10, 27, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 10, 5, 25, 5, 5, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.session", "sqlalchemy.util.typing", "sqlalchemy.orm._typing", "sqlalchemy.orm.identity", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.mapper", "sqlalchemy.orm.query", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.result", "sqlalchemy.sql._typing", "sqlalchemy.sql.base", "sqlalchemy.sql.dml", "sqlalchemy.sql.elements", "sqlalchemy.sql.roles", "sqlalchemy.sql.selectable", "sqlalchemy.exc", "sqlalchemy.util", "sqlalchemy.engine", "__future__", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "enum", "sqlalchemy.engine._py_row", "sqlalchemy.engine.base", "sqlalchemy.engine.cursor", "sqlalchemy.engine.row", "sqlalchemy.engine.util", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.orm.base", "sqlalchemy.orm.state_changes", "sqlalchemy.orm.util", "sqlalchemy.sql", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.compiler", "sqlalchemy.sql.operators", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util._collections", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers"], "hash": "d6f0b16992e79d265b737aa0e32256d5d622070c25475132f7fff889dbd5e636", "id": "sqlalchemy.orm.scoping", "ignore_all": true, "interface_hash": "271427d809d82837feb2cd8f15b13792f339ba165a5ea9240e433b584d121eb1", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\scoping.py", "plugin_data": null, "size": 80762, "suppressed": [], "version_id": "1.8.0"}