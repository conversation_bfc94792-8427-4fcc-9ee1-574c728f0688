{"data_mtime": 1759503828, "dep_lines": [3, 5, 6, 7, 8, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 5, 5, 20, 20, 30, 30, 30, 30], "dependencies": ["__future__", "ast", "inspect", "textwrap", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "_ast", "_collections_abc", "abc", "types"], "hash": "a7e49316f2c7533c6b8fa6db9696800185a6ab820dc55080748ba90e0418488c", "id": "pydantic._internal._docs_extraction", "ignore_all": true, "interface_hash": "acd7a824c31526411b700cfeb9462c863b9a30751555654b94f7e1b158d906af", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\_internal\\_docs_extraction.py", "plugin_data": null, "size": 3831, "suppressed": [], "version_id": "1.8.0"}