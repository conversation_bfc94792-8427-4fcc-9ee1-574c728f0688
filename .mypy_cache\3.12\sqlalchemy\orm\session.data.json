{".class": "MypyFile", "_fullname": "sqlalchemy.orm.session", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ACTIVE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.session.ACTIVE", "name": "ACTIVE", "type": "sqlalchemy.orm.session.SessionTransactionState"}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "CLOSED": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.session.CLOSED", "name": "CLOSED", "type": "sqlalchemy.orm.session.SessionTransactionState"}}, "COMMITTED": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.session.COMMITTED", "name": "COMMITTED", "type": "sqlalchemy.orm.session.SessionTransactionState"}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "ClauseElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ClauseElement", "kind": "Gdef", "module_public": false}, "CompileState": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.CompileState", "kind": "Gdef", "module_public": false}, "Connection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.base.Connection", "kind": "Gdef", "module_public": false}, "CoreExecuteOptionsParameter": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.CoreExecuteOptionsParameter", "kind": "Gdef", "module_public": false}, "CursorResult": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.cursor.CursorResult", "kind": "Gdef", "module_public": false}, "DEACTIVE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.session.DEACTIVE", "name": "DEACTIVE", "type": "sqlalchemy.orm.session.SessionTransactionState"}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "Engine": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.base.Engine", "kind": "Gdef", "module_public": false}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef", "module_public": false}, "EventTarget": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.registry.EventTarget", "kind": "Gdef", "module_public": false}, "Executable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.Executable", "kind": "Gdef", "module_public": false}, "ExecutableOption": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.ExecutableOption", "kind": "Gdef", "module_public": false}, "ForUpdateArg": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.ForUpdateArg", "kind": "Gdef", "module_public": false}, "ForUpdateParameter": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.ForUpdateParameter", "kind": "Gdef", "module_public": false}, "FromStatement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.context.FromStatement", "kind": "Gdef", "module_public": false}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef", "module_public": false}, "IdentityMap": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.identity.IdentityMap", "kind": "Gdef", "module_public": false}, "IdentitySet": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._py_collections.IdentitySet", "kind": "Gdef", "module_public": false}, "Inspectable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.inspection.Inspectable", "kind": "Gdef", "module_public": false}, "InstanceState": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.state.InstanceState", "kind": "Gdef", "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_public": false}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_public": false}, "JoinTransactionMode": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.orm.session.JoinTransactionMode", "line": 165, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "conditional_savepoint"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rollback_only"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "control_fully"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "create_savepoint"}]}}}, "LABEL_STYLE_TABLENAME_PLUS_COL": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.LABEL_STYLE_TABLENAME_PLUS_COL", "kind": "Gdef", "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef", "module_public": false}, "LoaderCallableStatus": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.LoaderCallableStatus", "kind": "Gdef", "module_public": false}, "Mapper": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.mapper.Mapper", "kind": "Gdef", "module_public": false}, "NoReturn": {".class": "SymbolTableNode", "cross_ref": "typing.NoReturn", "kind": "Gdef", "module_public": false}, "ORMCompileState": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.context.ORMCompileState", "kind": "Gdef", "module_public": false}, "ORMExecuteState": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.util.langhelpers.MemoizedSlots"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.session.ORMExecuteState", "name": "ORMExecuteState", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.session.ORMExecuteState", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.session", "mro": ["sqlalchemy.orm.session.ORMExecuteState", "sqlalchemy.util.langhelpers.MemoizedSlots", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "session", "statement", "parameters", "execution_options", "bind_arguments", "compile_state_cls", "events_todo"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.ORMExecuteState.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "session", "statement", "parameters", "execution_options", "bind_arguments", "compile_state_cls", "events_todo"], "arg_types": ["sqlalchemy.orm.session.ORMExecuteState", "sqlalchemy.orm.session.Session", "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreMultiExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._ExecuteOptions"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "sqlalchemy.orm.context.ORMCompileState"}, {".class": "NoneType"}]}, {".class": "Instance", "args": [{".class": "Instance", "args": ["sqlalchemy.orm.session.Session"], "type_ref": "sqlalchemy.event.attr._InstanceLevelDispatch"}], "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ORMExecuteState", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.session.ORMExecuteState.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_compile_state_cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.session.ORMExecuteState._compile_state_cls", "name": "_compile_state_cls", "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": "sqlalchemy.orm.context.ORMCompileState"}, {".class": "NoneType"}]}}}, "_events_todo": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.session.ORMExecuteState._events_todo", "name": "_events_todo", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.list"}}}, "_is_crud": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.session.ORMExecuteState._is_crud", "name": "_is_crud", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.ORMExecuteState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_crud of ORMExecuteState", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.ORMExecuteState._is_crud", "name": "_is_crud", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.ORMExecuteState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_crud of ORMExecuteState", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_non_compile_orm_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.session.ORMExecuteState._non_compile_orm_options", "name": "_non_compile_orm_options", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.ORMExecuteState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_non_compile_orm_options of ORMExecuteState", "ret_type": {".class": "Instance", "args": ["sqlalchemy.orm.interfaces.ORMOption"], "type_ref": "typing.Sequence"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.ORMExecuteState._non_compile_orm_options", "name": "_non_compile_orm_options", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.ORMExecuteState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_non_compile_orm_options of ORMExecuteState", "ret_type": {".class": "Instance", "args": ["sqlalchemy.orm.interfaces.ORMOption"], "type_ref": "typing.Sequence"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_orm_compile_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.ORMExecuteState._orm_compile_options", "name": "_orm_compile_options", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.ORMExecuteState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_orm_compile_options of ORMExecuteState", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.orm.context.ORMCompileState.default_compile_options", {".class": "TypeType", "item": "sqlalchemy.orm.context.ORMCompileState.default_compile_options"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_remaining_events": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.ORMExecuteState._remaining_events", "name": "_remaining_events", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.ORMExecuteState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_remaining_events of ORMExecuteState", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["sqlalchemy.orm.session.Session"], "type_ref": "sqlalchemy.event.attr._InstanceLevelDispatch"}], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_starting_event_idx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.session.ORMExecuteState._starting_event_idx", "name": "_starting_event_idx", "type": "builtins.int"}}, "_update_execution_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.session.ORMExecuteState._update_execution_options", "name": "_update_execution_options", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._ExecuteOptions"}, {".class": "NoneType"}]}}}, "all_mappers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.session.ORMExecuteState.all_mappers", "name": "all_mappers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.ORMExecuteState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "all_mappers of ORMExecuteState", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}], "type_ref": "typing.Sequence"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.ORMExecuteState.all_mappers", "name": "all_mappers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.ORMExecuteState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "all_mappers of ORMExecuteState", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}], "type_ref": "typing.Sequence"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "bind_arguments": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.session.ORMExecuteState.bind_arguments", "name": "bind_arguments", "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}}}, "bind_mapper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.session.ORMExecuteState.bind_mapper", "name": "bind_mapper", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.ORMExecuteState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bind_mapper of ORMExecuteState", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.ORMExecuteState.bind_mapper", "name": "bind_mapper", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.ORMExecuteState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bind_mapper of ORMExecuteState", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "execution_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.session.ORMExecuteState.execution_options", "name": "execution_options", "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._ExecuteOptions"}}}, "invoke_statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.ORMExecuteState.invoke_statement", "name": "invoke_statement", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments"], "arg_types": ["sqlalchemy.orm.session.ORMExecuteState", {".class": "UnionType", "items": ["sqlalchemy.sql.base.Executable", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreMultiExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing._OrmKnownExecutionOptions"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "invoke_statement of ORMExecuteState", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.engine.result.Result"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "is_column_load": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.session.ORMExecuteState.is_column_load", "name": "is_column_load", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.ORMExecuteState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_column_load of ORMExecuteState", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.ORMExecuteState.is_column_load", "name": "is_column_load", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.ORMExecuteState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_column_load of ORMExecuteState", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "is_delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.session.ORMExecuteState.is_delete", "name": "is_delete", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.ORMExecuteState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_delete of ORMExecuteState", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.ORMExecuteState.is_delete", "name": "is_delete", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.ORMExecuteState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_delete of ORMExecuteState", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "is_executemany": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.session.ORMExecuteState.is_executemany", "name": "is_executemany", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.ORMExecuteState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_executemany of ORMExecuteState", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.ORMExecuteState.is_executemany", "name": "is_executemany", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.ORMExecuteState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_executemany of ORMExecuteState", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "is_from_statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.session.ORMExecuteState.is_from_statement", "name": "is_from_statement", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.ORMExecuteState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_from_statement of ORMExecuteState", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.ORMExecuteState.is_from_statement", "name": "is_from_statement", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.ORMExecuteState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_from_statement of ORMExecuteState", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "is_insert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.session.ORMExecuteState.is_insert", "name": "is_insert", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.ORMExecuteState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_insert of ORMExecuteState", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.ORMExecuteState.is_insert", "name": "is_insert", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.ORMExecuteState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_insert of ORMExecuteState", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "is_orm_statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.session.ORMExecuteState.is_orm_statement", "name": "is_orm_statement", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.ORMExecuteState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_orm_statement of ORMExecuteState", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.ORMExecuteState.is_orm_statement", "name": "is_orm_statement", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.ORMExecuteState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_orm_statement of ORMExecuteState", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "is_relationship_load": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.session.ORMExecuteState.is_relationship_load", "name": "is_relationship_load", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.ORMExecuteState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_relationship_load of ORMExecuteState", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.ORMExecuteState.is_relationship_load", "name": "is_relationship_load", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.ORMExecuteState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_relationship_load of ORMExecuteState", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "is_select": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.session.ORMExecuteState.is_select", "name": "is_select", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.ORMExecuteState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_select of ORMExecuteState", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.ORMExecuteState.is_select", "name": "is_select", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.ORMExecuteState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_select of ORMExecuteState", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "is_update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.session.ORMExecuteState.is_update", "name": "is_update", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.ORMExecuteState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_update of ORMExecuteState", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.ORMExecuteState.is_update", "name": "is_update", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.ORMExecuteState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_update of ORMExecuteState", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "lazy_loaded_from": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.session.ORMExecuteState.lazy_loaded_from", "name": "lazy_loaded_from", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.ORMExecuteState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lazy_loaded_from of ORMExecuteState", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.state.InstanceState"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.ORMExecuteState.lazy_loaded_from", "name": "lazy_loaded_from", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.ORMExecuteState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lazy_loaded_from of ORMExecuteState", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.state.InstanceState"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "load_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.session.ORMExecuteState.load_options", "name": "load_options", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.ORMExecuteState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_options of ORMExecuteState", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.orm.context.QueryContext.default_load_options", {".class": "TypeType", "item": "sqlalchemy.orm.context.QueryContext.default_load_options"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.ORMExecuteState.load_options", "name": "load_options", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.ORMExecuteState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_options of ORMExecuteState", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.orm.context.QueryContext.default_load_options", {".class": "TypeType", "item": "sqlalchemy.orm.context.QueryContext.default_load_options"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "loader_strategy_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.session.ORMExecuteState.loader_strategy_path", "name": "loader_strategy_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.ORMExecuteState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "loader_strategy_path of ORMExecuteState", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.orm.path_registry.PathRegistry", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.ORMExecuteState.loader_strategy_path", "name": "loader_strategy_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.ORMExecuteState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "loader_strategy_path of ORMExecuteState", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.orm.path_registry.PathRegistry", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "local_execution_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.session.ORMExecuteState.local_execution_options", "name": "local_execution_options", "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._ExecuteOptions"}}}, "parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.session.ORMExecuteState.parameters", "name": "parameters", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreMultiExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "NoneType"}]}}}, "session": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.session.ORMExecuteState.session", "name": "session", "type": "sqlalchemy.orm.session.Session"}}, "statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.session.ORMExecuteState.statement", "name": "statement", "type": "sqlalchemy.sql.base.Executable"}}, "update_delete_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.session.ORMExecuteState.update_delete_options", "name": "update_delete_options", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.ORMExecuteState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_delete_options of ORMExecuteState", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.orm.bulk_persistence.BulkUDCompileState.default_update_options", {".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState.default_update_options"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.ORMExecuteState.update_delete_options", "name": "update_delete_options", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.ORMExecuteState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_delete_options of ORMExecuteState", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.orm.bulk_persistence.BulkUDCompileState.default_update_options", {".class": "TypeType", "item": "sqlalchemy.orm.bulk_persistence.BulkUDCompileState.default_update_options"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "update_execution_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "opts"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.ORMExecuteState.update_execution_options", "name": "update_execution_options", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "opts"], "arg_types": ["sqlalchemy.orm.session.ORMExecuteState", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_execution_options of ORMExecuteState", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "user_defined_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.session.ORMExecuteState.user_defined_options", "name": "user_defined_options", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.ORMExecuteState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_defined_options of ORMExecuteState", "ret_type": {".class": "Instance", "args": ["sqlalchemy.orm.interfaces.UserDefinedOption"], "type_ref": "typing.Sequence"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.ORMExecuteState.user_defined_options", "name": "user_defined_options", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.ORMExecuteState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_defined_options of ORMExecuteState", "ret_type": {".class": "Instance", "args": ["sqlalchemy.orm.interfaces.UserDefinedOption"], "type_ref": "typing.Sequence"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session.ORMExecuteState.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.session.ORMExecuteState", "values": [], "variance": 0}, "slots": ["_compile_state_cls", "_events_todo", "_starting_event_idx", "_update_execution_options", "bind_arguments", "execution_options", "identity_token", "local_execution_options", "parameters", "session", "statement"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ORMOption": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.interfaces.ORMOption", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "OrmExecuteOptionsParameter": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter", "kind": "Gdef", "module_public": false}, "PREPARED": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.session.PREPARED", "name": "PREPARED", "type": "sqlalchemy.orm.session.SessionTransactionState"}}, "PROVISIONING_CONNECTION": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.session.PROVISIONING_CONNECTION", "name": "PROVISIONING_CONNECTION", "type": "sqlalchemy.orm.session.SessionTransactionState"}}, "PassiveFlag": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.PassiveFlag", "kind": "Gdef", "module_public": false}, "PathRegistry": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.path_registry.PathRegistry", "kind": "Gdef", "module_public": false}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Protocol", "kind": "Gdef", "module_public": false}, "Query": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.query.Query", "kind": "Gdef", "module_public": false}, "Result": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result.Result", "kind": "Gdef", "module_public": false}, "Row": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.row.Row", "kind": "Gdef", "module_public": false}, "RowMapping": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.row.RowMapping", "kind": "Gdef", "module_public": false}, "RowReturningQuery": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.query.RowReturningQuery", "kind": "Gdef", "module_public": false}, "ScalarResult": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result.ScalarResult", "kind": "Gdef", "module_public": false}, "Select": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.Select", "kind": "Gdef", "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "Session": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.session._SessionClassMethods", "sqlalchemy.event.registry.EventTarget"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.session.Session", "name": "Session", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.session.Session", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.session", "mro": ["sqlalchemy.orm.session.Session", "sqlalchemy.orm.session._SessionClassMethods", "sqlalchemy.event.registry.EventTarget", "builtins.object"], "names": {".class": "SymbolTable", "__binds": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.session.Session.__binds", "name": "__binds", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._SessionBind<PERSON>ey"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._SessionBind"}], "type_ref": "builtins.dict"}}}, "__contains__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session.__contains__", "name": "__contains__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.orm.session.Session", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__contains__ of Session", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._S", "id": -1, "name": "_S", "namespace": "", "upper_bound": "sqlalchemy.orm.session.Session", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__enter__ of Session", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._S", "id": -1, "name": "_S", "namespace": "", "upper_bound": "sqlalchemy.orm.session.Session", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._S", "id": -1, "name": "_S", "namespace": "", "upper_bound": "sqlalchemy.orm.session.Session", "values": [], "variance": 0}]}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__exit__ of Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "bind", "autoflush", "future", "expire_on_commit", "autobegin", "twophase", "binds", "enable_baked_queries", "info", "query_cls", "autocommit", "join_transaction_mode", "close_resets_only"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "bind", "autoflush", "future", "expire_on_commit", "autobegin", "twophase", "binds", "enable_baked_queries", "info", "query_cls", "autocommit", "join_transaction_mode", "close_resets_only"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "UnionType", "items": ["sqlalchemy.engine.base.Engine", "sqlalchemy.engine.base.Connection", {".class": "NoneType"}]}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._SessionBind<PERSON>ey"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._SessionBind"}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._InfoType"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.query.Query"}}, {".class": "NoneType"}]}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session.JoinTransactionMode"}, {".class": "UnionType", "items": ["builtins.bool", "sqlalchemy.sql.base._NoArg"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["sqlalchemy.orm.session.Session"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iter__ of Session", "ret_type": {".class": "Instance", "args": ["builtins.object"], "type_ref": "typing.Iterator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_add_bind": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "bind"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session._add_bind", "name": "_add_bind", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "bind"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._SessionBind<PERSON>ey"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._SessionBind"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_bind of Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_after_attach": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "state", "obj"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session._after_attach", "name": "_after_attach", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "state", "obj"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.state.InstanceState"}, "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_after_attach of Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_autobegin_t": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "begin"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session._autobegin_t", "name": "_autobegin_t", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "begin"], "arg_types": ["sqlalchemy.orm.session.Session", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_autobegin_t of Session", "ret_type": "sqlalchemy.orm.session.SessionTransaction", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_autoflush": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.session.Session._autoflush", "name": "_autoflush", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.Session"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_autoflush of Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.Session._autoflush", "name": "_autoflush", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.Session"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_autoflush of Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_before_attach": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "state", "obj"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session._before_attach", "name": "_before_attach", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "state", "obj"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.state.InstanceState"}, "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_before_attach of Session", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_bulk_save_mappings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 3, 3, 3, 3, 3], "arg_names": ["self", "mapper", "mappings", "isupdate", "isstates", "return_defaults", "update_changed_only", "render_nulls"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session._bulk_save_mappings", "name": "_bulk_save_mappings", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 3, 3, 3, 3, 3], "arg_names": ["self", "mapper", "mappings", "isupdate", "isstates", "return_defaults", "update_changed_only", "render_nulls"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.state.InstanceState"}], "type_ref": "typing.Iterable"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "type_ref": "typing.Iterable"}]}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_bulk_save_mappings of Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "_close_impl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "invalidate", "is_reset"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session._close_impl", "name": "_close_impl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "invalidate", "is_reset"], "arg_types": ["sqlalchemy.orm.session.Session", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_close_impl of Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_close_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.session.Session._close_state", "name": "_close_state", "type": "sqlalchemy.orm.session._SessionCloseState"}}, "_conditional_expire": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "state", "autoflush"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session._conditional_expire", "name": "_conditional_expire", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "state", "autoflush"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.state.InstanceState"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_conditional_expire of Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_connection_for_bind": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "engine", "execution_options", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session._connection_for_bind", "name": "_connection_for_bind", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "engine", "execution_options", "kw"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._SessionBind"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreKnownExecutionOptions"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_connection_for_bind of Session", "ret_type": "sqlalchemy.engine.base.Connection", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_contains_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session._contains_state", "name": "_contains_state", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "state"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.state.InstanceState"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_contains_state of Session", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_delete_impl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "state", "obj", "head"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session._delete_impl", "name": "_delete_impl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "state", "obj", "head"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.state.InstanceState"}, "builtins.object", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_delete_impl of Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_deleted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.session.Session._deleted", "name": "_deleted", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.state.InstanceState"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}}}, "_dirty_states": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.session.Session._dirty_states", "name": "_dirty_states", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.Session"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_dirty_states of Session", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.state.InstanceState"}], "type_ref": "typing.Iterable"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.Session._dirty_states", "name": "_dirty_states", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.Session"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_dirty_states of Session", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.state.InstanceState"}], "type_ref": "typing.Iterable"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_execute_internal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "sqlalchemy.orm.session.Session._execute_internal", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 5, 5, 5], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "_parent_execute_state", "_add_event", "_scalar_result"], "dataclass_transform_spec": null, "flags": ["is_overload"], "fullname": "sqlalchemy.orm.session.Session._execute_internal", "name": "_execute_internal", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5, 5, 5], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "_parent_execute_state", "_add_event", "_scalar_result"], "arg_types": ["sqlalchemy.orm.session.Session", "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreMultiExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_execute_internal of Session", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 5, 5, 5], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "_parent_execute_state", "_add_event", "_scalar_result"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.orm.session.Session._execute_internal", "name": "_execute_internal", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5, 5, 5], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "_parent_execute_state", "_add_event", "_scalar_result"], "arg_types": ["sqlalchemy.orm.session.Session", "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_execute_internal of Session", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.Session._execute_internal", "name": "_execute_internal", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5, 5, 5], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "_parent_execute_state", "_add_event", "_scalar_result"], "arg_types": ["sqlalchemy.orm.session.Session", "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_execute_internal of Session", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 5, 5, 5], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "_parent_execute_state", "_add_event", "_scalar_result"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.orm.session.Session._execute_internal", "name": "_execute_internal", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5, 5, 5], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "_parent_execute_state", "_add_event", "_scalar_result"], "arg_types": ["sqlalchemy.orm.session.Session", "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreMultiExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_execute_internal of Session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.engine.result.Result"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.Session._execute_internal", "name": "_execute_internal", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5, 5, 5], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "_parent_execute_state", "_add_event", "_scalar_result"], "arg_types": ["sqlalchemy.orm.session.Session", "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreMultiExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_execute_internal of Session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.engine.result.Result"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5, 5, 5], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "_parent_execute_state", "_add_event", "_scalar_result"], "arg_types": ["sqlalchemy.orm.session.Session", "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_execute_internal of Session", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5, 5, 5], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "_parent_execute_state", "_add_event", "_scalar_result"], "arg_types": ["sqlalchemy.orm.session.Session", "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreMultiExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_execute_internal of Session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.engine.result.Result"}, "type_guard": null, "unpack_kwargs": false, "variables": []}]}}}, "_expire_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "state", "attribute_names"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session._expire_state", "name": "_expire_state", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "state", "attribute_names"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.state.InstanceState"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_expire_state of Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_expunge_states": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "states", "to_transient"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session._expunge_states", "name": "_expunge_states", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "states", "to_transient"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.state.InstanceState"}], "type_ref": "typing.Iterable"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_expunge_states of Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_flush": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "objects"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session._flush", "name": "_flush", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "objects"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.object"], "type_ref": "typing.Sequence"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_flush of Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_flush_warning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "method"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session._flush_warning", "name": "_flush_warning", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "method"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_flush_warning of Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_flushing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.session.Session._flushing", "name": "_flushing", "type": "builtins.bool"}}, "_get_impl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "entity", "primary_key_identity", "db_load_fn", "options", "populate_existing", "with_for_update", "identity_token", "execution_options", "bind_arguments"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session._get_impl", "name": "_get_impl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "entity", "primary_key_identity", "db_load_fn", "options", "populate_existing", "with_for_update", "identity_token", "execution_options", "bind_arguments"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.session._EntityBindKey"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._PKIdentityArgument"}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["sqlalchemy.sql.base.ExecutableOption"], "type_ref": "typing.Sequence"}, {".class": "NoneType"}]}, "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.selectable.ForUpdateParameter"}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_impl of Session", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "_identity_lookup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "mapper", "primary_key_identity", "identity_token", "passive", "lazy_loaded_from", "execution_options", "bind_arguments"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session._identity_lookup", "name": "_identity_lookup", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "mapper", "primary_key_identity", "identity_token", "passive", "lazy_loaded_from", "execution_options", "bind_arguments"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "sqlalchemy.orm.base.PassiveFlag", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.state.InstanceState"}, {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_identity_lookup of Session", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "NoneType"}, "sqlalchemy.orm.base.LoaderCallableStatus"]}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "_is_asyncio": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.session.Session._is_asyncio", "name": "_is_asyncio", "type": "builtins.bool"}}, "_is_clean": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session._is_clean", "name": "_is_clean", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.Session"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_clean of Session", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_maker_context_manager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.session.Session._maker_context_manager", "name": "_maker_context_manager", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._S", "id": -1, "name": "_S", "namespace": "", "upper_bound": "sqlalchemy.orm.session.Session", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_maker_context_manager of Session", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._S", "id": -1, "name": "_S", "namespace": "", "upper_bound": "sqlalchemy.orm.session.Session", "values": [], "variance": 0}], "type_ref": "typing.Iterator"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._S", "id": -1, "name": "_S", "namespace": "", "upper_bound": "sqlalchemy.orm.session.Session", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.Session._maker_context_manager", "name": "_maker_context_manager", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "contextlib._T_co", "id": 14957, "name": "_T_co", "namespace": "", "upper_bound": "sqlalchemy.orm.session.Session", "values": [], "variance": 1}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_maker_context_manager of Session", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "contextlib._T_co", "id": 14957, "name": "_T_co", "namespace": "", "upper_bound": "sqlalchemy.orm.session.Session", "values": [], "variance": 1}], "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "contextlib._T_co", "id": 14957, "name": "_T_co", "namespace": "", "upper_bound": "sqlalchemy.orm.session.Session", "values": [], "variance": 1}]}}}}, "_merge": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 3, 3, 3], "arg_names": ["self", "state", "state_dict", "options", "load", "_recursive", "_resolve_conflict_map"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session._merge", "name": "_merge", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 3, 3, 3], "arg_names": ["self", "state", "state_dict", "options", "load", "_recursive", "_resolve_conflict_map"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.state.InstanceState"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing._InstanceDict"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["sqlalchemy.orm.interfaces.ORMOption"], "type_ref": "typing.Sequence"}, {".class": "NoneType"}]}, "builtins.bool", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.object"], "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._IdentityKeyType"}, "builtins.object"], "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_merge of Session", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "_nested_transaction": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.session.Session._nested_transaction", "name": "_nested_transaction", "type": {".class": "UnionType", "items": ["sqlalchemy.orm.session.SessionTransaction", {".class": "NoneType"}]}}}, "_new": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.session.Session._new", "name": "_new", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.state.InstanceState"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}}}, "_query_cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.session.Session._query_cls", "name": "_query_cls", "type": {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.query.Query"}}}}, "_register_altered": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "states"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session._register_altered", "name": "_register_altered", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "states"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.state.InstanceState"}], "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_register_altered of Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_register_persistent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "states"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session._register_persistent", "name": "_register_persistent", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "states"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.state.InstanceState"}], "type_ref": "builtins.set"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_register_persistent of Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_remove_newly_deleted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "states"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session._remove_newly_deleted", "name": "_remove_newly_deleted", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "states"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.state.InstanceState"}], "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_remove_newly_deleted of Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_save_impl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session._save_impl", "name": "_save_impl", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "state"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.state.InstanceState"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_save_impl of Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_save_or_update_impl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session._save_or_update_impl", "name": "_save_or_update_impl", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "state"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.state.InstanceState"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_save_or_update_impl of Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_save_or_update_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session._save_or_update_state", "name": "_save_or_update_state", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "state"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.state.InstanceState"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_save_or_update_state of Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_trans_context_manager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.orm.session.Session._trans_context_manager", "name": "_trans_context_manager", "type": {".class": "UnionType", "items": ["sqlalchemy.engine.util.TransactionalContext", {".class": "NoneType"}]}}}, "_transaction": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.session.Session._transaction", "name": "_transaction", "type": {".class": "UnionType", "items": ["sqlalchemy.orm.session.SessionTransaction", {".class": "NoneType"}]}}}, "_update_impl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "state", "revert_deletion"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session._update_impl", "name": "_update_impl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "state", "revert_deletion"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.state.InstanceState"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_update_impl of Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_validate_persistent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session._validate_persistent", "name": "_validate_persistent", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "state"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.state.InstanceState"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_validate_persistent of Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_warn_on_events": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.session.Session._warn_on_events", "name": "_warn_on_events", "type": "builtins.bool"}}, "add": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "instance", "_warn"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session.add", "name": "add", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "instance", "_warn"], "arg_types": ["sqlalchemy.orm.session.Session", "builtins.object", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add of Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "add_all": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "instances"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session.add_all", "name": "add_all", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "instances"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "Instance", "args": ["builtins.object"], "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_all of Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "autobegin": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.session.Session.autobegin", "name": "autobegin", "type": "builtins.bool"}}, "autoflush": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.session.Session.autoflush", "name": "autoflush", "type": "builtins.bool"}}, "begin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "nested"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session.begin", "name": "begin", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "nested"], "arg_types": ["sqlalchemy.orm.session.Session", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "begin of Session", "ret_type": "sqlalchemy.orm.session.SessionTransaction", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "begin_nested": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session.begin_nested", "name": "begin_nested", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.Session"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "begin_nested of Session", "ret_type": "sqlalchemy.orm.session.SessionTransaction", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "bind": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.session.Session.bind", "name": "bind", "type": {".class": "UnionType", "items": ["sqlalchemy.engine.base.Engine", "sqlalchemy.engine.base.Connection", {".class": "NoneType"}]}}}, "bind_mapper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "mapper", "bind"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session.bind_mapper", "name": "bind_mapper", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "mapper", "bind"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.session._EntityBindKey"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._SessionBind"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bind_mapper of Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "bind_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "table", "bind"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session.bind_table", "name": "bind_table", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "table", "bind"], "arg_types": ["sqlalchemy.orm.session.Session", "sqlalchemy.sql.selectable.TableClause", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._SessionBind"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bind_table of Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "bulk_insert_mappings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "mapper", "mappings", "return_defaults", "render_nulls"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session.bulk_insert_mappings", "name": "bulk_insert_mappings", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "mapper", "mappings", "return_defaults", "render_nulls"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "type_ref": "typing.Iterable"}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bulk_insert_mappings of Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "bulk_save_objects": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "objects", "return_defaults", "update_changed_only", "preserve_order"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session.bulk_save_objects", "name": "bulk_save_objects", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "objects", "return_defaults", "update_changed_only", "preserve_order"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "Instance", "args": ["builtins.object"], "type_ref": "typing.Iterable"}, "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bulk_save_objects of Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "bulk_update_mappings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "mapper", "mappings"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session.bulk_update_mappings", "name": "bulk_update_mappings", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "mapper", "mappings"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bulk_update_mappings of Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.Session"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "commit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session.commit", "name": "commit", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.Session"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "commit of Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "bind_arguments", "execution_options"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session.connection", "name": "connection", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "bind_arguments", "execution_options"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreKnownExecutionOptions"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connection of Session", "ret_type": "sqlalchemy.engine.base.Connection", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "connection_callable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.orm.session.Session.connection_callable", "name": "connection_callable", "type": {".class": "UnionType", "items": ["sqlalchemy.orm.session._ConnectionCallableProto", {".class": "NoneType"}]}}}, "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "instance"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session.delete", "name": "delete", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "instance"], "arg_types": ["sqlalchemy.orm.session.Session", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete of Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "deleted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.session.Session.deleted", "name": "deleted", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.Session"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "deleted of Session", "ret_type": "sqlalchemy.util._py_collections.IdentitySet", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.Session.deleted", "name": "deleted", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.Session"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "deleted of Session", "ret_type": "sqlalchemy.util._py_collections.IdentitySet", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "dirty": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.session.Session.dirty", "name": "dirty", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.Session"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dirty of Session", "ret_type": "sqlalchemy.util._py_collections.IdentitySet", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.Session.dirty", "name": "dirty", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.Session"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dirty of Session", "ret_type": "sqlalchemy.util._py_collections.IdentitySet", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "dispatch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.session.Session.dispatch", "name": "dispatch", "type": {".class": "Instance", "args": ["sqlalchemy.orm.session.Session"], "type_ref": "sqlalchemy.event.base.dispatcher"}}}, "enable_baked_queries": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.session.Session.enable_baked_queries", "name": "enable_baked_queries", "type": "builtins.bool"}}, "enable_relationship_loading": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session.enable_relationship_loading", "name": "enable_relationship_loading", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["sqlalchemy.orm.session.Session", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "enable_relationship_loading of Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "sqlalchemy.orm.session.Session.execute", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 5, 5], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "_parent_execute_state", "_add_event"], "dataclass_transform_spec": null, "flags": ["is_overload"], "fullname": "sqlalchemy.orm.session.Session.execute", "name": "execute", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5, 5], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "_parent_execute_state", "_add_event"], "arg_types": ["sqlalchemy.orm.session.Session", "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreMultiExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute of Session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.engine.result.Result"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 5, 5], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "_parent_execute_state", "_add_event"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.orm.session.Session.execute", "name": "execute", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5, 5], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "_parent_execute_state", "_add_event"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql.selectable.TypedReturnsRows"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreMultiExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute of Session", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.engine.result.Result"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.Session.execute", "name": "execute", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5, 5], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "_parent_execute_state", "_add_event"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql.selectable.TypedReturnsRows"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreMultiExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute of Session", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.engine.result.Result"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 5, 5], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "_parent_execute_state", "_add_event"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.orm.session.Session.execute", "name": "execute", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5, 5], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "_parent_execute_state", "_add_event"], "arg_types": ["sqlalchemy.orm.session.Session", "sqlalchemy.sql.dml.UpdateBase", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreMultiExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute of Session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.engine.cursor.CursorResult"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.Session.execute", "name": "execute", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5, 5], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "_parent_execute_state", "_add_event"], "arg_types": ["sqlalchemy.orm.session.Session", "sqlalchemy.sql.dml.UpdateBase", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreMultiExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute of Session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.engine.cursor.CursorResult"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 5, 5], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "_parent_execute_state", "_add_event"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.orm.session.Session.execute", "name": "execute", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5, 5], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "_parent_execute_state", "_add_event"], "arg_types": ["sqlalchemy.orm.session.Session", "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreMultiExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute of Session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.engine.result.Result"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.Session.execute", "name": "execute", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5, 5], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "_parent_execute_state", "_add_event"], "arg_types": ["sqlalchemy.orm.session.Session", "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreMultiExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute of Session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.engine.result.Result"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5, 5], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "_parent_execute_state", "_add_event"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql.selectable.TypedReturnsRows"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreMultiExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute of Session", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.engine.result.Result"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5, 5], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "_parent_execute_state", "_add_event"], "arg_types": ["sqlalchemy.orm.session.Session", "sqlalchemy.sql.dml.UpdateBase", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreMultiExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute of Session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.engine.cursor.CursorResult"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5, 5], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "_parent_execute_state", "_add_event"], "arg_types": ["sqlalchemy.orm.session.Session", "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreMultiExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute of Session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.engine.result.Result"}, "type_guard": null, "unpack_kwargs": false, "variables": []}]}}}, "expire": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "instance", "attribute_names"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session.expire", "name": "expire", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "instance", "attribute_names"], "arg_types": ["sqlalchemy.orm.session.Session", "builtins.object", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "expire of Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "expire_all": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session.expire_all", "name": "expire_all", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.Session"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "expire_all of Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "expire_on_commit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.session.Session.expire_on_commit", "name": "expire_on_commit", "type": "builtins.bool"}}, "expunge": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "instance"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session.expunge", "name": "expunge", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "instance"], "arg_types": ["sqlalchemy.orm.session.Session", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "expunge of Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "expunge_all": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session.expunge_all", "name": "expunge_all", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.Session"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "expunge_all of Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "flush": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "objects"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session.flush", "name": "flush", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "objects"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Sequence"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "flush of Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "entity", "ident", "options", "populate_existing", "with_for_update", "identity_token", "execution_options", "bind_arguments"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "entity", "ident", "options", "populate_existing", "with_for_update", "identity_token", "execution_options", "bind_arguments"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.session._EntityBindKey"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._PKIdentityArgument"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["sqlalchemy.orm.interfaces.ORMOption"], "type_ref": "typing.Sequence"}, {".class": "NoneType"}]}, "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.selectable.ForUpdateParameter"}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of Session", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "get_bind": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5, 4], "arg_names": ["self", "mapper", "clause", "bind", "_sa_skip_events", "_sa_skip_for_implicit_returning", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session.get_bind", "name": "get_bind", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 4], "arg_names": ["self", "mapper", "clause", "bind", "_sa_skip_events", "_sa_skip_for_implicit_returning", "kw"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.ClauseElement", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["sqlalchemy.engine.base.Engine", "sqlalchemy.engine.base.Connection", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_bind of Session", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.engine.base.Engine", "sqlalchemy.engine.base.Connection"]}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "get_nested_transaction": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session.get_nested_transaction", "name": "get_nested_transaction", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.Session"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_nested_transaction of Session", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.orm.session.SessionTransaction", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_one": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "entity", "ident", "options", "populate_existing", "with_for_update", "identity_token", "execution_options", "bind_arguments"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session.get_one", "name": "get_one", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "entity", "ident", "options", "populate_existing", "with_for_update", "identity_token", "execution_options", "bind_arguments"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.session._EntityBindKey"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._PKIdentityArgument"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["sqlalchemy.orm.interfaces.ORMOption"], "type_ref": "typing.Sequence"}, {".class": "NoneType"}]}, "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.selectable.ForUpdateParameter"}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_one of Session", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "get_transaction": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session.get_transaction", "name": "get_transaction", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.Session"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_transaction of Session", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.orm.session.SessionTransaction", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "hash_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.session.Session.hash_key", "name": "hash_key", "type": "builtins.int"}}, "identity_map": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.session.Session.identity_map", "name": "identity_map", "type": "sqlalchemy.orm.identity.IdentityMap"}}, "in_nested_transaction": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session.in_nested_transaction", "name": "in_nested_transaction", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.Session"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "in_nested_transaction of Session", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "in_transaction": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session.in_transaction", "name": "in_transaction", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.Session"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "in_transaction of Session", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.session.Session.info", "name": "info", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.Session"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "info of Session", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._InfoType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.Session.info", "name": "info", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}], "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "invalidate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session.invalidate", "name": "invalidate", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.Session"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "invalidate of Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "is_active": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.session.Session.is_active", "name": "is_active", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.Session"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_active of Session", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.Session.is_active", "name": "is_active", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.Session"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_active of Session", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "is_modified": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "instance", "include_collections"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session.is_modified", "name": "is_modified", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "instance", "include_collections"], "arg_types": ["sqlalchemy.orm.session.Session", "builtins.object", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_modified of Session", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "join_transaction_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.session.Session.join_transaction_mode", "name": "join_transaction_mode", "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session.JoinTransactionMode"}}}, "merge": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "instance", "load", "options"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session.merge", "name": "merge", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "instance", "load", "options"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["sqlalchemy.orm.interfaces.ORMOption"], "type_ref": "typing.Sequence"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "merge of Session", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "new": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.session.Session.new", "name": "new", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.Session"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "new of Session", "ret_type": "sqlalchemy.util._py_collections.IdentitySet", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.Session.new", "name": "new", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.Session"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "new of Session", "ret_type": "sqlalchemy.util._py_collections.IdentitySet", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "no_autoflush": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_generator", "is_decorated"], "fullname": "sqlalchemy.orm.session.Session.no_autoflush", "name": "no_autoflush", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.Session"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "no_autoflush of Session", "ret_type": {".class": "Instance", "args": ["sqlalchemy.orm.session.Session"], "type_ref": "typing.Iterator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.Session.no_autoflush", "name": "no_autoflush", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["sqlalchemy.orm.session.Session"], "type_ref": "contextlib._GeneratorContextManager"}], "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "prepare": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session.prepare", "name": "prepare", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.Session"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prepare of Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "sqlalchemy.orm.session.Session.query", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "entities", "kwargs"], "dataclass_transform_spec": null, "flags": ["is_overload"], "fullname": "sqlalchemy.orm.session.Session.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "entities", "kwargs"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql._typing._ColumnsClauseArgument"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of Session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.query.Query"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "_entity"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.orm.session.Session.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "_entity"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm._typing._EntityType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of Session", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.query.Query"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.Session.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "_entity"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm._typing._EntityType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of Session", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.query.Query"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "_colexpr"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.orm.session.Session.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "_colexpr"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql.roles.TypedColumnsClauseRole"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of Session", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "sqlalchemy.orm.query.RowReturningQuery"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.Session.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "_colexpr"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql.roles.TypedColumnsClauseRole"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of Session", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "sqlalchemy.orm.query.RowReturningQuery"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", null, null], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.orm.session.Session.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", null, null], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of Session", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "sqlalchemy.orm.query.RowReturningQuery"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.Session.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", null, null], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of Session", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "sqlalchemy.orm.query.RowReturningQuery"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", null, null, null], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.orm.session.Session.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", null, null, null], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of Session", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "sqlalchemy.orm.query.RowReturningQuery"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.Session.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", null, null, null], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of Session", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "sqlalchemy.orm.query.RowReturningQuery"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", null, null, null, null], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.orm.session.Session.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", null, null, null, null], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of Session", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "sqlalchemy.orm.query.RowReturningQuery"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.Session.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", null, null, null, null], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of Session", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "sqlalchemy.orm.query.RowReturningQuery"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", null, null, null, null, null], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.orm.session.Session.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", null, null, null, null, null], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T4", "id": -5, "name": "_T4", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of Session", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T4", "id": -5, "name": "_T4", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "sqlalchemy.orm.query.RowReturningQuery"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T4", "id": -5, "name": "_T4", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.Session.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", null, null, null, null, null], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T4", "id": -5, "name": "_T4", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of Session", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T4", "id": -5, "name": "_T4", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "sqlalchemy.orm.query.RowReturningQuery"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T4", "id": -5, "name": "_T4", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", null, null, null, null, null, null], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.orm.session.Session.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", null, null, null, null, null, null], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T4", "id": -5, "name": "_T4", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T5", "id": -6, "name": "_T5", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of Session", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T4", "id": -5, "name": "_T4", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T5", "id": -6, "name": "_T5", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "sqlalchemy.orm.query.RowReturningQuery"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T4", "id": -5, "name": "_T4", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T5", "id": -6, "name": "_T5", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.Session.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", null, null, null, null, null, null], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T4", "id": -5, "name": "_T4", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T5", "id": -6, "name": "_T5", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of Session", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T4", "id": -5, "name": "_T4", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T5", "id": -6, "name": "_T5", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "sqlalchemy.orm.query.RowReturningQuery"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T4", "id": -5, "name": "_T4", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T5", "id": -6, "name": "_T5", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", null, null, null, null, null, null, null], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.orm.session.Session.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", null, null, null, null, null, null, null], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T4", "id": -5, "name": "_T4", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T5", "id": -6, "name": "_T5", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T6", "id": -7, "name": "_T6", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of Session", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T4", "id": -5, "name": "_T4", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T5", "id": -6, "name": "_T5", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T6", "id": -7, "name": "_T6", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "sqlalchemy.orm.query.RowReturningQuery"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T4", "id": -5, "name": "_T4", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T5", "id": -6, "name": "_T5", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T6", "id": -7, "name": "_T6", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.Session.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", null, null, null, null, null, null, null], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T4", "id": -5, "name": "_T4", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T5", "id": -6, "name": "_T5", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T6", "id": -7, "name": "_T6", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of Session", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T4", "id": -5, "name": "_T4", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T5", "id": -6, "name": "_T5", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T6", "id": -7, "name": "_T6", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "sqlalchemy.orm.query.RowReturningQuery"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T4", "id": -5, "name": "_T4", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T5", "id": -6, "name": "_T5", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T6", "id": -7, "name": "_T6", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", null, null, null, null, null, null, null, null], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.orm.session.Session.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", null, null, null, null, null, null, null, null], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T4", "id": -5, "name": "_T4", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T5", "id": -6, "name": "_T5", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T6", "id": -7, "name": "_T6", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T7", "id": -8, "name": "_T7", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of Session", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T4", "id": -5, "name": "_T4", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T5", "id": -6, "name": "_T5", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T6", "id": -7, "name": "_T6", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T7", "id": -8, "name": "_T7", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "sqlalchemy.orm.query.RowReturningQuery"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T4", "id": -5, "name": "_T4", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T5", "id": -6, "name": "_T5", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T6", "id": -7, "name": "_T6", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T7", "id": -8, "name": "_T7", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.Session.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", null, null, null, null, null, null, null, null], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T4", "id": -5, "name": "_T4", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T5", "id": -6, "name": "_T5", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T6", "id": -7, "name": "_T6", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T7", "id": -8, "name": "_T7", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of Session", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T4", "id": -5, "name": "_T4", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T5", "id": -6, "name": "_T5", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T6", "id": -7, "name": "_T6", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T7", "id": -8, "name": "_T7", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "sqlalchemy.orm.query.RowReturningQuery"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T4", "id": -5, "name": "_T4", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T5", "id": -6, "name": "_T5", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T6", "id": -7, "name": "_T6", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T7", "id": -8, "name": "_T7", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "entities", "kwargs"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.orm.session.Session.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "entities", "kwargs"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql._typing._ColumnsClauseArgument"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of Session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.query.Query"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.Session.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "entities", "kwargs"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql._typing._ColumnsClauseArgument"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of Session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.query.Query"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "_entity"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm._typing._EntityType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of Session", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.query.Query"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "_colexpr"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql.roles.TypedColumnsClauseRole"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of Session", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "sqlalchemy.orm.query.RowReturningQuery"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", null, null], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of Session", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "sqlalchemy.orm.query.RowReturningQuery"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", null, null, null], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of Session", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "sqlalchemy.orm.query.RowReturningQuery"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", null, null, null, null], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of Session", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "sqlalchemy.orm.query.RowReturningQuery"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", null, null, null, null, null], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T4", "id": -5, "name": "_T4", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of Session", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T4", "id": -5, "name": "_T4", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "sqlalchemy.orm.query.RowReturningQuery"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T4", "id": -5, "name": "_T4", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", null, null, null, null, null, null], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T4", "id": -5, "name": "_T4", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T5", "id": -6, "name": "_T5", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of Session", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T4", "id": -5, "name": "_T4", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T5", "id": -6, "name": "_T5", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "sqlalchemy.orm.query.RowReturningQuery"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T4", "id": -5, "name": "_T4", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T5", "id": -6, "name": "_T5", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", null, null, null, null, null, null, null], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T4", "id": -5, "name": "_T4", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T5", "id": -6, "name": "_T5", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T6", "id": -7, "name": "_T6", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of Session", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T4", "id": -5, "name": "_T4", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T5", "id": -6, "name": "_T5", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T6", "id": -7, "name": "_T6", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "sqlalchemy.orm.query.RowReturningQuery"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T4", "id": -5, "name": "_T4", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T5", "id": -6, "name": "_T5", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T6", "id": -7, "name": "_T6", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", null, null, null, null, null, null, null, null], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T4", "id": -5, "name": "_T4", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T5", "id": -6, "name": "_T5", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T6", "id": -7, "name": "_T6", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T7", "id": -8, "name": "_T7", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of Session", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T4", "id": -5, "name": "_T4", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T5", "id": -6, "name": "_T5", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T6", "id": -7, "name": "_T6", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T7", "id": -8, "name": "_T7", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "sqlalchemy.orm.query.RowReturningQuery"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "id": -1, "name": "_T0", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "id": -2, "name": "_T1", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "id": -3, "name": "_T2", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "id": -4, "name": "_T3", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T4", "id": -5, "name": "_T4", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T5", "id": -6, "name": "_T5", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T6", "id": -7, "name": "_T6", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T7", "id": -8, "name": "_T7", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "entities", "kwargs"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql._typing._ColumnsClauseArgument"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of Session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.query.Query"}, "type_guard": null, "unpack_kwargs": false, "variables": []}]}}}, "refresh": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "instance", "attribute_names", "with_for_update"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session.refresh", "name": "refresh", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "instance", "attribute_names", "with_for_update"], "arg_types": ["sqlalchemy.orm.session.Session", "builtins.object", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}, {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.selectable.ForUpdateParameter"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "refresh of Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session.reset", "name": "reset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.Session"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset of Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "rollback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.Session.rollback", "name": "rollback", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.Session"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rollback of Session", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "scalar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "sqlalchemy.orm.session.Session.scalar", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "dataclass_transform_spec": null, "flags": ["is_overload"], "fullname": "sqlalchemy.orm.session.Session.scalar", "name": "scalar", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "arg_types": ["sqlalchemy.orm.session.Session", "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scalar of Session", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.orm.session.Session.scalar", "name": "scalar", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "sqlalchemy.sql.selectable.TypedReturnsRows"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scalar of Session", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.Session.scalar", "name": "scalar", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "sqlalchemy.sql.selectable.TypedReturnsRows"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scalar of Session", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.orm.session.Session.scalar", "name": "scalar", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "arg_types": ["sqlalchemy.orm.session.Session", "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scalar of Session", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.Session.scalar", "name": "scalar", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "arg_types": ["sqlalchemy.orm.session.Session", "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scalar of Session", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "sqlalchemy.sql.selectable.TypedReturnsRows"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scalar of Session", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "arg_types": ["sqlalchemy.orm.session.Session", "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scalar of Session", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}]}}}, "scalars": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "sqlalchemy.orm.session.Session.scalars", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "dataclass_transform_spec": null, "flags": ["is_overload"], "fullname": "sqlalchemy.orm.session.Session.scalars", "name": "scalars", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "arg_types": ["sqlalchemy.orm.session.Session", "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreMultiExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scalars of Session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.engine.result.ScalarResult"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.orm.session.Session.scalars", "name": "scalars", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "sqlalchemy.sql.selectable.TypedReturnsRows"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreMultiExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scalars of Session", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.engine.result.ScalarResult"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.Session.scalars", "name": "scalars", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "sqlalchemy.sql.selectable.TypedReturnsRows"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreMultiExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scalars of Session", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.engine.result.ScalarResult"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.orm.session.Session.scalars", "name": "scalars", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "arg_types": ["sqlalchemy.orm.session.Session", "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreMultiExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scalars of Session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.engine.result.ScalarResult"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.Session.scalars", "name": "scalars", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "arg_types": ["sqlalchemy.orm.session.Session", "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreMultiExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scalars of Session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.engine.result.ScalarResult"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "sqlalchemy.sql.selectable.TypedReturnsRows"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreMultiExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scalars of Session", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.engine.result.ScalarResult"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "arg_types": ["sqlalchemy.orm.session.Session", "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreMultiExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scalars of Session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.engine.result.ScalarResult"}, "type_guard": null, "unpack_kwargs": false, "variables": []}]}}}, "twophase": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.session.Session.twophase", "name": "twophase", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session.Session.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.session.Session", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SessionTransaction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.state_changes._StateChange", "sqlalchemy.engine.util.TransactionalContext"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.session.SessionTransaction", "name": "SessionTransaction", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.session.SessionTransaction", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.session", "mro": ["sqlalchemy.orm.session.SessionTransaction", "sqlalchemy.orm.state_changes._StateChange", "sqlalchemy.engine.util.TransactionalContext", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "session", "origin", "parent"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.SessionTransaction.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "session", "origin", "parent"], "arg_types": ["sqlalchemy.orm.session.SessionTransaction", "sqlalchemy.orm.session.Session", "sqlalchemy.orm.session.SessionTransactionOrigin", {".class": "UnionType", "items": ["sqlalchemy.orm.session.SessionTransaction", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SessionTransaction", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_begin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "nested"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.session.SessionTransaction._begin", "name": "_begin", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "nested"], "arg_types": ["sqlalchemy.orm.session.SessionTransaction", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_begin of SessionTransaction", "ret_type": "sqlalchemy.orm.session.SessionTransaction", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.SessionTransaction._begin", "name": "_begin", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "nested"], "arg_types": ["sqlalchemy.orm.session.SessionTransaction", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_begin of SessionTransaction", "ret_type": "sqlalchemy.orm.session.SessionTransaction", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_connection_for_bind": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "bind", "execution_options"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.session.SessionTransaction._connection_for_bind", "name": "_connection_for_bind", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "bind", "execution_options"], "arg_types": ["sqlalchemy.orm.session.SessionTransaction", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._SessionBind"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreKnownExecutionOptions"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_connection_for_bind of SessionTransaction", "ret_type": "sqlalchemy.engine.base.Connection", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.SessionTransaction._connection_for_bind", "name": "_connection_for_bind", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "bind", "execution_options"], "arg_types": ["sqlalchemy.orm.session.SessionTransaction", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._SessionBind"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreKnownExecutionOptions"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_connection_for_bind of SessionTransaction", "ret_type": "sqlalchemy.engine.base.Connection", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_connections": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.session.SessionTransaction._connections", "name": "_connections", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["sqlalchemy.engine.base.Engine", "sqlalchemy.engine.base.Connection"]}, {".class": "TupleType", "implicit": false, "items": ["sqlalchemy.engine.base.Connection", "sqlalchemy.engine.base.Transaction", "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.dict"}}}, "_deleted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.session.SessionTransaction._deleted", "name": "_deleted", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.state.InstanceState"}, "builtins.object"], "type_ref": "weakref.<PERSON>eyDictionary"}}}, "_dirty": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.session.SessionTransaction._dirty", "name": "_dirty", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.state.InstanceState"}, "builtins.object"], "type_ref": "weakref.<PERSON>eyDictionary"}}}, "_get_subject": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.SessionTransaction._get_subject", "name": "_get_subject", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.SessionTransaction"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_subject of SessionTransaction", "ret_type": "sqlalchemy.orm.session.Session", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_is_transaction_boundary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.session.SessionTransaction._is_transaction_boundary", "name": "_is_transaction_boundary", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.SessionTransaction"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_transaction_boundary of SessionTransaction", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.SessionTransaction._is_transaction_boundary", "name": "_is_transaction_boundary", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.SessionTransaction"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_transaction_boundary of SessionTransaction", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_iterate_self_and_parents": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "upto"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.SessionTransaction._iterate_self_and_parents", "name": "_iterate_self_and_parents", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "upto"], "arg_types": ["sqlalchemy.orm.session.SessionTransaction", {".class": "UnionType", "items": ["sqlalchemy.orm.session.SessionTransaction", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_iterate_self_and_parents of SessionTransaction", "ret_type": {".class": "Instance", "args": ["sqlalchemy.orm.session.SessionTransaction"], "type_ref": "typing.Iterable"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_key_switches": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.session.SessionTransaction._key_switches", "name": "_key_switches", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.state.InstanceState"}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "weakref.<PERSON>eyDictionary"}}}, "_new": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.session.SessionTransaction._new", "name": "_new", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.state.InstanceState"}, "builtins.object"], "type_ref": "weakref.<PERSON>eyDictionary"}}}, "_parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.session.SessionTransaction._parent", "name": "_parent", "type": {".class": "UnionType", "items": ["sqlalchemy.orm.session.SessionTransaction", {".class": "NoneType"}]}}}, "_prepare_impl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.session.SessionTransaction._prepare_impl", "name": "_prepare_impl", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.SessionTransaction"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prepare_impl of SessionTransaction", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.SessionTransaction._prepare_impl", "name": "_prepare_impl", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.SessionTransaction"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prepare_impl of SessionTransaction", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_previous_nested_transaction": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.session.SessionTransaction._previous_nested_transaction", "name": "_previous_nested_transaction", "type": {".class": "UnionType", "items": ["sqlalchemy.orm.session.SessionTransaction", {".class": "NoneType"}]}}}, "_raise_for_prerequisite_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "operation_name", "state"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.SessionTransaction._raise_for_prerequisite_state", "name": "_raise_for_prerequisite_state", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "operation_name", "state"], "arg_types": ["sqlalchemy.orm.session.SessionTransaction", "builtins.str", "sqlalchemy.orm.state_changes._StateChangeState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_raise_for_prerequisite_state of SessionTransaction", "ret_type": {".class": "UninhabitedType", "is_noreturn": true}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_remove_snapshot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.SessionTransaction._remove_snapshot", "name": "_remove_snapshot", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.SessionTransaction"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_remove_snapshot of SessionTransaction", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_restore_snapshot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "dirty_only"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.SessionTransaction._restore_snapshot", "name": "_restore_snapshot", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "dirty_only"], "arg_types": ["sqlalchemy.orm.session.SessionTransaction", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_restore_snapshot of SessionTransaction", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_rollback_can_be_called": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.SessionTransaction._rollback_can_be_called", "name": "_rollback_can_be_called", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.SessionTransaction"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_rollback_can_be_called of SessionTransaction", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_rollback_exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.orm.session.SessionTransaction._rollback_exception", "name": "_rollback_exception", "type": {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}]}}}, "_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.session.SessionTransaction._state", "name": "_state", "type": "sqlalchemy.orm.session.SessionTransactionState"}}, "_take_snapshot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.SessionTransaction._take_snapshot", "name": "_take_snapshot", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.SessionTransaction"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_take_snapshot of SessionTransaction", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_transaction_is_active": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.SessionTransaction._transaction_is_active", "name": "_transaction_is_active", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.SessionTransaction"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_transaction_is_active of SessionTransaction", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_transaction_is_closed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.SessionTransaction._transaction_is_closed", "name": "_transaction_is_closed", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.SessionTransaction"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_transaction_is_closed of SessionTransaction", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "invalidate"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.session.SessionTransaction.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "invalidate"], "arg_types": ["sqlalchemy.orm.session.SessionTransaction", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of SessionTransaction", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.SessionTransaction.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "invalidate"], "arg_types": ["sqlalchemy.orm.session.SessionTransaction", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of SessionTransaction", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "commit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "_to_root"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.session.SessionTransaction.commit", "name": "commit", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "_to_root"], "arg_types": ["sqlalchemy.orm.session.SessionTransaction", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "commit of SessionTransaction", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.SessionTransaction.commit", "name": "commit", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "_to_root"], "arg_types": ["sqlalchemy.orm.session.SessionTransaction", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "commit of SessionTransaction", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "bindkey", "execution_options", "kwargs"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.session.SessionTransaction.connection", "name": "connection", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "bindkey", "execution_options", "kwargs"], "arg_types": ["sqlalchemy.orm.session.SessionTransaction", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._ExecuteOptions"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connection of SessionTransaction", "ret_type": "sqlalchemy.engine.base.Connection", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.SessionTransaction.connection", "name": "connection", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "bindkey", "execution_options", "kwargs"], "arg_types": ["sqlalchemy.orm.session.SessionTransaction", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._ExecuteOptions"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connection of SessionTransaction", "ret_type": "sqlalchemy.engine.base.Connection", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "is_active": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.session.SessionTransaction.is_active", "name": "is_active", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.SessionTransaction"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_active of SessionTransaction", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.SessionTransaction.is_active", "name": "is_active", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.SessionTransaction"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_active of SessionTransaction", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "nested": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.orm.session.SessionTransaction.nested", "name": "nested", "type": "builtins.bool"}}, "origin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.session.SessionTransaction.origin", "name": "origin", "type": "sqlalchemy.orm.session.SessionTransactionOrigin"}}, "parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.session.SessionTransaction.parent", "name": "parent", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.SessionTransaction"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parent of SessionTransaction", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.orm.session.SessionTransaction", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.SessionTransaction.parent", "name": "parent", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.SessionTransaction"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parent of SessionTransaction", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.orm.session.SessionTransaction", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "prepare": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.SessionTransaction.prepare", "name": "prepare", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.session.SessionTransaction"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prepare of SessionTransaction", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "rollback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "_capture_exception", "_to_root"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.session.SessionTransaction.rollback", "name": "rollback", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "_capture_exception", "_to_root"], "arg_types": ["sqlalchemy.orm.session.SessionTransaction", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rollback of SessionTransaction", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.SessionTransaction.rollback", "name": "rollback", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "_capture_exception", "_to_root"], "arg_types": ["sqlalchemy.orm.session.SessionTransaction", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rollback of SessionTransaction", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "session": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.session.SessionTransaction.session", "name": "session", "type": "sqlalchemy.orm.session.Session"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session.SessionTransaction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.session.SessionTransaction", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SessionTransactionOrigin": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.session.SessionTransactionOrigin", "name": "SessionTransactionOrigin", "type_vars": []}, "deletable_attributes": [], "flags": ["is_enum"], "fullname": "sqlalchemy.orm.session.SessionTransactionOrigin", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "sqlalchemy.orm.session", "mro": ["sqlalchemy.orm.session.SessionTransactionOrigin", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "AUTOBEGIN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.session.SessionTransactionOrigin.AUTOBEGIN", "name": "AUTOBEGIN", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "BEGIN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.session.SessionTransactionOrigin.BEGIN", "name": "BEGIN", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "BEGIN_NESTED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.session.SessionTransactionOrigin.BEGIN_NESTED", "name": "BEGIN_NESTED", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "SUBTRANSACTION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.session.SessionTransactionOrigin.SUBTRANSACTION", "name": "SUBTRANSACTION", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session.SessionTransactionOrigin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.session.SessionTransactionOrigin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SessionTransactionState": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.state_changes._StateChangeState"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.session.SessionTransactionState", "name": "SessionTransactionState", "type_vars": []}, "deletable_attributes": [], "flags": ["is_enum"], "fullname": "sqlalchemy.orm.session.SessionTransactionState", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "sqlalchemy.orm.session", "mro": ["sqlalchemy.orm.session.SessionTransactionState", "sqlalchemy.orm.state_changes._StateChangeState", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "ACTIVE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.session.SessionTransactionState.ACTIVE", "name": "ACTIVE", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "CLOSED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.session.SessionTransactionState.CLOSED", "name": "CLOSED", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 5}, "type_ref": "builtins.int"}}}, "COMMITTED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.session.SessionTransactionState.COMMITTED", "name": "COMMITTED", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_ref": "builtins.int"}}}, "DEACTIVE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.session.SessionTransactionState.DEACTIVE", "name": "DEACTIVE", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "PREPARED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.session.SessionTransactionState.PREPARED", "name": "PREPARED", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "PROVISIONING_CONNECTION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.session.SessionTransactionState.PROVISIONING_CONNECTION", "name": "PROVISIONING_CONNECTION", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 6}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session.SessionTransactionState.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.session.SessionTransactionState", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "Table": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Table", "kind": "Gdef", "module_public": false}, "TableClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.TableClause", "kind": "Gdef", "module_public": false}, "Transaction": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.base.Transaction", "kind": "Gdef", "module_public": false}, "TransactionalContext": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.util.TransactionalContext", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "TwoPhaseTransaction": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.base.TwoPhaseTransaction", "kind": "Gdef", "module_public": false}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef", "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_public": false}, "TypedColumnsClauseRole": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.roles.TypedColumnsClauseRole", "kind": "Gdef", "module_public": false}, "TypedReturnsRows": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.TypedReturnsRows", "kind": "Gdef", "module_public": false}, "UOWTransaction": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.unitofwork.UOWTransaction", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "UpdateBase": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.dml.UpdateBase", "kind": "Gdef", "module_public": false}, "UserDefinedOption": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.interfaces.UserDefinedOption", "kind": "Gdef", "module_public": false}, "_BindArguments": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.orm.session._BindArguments", "line": 159, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "_ColumnsClauseArgument": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._ColumnsClauseArgument", "kind": "Gdef", "module_public": false}, "_ConnectionCallableProto": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.session._ConnectionCallableProto", "name": "_ConnectionCallableProto", "type_vars": []}, "deletable_attributes": [], "flags": ["is_abstract", "is_protocol"], "fullname": "sqlalchemy.orm.session._ConnectionCallableProto", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.orm.session", "mro": ["sqlalchemy.orm.session._ConnectionCallableProto", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "mapper", "instance", "kw"], "dataclass_transform_spec": null, "flags": ["is_trivial_body"], "fullname": "sqlalchemy.orm.session._ConnectionCallableProto.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "mapper", "instance", "kw"], "arg_types": ["sqlalchemy.orm.session._ConnectionCallableProto", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _ConnectionCallableProto", "ret_type": "sqlalchemy.engine.base.Connection", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._ConnectionCallableProto.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.session._ConnectionCallableProto", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_CoreAnyExecuteParams": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams", "kind": "Gdef", "module_public": false}, "_CoreSingleExecuteParams": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams", "kind": "Gdef", "module_public": false}, "_EntityBindKey": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.session._EntityBindKey", "upper_bound": "builtins.object", "values": [], "variance": 0}], "column": 0, "fullname": "sqlalchemy.orm.session._EntityBindKey", "line": 161, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.session._EntityBindKey", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.session._EntityBindKey", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}]}}}, "_EntityType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing._EntityType", "kind": "Gdef", "module_public": false}, "_ExecuteOptions": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces._ExecuteOptions", "kind": "Gdef", "module_public": false}, "_IdentityKeyType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing._IdentityKeyType", "kind": "Gdef", "module_public": false}, "_InfoType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._InfoType", "kind": "Gdef", "module_public": false}, "_InstanceDict": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing._InstanceDict", "kind": "Gdef", "module_public": false}, "_InstanceLevelDispatch": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.attr._InstanceLevelDispatch", "kind": "Gdef", "module_public": false}, "_NoArg": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base._NoArg", "kind": "Gdef", "module_public": false}, "_O": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing._O", "kind": "Gdef", "module_public": false}, "_PKIdentityArgument": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.orm.session._PKIdentityArgument", "line": 157, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}]}}}, "_S": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._S", "name": "_S", "upper_bound": "sqlalchemy.orm.session.Session", "values": [], "variance": 0}}, "_SessionBind": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.orm.session._SessionBind", "line": 163, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": ["sqlalchemy.engine.base.Engine", "sqlalchemy.engine.base.Connection"]}}}, "_SessionBindKey": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.orm.session._SessionBind<PERSON>ey", "line": 162, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, "sqlalchemy.sql.selectable.TableClause", "builtins.str"]}}}, "_SessionClassMethods": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.session._SessionClassMethods", "name": "_SessionClassMethods", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.session._SessionClassMethods", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.session", "mro": ["sqlalchemy.orm.session._SessionClassMethods", "builtins.object"], "names": {".class": "SymbolTable", "close_all": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.session._SessionClassMethods.close_all", "name": "close_all", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.session._SessionClassMethods"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close_all of _SessionClassMethods", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session._SessionClassMethods.close_all", "name": "close_all", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.session._SessionClassMethods"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close_all of _SessionClassMethods", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "identity_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 5, 5, 5], "arg_names": ["cls", "class_", "ident", "instance", "row", "identity_token"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.session._SessionClassMethods.identity_key", "name": "identity_key", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5, 5, 5], "arg_names": ["cls", "class_", "ident", "instance", "row", "identity_token"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.session._SessionClassMethods"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.engine.row.Row"}, "sqlalchemy.engine.row.RowMapping", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "identity_key of _SessionClassMethods", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._IdentityKeyType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session._SessionClassMethods.identity_key", "name": "identity_key", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5, 5, 5], "arg_names": ["cls", "class_", "ident", "instance", "row", "identity_token"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.session._SessionClassMethods"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.engine.row.Row"}, "sqlalchemy.engine.row.RowMapping", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "identity_key of _SessionClassMethods", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._IdentityKeyType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "object_session": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "instance"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.session._SessionClassMethods.object_session", "name": "object_session", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "instance"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.session._SessionClassMethods"}, "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "object_session of _SessionClassMethods", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.orm.session.Session", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session._SessionClassMethods.object_session", "name": "object_session", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "instance"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.session._SessionClassMethods"}, "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "object_session of _SessionClassMethods", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.orm.session.Session", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._SessionClassMethods.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.session._SessionClassMethods", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_SessionCloseState": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.session._SessionCloseState", "name": "_SessionCloseState", "type_vars": []}, "deletable_attributes": [], "flags": ["is_enum"], "fullname": "sqlalchemy.orm.session._SessionCloseState", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "sqlalchemy.orm.session", "mro": ["sqlalchemy.orm.session._SessionCloseState", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "ACTIVE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.session._SessionCloseState.ACTIVE", "name": "ACTIVE", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "CLOSED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.session._SessionCloseState.CLOSED", "name": "CLOSED", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "CLOSE_IS_RESET": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.session._SessionCloseState.CLOSE_IS_RESET", "name": "CLOSE_IS_RESET", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._SessionCloseState.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.session._SessionCloseState", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_StateChange": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.state_changes._StateChange", "kind": "Gdef", "module_public": false}, "_StateChangeState": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.state_changes._StateChangeState", "kind": "Gdef", "module_public": false}, "_StateChangeStates": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.state_changes._StateChangeStates", "kind": "Gdef", "module_public": false}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._T", "name": "_T", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, "_T0": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._T0", "kind": "Gdef", "module_public": false}, "_T1": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._T1", "kind": "Gdef", "module_public": false}, "_T2": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._T2", "kind": "Gdef", "module_public": false}, "_T3": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._T3", "kind": "Gdef", "module_public": false}, "_T4": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._T4", "kind": "Gdef", "module_public": false}, "_T5": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._T5", "kind": "Gdef", "module_public": false}, "_T6": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._T6", "kind": "Gdef", "module_public": false}, "_T7": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._T7", "kind": "Gdef", "module_public": false}, "_TCCA": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.session.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.session.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.session.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.session.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.session.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.session.__package__", "name": "__package__", "type": "builtins.str"}}, "_class_to_mapper": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base._class_to_mapper", "kind": "Gdef", "module_public": false}, "_new_sessionid": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.session._new_sessionid", "name": "_new_sessionid", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_none_set": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base._none_set", "kind": "Gdef", "module_public": false}, "_sessions": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "sqlalchemy.orm.session._sessions", "name": "_sessions", "type": {".class": "Instance", "args": ["builtins.int", "sqlalchemy.orm.session.Session"], "type_ref": "weakref.<PERSON>ak<PERSON>alueDictionary"}}}, "_state_mapper": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base._state_mapper", "kind": "Gdef", "module_public": false}, "_state_session": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["state"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session._state_session", "name": "_state_session", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["state"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.state.InstanceState"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_state_session", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.orm.session.Session", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "attributes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.attributes", "kind": "Gdef", "module_public": false}, "bulk_persistence": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.bulk_persistence", "kind": "Gdef", "module_public": false}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_public": false}, "close_all_sessions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.close_all_sessions", "name": "close_all_sessions", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close_all_sessions", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "coercions": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.coercions", "kind": "Gdef", "module_public": false}, "context": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.context", "kind": "Gdef", "module_public": false}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef", "module_public": false}, "descriptor_props": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.descriptor_props", "kind": "Gdef", "module_public": false}, "dispatcher": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.base.dispatcher", "kind": "Gdef", "module_public": false}, "dml": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.dml", "kind": "Gdef", "module_public": false}, "engine": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine", "kind": "Gdef", "module_public": false}, "exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.exc", "kind": "Gdef", "module_public": false}, "identity": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.identity", "kind": "Gdef", "module_public": false}, "insp_is_mapper": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing.insp_is_mapper", "kind": "Gdef", "module_public": false}, "inspect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.inspection.inspect", "kind": "Gdef", "module_public": false}, "instance_str": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.instance_str", "kind": "Gdef", "module_public": false}, "is_composite_class": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing.is_composite_class", "kind": "Gdef", "module_public": false}, "is_orm_option": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing.is_orm_option", "kind": "Gdef", "module_public": false}, "is_user_defined_option": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing.is_user_defined_option", "kind": "Gdef", "module_public": false}, "itertools": {".class": "SymbolTableNode", "cross_ref": "itertools", "kind": "Gdef", "module_public": false}, "loading": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.loading", "kind": "Gdef", "module_public": false}, "make_transient": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["instance"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.make_transient", "name": "make_transient", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["instance"], "arg_types": ["builtins.object"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_transient", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "make_transient_to_detached": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["instance"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.make_transient_to_detached", "name": "make_transient_to_detached", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["instance"], "arg_types": ["builtins.object"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_transient_to_detached", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "object_mapper": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.object_mapper", "kind": "Gdef", "module_public": false}, "object_session": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["instance"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.object_session", "name": "object_session", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["instance"], "arg_types": ["builtins.object"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "object_session", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.orm.session.Session", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "object_state": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.object_state", "kind": "Gdef", "module_public": false}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_public": false}, "query": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.query", "kind": "Gdef", "module_public": false}, "roles": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.roles", "kind": "Gdef", "module_public": false}, "sa_exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc", "kind": "Gdef", "module_public": false}, "sessionmaker": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.session._SessionClassMethods"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.session.sessionmaker", "name": "sessionmaker", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._S", "id": 1, "name": "_S", "namespace": "sqlalchemy.orm.session.sessionmaker", "upper_bound": "sqlalchemy.orm.session.Session", "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.session.sessionmaker", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.session", "mro": ["sqlalchemy.orm.session.sessionmaker", "sqlalchemy.orm.session._SessionClassMethods", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "local_kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.sessionmaker.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "local_kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._S", "id": 1, "name": "_S", "namespace": "sqlalchemy.orm.session.sessionmaker", "upper_bound": "sqlalchemy.orm.session.Session", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.session.sessionmaker"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of sessionmaker", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._S", "id": 1, "name": "_S", "namespace": "sqlalchemy.orm.session.sessionmaker", "upper_bound": "sqlalchemy.orm.session.Session", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "sqlalchemy.orm.session.sessionmaker.__init__", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5, 4], "arg_names": ["self", "bind", "class_", "autoflush", "expire_on_commit", "info", "kw"], "dataclass_transform_spec": null, "flags": ["is_overload"], "fullname": "sqlalchemy.orm.session.sessionmaker.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 4], "arg_names": ["self", "bind", "class_", "autoflush", "expire_on_commit", "info", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._S", "id": 1, "name": "_S", "namespace": "sqlalchemy.orm.session.sessionmaker", "upper_bound": "sqlalchemy.orm.session.Session", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.session.sessionmaker"}, {".class": "UnionType", "items": ["sqlalchemy.engine.base.Engine", "sqlalchemy.engine.base.Connection", {".class": "NoneType"}]}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._S", "id": 1, "name": "_S", "namespace": "sqlalchemy.orm.session.sessionmaker", "upper_bound": "sqlalchemy.orm.session.Session", "values": [], "variance": 0}}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._InfoType"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of sessionmaker", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 3, 5, 5, 5, 4], "arg_names": ["self", "bind", "class_", "autoflush", "expire_on_commit", "info", "kw"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.orm.session.sessionmaker.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 3, 5, 5, 5, 4], "arg_names": ["self", "bind", "class_", "autoflush", "expire_on_commit", "info", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._S", "id": 1, "name": "_S", "namespace": "sqlalchemy.orm.session.sessionmaker", "upper_bound": "sqlalchemy.orm.session.Session", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.session.sessionmaker"}, {".class": "UnionType", "items": ["sqlalchemy.engine.base.Engine", "sqlalchemy.engine.base.Connection", {".class": "NoneType"}]}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._S", "id": 1, "name": "_S", "namespace": "sqlalchemy.orm.session.sessionmaker", "upper_bound": "sqlalchemy.orm.session.Session", "values": [], "variance": 0}}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._InfoType"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of sessionmaker", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.sessionmaker.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 3, 5, 5, 5, 4], "arg_names": ["self", "bind", "class_", "autoflush", "expire_on_commit", "info", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._S", "id": 1, "name": "_S", "namespace": "sqlalchemy.orm.session.sessionmaker", "upper_bound": "sqlalchemy.orm.session.Session", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.session.sessionmaker"}, {".class": "UnionType", "items": ["sqlalchemy.engine.base.Engine", "sqlalchemy.engine.base.Connection", {".class": "NoneType"}]}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._S", "id": 1, "name": "_S", "namespace": "sqlalchemy.orm.session.sessionmaker", "upper_bound": "sqlalchemy.orm.session.Session", "values": [], "variance": 0}}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._InfoType"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of sessionmaker", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 4], "arg_names": ["self", "bind", "autoflush", "expire_on_commit", "info", "kw"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.orm.session.sessionmaker.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 4], "arg_names": ["self", "bind", "autoflush", "expire_on_commit", "info", "kw"], "arg_types": [{".class": "Instance", "args": ["sqlalchemy.orm.session.Session"], "type_ref": "sqlalchemy.orm.session.sessionmaker"}, {".class": "UnionType", "items": ["sqlalchemy.engine.base.Engine", "sqlalchemy.engine.base.Connection", {".class": "NoneType"}]}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._InfoType"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of sessionmaker", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.session.sessionmaker.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 4], "arg_names": ["self", "bind", "autoflush", "expire_on_commit", "info", "kw"], "arg_types": [{".class": "Instance", "args": ["sqlalchemy.orm.session.Session"], "type_ref": "sqlalchemy.orm.session.sessionmaker"}, {".class": "UnionType", "items": ["sqlalchemy.engine.base.Engine", "sqlalchemy.engine.base.Connection", {".class": "NoneType"}]}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._InfoType"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of sessionmaker", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 3, 5, 5, 5, 4], "arg_names": ["self", "bind", "class_", "autoflush", "expire_on_commit", "info", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._S", "id": 1, "name": "_S", "namespace": "sqlalchemy.orm.session.sessionmaker", "upper_bound": "sqlalchemy.orm.session.Session", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.session.sessionmaker"}, {".class": "UnionType", "items": ["sqlalchemy.engine.base.Engine", "sqlalchemy.engine.base.Connection", {".class": "NoneType"}]}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._S", "id": 1, "name": "_S", "namespace": "sqlalchemy.orm.session.sessionmaker", "upper_bound": "sqlalchemy.orm.session.Session", "values": [], "variance": 0}}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._InfoType"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of sessionmaker", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 4], "arg_names": ["self", "bind", "autoflush", "expire_on_commit", "info", "kw"], "arg_types": [{".class": "Instance", "args": ["sqlalchemy.orm.session.Session"], "type_ref": "sqlalchemy.orm.session.sessionmaker"}, {".class": "UnionType", "items": ["sqlalchemy.engine.base.Engine", "sqlalchemy.engine.base.Connection", {".class": "NoneType"}]}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._InfoType"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of sessionmaker", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}]}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.sessionmaker.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._S", "id": 1, "name": "_S", "namespace": "sqlalchemy.orm.session.sessionmaker", "upper_bound": "sqlalchemy.orm.session.Session", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.session.sessionmaker"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of sessionmaker", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "begin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.sessionmaker.begin", "name": "begin", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._S", "id": 1, "name": "_S", "namespace": "sqlalchemy.orm.session.sessionmaker", "upper_bound": "sqlalchemy.orm.session.Session", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.session.sessionmaker"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "begin of sessionmaker", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._S", "id": 1, "name": "_S", "namespace": "sqlalchemy.orm.session.sessionmaker", "upper_bound": "sqlalchemy.orm.session.Session", "values": [], "variance": 0}], "type_ref": "contextlib.AbstractContextManager"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "class_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.session.sessionmaker.class_", "name": "class_", "type": {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._S", "id": 1, "name": "_S", "namespace": "sqlalchemy.orm.session.sessionmaker", "upper_bound": "sqlalchemy.orm.session.Session", "values": [], "variance": 0}}}}, "configure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "new_kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.session.sessionmaker.configure", "name": "configure", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "new_kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._S", "id": 1, "name": "_S", "namespace": "sqlalchemy.orm.session.sessionmaker", "upper_bound": "sqlalchemy.orm.session.Session", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.session.sessionmaker"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "configure of sessionmaker", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kw": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.session.sessionmaker.kw", "name": "kw", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session.sessionmaker.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.session._S", "id": 1, "name": "_S", "namespace": "sqlalchemy.orm.session.sessionmaker", "upper_bound": "sqlalchemy.orm.session.Session", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.session.sessionmaker"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_S"], "typeddict_type": null}}, "sql": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql", "kind": "Gdef", "module_public": false}, "state_str": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.state_str", "kind": "Gdef", "module_public": false}, "statelib": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.state", "kind": "Gdef", "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_public": false}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_public": false}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef", "module_public": false}, "visitors": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.visitors", "kind": "Gdef", "module_public": false}, "weakref": {".class": "SymbolTableNode", "cross_ref": "weakref", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\session.py"}