{"data_mtime": 1759504548, "dep_lines": [7, 11, 16, 17, 18, 8, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "botocore.compat", "botocore.exceptions", "urllib3.connection", "urllib3.connectionpool", "logging", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "email", "email.message", "http", "http.client", "io", "urllib3", "urllib3._request_methods"], "hash": "88208fc7901610efc6a5244e69d07522e7754ddf6ffff792a3ba190e6662f88f", "id": "botocore.awsrequest", "ignore_all": true, "interface_hash": "2436024f700427219bdd724b347c6b0b5d07e8a9cee0c43425359a4afb7f03e6", "mtime": 1757092235, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\botocore-stubs\\awsrequest.pyi", "plugin_data": null, "size": 4164, "suppressed": [], "version_id": "1.8.0"}