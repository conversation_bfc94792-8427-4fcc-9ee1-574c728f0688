{"data_mtime": 1759503866, "dep_lines": [13, 16, 46, 62, 63, 67, 68, 73, 107, 111, 114, 117, 152, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql._dml_constructors", "sqlalchemy.sql._elements_constructors", "sqlalchemy.sql._selectable_constructors", "sqlalchemy.sql._typing", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.dml", "sqlalchemy.sql.elements", "sqlalchemy.sql.functions", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.operators", "sqlalchemy.sql.selectable", "sqlalchemy.sql.visitors", "__future__", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "sqlalchemy.sql.annotation", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.util", "sqlalchemy.util.langhelpers", "typing"], "hash": "20ec84dec8e6a648440d5ff52f31fc1398c0235696a100a31508dc495aefd269", "id": "sqlalchemy.sql.expression", "ignore_all": true, "interface_hash": "1341c2b02b3f3207bda7f9b2669231fd45ec15cde2d829687b97135876684094", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\expression.py", "plugin_data": null, "size": 7742, "suppressed": [], "version_id": "1.8.0"}