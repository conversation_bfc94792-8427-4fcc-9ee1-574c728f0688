{"data_mtime": 1759503833, "dep_lines": [5, 7, 8, 9, 10, 1, 3, 4, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 10, 5, 20, 20, 30, 30, 30], "dependencies": ["urllib.parse", "urllib3._base_connection", "urllib3._collections", "urllib3.filepost", "urllib3.response", "__future__", "json", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "io", "urllib3.fields"], "hash": "802785f3948efd45385a83f0607228cffb70f9e33f1153a42c5a7c385b02ec30", "id": "urllib3._request_methods", "ignore_all": true, "interface_hash": "d4519c65afc63cbb7495570f891541f389f9ae467123b5fda618b8a787f84954", "mtime": 1757091871, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\urllib3\\_request_methods.py", "plugin_data": null, "size": 9931, "suppressed": [], "version_id": "1.8.0"}