{"data_mtime": 1759504550, "dep_lines": [7, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 20, 20, 30, 30], "dependencies": ["botocore.retries.standard", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "typing"], "hash": "b5763cfd3897fd4f03ad9558f6ee093e7c06a1464c65b98850d7f1ad8b8d304b", "id": "botocore.retries.base", "ignore_all": true, "interface_hash": "a84622f7be660e3a3c3fca5d02bc5c5e95a500211dd5549c075158e87c33b78c", "mtime": 1757092235, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\botocore-stubs\\retries\\base.pyi", "plugin_data": null, "size": 323, "suppressed": [], "version_id": "1.8.0"}