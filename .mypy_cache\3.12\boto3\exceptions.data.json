{".class": "MypyFile", "_fullname": "boto3.exceptions", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Boto3Error": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "boto3.exceptions.Boto3Error", "name": "Boto3Error", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "boto3.exceptions.Boto3Error", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "boto3.exceptions", "mro": ["boto3.exceptions.Boto3Error", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "boto3.exceptions.Boto3Error.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "boto3.exceptions.Boto3Error", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DynamoDBNeedsConditionError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["boto3.exceptions.Boto3Error"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "boto3.exceptions.DynamoDBNeedsConditionError", "name": "DynamoDBNeedsConditionError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "boto3.exceptions.DynamoDBNeedsConditionError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "boto3.exceptions", "mro": ["boto3.exceptions.DynamoDBNeedsConditionError", "boto3.exceptions.Boto3Error", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "flags": [], "fullname": "boto3.exceptions.DynamoDBNeedsConditionError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["boto3.exceptions.DynamoDBNeedsConditionError", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DynamoDBNeedsConditionError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "boto3.exceptions.DynamoDBNeedsConditionError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "boto3.exceptions.DynamoDBNeedsConditionError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DynamoDBNeedsKeyConditionError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["boto3.exceptions.Boto3Error"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "boto3.exceptions.DynamoDBNeedsKeyConditionError", "name": "DynamoDBNeedsKeyConditionError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "boto3.exceptions.DynamoDBNeedsKeyConditionError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "boto3.exceptions", "mro": ["boto3.exceptions.DynamoDBNeedsKeyConditionError", "boto3.exceptions.Boto3Error", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "boto3.exceptions.DynamoDBNeedsKeyConditionError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "boto3.exceptions.DynamoDBNeedsKeyConditionError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DynamoDBOperationNotSupportedError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["boto3.exceptions.Boto3Error"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "boto3.exceptions.DynamoDBOperationNotSupportedError", "name": "DynamoDBOperationNotSupportedError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "boto3.exceptions.DynamoDBOperationNotSupportedError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "boto3.exceptions", "mro": ["boto3.exceptions.DynamoDBOperationNotSupportedError", "boto3.exceptions.Boto3Error", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "operation", "value"], "dataclass_transform_spec": null, "flags": [], "fullname": "boto3.exceptions.DynamoDBOperationNotSupportedError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "operation", "value"], "arg_types": ["boto3.exceptions.DynamoDBOperationNotSupportedError", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DynamoDBOperationNotSupportedError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "boto3.exceptions.DynamoDBOperationNotSupportedError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "boto3.exceptions.DynamoDBOperationNotSupportedError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DynanmoDBOperationNotSupportedError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "boto3.exceptions.DynanmoDBOperationNotSupportedError", "line": 40, "no_args": true, "normalized": false, "target": "boto3.exceptions.DynamoDBOperationNotSupportedError"}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NoVersionFound": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["boto3.exceptions.Boto3Error"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "boto3.exceptions.NoVersionFound", "name": "NoVersionFound", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "boto3.exceptions.NoVersionFound", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "boto3.exceptions", "mro": ["boto3.exceptions.NoVersionFound", "boto3.exceptions.Boto3Error", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "boto3.exceptions.NoVersionFound.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "boto3.exceptions.NoVersionFound", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PythonDeprecationWarning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Warning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "boto3.exceptions.PythonDeprecationWarning", "name": "PythonDeprecationWarning", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "boto3.exceptions.PythonDeprecationWarning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "boto3.exceptions", "mro": ["boto3.exceptions.PythonDeprecationWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "boto3.exceptions.PythonDeprecationWarning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "boto3.exceptions.PythonDeprecationWarning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ResourceLoadException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["boto3.exceptions.Boto3Error"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "boto3.exceptions.ResourceLoadException", "name": "ResourceLoadException", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "boto3.exceptions.ResourceLoadException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "boto3.exceptions", "mro": ["boto3.exceptions.ResourceLoadException", "boto3.exceptions.Boto3Error", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "boto3.exceptions.ResourceLoadException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "boto3.exceptions.ResourceLoadException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ResourceNotExistsError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["boto3.exceptions.Boto3Error", "botocore.exceptions.DataNotFoundError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "boto3.exceptions.ResourceNotExistsError", "name": "ResourceNotExistsError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "boto3.exceptions.ResourceNotExistsError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "boto3.exceptions", "mro": ["boto3.exceptions.ResourceNotExistsError", "boto3.exceptions.Boto3Error", "botocore.exceptions.DataNotFoundError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "service_name", "available_services", "has_low_level_client"], "dataclass_transform_spec": null, "flags": [], "fullname": "boto3.exceptions.ResourceNotExistsError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "service_name", "available_services", "has_low_level_client"], "arg_types": ["boto3.exceptions.ResourceNotExistsError", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ResourceNotExistsError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "boto3.exceptions.ResourceNotExistsError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "boto3.exceptions.ResourceNotExistsError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RetriesExceededError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["boto3.exceptions.Boto3Error"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "boto3.exceptions.RetriesExceededError", "name": "RetriesExceededError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "boto3.exceptions.RetriesExceededError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "boto3.exceptions", "mro": ["boto3.exceptions.RetriesExceededError", "boto3.exceptions.Boto3Error", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "last_exception", "msg"], "dataclass_transform_spec": null, "flags": [], "fullname": "boto3.exceptions.RetriesExceededError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "last_exception", "msg"], "arg_types": ["boto3.exceptions.RetriesExceededError", "boto3.exceptions.Boto3Error", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RetriesExceededError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "boto3.exceptions.RetriesExceededError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "boto3.exceptions.RetriesExceededError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "S3TransferFailedError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["boto3.exceptions.Boto3Error"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "boto3.exceptions.S3TransferFailedError", "name": "S3TransferFailedError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "boto3.exceptions.S3TransferFailedError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "boto3.exceptions", "mro": ["boto3.exceptions.S3TransferFailedError", "boto3.exceptions.Boto3Error", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "boto3.exceptions.S3TransferFailedError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "boto3.exceptions.S3TransferFailedError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "S3UploadFailedError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["boto3.exceptions.Boto3Error"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "boto3.exceptions.S3UploadFailedError", "name": "S3UploadFailedError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "boto3.exceptions.S3UploadFailedError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "boto3.exceptions", "mro": ["boto3.exceptions.S3UploadFailedError", "boto3.exceptions.Boto3Error", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "boto3.exceptions.S3UploadFailedError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "boto3.exceptions.S3UploadFailedError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnknownAPIVersionError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["boto3.exceptions.Boto3Error", "botocore.exceptions.DataNotFoundError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "boto3.exceptions.UnknownAPIVersionError", "name": "UnknownAPIVersionError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "boto3.exceptions.UnknownAPIVersionError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "boto3.exceptions", "mro": ["boto3.exceptions.UnknownAPIVersionError", "boto3.exceptions.Boto3Error", "botocore.exceptions.DataNotFoundError", "botocore.exceptions.BotoCoreError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "service_name", "bad_api_version", "available_api_versions"], "dataclass_transform_spec": null, "flags": [], "fullname": "boto3.exceptions.UnknownAPIVersionError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "service_name", "bad_api_version", "available_api_versions"], "arg_types": ["boto3.exceptions.UnknownAPIVersionError", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UnknownAPIVersionError", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "boto3.exceptions.UnknownAPIVersionError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "boto3.exceptions.UnknownAPIVersionError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "boto3.exceptions.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "boto3.exceptions.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "boto3.exceptions.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "boto3.exceptions.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "boto3.exceptions.__package__", "name": "__package__", "type": "builtins.str"}}, "botocore": {".class": "SymbolTableNode", "cross_ref": "botocore", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\boto3-stubs\\exceptions.pyi"}