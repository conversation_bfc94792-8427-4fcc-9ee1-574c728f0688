{"data_mtime": 1759504514, "dep_lines": [9, 12, 13, 1, 3, 4, 5, 7, 1, 1, 1, 1, 1], "dep_prios": [5, 25, 25, 5, 10, 5, 5, 5, 5, 20, 20, 30, 30], "dependencies": ["pydantic._internal._utils", "pydantic.config", "pydantic.fields", "__future__", "dataclasses", "inspect", "typing", "pydantic_core", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "pydantic._internal._repr"], "hash": "f049633c97b8a529daa6e8ab1b90e4040803d618201f1100cb314f1fef47d331", "id": "pydantic._internal._signature", "ignore_all": true, "interface_hash": "e32d643dd8e034d2018b8764a965b9c01e09d47c7f1a5e3ee2c7452abcf859e6", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\_internal\\_signature.py", "plugin_data": null, "size": 6779, "suppressed": [], "version_id": "1.8.0"}