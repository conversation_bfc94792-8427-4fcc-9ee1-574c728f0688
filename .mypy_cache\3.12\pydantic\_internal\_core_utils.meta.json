{"data_mtime": 1759504514, "dep_lines": [13, 14, 5, 8, 11, 13, 17, 1, 3, 4, 6, 8, 10, 11, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 10, 10, 20, 25, 5, 10, 10, 5, 5, 5, 20, 5, 20, 20, 30, 30], "dependencies": ["pydantic._internal._repr", "pydantic._internal._typing_extra", "collections.abc", "pydantic_core.core_schema", "typing_inspection.typing_objects", "pydantic._internal", "rich.console", "__future__", "inspect", "os", "typing", "pydantic_core", "typing_extensions", "typing_inspection", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "rich"], "hash": "ffe66e5e1a62ff4243a59cf3f23bc6afcda4812dcf12b8ad591db67e35a9c38f", "id": "pydantic._internal._core_utils", "ignore_all": true, "interface_hash": "24930f5d34cf7525f8739ce3004b67eb81df390234f632525857f1ce96255834", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\_internal\\_core_utils.py", "plugin_data": null, "size": 6746, "suppressed": [], "version_id": "1.8.0"}