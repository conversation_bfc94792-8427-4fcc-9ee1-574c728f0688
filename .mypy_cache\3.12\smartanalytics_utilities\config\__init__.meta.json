{"data_mtime": 1759504552, "dep_lines": [13, 14, 15, 16, 17, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 20, 20, 30, 30], "dependencies": ["smartanalytics_utilities.config.aws_config", "smartanalytics_utilities.config.base", "smartanalytics_utilities.config.database_config", "smartanalytics_utilities.config.logging_config", "smartanalytics_utilities.config.settings", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "typing"], "hash": "330257117f415ac9283d0134413ef87ca59079dd8987d4b16452b7960628fc51", "id": "smartanalytics_utilities.config", "ignore_all": false, "interface_hash": "38c07b4865ebce1a0a4930634d03271c937f1f955299e30ce00f8a1076938c48", "mtime": 1759421769, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "layers\\utilities\\python\\smartanalytics_utilities\\config\\__init__.py", "plugin_data": null, "size": 911, "suppressed": [], "version_id": "1.8.0"}