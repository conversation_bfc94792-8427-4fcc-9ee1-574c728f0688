{"data_mtime": **********, "dep_lines": [3, 4, 5, 14, 15, 16, 17, 18, 19, 20, 21, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 30, 30], "dependencies": ["pydantic_settings.sources.providers.aws", "pydantic_settings.sources.providers.azure", "pydantic_settings.sources.providers.cli", "pydantic_settings.sources.providers.dotenv", "pydantic_settings.sources.providers.env", "pydantic_settings.sources.providers.gcp", "pydantic_settings.sources.providers.json", "pydantic_settings.sources.providers.pyproject", "pydantic_settings.sources.providers.secrets", "pydantic_settings.sources.providers.toml", "pydantic_settings.sources.providers.yaml", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "typing"], "hash": "8c14eeaea057789bcc7d397696f1ebda20d50cebc77cefbc3d534988ab7b3019", "id": "pydantic_settings.sources.providers", "ignore_all": true, "interface_hash": "1e041ab51d0f182bf2031c0bc3eb2181f5c460c6787dc5d8208f2f3b5d331dda", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic_settings\\sources\\providers\\__init__.py", "plugin_data": null, "size": 1205, "suppressed": [], "version_id": "1.8.0"}