{"data_mtime": 1759503866, "dep_lines": [22, 23, 24, 26, 47, 48, 51, 57, 58, 22, 8, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 5, 25, 25, 25, 20, 5, 5, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.coercions", "sqlalchemy.sql.roles", "sqlalchemy.sql.base", "sqlalchemy.sql.elements", "sqlalchemy.sql.functions", "sqlalchemy.util.typing", "sqlalchemy.sql._typing", "sqlalchemy.sql.selectable", "sqlalchemy.sql.type_api", "sqlalchemy.sql", "__future__", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "enum", "sqlalchemy.inspection", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.operators", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util", "sqlalchemy.util.langhelpers"], "hash": "bac4fcd6b7c99c4e185b2437fa5eaf2f5dd34824f053b5a3596a5221c54eeaf4", "id": "sqlalchemy.sql._elements_constructors", "ignore_all": true, "interface_hash": "6b16842ba7392f0c4a871a5b8e006b5fcb05677621b4366cbd32fb7cb15d9cca", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\_elements_constructors.py", "plugin_data": null, "size": 64968, "suppressed": [], "version_id": "1.8.0"}