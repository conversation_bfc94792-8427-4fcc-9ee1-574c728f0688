{"data_mtime": 1759503866, "dep_lines": [26, 27, 28, 31, 35, 36, 37, 38, 40, 26, 29, 30, 18, 20, 29, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 25, 25, 25, 25, 25, 20, 10, 10, 5, 5, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.attributes", "sqlalchemy.orm.exc", "sqlalchemy.orm.util", "sqlalchemy.util.topological", "sqlalchemy.orm.dependency", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.mapper", "sqlalchemy.orm.session", "sqlalchemy.orm.state", "sqlalchemy.orm", "sqlalchemy.event", "sqlalchemy.util", "__future__", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "_typeshed", "abc", "collections", "sqlalchemy.engine", "sqlalchemy.engine.util", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.orm.base", "sqlalchemy.orm.state_changes", "sqlalchemy.sql", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.roles", "sqlalchemy.util.langhelpers", "sqlalchemy.util.preloaded"], "hash": "ff9ad1aa83deaeafca70c71feecaec12577d5ee443f9f395a1d62cf35c3e7bd2", "id": "sqlalchemy.orm.unitofwork", "ignore_all": true, "interface_hash": "51eda9b3b3ea7fa5475b25c3a27a35f6759c7c8cc14dec00d10f36fc4e96b405", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\unitofwork.py", "plugin_data": null, "size": 27829, "suppressed": [], "version_id": "1.8.0"}