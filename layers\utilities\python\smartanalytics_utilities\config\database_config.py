"""Database configuration for Aurora PostgreSQL with RDS Proxy support."""

import ssl
from typing import Any

from urllib.parse import quote_plus
from pydantic import Field, field_validator, model_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class DatabaseConfig(BaseSettings):
    """Aurora PostgreSQL database configuration with RDS Proxy support."""

    # Connection settings
    host: str = Field(
        default="localhost", description="Database host (RDS Proxy endpoint)"
    )

    port: int = Field(default=5432, ge=1, le=65535, description="Database port")

    database_name: str = Field(default="smartanalytics", description="Database name")

    schema_name: str = Field(default="public", description="Database schema name")

    # Authentication settings
    username: str | None = Field(default="postgres", description="Database username")

    password: str | None = Field(default=None, description="Database password")

    # RDS Proxy with IAM authentication
    use_iam_auth: bool = Field(
        default=False, description="Use IAM authentication with RDS Proxy"
    )

    credentials_secret_arn: str | None = Field(
        default=None, description="AWS Secrets Manager ARN for database credentials"
    )

    # Connection pool settings
    pool_size: int = Field(default=5, ge=1, le=20, description="Connection pool size")

    max_overflow: int = Field(
        default=10, ge=0, le=50, description="Maximum pool overflow"
    )

    pool_timeout: int = Field(
        default=30, ge=1, le=300, description="Pool checkout timeout in seconds"
    )

    pool_recycle: int = Field(
        default=3600,
        ge=300,
        le=86400,
        description="Pool connection recycle time in seconds",
    )

    pool_pre_ping: bool = Field(
        default=True, description="Enable pool pre-ping for connection validation"
    )

    # Query settings
    query_timeout: int = Field(
        default=60, ge=1, le=300, description="Query timeout in seconds"
    )

    statement_timeout: int = Field(
        default=30, ge=1, le=300, description="Statement timeout in seconds"
    )

    # Performance settings
    echo_sql: bool = Field(default=False, description="Echo SQL statements to logs")

    enable_query_cache: bool = Field(
        default=False, description="Enable SQLAlchemy query cache"
    )

    # SSL settings
    ssl_mode: str = Field(
        default="require", description="SSL mode for database connections"
    )

    ssl_cert_path: str | None = Field(
        default=None, description="Path to SSL certificate file"
    )

    model_config = SettingsConfigDict(
        env_prefix="DATABASE_",
        case_sensitive=False,
        str_strip_whitespace=True,
        validate_assignment=True,
        extra="ignore",  # Changed from "forbid" to "ignore" to allow extra env vars
    )

    @field_validator("ssl_mode")
    @classmethod
    def validate_ssl_mode(cls, v: str) -> str:
        """Validate SSL mode."""
        valid_modes = [
            "disable",
            "allow",
            "prefer",
            "require",
            "verify-ca",
            "verify-full",
        ]
        if v not in valid_modes:
            raise ValueError(f"Invalid SSL mode. Must be one of: {valid_modes}")
        return v

    @model_validator(mode="after")
    def validate_credentials(self) -> "DatabaseConfig":
        """Validate authentication configuration."""
        if self.use_iam_auth:
            # For IAM auth, only username is required
            if not self.username:
                raise ValueError("Username is required for IAM authentication")
        else:
            # For password auth, either secret ARN or username/password is required
            if not self.credentials_secret_arn and not self.username:
                raise ValueError(
                    "Either credentials_secret_arn or username must be provided"
                )
        return self

    def get_connection_url(self, iam_token: str | None = None) -> str:
        """Get database connection URL for SQLAlchemy.

        Args:
            iam_token: IAM authentication token (for RDS Proxy)

        Returns:
            Database connection URL
        """
        if not self.username:
            raise ValueError("Username must be set to generate connection URL")

        # URL encode username
        encoded_username = quote_plus(self.username)

        if self.use_iam_auth:
            if not iam_token:
                raise ValueError("IAM token is required for IAM authentication")
            # Use IAM token as password
            encoded_password = quote_plus(iam_token)
        else:
            if not self.password:
                raise ValueError("Password must be set for password authentication")
            # URL encode password to handle special characters
            encoded_password = quote_plus(self.password)

        return (
            f"postgresql+asyncpg://{encoded_username}:{encoded_password}"
            f"@{self.host}:{self.port}/{self.database_name}"
        )

    def get_connection_params(self) -> dict[str, Any]:
        """Get connection parameters for asyncpg."""
        params = {
            "server_settings": {
                "application_name": "smartanalytics-agent-event-processor",
                "search_path": self.schema_name,
                "statement_timeout": f"{self.statement_timeout}s",
                "jit": "off",  # Disable JIT for Lambda cold starts
            },
            "command_timeout": self.query_timeout,
        }

        # Configure SSL for RDS Proxy (required for IAM auth)
        if self.use_iam_auth or self.ssl_mode != "disable":
            params["ssl"] = self._get_ssl_context()
        else:
            params["ssl"] = False

        return params

    def _get_ssl_context(self) -> ssl.SSLContext:
        """Get SSL context for RDS Proxy connections.

        RDS Proxy requires TLS for IAM authentication.
        """
        ctx = ssl.create_default_context()

        # Use system CA bundle in Lambda environment
        if self.ssl_cert_path:
            ctx.load_verify_locations(cafile=self.ssl_cert_path)

        return ctx
