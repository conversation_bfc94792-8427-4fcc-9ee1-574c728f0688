{"data_mtime": 1759503870, "dep_lines": [11, 12, 13, 14, 15, 16, 17, 36, 38, 47, 49, 52, 58, 75, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.dialects.postgresql.array", "sqlalchemy.dialects.postgresql.asyncpg", "sqlalchemy.dialects.postgresql.base", "sqlalchemy.dialects.postgresql.pg8000", "sqlalchemy.dialects.postgresql.psycopg", "sqlalchemy.dialects.postgresql.psycopg2", "sqlalchemy.dialects.postgresql.psycopg2cffi", "sqlalchemy.dialects.postgresql.dml", "sqlalchemy.dialects.postgresql.ext", "sqlalchemy.dialects.postgresql.hstore", "sqlalchemy.dialects.postgresql.json", "sqlalchemy.dialects.postgresql.named_types", "sqlalchemy.dialects.postgresql.ranges", "sqlalchemy.dialects.postgresql.types", "types", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "sqlalchemy.dialects.postgresql._psycopg_common", "sqlalchemy.engine", "sqlalchemy.engine.default", "sqlalchemy.engine.interfaces", "sqlalchemy.event", "sqlalchemy.event.registry", "typing"], "hash": "6fc735798bd3bcfdc9f0515be9ef4379ac39ada1de01f1aa8f55902ce39a4381", "id": "sqlalchemy.dialects.postgresql", "ignore_all": true, "interface_hash": "80b1675e07be3cd8d9fd4a9b566dd4ad3ca2091f38f415b76f6fcc7bee012663", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\__init__.py", "plugin_data": null, "size": 4059, "suppressed": [], "version_id": "1.8.0"}