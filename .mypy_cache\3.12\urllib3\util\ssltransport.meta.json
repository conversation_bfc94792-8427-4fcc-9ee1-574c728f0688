{"data_mtime": 1759504509, "dep_lines": [13, 8, 1, 3, 4, 5, 6, 11, 1, 1, 1, 1, 1], "dep_prios": [25, 5, 5, 10, 10, 10, 10, 25, 5, 20, 20, 30, 30], "dependencies": ["urllib3.util.ssl_", "urllib3.exceptions", "__future__", "io", "socket", "ssl", "typing", "typing_extensions", "builtins", "pyexpat.model", "pyexpat.errors", "_socket", "abc"], "hash": "133e0ef2947fbd3f1d6a7fc5bea0584ba7600df05710c7d57ebcdc754a167e2e", "id": "urllib3.util.ssltransport", "ignore_all": true, "interface_hash": "5411a46603d649e283db9453c357cf47f94edc1e2446a1e3b7a698be20c27379", "mtime": 1757091871, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\urllib3\\util\\ssltransport.py", "plugin_data": null, "size": 8847, "suppressed": [], "version_id": "1.8.0"}