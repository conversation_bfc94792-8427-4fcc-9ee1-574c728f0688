{"data_mtime": 1759504515, "dep_lines": [1, 3, 9, 10, 1, 2, 4, 5, 6, 8, 15, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 20, 10, 5, 5, 5, 5, 5, 5, 20, 20, 30, 30, 30], "dependencies": ["concurrent.futures", "collections.abc", "asyncio.events", "asyncio.futures", "concurrent", "sys", "types", "typing", "typing_extensions", "asyncio", "<PERSON><PERSON><PERSON>", "builtins", "pyexpat.model", "pyexpat.errors", "_typeshed", "abc", "concurrent.futures._base"], "hash": "d56bd1eb9e3e7d6f5a5344a3d4375ad96d1ea0cbc74f992be8feb42c1b590ca7", "id": "asyncio.tasks", "ignore_all": true, "interface_hash": "cd523e131bfc7858c5c56564a658dbb5c482905def9129b9f68dd303775aefc6", "mtime": 1757092231, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\asyncio\\tasks.pyi", "plugin_data": null, "size": 18652, "suppressed": [], "version_id": "1.8.0"}