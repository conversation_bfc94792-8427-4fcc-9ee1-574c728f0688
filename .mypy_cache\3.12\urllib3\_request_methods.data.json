{".class": "MypyFile", "_fullname": "urllib3._request_methods", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BaseHTTPResponse": {".class": "SymbolTableNode", "cross_ref": "urllib3.response.BaseHTTPResponse", "kind": "Gdef", "module_public": false}, "HTTPHeaderDict": {".class": "SymbolTableNode", "cross_ref": "urllib3._collections.HTTPHeaderDict", "kind": "Gdef", "module_public": false}, "RequestMethods": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3._request_methods.RequestMethods", "name": "RequestMethods", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "urllib3._request_methods.RequestMethods", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3._request_methods", "mro": ["urllib3._request_methods.RequestMethods", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "headers"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3._request_methods.RequestMethods.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "headers"], "arg_types": ["urllib3._request_methods.RequestMethods", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RequestMethods", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_encode_url_methods": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "urllib3._request_methods.RequestMethods._encode_url_methods", "name": "_encode_url_methods", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}}}, "headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3._request_methods.RequestMethods.headers", "name": "headers", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "typing.Mapping"}}}, "request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 4], "arg_names": ["self", "method", "url", "body", "fields", "headers", "json", "urlopen_kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3._request_methods.RequestMethods.request", "name": "request", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 4], "arg_names": ["self", "method", "url", "body", "fields", "headers", "json", "urlopen_kw"], "arg_types": ["urllib3._request_methods.RequestMethods", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection._TYPE_BODY"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.filepost._TYPE_FIELDS"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "request of RequestMethods", "ret_type": "urllib3.response.BaseHTTPResponse", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "request_encode_body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 4], "arg_names": ["self", "method", "url", "fields", "headers", "encode_multipart", "multipart_boundary", "urlopen_kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3._request_methods.RequestMethods.request_encode_body", "name": "request_encode_body", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 4], "arg_names": ["self", "method", "url", "fields", "headers", "encode_multipart", "multipart_boundary", "urlopen_kw"], "arg_types": ["urllib3._request_methods.RequestMethods", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.filepost._TYPE_FIELDS"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "request_encode_body of RequestMethods", "ret_type": "urllib3.response.BaseHTTPResponse", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "request_encode_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 4], "arg_names": ["self", "method", "url", "fields", "headers", "urlopen_kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3._request_methods.RequestMethods.request_encode_url", "name": "request_encode_url", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 4], "arg_names": ["self", "method", "url", "fields", "headers", "urlopen_kw"], "arg_types": ["urllib3._request_methods.RequestMethods", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3._request_methods._TYPE_ENCODE_URL_FIELDS"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "request_encode_url of RequestMethods", "ret_type": "urllib3.response.BaseHTTPResponse", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "urlopen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 4], "arg_names": ["self", "method", "url", "body", "headers", "encode_multipart", "multipart_boundary", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3._request_methods.RequestMethods.urlopen", "name": "urlopen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 4], "arg_names": ["self", "method", "url", "body", "headers", "encode_multipart", "multipart_boundary", "kw"], "arg_types": ["urllib3._request_methods.RequestMethods", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection._TYPE_BODY"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "urlopen of RequestMethods", "ret_type": "urllib3.response.BaseHTTPResponse", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3._request_methods.RequestMethods.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3._request_methods.RequestMethods", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_TYPE_BODY": {".class": "SymbolTableNode", "cross_ref": "urllib3._base_connection._TYPE_BODY", "kind": "Gdef", "module_public": false}, "_TYPE_ENCODE_URL_FIELDS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "urllib3._request_methods._TYPE_ENCODE_URL_FIELDS", "line": 14, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"]}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"]}], "type_ref": "typing.Mapping"}]}}}, "_TYPE_FIELDS": {".class": "SymbolTableNode", "cross_ref": "urllib3.filepost._TYPE_FIELDS", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "urllib3._request_methods.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3._request_methods.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3._request_methods.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3._request_methods.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3._request_methods.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3._request_methods.__package__", "name": "__package__", "type": "builtins.str"}}, "_json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef", "module_public": false}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "encode_multipart_formdata": {".class": "SymbolTableNode", "cross_ref": "urllib3.filepost.encode_multipart_formdata", "kind": "Gdef", "module_public": false}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_public": false}, "urlencode": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urlencode", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\urllib3\\_request_methods.py"}