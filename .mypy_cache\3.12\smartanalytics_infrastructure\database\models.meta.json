{"data_mtime": 1759504552, "dep_lines": [15, 27, 28, 12, 13, 16, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["smartanalytics_utilities.config.settings", "sqlalchemy.orm", "sqlalchemy.sql", "datetime", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "enum", "functools", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "pydantic_settings", "pydantic_settings.main", "smartanalytics_utilities", "smartanalytics_utilities.config", "smartanalytics_utilities.config.base", "smartanalytics_utilities.config.database_config", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.orm._orm_constructors", "sqlalchemy.orm.base", "sqlalchemy.orm.decl_api", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.mapper", "sqlalchemy.orm.properties", "sqlalchemy.orm.query", "sqlalchemy.orm.relationships", "sqlalchemy.orm.util", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.elements", "sqlalchemy.sql.functions", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.sql.visitors", "sqlalchemy.util", "sqlalchemy.util.langhelpers"], "hash": "218f2fe23c7427e9cd56c56522e48a31db53c4da466ab73e5fd1cb3f34d06c0e", "id": "smartanalytics_infrastructure.database.models", "ignore_all": false, "interface_hash": "c07830600dd6d618119dc7b69c009b8599e1f03fbe198393083319ed30faea1e", "mtime": 1759502092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "layers\\infrastructure\\python\\smartanalytics_infrastructure\\database\\models.py", "plugin_data": null, "size": 9172, "suppressed": [], "version_id": "1.8.0"}