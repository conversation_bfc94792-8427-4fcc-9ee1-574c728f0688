{"data_mtime": 1759503866, "dep_lines": [32, 34, 37, 42, 13, 32, 33, 10, 12, 13, 14, 15, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 25, 25, 10, 20, 5, 5, 5, 20, 10, 5, 5, 20, 20, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.util", "sqlalchemy.util._has_cy", "sqlalchemy.engine._py_row", "sqlalchemy.engine.result", "collections.abc", "sqlalchemy.sql", "sqlalchemy.util", "__future__", "abc", "collections", "operator", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "sqlalchemy.exc", "sqlalchemy.sql.elements", "sqlalchemy.sql.operators", "sqlalchemy.sql.type_api", "sqlalchemy.util.deprecations", "sqlalchemy.util.langhelpers"], "hash": "94e3afac6a61205246057445908883ff9716bddc934ac3a5c831634c7e48b207", "id": "sqlalchemy.engine.row", "ignore_all": true, "interface_hash": "d04705a0b1dfeaa13ade37d34893ae6b8222b56b5e3f328197b3b8cb52489dc8", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\row.py", "plugin_data": null, "size": 12431, "suppressed": [], "version_id": "1.8.0"}