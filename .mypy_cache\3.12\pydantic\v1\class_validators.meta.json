{"data_mtime": 1759503828, "dep_lines": [8, 9, 10, 39, 40, 41, 1, 2, 3, 4, 5, 6, 37, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 25, 25, 25, 10, 5, 5, 5, 5, 5, 25, 5, 20, 20, 30, 30, 30, 30], "dependencies": ["pydantic.v1.errors", "pydantic.v1.typing", "pydantic.v1.utils", "pydantic.v1.config", "pydantic.v1.fields", "pydantic.v1.types", "warnings", "collections", "functools", "itertools", "types", "typing", "inspect", "builtins", "pyexpat.model", "pyexpat.errors", "_typeshed", "abc", "pydantic.v1.dataclasses", "pydantic.v1.main"], "hash": "50b39a21481850306c1cbec411511aadc33e52e6ca52082837c8526c3a27b051", "id": "pydantic.v1.class_validators", "ignore_all": true, "interface_hash": "226fdd92be6d9b34085306e160f1fc95bfd2ab3586e6ca0d7897e39a58f0cb25", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\v1\\class_validators.py", "plugin_data": null, "size": 14672, "suppressed": [], "version_id": "1.8.0"}