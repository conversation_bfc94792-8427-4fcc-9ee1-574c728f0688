{"data_mtime": 1759503867, "dep_lines": [18, 19, 13, 15, 17, 22, 3, 5, 6, 7, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 25, 5, 10, 10, 5, 5, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic_settings.sources.base", "pydantic_settings.sources.types", "pydantic.fields", "pydantic_settings.utils", "pydantic_settings.exceptions", "pydantic_settings.main", "__future__", "os", "warnings", "pathlib", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "_warnings", "abc", "pydantic", "pydantic._internal", "pydantic._internal._generate_schema", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.main", "re", "types"], "hash": "24b3088f7555c29f3a7e81933fc7dbeb359d76662910b06ef792ddce86e7b30f", "id": "pydantic_settings.sources.providers.secrets", "ignore_all": true, "interface_hash": "e81bd57b1c8ff27677937ad48343e1a901d59b338f1f7ea268d262bb63dc6d12", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic_settings\\sources\\providers\\secrets.py", "plugin_data": null, "size": 4303, "suppressed": [], "version_id": "1.8.0"}