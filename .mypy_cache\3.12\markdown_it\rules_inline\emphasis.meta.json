{"data_mtime": 1759503829, "dep_lines": [5, 3, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 20, 20, 30, 30, 30, 30], "dependencies": ["markdown_it.rules_inline.state_inline", "__future__", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "markdown_it.ruler", "markdown_it.rules_inline.strikethrough", "typing"], "hash": "eda0cb671d0995e92ebdbbb7b845130e1269d346743e3ea0e02dfe54b848f02a", "id": "markdown_it.rules_inline.emphasis", "ignore_all": true, "interface_hash": "a2da4654d239be67a2b7445e665387672c8d52d863cd4febaf142e9e95edf4ed", "mtime": 1757092234, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\markdown_it\\rules_inline\\emphasis.py", "plugin_data": null, "size": 3123, "suppressed": [], "version_id": "1.8.0"}