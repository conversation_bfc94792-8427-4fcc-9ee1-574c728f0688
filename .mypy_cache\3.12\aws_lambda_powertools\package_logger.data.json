{".class": "MypyFile", "_fullname": "aws_lambda_powertools.package_logger", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aws_lambda_powertools.package_logger.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aws_lambda_powertools.package_logger.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aws_lambda_powertools.package_logger.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aws_lambda_powertools.package_logger.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aws_lambda_powertools.package_logger.__package__", "name": "__package__", "type": "builtins.str"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "powertools_debug_is_set": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.shared.functions.powertools_debug_is_set", "kind": "Gdef"}, "set_package_logger": {".class": "SymbolTableNode", "cross_ref": "aws_lambda_powertools.logging.logger.set_package_logger", "kind": "Gdef"}, "set_package_logger_handler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["stream"], "dataclass_transform_spec": null, "flags": [], "fullname": "aws_lambda_powertools.package_logger.set_package_logger_handler", "name": "set_package_logger_handler", "type": null}}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\aws_lambda_powertools\\package_logger.py"}