{"data_mtime": 1759503828, "dep_lines": [15, 16, 19, 10, 11, 12, 13, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 25, 10, 10, 5, 5, 5, 20, 20, 30, 30, 30, 30, 30], "dependencies": ["pydantic.v1.errors", "pydantic.v1.utils", "pydantic.v1.typing", "math", "re", "colorsys", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "_collections_abc", "_typeshed", "abc", "types", "typing_extensions"], "hash": "8990012d8a7a395a28d801643fd229ae2ff049cebe2a496ef18ba149aaed7798", "id": "pydantic.v1.color", "ignore_all": true, "interface_hash": "d6bd7406f9ef82207091d34db5cbea20d7d0db1bbacf03d629f0ea086afa910f", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\v1\\color.py", "plugin_data": null, "size": 16844, "suppressed": [], "version_id": "1.8.0"}