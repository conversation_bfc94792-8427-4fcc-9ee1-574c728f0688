{".class": "MypyFile", "_fullname": "sqlalchemy.sql", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Alias": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.<PERSON><PERSON>", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BaseDDLElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.ddl.BaseDDLElement", "kind": "Gdef"}, "COLLECT_CARTESIAN_PRODUCTS": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.COLLECT_CARTESIAN_PRODUCTS", "kind": "Gdef"}, "ClauseElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ClauseElement", "kind": "Gdef"}, "ClauseVisitor": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.visitors.ClauseVisitor", "kind": "Gdef"}, "ColumnCollection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.ColumnCollection", "kind": "Gdef"}, "ColumnElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ColumnElement", "kind": "Gdef"}, "ColumnExpressionArgument": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing.ColumnExpressionArgument", "kind": "Gdef"}, "CompoundSelect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.CompoundSelect", "kind": "Gdef"}, "DDL": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.ddl.DDL", "kind": "Gdef"}, "DDLElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.ddl.DDLElement", "kind": "Gdef"}, "Delete": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.dml.Delete", "kind": "Gdef"}, "Executable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.Executable", "kind": "Gdef"}, "ExecutableDDLElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.ddl.ExecutableDDLElement", "kind": "Gdef"}, "FROM_LINTING": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.FROM_LINTING", "kind": "Gdef"}, "False_": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.False_", "kind": "Gdef"}, "FromClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.FromClause", "kind": "Gdef"}, "Insert": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.dml.Insert", "kind": "Gdef"}, "Join": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.Join", "kind": "Gdef"}, "LABEL_STYLE_DEFAULT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.LABEL_STYLE_DEFAULT", "kind": "Gdef"}, "LABEL_STYLE_DISAMBIGUATE_ONLY": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.LABEL_STYLE_DISAMBIGUATE_ONLY", "kind": "Gdef"}, "LABEL_STYLE_NONE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.LABEL_STYLE_NONE", "kind": "Gdef"}, "LABEL_STYLE_TABLENAME_PLUS_COL": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.LABEL_STYLE_TABLENAME_PLUS_COL", "kind": "Gdef"}, "LambdaElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.lambdas.LambdaElement", "kind": "Gdef"}, "NO_LINTING": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.NO_LINTING", "kind": "Gdef"}, "NotNullable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing.NotNullable", "kind": "Gdef"}, "Nullable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing.Nullable", "kind": "Gdef"}, "SQLColumnExpression": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.SQLColumnExpression", "kind": "Gdef"}, "Select": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.Select", "kind": "Gdef"}, "SelectLabelStyle": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.SelectLabelStyle", "kind": "Gdef"}, "Selectable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.Selectable", "kind": "Gdef"}, "StatementLambdaElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.lambdas.StatementLambdaElement", "kind": "Gdef"}, "Subquery": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.Subquery", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TableClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.TableClause", "kind": "Gdef"}, "TableSample": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.TableSample", "kind": "Gdef"}, "True_": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.True_", "kind": "Gdef"}, "Update": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.dml.Update", "kind": "Gdef"}, "Values": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.Values", "kind": "Gdef"}, "WARN_LINTING": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.WARN_LINTING", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.__file__", "name": "__file__", "type": "builtins.str"}}, "__go": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["lcls"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.__go", "name": "__go", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["lcls"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__go", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}, "alias": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.alias", "kind": "Gdef"}, "all_": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.all_", "kind": "Gdef"}, "and_": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.and_", "kind": "Gdef"}, "any_": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.any_", "kind": "Gdef"}, "asc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.asc", "kind": "Gdef"}, "between": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.between", "kind": "Gdef"}, "bindparam": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.bindparam", "kind": "Gdef"}, "case": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.case", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.cast", "kind": "Gdef"}, "collate": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.collate", "kind": "Gdef"}, "column": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.column", "kind": "Gdef"}, "cte": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.cte", "kind": "Gdef"}, "delete": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._dml_constructors.delete", "kind": "Gdef"}, "desc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.desc", "kind": "Gdef"}, "distinct": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.distinct", "kind": "Gdef"}, "except_": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.except_", "kind": "Gdef"}, "except_all": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.except_all", "kind": "Gdef"}, "exists": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.exists", "kind": "Gdef"}, "extract": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.extract", "kind": "Gdef"}, "false": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.false", "kind": "Gdef"}, "func": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.functions.func", "kind": "Gdef"}, "funcfilter": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.funcfilter", "kind": "Gdef"}, "insert": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._dml_constructors.insert", "kind": "Gdef"}, "intersect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.intersect", "kind": "Gdef"}, "intersect_all": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.intersect_all", "kind": "Gdef"}, "join": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.join", "kind": "Gdef"}, "label": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.label", "kind": "Gdef"}, "lambda_stmt": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.lambdas.lambda_stmt", "kind": "Gdef"}, "lateral": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.lateral", "kind": "Gdef"}, "literal": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.literal", "kind": "Gdef"}, "literal_column": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.literal_column", "kind": "Gdef"}, "modifier": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.functions.modifier", "kind": "Gdef"}, "not_": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.not_", "kind": "Gdef"}, "null": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.null", "kind": "Gdef"}, "nulls_first": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.nulls_first", "kind": "Gdef"}, "nulls_last": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.nulls_last", "kind": "Gdef"}, "nullsfirst": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.expression.nullsfirst", "kind": "Gdef"}, "nullslast": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.expression.nullslast", "kind": "Gdef"}, "or_": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.or_", "kind": "Gdef"}, "outerjoin": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.outerjoin", "kind": "Gdef"}, "outparam": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.outparam", "kind": "Gdef"}, "over": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.over", "kind": "Gdef"}, "quoted_name": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.quoted_name", "kind": "Gdef"}, "select": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.select", "kind": "Gdef"}, "table": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.table", "kind": "Gdef"}, "tablesample": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.tablesample", "kind": "Gdef"}, "text": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.text", "kind": "Gdef"}, "true": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.true", "kind": "Gdef"}, "try_cast": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.try_cast", "kind": "Gdef"}, "tuple_": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.tuple_", "kind": "Gdef"}, "type_coerce": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.type_coerce", "kind": "Gdef"}, "union": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.union", "kind": "Gdef"}, "union_all": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.union_all", "kind": "Gdef"}, "update": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._dml_constructors.update", "kind": "Gdef"}, "values": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.values", "kind": "Gdef"}, "within_group": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.within_group", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\__init__.py"}