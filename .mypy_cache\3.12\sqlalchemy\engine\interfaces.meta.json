{"data_mtime": 1759504547, "dep_lines": [37, 42, 43, 49, 51, 52, 53, 64, 66, 70, 71, 73, 33, 34, 35, 56, 57, 10, 12, 13, 33, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 25, 25, 25, 25, 25, 25, 25, 25, 25, 5, 5, 5, 25, 25, 5, 5, 5, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.compiler", "sqlalchemy.util.concurrency", "sqlalchemy.util.typing", "sqlalchemy.engine.base", "sqlalchemy.engine.cursor", "sqlalchemy.engine.url", "sqlalchemy.connectors.asyncio", "sqlalchemy.sql.elements", "sqlalchemy.sql.schema", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.type_api", "sqlalchemy.util.langhelpers", "sqlalchemy.util", "sqlalchemy.event", "sqlalchemy.pool", "sqlalchemy.exc", "sqlalchemy.sql", "__future__", "enum", "typing", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "sqlalchemy.connectors", "sqlalchemy.engine.result", "sqlalchemy.event.base", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.pool.base", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util._py_collections"], "hash": "b474d92652f91ce632458df641ffc297204dd2a98b4ef0f26923ccbf83f8b4e9", "id": "sqlalchemy.engine.interfaces", "ignore_all": true, "interface_hash": "30d78123ee2574aaa76cbb1683813ba3f80e26d25a8a49e26c0f6037f03f4461", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\interfaces.py", "plugin_data": null, "size": 118583, "suppressed": [], "version_id": "1.8.0"}