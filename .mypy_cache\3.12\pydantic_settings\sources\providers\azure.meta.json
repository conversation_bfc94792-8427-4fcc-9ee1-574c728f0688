{"data_mtime": **********, "dep_lines": [10, 5, 8, 17, 3, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 13, 14, 15], "dep_prios": [5, 5, 5, 25, 5, 5, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30, 25, 25, 25], "dependencies": ["pydantic_settings.sources.providers.env", "collections.abc", "pydantic.fields", "pydantic_settings.main", "__future__", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.main", "pydantic_settings.sources.base"], "hash": "8d1d7c842a402e30e710e6d619e82f1ece3f0dae63ecfa0122dda405b1d831a8", "id": "pydantic_settings.sources.providers.azure", "ignore_all": true, "interface_hash": "7daa332c5b7f923fcee0a9c28a7426b95ca08109f1994ca3fe2ccefba28ce4ce", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic_settings\\sources\\providers\\azure.py", "plugin_data": null, "size": 4108, "suppressed": ["azure.core.credentials", "azure.core.exceptions", "azure.keyvault.secrets"], "version_id": "1.8.0"}