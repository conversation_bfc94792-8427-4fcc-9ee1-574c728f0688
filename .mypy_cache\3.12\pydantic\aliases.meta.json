{"data_mtime": 1759504508, "dep_lines": [10, 10, 3, 5, 6, 8, 1, 1, 1, 1], "dep_prios": [10, 20, 5, 10, 5, 5, 5, 20, 20, 30], "dependencies": ["pydantic._internal._internal_dataclass", "pydantic._internal", "__future__", "dataclasses", "typing", "pydantic_core", "builtins", "pyexpat.model", "pyexpat.errors", "abc"], "hash": "be1087ca84969d7f8427ec166f9aa3e31c91b2c8065a74d07f3429e06499f6e8", "id": "pydantic.aliases", "ignore_all": true, "interface_hash": "fde2a1b0b4b68baff19f390e5b6872553174262ef91a98a86f75599955d3fc88", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\aliases.py", "plugin_data": null, "size": 4937, "suppressed": [], "version_id": "1.8.0"}