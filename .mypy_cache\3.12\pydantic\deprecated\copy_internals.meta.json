{"data_mtime": 1759504514, "dep_lines": [10, 10, 10, 10, 1, 3, 4, 5, 8, 17, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 20, 5, 5, 5, 5, 10, 25, 5, 20, 20, 30, 30], "dependencies": ["pydantic._internal._model_construction", "pydantic._internal._typing_extra", "pydantic._internal._utils", "pydantic._internal", "__future__", "typing", "copy", "enum", "typing_extensions", "pydantic", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "pydantic.main"], "hash": "2aed0b1cb114d167282089cd1e5b3b3e3b81be92c54d0e14bacefb8d0dd88b4f", "id": "pydantic.deprecated.copy_internals", "ignore_all": true, "interface_hash": "a0909e04e78e823a38bb29ec20a1bd4c7514286876aa5fb2030fb17e27e6f1a1", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\deprecated\\copy_internals.py", "plugin_data": null, "size": 7616, "suppressed": [], "version_id": "1.8.0"}