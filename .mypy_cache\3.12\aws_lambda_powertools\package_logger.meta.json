{"data_mtime": 1759504512, "dep_lines": [3, 4, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 20, 20, 30, 30], "dependencies": ["aws_lambda_powertools.logging.logger", "aws_lambda_powertools.shared.functions", "logging", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "typing"], "hash": "436417d495c375cf8f89e924d6bd7da8420350c3768b86592bb307757c4716de", "id": "aws_lambda_powertools.package_logger", "ignore_all": true, "interface_hash": "52d9bb456e5e954edc5af6110b2a902ff01afe65ced4d6f553de32b6d8e0537f", "mtime": 1757091873, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\aws_lambda_powertools\\package_logger.py", "plugin_data": null, "size": 767, "suppressed": [], "version_id": "1.8.0"}