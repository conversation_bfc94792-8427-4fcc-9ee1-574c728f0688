{"data_mtime": 1759503829, "dep_lines": [1, 10, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 20, 20, 30, 30], "dependencies": ["dateutil.parser._parser", "dateutil.parser.isop<PERSON>er", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "typing"], "hash": "2cded9b6d1808d5527dead915b69ade83459d9f39ad082750ec0c1443f6e2903", "id": "dateutil.parser", "ignore_all": true, "interface_hash": "58263bbe215603dff303f608a9a587ec442dc6be1597be15ef6a28f2db6c5595", "mtime": 1759341510, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\dateutil-stubs\\parser\\__init__.pyi", "plugin_data": null, "size": 438, "suppressed": [], "version_id": "1.8.0"}