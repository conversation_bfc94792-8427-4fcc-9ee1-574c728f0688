{"data_mtime": 1759503836, "dep_lines": [12, 8, 11, 3, 5, 6, 8, 9, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 5, 20, 5, 5, 20, 20, 30, 30], "dependencies": ["pydantic._internal._utils", "pydantic_core.core_schema", "pydantic.errors", "__future__", "inspect", "typing", "pydantic_core", "typing_extensions", "builtins", "pyexpat.model", "pyexpat.errors", "_typeshed", "abc"], "hash": "b5f75f76940a6384765c23b0a876d97a840cbabe95362811adf86e757047c77f", "id": "pydantic._internal._decorators_v1", "ignore_all": true, "interface_hash": "5d5c476afb384e2ea302c4261ab0faa654255d37899d4f4a3e528ce96a5daabf", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\_internal\\_decorators_v1.py", "plugin_data": null, "size": 6185, "suppressed": [], "version_id": "1.8.0"}