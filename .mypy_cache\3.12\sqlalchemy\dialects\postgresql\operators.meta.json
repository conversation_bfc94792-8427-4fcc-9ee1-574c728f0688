{"data_mtime": 1759503867, "dep_lines": [8, 8, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 20, 5, 20, 20, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.operators", "sqlalchemy.sql", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "sqlalchemy.sql.type_api", "sqlalchemy.sql.visitors", "typing"], "hash": "5366eb8bc75fd48ba6a6e0773c6ac313d93637fff25f6109b4f98a7f517bf9b5", "id": "sqlalchemy.dialects.postgresql.operators", "ignore_all": true, "interface_hash": "c4ab74f8315ab8ec6d90b56ba09d245a68cffafdf80492a1d7119f47c8cdddf3", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\operators.py", "plugin_data": null, "size": 2937, "suppressed": [], "version_id": "1.8.0"}