{"data_mtime": 1759503866, "dep_lines": [13, 14, 49, 73, 77, 83, 160, 9, 10, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 30, 30], "dependencies": ["sqlalchemy.util.preloaded", "sqlalchemy.util._collections", "sqlalchemy.util.compat", "sqlalchemy.util.concurrency", "sqlalchemy.util.deprecations", "sqlalchemy.util.langhelpers", "sqlalchemy.util.typing", "collections", "functools", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "typing"], "hash": "0df716749faa8cffcad33ca4eab7e7bb132183dcf294e0e257a6fa1cf991f742", "id": "sqlalchemy.util", "ignore_all": true, "interface_hash": "fddbf7b306c7de6612011072646e35d7e8f0e7787cfba413bd6d002a482030be", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\__init__.py", "plugin_data": null, "size": 8474, "suppressed": [], "version_id": "1.8.0"}