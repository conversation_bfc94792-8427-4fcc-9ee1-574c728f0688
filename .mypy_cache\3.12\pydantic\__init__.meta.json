{"data_mtime": 1759503836, "dep_lines": [62, 63, 64, 5, 6, 12, 20, 21, 22, 23, 24, 25, 26, 33, 44, 45, 46, 47, 48, 49, 50, 65, 1, 2, 3, 11, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 25, 25, 5, 5, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 10, 5, 5, 25, 5, 20, 20, 30, 30, 30], "dependencies": ["pydantic.deprecated.class_validators", "pydantic.deprecated.config", "pydantic.deprecated.tools", "pydantic._migration", "pydantic.version", "pydantic_core.core_schema", "pydantic.dataclasses", "pydantic.aliases", "pydantic.annotated_handlers", "pydantic.config", "pydantic.errors", "pydantic.fields", "pydantic.functional_serializers", "pydantic.functional_validators", "pydantic.json_schema", "pydantic.main", "pydantic.networks", "pydantic.type_adapter", "pydantic.types", "pydantic.validate_call_decorator", "pydantic.warnings", "pydantic.root_model", "typing", "importlib", "warnings", "pydantic_core", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "pydantic_core._pydantic_core", "zoneinfo"], "hash": "0f7ffed1a44fa00179107e13e093d53982cd11cf8379a09c0ede94cc88cffc3d", "id": "pydantic", "ignore_all": true, "interface_hash": "134282d5e93c60c2e13cbe3a382a8df384a3696e53e4278206a4e01b4c57b970", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\__init__.py", "plugin_data": null, "size": 15395, "suppressed": [], "version_id": "1.8.0"}