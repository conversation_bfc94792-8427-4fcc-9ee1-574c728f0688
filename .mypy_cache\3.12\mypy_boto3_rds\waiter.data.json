{".class": "MypyFile", "_fullname": "mypy_boto3_rds.waiter", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "DBClusterAvailableWaiter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.waiter.Waiter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.waiter.DBClusterAvailableWaiter", "name": "DBClusterAvailableWaiter", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.waiter.DBClusterAvailableWaiter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.waiter", "mro": ["mypy_boto3_rds.waiter.DBClusterAvailableWaiter", "botocore.waiter.Waiter", "builtins.object"], "names": {".class": "SymbolTable", "wait": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.waiter.DBClusterAvailableWaiter.wait", "name": "wait", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.waiter.DBClusterAvailableWaiter", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBClustersMessageWaitTypeDef", "items": [["DBClusterIdentifier", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["MaxRecords", "builtins.int"], ["<PERSON><PERSON>", "builtins.str"], ["IncludeShared", "builtins.bool"], ["WaiterConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.WaiterConfigTypeDef"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wait of DBClusterAvailableWaiter", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.waiter.DBClusterAvailableWaiter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.waiter.DBClusterAvailableWaiter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DBClusterDeletedWaiter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.waiter.Waiter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.waiter.DBClusterDeletedWaiter", "name": "DBClusterDeletedWaiter", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.waiter.DBClusterDeletedWaiter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.waiter", "mro": ["mypy_boto3_rds.waiter.DBClusterDeletedWaiter", "botocore.waiter.Waiter", "builtins.object"], "names": {".class": "SymbolTable", "wait": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.waiter.DBClusterDeletedWaiter.wait", "name": "wait", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.waiter.DBClusterDeletedWaiter", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBClustersMessageWaitExtraTypeDef", "items": [["DBClusterIdentifier", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["MaxRecords", "builtins.int"], ["<PERSON><PERSON>", "builtins.str"], ["IncludeShared", "builtins.bool"], ["WaiterConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.WaiterConfigTypeDef"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wait of DBClusterDeletedWaiter", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.waiter.DBClusterDeletedWaiter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.waiter.DBClusterDeletedWaiter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DBClusterSnapshotAvailableWaiter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.waiter.Waiter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.waiter.DBClusterSnapshotAvailableWaiter", "name": "DBClusterSnapshotAvailableWaiter", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.waiter.DBClusterSnapshotAvailableWaiter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.waiter", "mro": ["mypy_boto3_rds.waiter.DBClusterSnapshotAvailableWaiter", "botocore.waiter.Waiter", "builtins.object"], "names": {".class": "SymbolTable", "wait": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.waiter.DBClusterSnapshotAvailableWaiter.wait", "name": "wait", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.waiter.DBClusterSnapshotAvailableWaiter", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBClusterSnapshotsMessageWaitTypeDef", "items": [["DBClusterIdentifier", "builtins.str"], ["DBClusterSnapshotIdentifier", "builtins.str"], ["SnapshotType", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["MaxRecords", "builtins.int"], ["<PERSON><PERSON>", "builtins.str"], ["IncludeShared", "builtins.bool"], ["IncludePublic", "builtins.bool"], ["DbClusterResourceId", "builtins.str"], ["WaiterConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.WaiterConfigTypeDef"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wait of DBClusterSnapshotAvailableWaiter", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.waiter.DBClusterSnapshotAvailableWaiter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.waiter.DBClusterSnapshotAvailableWaiter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DBClusterSnapshotDeletedWaiter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.waiter.Waiter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.waiter.DBClusterSnapshotDeletedWaiter", "name": "DBClusterSnapshotDeletedWaiter", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.waiter.DBClusterSnapshotDeletedWaiter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.waiter", "mro": ["mypy_boto3_rds.waiter.DBClusterSnapshotDeletedWaiter", "botocore.waiter.Waiter", "builtins.object"], "names": {".class": "SymbolTable", "wait": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.waiter.DBClusterSnapshotDeletedWaiter.wait", "name": "wait", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.waiter.DBClusterSnapshotDeletedWaiter", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBClusterSnapshotsMessageWaitExtraTypeDef", "items": [["DBClusterIdentifier", "builtins.str"], ["DBClusterSnapshotIdentifier", "builtins.str"], ["SnapshotType", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["MaxRecords", "builtins.int"], ["<PERSON><PERSON>", "builtins.str"], ["IncludeShared", "builtins.bool"], ["IncludePublic", "builtins.bool"], ["DbClusterResourceId", "builtins.str"], ["WaiterConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.WaiterConfigTypeDef"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wait of DBClusterSnapshotDeletedWaiter", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.waiter.DBClusterSnapshotDeletedWaiter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.waiter.DBClusterSnapshotDeletedWaiter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DBInstanceAvailableWaiter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.waiter.Waiter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.waiter.DBInstanceAvailableWaiter", "name": "DBInstanceAvailableWaiter", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.waiter.DBInstanceAvailableWaiter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.waiter", "mro": ["mypy_boto3_rds.waiter.DBInstanceAvailableWaiter", "botocore.waiter.Waiter", "builtins.object"], "names": {".class": "SymbolTable", "wait": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.waiter.DBInstanceAvailableWaiter.wait", "name": "wait", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.waiter.DBInstanceAvailableWaiter", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBInstancesMessageWaitTypeDef", "items": [["DBInstanceIdentifier", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["MaxRecords", "builtins.int"], ["<PERSON><PERSON>", "builtins.str"], ["WaiterConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.WaiterConfigTypeDef"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wait of DBInstanceAvailableWaiter", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.waiter.DBInstanceAvailableWaiter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.waiter.DBInstanceAvailableWaiter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DBInstanceDeletedWaiter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.waiter.Waiter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.waiter.DBInstanceDeletedWaiter", "name": "DBInstanceDeletedWaiter", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.waiter.DBInstanceDeletedWaiter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.waiter", "mro": ["mypy_boto3_rds.waiter.DBInstanceDeletedWaiter", "botocore.waiter.Waiter", "builtins.object"], "names": {".class": "SymbolTable", "wait": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.waiter.DBInstanceDeletedWaiter.wait", "name": "wait", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.waiter.DBInstanceDeletedWaiter", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBInstancesMessageWaitExtraTypeDef", "items": [["DBInstanceIdentifier", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["MaxRecords", "builtins.int"], ["<PERSON><PERSON>", "builtins.str"], ["WaiterConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.WaiterConfigTypeDef"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wait of DBInstanceDeletedWaiter", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.waiter.DBInstanceDeletedWaiter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.waiter.DBInstanceDeletedWaiter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DBSnapshotAvailableWaiter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.waiter.Waiter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.waiter.DBSnapshotAvailableWaiter", "name": "DBSnapshotAvailableWaiter", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.waiter.DBSnapshotAvailableWaiter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.waiter", "mro": ["mypy_boto3_rds.waiter.DBSnapshotAvailableWaiter", "botocore.waiter.Waiter", "builtins.object"], "names": {".class": "SymbolTable", "wait": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.waiter.DBSnapshotAvailableWaiter.wait", "name": "wait", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.waiter.DBSnapshotAvailableWaiter", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBSnapshotsMessageWaitTypeDef", "items": [["DBInstanceIdentifier", "builtins.str"], ["DBSnapshotIdentifier", "builtins.str"], ["SnapshotType", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["MaxRecords", "builtins.int"], ["<PERSON><PERSON>", "builtins.str"], ["IncludeShared", "builtins.bool"], ["IncludePublic", "builtins.bool"], ["DbiResourceId", "builtins.str"], ["WaiterConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.WaiterConfigTypeDef"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wait of DBSnapshotAvailableWaiter", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.waiter.DBSnapshotAvailableWaiter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.waiter.DBSnapshotAvailableWaiter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DBSnapshotCompletedWaiter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.waiter.Waiter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.waiter.DBSnapshotCompletedWaiter", "name": "DBSnapshotCompletedWaiter", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.waiter.DBSnapshotCompletedWaiter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.waiter", "mro": ["mypy_boto3_rds.waiter.DBSnapshotCompletedWaiter", "botocore.waiter.Waiter", "builtins.object"], "names": {".class": "SymbolTable", "wait": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.waiter.DBSnapshotCompletedWaiter.wait", "name": "wait", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.waiter.DBSnapshotCompletedWaiter", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBSnapshotsMessageWaitExtraExtraTypeDef", "items": [["DBInstanceIdentifier", "builtins.str"], ["DBSnapshotIdentifier", "builtins.str"], ["SnapshotType", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["MaxRecords", "builtins.int"], ["<PERSON><PERSON>", "builtins.str"], ["IncludeShared", "builtins.bool"], ["IncludePublic", "builtins.bool"], ["DbiResourceId", "builtins.str"], ["WaiterConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.WaiterConfigTypeDef"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wait of DBSnapshotCompletedWaiter", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.waiter.DBSnapshotCompletedWaiter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.waiter.DBSnapshotCompletedWaiter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DBSnapshotDeletedWaiter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.waiter.Waiter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.waiter.DBSnapshotDeletedWaiter", "name": "DBSnapshotDeletedWaiter", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.waiter.DBSnapshotDeletedWaiter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.waiter", "mro": ["mypy_boto3_rds.waiter.DBSnapshotDeletedWaiter", "botocore.waiter.Waiter", "builtins.object"], "names": {".class": "SymbolTable", "wait": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.waiter.DBSnapshotDeletedWaiter.wait", "name": "wait", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.waiter.DBSnapshotDeletedWaiter", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeDBSnapshotsMessageWaitExtraTypeDef", "items": [["DBInstanceIdentifier", "builtins.str"], ["DBSnapshotIdentifier", "builtins.str"], ["SnapshotType", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["MaxRecords", "builtins.int"], ["<PERSON><PERSON>", "builtins.str"], ["IncludeShared", "builtins.bool"], ["IncludePublic", "builtins.bool"], ["DbiResourceId", "builtins.str"], ["WaiterConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.WaiterConfigTypeDef"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wait of DBSnapshotDeletedWaiter", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.waiter.DBSnapshotDeletedWaiter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.waiter.DBSnapshotDeletedWaiter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DescribeDBClusterSnapshotsMessageWaitExtraTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBClusterSnapshotsMessageWaitExtraTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBClusterSnapshotsMessageWaitTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBClusterSnapshotsMessageWaitTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBClustersMessageWaitExtraTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBClustersMessageWaitExtraTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBClustersMessageWaitTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBClustersMessageWaitTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBInstancesMessageWaitExtraTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBInstancesMessageWaitExtraTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBInstancesMessageWaitTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBInstancesMessageWaitTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBSnapshotsMessageWaitExtraExtraTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBSnapshotsMessageWaitExtraExtraTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBSnapshotsMessageWaitExtraTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBSnapshotsMessageWaitExtraTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeDBSnapshotsMessageWaitTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeDBSnapshotsMessageWaitTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeTenantDatabasesMessageWaitExtraTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeTenantDatabasesMessageWaitExtraTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DescribeTenantDatabasesMessageWaitTypeDef": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_rds.type_defs.DescribeTenantDatabasesMessageWaitTypeDef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TenantDatabaseAvailableWaiter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.waiter.Waiter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.waiter.TenantDatabaseAvailableWaiter", "name": "TenantDatabaseAvailableWaiter", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.waiter.TenantDatabaseAvailableWaiter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.waiter", "mro": ["mypy_boto3_rds.waiter.TenantDatabaseAvailableWaiter", "botocore.waiter.Waiter", "builtins.object"], "names": {".class": "SymbolTable", "wait": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.waiter.TenantDatabaseAvailableWaiter.wait", "name": "wait", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.waiter.TenantDatabaseAvailableWaiter", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeTenantDatabasesMessageWaitTypeDef", "items": [["DBInstanceIdentifier", "builtins.str"], ["TenantDBName", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["<PERSON><PERSON>", "builtins.str"], ["MaxRecords", "builtins.int"], ["WaiterConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.WaiterConfigTypeDef"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wait of TenantDatabaseAvailableWaiter", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.waiter.TenantDatabaseAvailableWaiter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.waiter.TenantDatabaseAvailableWaiter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TenantDatabaseDeletedWaiter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["botocore.waiter.Waiter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mypy_boto3_rds.waiter.TenantDatabaseDeletedWaiter", "name": "TenantDatabaseDeletedWaiter", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "mypy_boto3_rds.waiter.TenantDatabaseDeletedWaiter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mypy_boto3_rds.waiter", "mro": ["mypy_boto3_rds.waiter.TenantDatabaseDeletedWaiter", "botocore.waiter.Waiter", "builtins.object"], "names": {".class": "SymbolTable", "wait": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "mypy_boto3_rds.waiter.TenantDatabaseDeletedWaiter.wait", "name": "wait", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["mypy_boto3_rds.waiter.TenantDatabaseDeletedWaiter", {".class": "TypedDictType", "fallback": "mypy_boto3_rds.type_defs.DescribeTenantDatabasesMessageWaitExtraTypeDef", "items": [["DBInstanceIdentifier", "builtins.str"], ["TenantDBName", "builtins.str"], ["Filters", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.FilterTypeDef"}], "type_ref": "typing.Sequence"}], ["<PERSON><PERSON>", "builtins.str"], ["MaxRecords", "builtins.int"], ["WaiterConfig", {".class": "TypeAliasType", "args": [], "type_ref": "mypy_boto3_rds.type_defs.WaiterConfigTypeDef"}]], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wait of TenantDatabaseDeletedWaiter", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": true, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mypy_boto3_rds.waiter.TenantDatabaseDeletedWaiter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mypy_boto3_rds.waiter.TenantDatabaseDeletedWaiter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Unpack": {".class": "SymbolTableNode", "cross_ref": "typing.Unpack", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Waiter": {".class": "SymbolTableNode", "cross_ref": "botocore.waiter.Waiter", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "mypy_boto3_rds.waiter.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mypy_boto3_rds.waiter.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mypy_boto3_rds.waiter.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mypy_boto3_rds.waiter.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mypy_boto3_rds.waiter.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mypy_boto3_rds.waiter.__package__", "name": "__package__", "type": "builtins.str"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\mypy_boto3_rds\\waiter.pyi"}