{"data_mtime": 1759504546, "dep_lines": [22, 7, 9, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 5, 10, 5, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.engine.result", "__future__", "operator", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "sqlalchemy.sql", "sqlalchemy.sql.elements", "sqlalchemy.sql.operators", "sqlalchemy.sql.type_api", "sqlalchemy.util", "sqlalchemy.util.langhelpers"], "hash": "ca5443935cc4b12ed7811b95a3823690d02b29e6ebff5377c0671b0cb6c7fb11", "id": "sqlalchemy.engine._py_row", "ignore_all": true, "interface_hash": "4534e0a9eb697cb6900cf543ae3a5767cd5e7d09819763d6ead7d82a1e818166", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\_py_row.py", "plugin_data": null, "size": 3915, "suppressed": [], "version_id": "1.8.0"}