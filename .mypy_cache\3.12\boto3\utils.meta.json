{"data_mtime": 1759504551, "dep_lines": [9, 10, 11, 7, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 20, 20, 30, 30], "dependencies": ["botocore.model", "botocore.session", "botocore.waiter", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "botocore"], "hash": "dafb46b8b47e7e77f350da1e36f7046dbd0b6fe0c6119f81439055f7117cbd28", "id": "boto3.utils", "ignore_all": true, "interface_hash": "e115c4334f0ffc69d8907865b802258bc2767251d6e9e15046ff676ae1c66807", "mtime": 1757092242, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\boto3-stubs\\utils.pyi", "plugin_data": null, "size": 841, "suppressed": [], "version_id": "1.8.0"}