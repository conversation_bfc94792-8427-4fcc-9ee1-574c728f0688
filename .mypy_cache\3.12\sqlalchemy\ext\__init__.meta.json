{"data_mtime": 1759504548, "dep_lines": [8, 8, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 20, 5, 20, 20, 30, 30, 30], "dependencies": ["sqlalchemy.util", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "sqlalchemy.util.preloaded", "typing"], "hash": "a19d79a8235cb08e93352ec63abd414e0d247b95efb8a0416f0c43a5b50165f2", "id": "sqlalchemy.ext", "ignore_all": true, "interface_hash": "ed41bbd3ecdd3fd68f0ca2e321c8e446dffa71bdac1da2839dc1774ea3a09440", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\__init__.py", "plugin_data": null, "size": 333, "suppressed": [], "version_id": "1.8.0"}