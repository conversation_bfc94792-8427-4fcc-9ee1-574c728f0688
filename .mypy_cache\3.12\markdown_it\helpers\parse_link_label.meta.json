{"data_mtime": 1759503829, "dep_lines": [9, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 20, 20, 30, 30, 30, 30], "dependencies": ["markdown_it.rules_inline", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "markdown_it.ruler", "markdown_it.rules_inline.state_inline", "typing"], "hash": "3c81c6e99326dc1530d1ada6d7b9421aa36b977bdacfdd75b6ea06be2583dc8e", "id": "markdown_it.helpers.parse_link_label", "ignore_all": true, "interface_hash": "8eecaebb07081f14366f6f9d1d98ab402a3d8632ad72fb4f52105872743f9a2c", "mtime": 1757092234, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\markdown_it\\helpers\\parse_link_label.py", "plugin_data": null, "size": 1037, "suppressed": [], "version_id": "1.8.0"}