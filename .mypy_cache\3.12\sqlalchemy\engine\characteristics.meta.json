{"data_mtime": 1759503866, "dep_lines": [15, 16, 7, 9, 10, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 25, 5, 10, 5, 5, 20, 20, 30, 30, 30], "dependencies": ["sqlalchemy.engine.base", "sqlalchemy.engine.interfaces", "__future__", "abc", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection"], "hash": "99557df342a703257fdbf08bfd677e52357d28010d6386f8365ee9baae555738", "id": "sqlalchemy.engine.characteristics", "ignore_all": true, "interface_hash": "31825504b53a805a3c6b016dad075d1961b7c372ec17025444e15d1f20c21e89", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\characteristics.py", "plugin_data": null, "size": 4920, "suppressed": [], "version_id": "1.8.0"}