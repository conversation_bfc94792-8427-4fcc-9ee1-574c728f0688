{"data_mtime": 1759503829, "dep_lines": [10, 6, 8, 9, 11, 12, 13, 14, 16, 29, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 5, 5, 5, 5, 5, 25, 5, 20, 20, 30, 30, 30, 30], "dependencies": ["collections.abc", "__future__", "sys", "warnings", "datetime", "decimal", "re", "typing", "typing_extensions", "pydantic_core", "builtins", "pyexpat.model", "pyexpat.errors", "_decimal", "_typeshed", "abc", "pydantic_core._pydantic_core"], "hash": "0dde97753c3b138c1e3d661ab7d5aed86fcb89f68389db386e3869134626ace2", "id": "pydantic_core.core_schema", "ignore_all": true, "interface_hash": "b91c7b8867e2e2f8b8e9b09d1700c81f06659040034fa47d0d5b17ea3fcdee78", "mtime": 1757091872, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic_core\\core_schema.py", "plugin_data": null, "size": 153980, "suppressed": [], "version_id": "1.8.0"}