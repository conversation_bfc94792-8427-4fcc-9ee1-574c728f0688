{"data_mtime": 1759503833, "dep_lines": [21, 22, 23, 24, 25, 8, 10, 11, 12, 13, 14, 20, 1, 3, 4, 5, 6, 7, 28, 30, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 5, 25, 25, 5, 20, 20, 30, 30, 30, 30, 30], "dependencies": ["urllib3.util.connection", "urllib3.util.proxy", "urllib3.util.retry", "urllib3.util.timeout", "urllib3.util.url", "urllib.parse", "urllib3._collections", "urllib3._request_methods", "urllib3.connection", "urllib3.connectionpool", "urllib3.exceptions", "urllib3.response", "__future__", "functools", "logging", "typing", "warnings", "types", "ssl", "typing_extensions", "builtins", "pyexpat.model", "pyexpat.errors", "abc", "enum", "io", "urllib3._base_connection", "urllib3.util"], "hash": "a0ab203f512c008e0e5602bdfbd0f70185d94b91d857cc8a512a20f906c9f13b", "id": "urllib3.poolmanager", "ignore_all": true, "interface_hash": "7b4a2816721a56061ba2fa65f0d86938aed638220124d8c8ada7bb53352eac08", "mtime": 1757091871, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\urllib3\\poolmanager.py", "plugin_data": null, "size": 23866, "suppressed": [], "version_id": "1.8.0"}