{"data_mtime": 1759503830, "dep_lines": [1, 4, 4, 5, 2, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 5, 5, 20, 20, 30], "dependencies": ["collections.abc", "psycopg2.errors", "psycopg2.extensions", "psycopg2._psycopg", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "abc"], "hash": "2002a8383bc9bb2083d47e1ec9a01323ef20688317f40d90e8fe656ef7788d40", "id": "psycopg2", "ignore_all": true, "interface_hash": "98ad9915f30d14ae2c807c8bd6eaf01d50f6ee91c7cd247b2176f8ae9dd0b8ce", "mtime": 1757092195, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\psycopg2-stubs\\__init__.pyi", "plugin_data": null, "size": 1739, "suppressed": [], "version_id": "1.8.0"}