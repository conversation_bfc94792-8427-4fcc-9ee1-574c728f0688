{"data_mtime": 1759504508, "dep_lines": [5, 10, 11, 14, 1, 2, 3, 4, 6, 7, 8, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 10, 10, 5, 5, 5, 5, 5, 20, 20, 30], "dependencies": ["collections.abc", "multiprocessing.connection", "multiprocessing.context", "multiprocessing.shared_memory", "queue", "sys", "threading", "_typeshed", "types", "typing", "typing_extensions", "builtins", "pyexpat.model", "pyexpat.errors", "abc"], "hash": "abc0f930e88a1484119c8fd4b441f6f3244bfa08b07de155c4d297d7558370a6", "id": "multiprocessing.managers", "ignore_all": true, "interface_hash": "673e8aa60f4c58d04d72ea0dd4ee07c5849c69a8c19dea9603a5cc4ad5e47970", "mtime": 1757092231, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\multiprocessing\\managers.pyi", "plugin_data": null, "size": 9056, "suppressed": [], "version_id": "1.8.0"}