"""Tests for database models."""

import pytest
from datetime import datetime, timezone
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from smartanalytics_infrastructure.database.models import (
    Base,
    DimTenant,
    DimAgent,
    DimRingGroup,
    DimAgentEvent,
    metadata
)


class TestDatabaseModels:
    """Test database model definitions."""

    @pytest.fixture
    def engine(self):
        """Create in-memory SQLite engine for testing."""
        engine = create_engine("sqlite:///:memory:")
        Base.metadata.create_all(engine)
        return engine

    @pytest.fixture
    def session(self, engine):
        """Create database session for testing."""
        Session = sessionmaker(bind=engine)
        session = Session()
        yield session
        session.close()

    def test_dim_tenant_creation(self, session):
        """Test DimTenant model creation."""
        tenant = DimTenant(
            tenant_name="TestTenant",
            effective_date=datetime.now(timezone.utc),
            expiry_date=datetime(9999, 12, 31, 23, 59, 59, tzinfo=timezone.utc),
            is_current=True
        )
        session.add(tenant)
        session.commit()
        
        assert tenant.tenant_key is not None
        assert tenant.tenant_name == "TestTenant"
        assert tenant.is_current is True

    def test_dim_agent_creation(self, session):
        """Test DimAgent model creation."""
        # Create tenant first
        tenant = DimTenant(
            tenant_name="TestTenant",
            effective_date=datetime.now(timezone.utc),
            expiry_date=datetime(9999, 12, 31, 23, 59, 59, tzinfo=timezone.utc),
            is_current=True
        )
        session.add(tenant)
        session.flush()

        agent = DimAgent(
            tenant_key=tenant.tenant_key,
            agent_name="TestAgent",
            effective_date=datetime.now(timezone.utc),
            expiry_date=datetime(9999, 12, 31, 23, 59, 59, tzinfo=timezone.utc),
            is_current=True
        )
        session.add(agent)
        session.commit()
        
        assert agent.agent_key is not None
        assert agent.agent_name == "TestAgent"
        assert agent.tenant_key == tenant.tenant_key

    def test_dim_ring_group_creation(self, session):
        """Test DimRingGroup model creation."""
        # Create tenant first
        tenant = DimTenant(
            tenant_name="TestTenant",
            effective_date=datetime.now(timezone.utc),
            expiry_date=datetime(9999, 12, 31, 23, 59, 59, tzinfo=timezone.utc),
            is_current=True
        )
        session.add(tenant)
        session.flush()

        ring_group = DimRingGroup(
            tenant_key=tenant.tenant_key,
            ring_group_name="Support",
            ring_group_uri="sip:<EMAIL>",
            effective_date=datetime.now(timezone.utc),
            expiry_date=datetime(9999, 12, 31, 23, 59, 59, tzinfo=timezone.utc),
            is_current=True
        )
        session.add(ring_group)
        session.commit()
        
        assert ring_group.ring_group_key is not None
        assert ring_group.ring_group_name == "Support"
        assert ring_group.tenant_key == tenant.tenant_key

    def test_dim_agent_event_creation(self, session):
        """Test DimAgentEvent model creation."""
        # Create tenant first
        tenant = DimTenant(
            tenant_name="TestTenant",
            effective_date=datetime.now(timezone.utc),
            expiry_date=datetime(9999, 12, 31, 23, 59, 59, tzinfo=timezone.utc),
            is_current=True
        )
        session.add(tenant)
        session.flush()

        # Create agent
        agent = DimAgent(
            tenant_key=tenant.tenant_key,
            agent_name="TestAgent",
            effective_date=datetime.now(timezone.utc),
            expiry_date=datetime(9999, 12, 31, 23, 59, 59, tzinfo=timezone.utc),
            is_current=True
        )
        session.add(agent)
        session.flush()

        event = DimAgentEvent(
            tenant_key=tenant.tenant_key,
            agent_key=agent.agent_key,
            event_type="Login",
            event_timestamp=datetime.now(timezone.utc),
            event_hash="test_hash_123",
            event_data={"test": "data"},
            sqs_message_id="test-message-id"
        )
        session.add(event)
        session.commit()
        
        assert event.event_key is not None
        assert event.event_type == "Login"
        assert event.tenant_key == tenant.tenant_key
        assert event.agent_key == agent.agent_key

    def test_metadata_schema(self):
        """Test that metadata has correct schema."""
        assert metadata.schema == "memo"

    def test_table_names(self):
        """Test that all expected tables are defined."""
        table_names = {table.name for table in Base.metadata.tables.values()}
        expected_tables = {
            "dim_tenant",
            "dim_agent", 
            "dim_ring_group",
            "dim_agent_event"
        }
        assert expected_tables.issubset(table_names)
