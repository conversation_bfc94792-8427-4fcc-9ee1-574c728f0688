{".class": "MypyFile", "_fullname": "urllib3.poolmanager", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "<subclass of \"str\" and \"HTTPConnectionPool\">": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.str", "urllib3.connectionpool.HTTPConnectionPool"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.poolmanager.<subclass of \"str\" and \"HTTPConnectionPool\">", "name": "<subclass of \"str\" and \"HTTPConnectionPool\">", "type_vars": []}, "deletable_attributes": [], "flags": ["is_intersection"], "fullname": "urllib3.poolmanager.<subclass of \"str\" and \"HTTPConnectionPool\">", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.poolmanager", "mro": ["urllib3.poolmanager.<subclass of \"str\" and \"HTTPConnectionPool\">", "builtins.str", "typing.Sequence", "typing.Collection", "typing.Reversible", "typing.Iterable", "typing.Container", "urllib3.connectionpool.HTTPConnectionPool", "urllib3.connectionpool.ConnectionPool", "urllib3._request_methods.RequestMethods", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BaseHTTPResponse": {".class": "SymbolTableNode", "cross_ref": "urllib3.response.BaseHTTPResponse", "kind": "Gdef", "module_public": false}, "HTTPConnectionPool": {".class": "SymbolTableNode", "cross_ref": "urllib3.connectionpool.HTTPConnectionPool", "kind": "Gdef", "module_public": false}, "HTTPHeaderDict": {".class": "SymbolTableNode", "cross_ref": "urllib3._collections.HTTPHeaderDict", "kind": "Gdef", "module_public": false}, "HTTPSConnectionPool": {".class": "SymbolTableNode", "cross_ref": "urllib3.connectionpool.HTTPSConnectionPool", "kind": "Gdef", "module_public": false}, "LocationValueError": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.LocationValueError", "kind": "Gdef", "module_public": false}, "MaxRetryError": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.MaxRetryError", "kind": "Gdef", "module_public": false}, "PoolKey": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.poolmanager.PoolKey", "name": "Pool<PERSON>ey", "type_vars": []}, "deletable_attributes": [], "flags": ["is_named_tuple"], "fullname": "urllib3.poolmanager.PoolKey", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["key_scheme", "key_host", "key_port", "key_timeout", "key_retries", "key_block", "key_source_address", "key_key_file", "key_key_password", "key_cert_file", "key_cert_reqs", "key_ca_certs", "key_ca_cert_data", "key_ssl_version", "key_ssl_minimum_version", "key_ssl_maximum_version", "key_ca_cert_dir", "key_ssl_context", "key_maxsize", "key_headers", "key__proxy", "key__proxy_headers", "key__proxy_config", "key_socket_options", "key__socks_options", "key_assert_hostname", "key_assert_fingerprint", "key_server_hostname", "key_blocksize"]}}, "module_name": "urllib3.poolmanager", "mro": ["urllib3.poolmanager.PoolKey", "builtins.tuple", "typing.Sequence", "typing.Collection", "typing.Reversible", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.poolmanager.PoolKey._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.timeout.Timeout", "builtins.float", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.retry.Retry", "builtins.bool", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int", "urllib3.util.timeout.Timeout", "builtins.float", "urllib3.util.retry.Retry", {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "builtins.bytes", "ssl.SSLContext", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "urllib3.poolmanager.PoolKey.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "urllib3.poolmanager.PoolKey.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "urllib3.poolmanager.PoolKey.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "key_scheme"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "key_host"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "key_port"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "key_timeout"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "key_retries"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "key_block"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "key_source_address"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "key_key_file"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "key_key_password"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "key_cert_file"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "key_cert_reqs"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "key_ca_certs"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "key_ca_cert_data"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "key_ssl_version"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "key_ssl_minimum_version"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "key_ssl_maximum_version"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "key_ca_cert_dir"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "key_ssl_context"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "key_maxsize"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "key_headers"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "key__proxy"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "key__proxy_headers"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "key__proxy_config"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "key_socket_options"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "key__socks_options"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "key_assert_hostname"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "key_assert_fingerprint"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "key_server_hostname"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "key_blocksize"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["_cls", "key_scheme", "key_host", "key_port", "key_timeout", "key_retries", "key_block", "key_source_address", "key_key_file", "key_key_password", "key_cert_file", "key_cert_reqs", "key_ca_certs", "key_ca_cert_data", "key_ssl_version", "key_ssl_minimum_version", "key_ssl_maximum_version", "key_ca_cert_dir", "key_ssl_context", "key_maxsize", "key_headers", "key__proxy", "key__proxy_headers", "key__proxy_config", "key_socket_options", "key__socks_options", "key_assert_hostname", "key_assert_fingerprint", "key_server_hostname", "key_blocksize"], "dataclass_transform_spec": null, "flags": ["is_static"], "fullname": "urllib3.poolmanager.PoolKey.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["_cls", "key_scheme", "key_host", "key_port", "key_timeout", "key_retries", "key_block", "key_source_address", "key_key_file", "key_key_password", "key_cert_file", "key_cert_reqs", "key_ca_certs", "key_ca_cert_data", "key_ssl_version", "key_ssl_minimum_version", "key_ssl_maximum_version", "key_ca_cert_dir", "key_ssl_context", "key_maxsize", "key_headers", "key__proxy", "key__proxy_headers", "key__proxy_config", "key_socket_options", "key__socks_options", "key_assert_hostname", "key_assert_fingerprint", "key_server_hostname", "key_blocksize"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.poolmanager.PoolKey._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.timeout.Timeout", "builtins.float", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.retry.Retry", "builtins.bool", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int", "urllib3.util.timeout.Timeout", "builtins.float", "urllib3.util.retry.Retry", {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "builtins.bytes", "ssl.SSLContext", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.timeout.Timeout", "builtins.float", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.retry.Retry", "builtins.bool", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of PoolKey", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.poolmanager.PoolKey._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.timeout.Timeout", "builtins.float", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.retry.Retry", "builtins.bool", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int", "urllib3.util.timeout.Timeout", "builtins.float", "urllib3.util.retry.Retry", {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "builtins.bytes", "ssl.SSLContext", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.poolmanager.PoolKey._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.timeout.Timeout", "builtins.float", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.retry.Retry", "builtins.bool", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int", "urllib3.util.timeout.Timeout", "builtins.float", "urllib3.util.retry.Retry", {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "builtins.bytes", "ssl.SSLContext", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.poolmanager.PoolKey._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.poolmanager.PoolKey._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.timeout.Timeout", "builtins.float", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.retry.Retry", "builtins.bool", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int", "urllib3.util.timeout.Timeout", "builtins.float", "urllib3.util.retry.Retry", {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "builtins.bytes", "ssl.SSLContext", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of PoolKey", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.poolmanager.PoolKey._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.timeout.Timeout", "builtins.float", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.retry.Retry", "builtins.bool", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int", "urllib3.util.timeout.Timeout", "builtins.float", "urllib3.util.retry.Retry", {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "builtins.bytes", "ssl.SSLContext", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "urllib3.poolmanager.PoolKey._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "urllib3.poolmanager.PoolKey._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "urllib3.poolmanager.PoolKey._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "urllib3.poolmanager.PoolKey._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.poolmanager.PoolKey._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.timeout.Timeout", "builtins.float", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.retry.Retry", "builtins.bool", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int", "urllib3.util.timeout.Timeout", "builtins.float", "urllib3.util.retry.Retry", {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "builtins.bytes", "ssl.SSLContext", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of PoolKey", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.poolmanager.PoolKey._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.timeout.Timeout", "builtins.float", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.retry.Retry", "builtins.bool", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int", "urllib3.util.timeout.Timeout", "builtins.float", "urllib3.util.retry.Retry", {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "builtins.bytes", "ssl.SSLContext", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.poolmanager.PoolKey._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.timeout.Timeout", "builtins.float", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.retry.Retry", "builtins.bool", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int", "urllib3.util.timeout.Timeout", "builtins.float", "urllib3.util.retry.Retry", {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "builtins.bytes", "ssl.SSLContext", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "urllib3.poolmanager.PoolKey._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.poolmanager.PoolKey._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.timeout.Timeout", "builtins.float", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.retry.Retry", "builtins.bool", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int", "urllib3.util.timeout.Timeout", "builtins.float", "urllib3.util.retry.Retry", {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "builtins.bytes", "ssl.SSLContext", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of PoolKey", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.poolmanager.PoolKey._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.timeout.Timeout", "builtins.float", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.retry.Retry", "builtins.bool", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int", "urllib3.util.timeout.Timeout", "builtins.float", "urllib3.util.retry.Retry", {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "builtins.bytes", "ssl.SSLContext", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.poolmanager.PoolKey._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.timeout.Timeout", "builtins.float", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.retry.Retry", "builtins.bool", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int", "urllib3.util.timeout.Timeout", "builtins.float", "urllib3.util.retry.Retry", {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "builtins.bytes", "ssl.SSLContext", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_self", "key_scheme", "key_host", "key_port", "key_timeout", "key_retries", "key_block", "key_source_address", "key_key_file", "key_key_password", "key_cert_file", "key_cert_reqs", "key_ca_certs", "key_ca_cert_data", "key_ssl_version", "key_ssl_minimum_version", "key_ssl_maximum_version", "key_ca_cert_dir", "key_ssl_context", "key_maxsize", "key_headers", "key__proxy", "key__proxy_headers", "key__proxy_config", "key_socket_options", "key__socks_options", "key_assert_hostname", "key_assert_fingerprint", "key_server_hostname", "key_blocksize"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.poolmanager.PoolKey._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_self", "key_scheme", "key_host", "key_port", "key_timeout", "key_retries", "key_block", "key_source_address", "key_key_file", "key_key_password", "key_cert_file", "key_cert_reqs", "key_ca_certs", "key_ca_cert_data", "key_ssl_version", "key_ssl_minimum_version", "key_ssl_maximum_version", "key_ca_cert_dir", "key_ssl_context", "key_maxsize", "key_headers", "key__proxy", "key__proxy_headers", "key__proxy_config", "key_socket_options", "key__socks_options", "key_assert_hostname", "key_assert_fingerprint", "key_server_hostname", "key_blocksize"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.poolmanager.PoolKey._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.timeout.Timeout", "builtins.float", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.retry.Retry", "builtins.bool", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int", "urllib3.util.timeout.Timeout", "builtins.float", "urllib3.util.retry.Retry", {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "builtins.bytes", "ssl.SSLContext", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.timeout.Timeout", "builtins.float", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.retry.Retry", "builtins.bool", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of Pool<PERSON>ey", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.poolmanager.PoolKey._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.timeout.Timeout", "builtins.float", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.retry.Retry", "builtins.bool", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int", "urllib3.util.timeout.Timeout", "builtins.float", "urllib3.util.retry.Retry", {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "builtins.bytes", "ssl.SSLContext", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.poolmanager.PoolKey._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.timeout.Timeout", "builtins.float", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.retry.Retry", "builtins.bool", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int", "urllib3.util.timeout.Timeout", "builtins.float", "urllib3.util.retry.Retry", {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "builtins.bytes", "ssl.SSLContext", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "urllib3.poolmanager.PoolKey._source", "name": "_source", "type": "builtins.str"}}, "key__proxy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "urllib3.poolmanager.PoolKey.key__proxy", "name": "key__proxy", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "NoneType"}]}}}, "key__proxy-redefinition": {".class": "SymbolTableNode", "cross_ref": "urllib3.poolmanager.PoolKey.key__proxy", "kind": "<PERSON><PERSON><PERSON>"}, "key__proxy_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "urllib3.poolmanager.PoolKey.key__proxy_config", "name": "key__proxy_config", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "NoneType"}]}}}, "key__proxy_config-redefinition": {".class": "SymbolTableNode", "cross_ref": "urllib3.poolmanager.PoolKey.key__proxy_config", "kind": "<PERSON><PERSON><PERSON>"}, "key__proxy_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "urllib3.poolmanager.PoolKey.key__proxy_headers", "name": "key__proxy_headers", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}}}, "key__proxy_headers-redefinition": {".class": "SymbolTableNode", "cross_ref": "urllib3.poolmanager.PoolKey.key__proxy_headers", "kind": "<PERSON><PERSON><PERSON>"}, "key__socks_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "urllib3.poolmanager.PoolKey.key__socks_options", "name": "key__socks_options", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}}}, "key__socks_options-redefinition": {".class": "SymbolTableNode", "cross_ref": "urllib3.poolmanager.PoolKey.key__socks_options", "kind": "<PERSON><PERSON><PERSON>"}, "key_assert_fingerprint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "urllib3.poolmanager.PoolKey.key_assert_fingerprint", "name": "key_assert_fingerprint", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "key_assert_fingerprint-redefinition": {".class": "SymbolTableNode", "cross_ref": "urllib3.poolmanager.PoolKey.key_assert_fingerprint", "kind": "<PERSON><PERSON><PERSON>"}, "key_assert_hostname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "urllib3.poolmanager.PoolKey.key_assert_hostname", "name": "key_assert_hostname", "type": {".class": "UnionType", "items": ["builtins.bool", "builtins.str", {".class": "NoneType"}]}}}, "key_assert_hostname-redefinition": {".class": "SymbolTableNode", "cross_ref": "urllib3.poolmanager.PoolKey.key_assert_hostname", "kind": "<PERSON><PERSON><PERSON>"}, "key_block": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "urllib3.poolmanager.PoolKey.key_block", "name": "key_block", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}}}, "key_block-redefinition": {".class": "SymbolTableNode", "cross_ref": "urllib3.poolmanager.PoolKey.key_block", "kind": "<PERSON><PERSON><PERSON>"}, "key_blocksize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "urllib3.poolmanager.PoolKey.key_blocksize", "name": "key_blocksize", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}}}, "key_blocksize-redefinition": {".class": "SymbolTableNode", "cross_ref": "urllib3.poolmanager.PoolKey.key_blocksize", "kind": "<PERSON><PERSON><PERSON>"}, "key_ca_cert_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "urllib3.poolmanager.PoolKey.key_ca_cert_data", "name": "key_ca_cert_data", "type": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "NoneType"}]}}}, "key_ca_cert_data-redefinition": {".class": "SymbolTableNode", "cross_ref": "urllib3.poolmanager.PoolKey.key_ca_cert_data", "kind": "<PERSON><PERSON><PERSON>"}, "key_ca_cert_dir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "urllib3.poolmanager.PoolKey.key_ca_cert_dir", "name": "key_ca_cert_dir", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "key_ca_cert_dir-redefinition": {".class": "SymbolTableNode", "cross_ref": "urllib3.poolmanager.PoolKey.key_ca_cert_dir", "kind": "<PERSON><PERSON><PERSON>"}, "key_ca_certs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "urllib3.poolmanager.PoolKey.key_ca_certs", "name": "key_ca_certs", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "key_ca_certs-redefinition": {".class": "SymbolTableNode", "cross_ref": "urllib3.poolmanager.PoolKey.key_ca_certs", "kind": "<PERSON><PERSON><PERSON>"}, "key_cert_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "urllib3.poolmanager.PoolKey.key_cert_file", "name": "key_cert_file", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "key_cert_file-redefinition": {".class": "SymbolTableNode", "cross_ref": "urllib3.poolmanager.PoolKey.key_cert_file", "kind": "<PERSON><PERSON><PERSON>"}, "key_cert_reqs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "urllib3.poolmanager.PoolKey.key_cert_reqs", "name": "key_cert_reqs", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "key_cert_reqs-redefinition": {".class": "SymbolTableNode", "cross_ref": "urllib3.poolmanager.PoolKey.key_cert_reqs", "kind": "<PERSON><PERSON><PERSON>"}, "key_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "urllib3.poolmanager.PoolKey.key_headers", "name": "key_headers", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}}}, "key_headers-redefinition": {".class": "SymbolTableNode", "cross_ref": "urllib3.poolmanager.PoolKey.key_headers", "kind": "<PERSON><PERSON><PERSON>"}, "key_host": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "urllib3.poolmanager.PoolKey.key_host", "name": "key_host", "type": "builtins.str"}}, "key_host-redefinition": {".class": "SymbolTableNode", "cross_ref": "urllib3.poolmanager.PoolKey.key_host", "kind": "<PERSON><PERSON><PERSON>"}, "key_key_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "urllib3.poolmanager.PoolKey.key_key_file", "name": "key_key_file", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "key_key_file-redefinition": {".class": "SymbolTableNode", "cross_ref": "urllib3.poolmanager.PoolKey.key_key_file", "kind": "<PERSON><PERSON><PERSON>"}, "key_key_password": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "urllib3.poolmanager.PoolKey.key_key_password", "name": "key_key_password", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "key_key_password-redefinition": {".class": "SymbolTableNode", "cross_ref": "urllib3.poolmanager.PoolKey.key_key_password", "kind": "<PERSON><PERSON><PERSON>"}, "key_maxsize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "urllib3.poolmanager.PoolKey.key_maxsize", "name": "key_maxsize", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}}}, "key_maxsize-redefinition": {".class": "SymbolTableNode", "cross_ref": "urllib3.poolmanager.PoolKey.key_maxsize", "kind": "<PERSON><PERSON><PERSON>"}, "key_port": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "urllib3.poolmanager.PoolKey.key_port", "name": "key_port", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}}}, "key_port-redefinition": {".class": "SymbolTableNode", "cross_ref": "urllib3.poolmanager.PoolKey.key_port", "kind": "<PERSON><PERSON><PERSON>"}, "key_retries": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "urllib3.poolmanager.PoolKey.key_retries", "name": "key_retries", "type": {".class": "UnionType", "items": ["urllib3.util.retry.Retry", "builtins.bool", "builtins.int", {".class": "NoneType"}]}}}, "key_retries-redefinition": {".class": "SymbolTableNode", "cross_ref": "urllib3.poolmanager.PoolKey.key_retries", "kind": "<PERSON><PERSON><PERSON>"}, "key_scheme": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "urllib3.poolmanager.PoolKey.key_scheme", "name": "key_scheme", "type": "builtins.str"}}, "key_scheme-redefinition": {".class": "SymbolTableNode", "cross_ref": "urllib3.poolmanager.PoolKey.key_scheme", "kind": "<PERSON><PERSON><PERSON>"}, "key_server_hostname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "urllib3.poolmanager.PoolKey.key_server_hostname", "name": "key_server_hostname", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "key_server_hostname-redefinition": {".class": "SymbolTableNode", "cross_ref": "urllib3.poolmanager.PoolKey.key_server_hostname", "kind": "<PERSON><PERSON><PERSON>"}, "key_socket_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "urllib3.poolmanager.PoolKey.key_socket_options", "name": "key_socket_options", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}, {".class": "NoneType"}]}}}, "key_socket_options-redefinition": {".class": "SymbolTableNode", "cross_ref": "urllib3.poolmanager.PoolKey.key_socket_options", "kind": "<PERSON><PERSON><PERSON>"}, "key_source_address": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "urllib3.poolmanager.PoolKey.key_source_address", "name": "key_source_address", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "NoneType"}]}}}, "key_source_address-redefinition": {".class": "SymbolTableNode", "cross_ref": "urllib3.poolmanager.PoolKey.key_source_address", "kind": "<PERSON><PERSON><PERSON>"}, "key_ssl_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "urllib3.poolmanager.PoolKey.key_ssl_context", "name": "key_ssl_context", "type": {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}]}}}, "key_ssl_context-redefinition": {".class": "SymbolTableNode", "cross_ref": "urllib3.poolmanager.PoolKey.key_ssl_context", "kind": "<PERSON><PERSON><PERSON>"}, "key_ssl_maximum_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "urllib3.poolmanager.PoolKey.key_ssl_maximum_version", "name": "key_ssl_maximum_version", "type": {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}}}, "key_ssl_maximum_version-redefinition": {".class": "SymbolTableNode", "cross_ref": "urllib3.poolmanager.PoolKey.key_ssl_maximum_version", "kind": "<PERSON><PERSON><PERSON>"}, "key_ssl_minimum_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "urllib3.poolmanager.PoolKey.key_ssl_minimum_version", "name": "key_ssl_minimum_version", "type": {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}}}, "key_ssl_minimum_version-redefinition": {".class": "SymbolTableNode", "cross_ref": "urllib3.poolmanager.PoolKey.key_ssl_minimum_version", "kind": "<PERSON><PERSON><PERSON>"}, "key_ssl_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "urllib3.poolmanager.PoolKey.key_ssl_version", "name": "key_ssl_version", "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}]}}}, "key_ssl_version-redefinition": {".class": "SymbolTableNode", "cross_ref": "urllib3.poolmanager.PoolKey.key_ssl_version", "kind": "<PERSON><PERSON><PERSON>"}, "key_timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "urllib3.poolmanager.PoolKey.key_timeout", "name": "key_timeout", "type": {".class": "UnionType", "items": ["urllib3.util.timeout.Timeout", "builtins.float", "builtins.int", {".class": "NoneType"}]}}}, "key_timeout-redefinition": {".class": "SymbolTableNode", "cross_ref": "urllib3.poolmanager.PoolKey.key_timeout", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.poolmanager.PoolKey.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.timeout.Timeout", "builtins.float", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.retry.Retry", "builtins.bool", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "partial_fallback": "urllib3.poolmanager.PoolKey"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.timeout.Timeout", "builtins.float", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.retry.Retry", "builtins.bool", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int", "urllib3.util.timeout.Timeout", "builtins.float", "urllib3.util.retry.Retry", {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "builtins.bytes", "ssl.SSLContext", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}]}], "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "PoolManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib3._request_methods.RequestMethods"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.poolmanager.PoolManager", "name": "PoolManager", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "urllib3.poolmanager.PoolManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.poolmanager", "mro": ["urllib3.poolmanager.PoolManager", "urllib3._request_methods.RequestMethods", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.poolmanager.PoolManager.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.poolmanager.PoolManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.poolmanager.PoolManager", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__enter__ of PoolManager", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.poolmanager.PoolManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.poolmanager.PoolManager", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.poolmanager.PoolManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.poolmanager.PoolManager", "values": [], "variance": 0}]}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.poolmanager.PoolManager.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["urllib3.poolmanager.PoolManager", {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__exit__ of PoolManager", "ret_type": {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "num_pools", "headers", "connection_pool_kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.poolmanager.PoolManager.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "num_pools", "headers", "connection_pool_kw"], "arg_types": ["urllib3.poolmanager.PoolManager", "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PoolManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_merge_pool_kwargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "override"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.poolmanager.PoolManager._merge_pool_kwargs", "name": "_merge_pool_kwargs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "override"], "arg_types": ["urllib3.poolmanager.PoolManager", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_merge_pool_kwargs of PoolManager", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_new_pool": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "scheme", "host", "port", "request_context"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.poolmanager.PoolManager._new_pool", "name": "_new_pool", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "scheme", "host", "port", "request_context"], "arg_types": ["urllib3.poolmanager.PoolManager", "builtins.str", "builtins.str", "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_new_pool of PoolManager", "ret_type": "urllib3.connectionpool.HTTPConnectionPool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_proxy_requires_url_absolute_form": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parsed_url"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.poolmanager.PoolManager._proxy_requires_url_absolute_form", "name": "_proxy_requires_url_absolute_form", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "parsed_url"], "arg_types": ["urllib3.poolmanager.PoolManager", {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_proxy_requires_url_absolute_form of PoolManager", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.poolmanager.PoolManager.clear", "name": "clear", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.poolmanager.PoolManager"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear of PoolManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "connection_from_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request_context"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.poolmanager.PoolManager.connection_from_context", "name": "connection_from_context", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request_context"], "arg_types": ["urllib3.poolmanager.PoolManager", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connection_from_context of PoolManager", "ret_type": "urllib3.connectionpool.HTTPConnectionPool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "connection_from_host": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "host", "port", "scheme", "pool_kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.poolmanager.PoolManager.connection_from_host", "name": "connection_from_host", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "host", "port", "scheme", "pool_kwargs"], "arg_types": ["urllib3.poolmanager.PoolManager", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connection_from_host of PoolManager", "ret_type": "urllib3.connectionpool.HTTPConnectionPool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "connection_from_pool_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "pool_key", "request_context"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.poolmanager.PoolManager.connection_from_pool_key", "name": "connection_from_pool_key", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "pool_key", "request_context"], "arg_types": ["urllib3.poolmanager.PoolManager", {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.poolmanager.PoolKey"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connection_from_pool_key of PoolManager", "ret_type": "urllib3.connectionpool.HTTPConnectionPool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "connection_from_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "url", "pool_kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.poolmanager.PoolManager.connection_from_url", "name": "connection_from_url", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "url", "pool_kwargs"], "arg_types": ["urllib3.poolmanager.PoolManager", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connection_from_url of PoolManager", "ret_type": "urllib3.connectionpool.HTTPConnectionPool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "connection_pool_kw": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.poolmanager.PoolManager.connection_pool_kw", "name": "connection_pool_kw", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}}}, "key_fn_by_scheme": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.poolmanager.PoolManager.key_fn_by_scheme", "name": "key_fn_by_scheme", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.timeout.Timeout", "builtins.float", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.retry.Retry", "builtins.bool", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "partial_fallback": "urllib3.poolmanager.PoolKey"}], "type_ref": "functools.partial"}], "type_ref": "builtins.dict"}}}, "pool_classes_by_scheme": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.poolmanager.PoolManager.pool_classes_by_scheme", "name": "pool_classes_by_scheme", "type": {".class": "Instance", "args": ["builtins.str", "builtins.type"], "type_ref": "builtins.dict"}}}, "pools": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "urllib3.poolmanager.PoolManager.pools", "name": "pools", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.poolmanager.PoolKey"}, "urllib3.connectionpool.HTTPConnectionPool"], "type_ref": "urllib3._collections.RecentlyUsedContainer"}}}, "proxy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "urllib3.poolmanager.PoolManager.proxy", "name": "proxy", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "NoneType"}]}}}, "proxy_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "urllib3.poolmanager.PoolManager.proxy_config", "name": "proxy_config", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "NoneType"}]}}}, "urlopen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "method", "url", "redirect", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.poolmanager.PoolManager.urlopen", "name": "urlopen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "method", "url", "redirect", "kw"], "arg_types": ["urllib3.poolmanager.PoolManager", "builtins.str", "builtins.str", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "urlopen of PoolManager", "ret_type": "urllib3.response.BaseHTTPResponse", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.poolmanager.PoolManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.poolmanager.PoolManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ProxyConfig": {".class": "SymbolTableNode", "cross_ref": "urllib3._base_connection.ProxyConfig", "kind": "Gdef", "module_public": false}, "ProxyManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib3.poolmanager.PoolManager"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.poolmanager.ProxyManager", "name": "ProxyManager", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "urllib3.poolmanager.ProxyManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.poolmanager", "mro": ["urllib3.poolmanager.ProxyManager", "urllib3.poolmanager.PoolManager", "urllib3._request_methods.RequestMethods", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "proxy_url", "num_pools", "headers", "proxy_headers", "proxy_ssl_context", "use_forwarding_for_https", "proxy_assert_hostname", "proxy_assert_fingerprint", "connection_pool_kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.poolmanager.ProxyManager.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "proxy_url", "num_pools", "headers", "proxy_headers", "proxy_ssl_context", "use_forwarding_for_https", "proxy_assert_hostname", "proxy_assert_fingerprint", "connection_pool_kw"], "arg_types": ["urllib3.poolmanager.ProxyManager", "builtins.str", "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}]}, "builtins.bool", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ProxyManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_set_proxy_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "url", "headers"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.poolmanager.ProxyManager._set_proxy_headers", "name": "_set_proxy_headers", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "url", "headers"], "arg_types": ["urllib3.poolmanager.ProxyManager", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_proxy_headers of ProxyManager", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "typing.Mapping"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "connection_from_host": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "host", "port", "scheme", "pool_kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.poolmanager.ProxyManager.connection_from_host", "name": "connection_from_host", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "host", "port", "scheme", "pool_kwargs"], "arg_types": ["urllib3.poolmanager.ProxyManager", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connection_from_host of ProxyManager", "ret_type": "urllib3.connectionpool.HTTPConnectionPool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "proxy_headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.poolmanager.ProxyManager.proxy_headers", "name": "proxy_headers", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "typing.Mapping"}}}, "proxy_ssl_context": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.poolmanager.ProxyManager.proxy_ssl_context", "name": "proxy_ssl_context", "type": {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}]}}}, "urlopen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "method", "url", "redirect", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.poolmanager.ProxyManager.urlopen", "name": "urlopen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "method", "url", "redirect", "kw"], "arg_types": ["urllib3.poolmanager.ProxyManager", "builtins.str", "builtins.str", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "urlopen of ProxyManager", "ret_type": "urllib3.response.BaseHTTPResponse", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.poolmanager.ProxyManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.poolmanager.ProxyManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ProxySchemeUnknown": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.ProxySchemeUnknown", "kind": "Gdef", "module_public": false}, "RecentlyUsedContainer": {".class": "SymbolTableNode", "cross_ref": "urllib3._collections.RecentlyUsedContainer", "kind": "Gdef", "module_public": false}, "RequestMethods": {".class": "SymbolTableNode", "cross_ref": "urllib3._request_methods.RequestMethods", "kind": "Gdef", "module_public": false}, "Retry": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.retry.Retry", "kind": "Gdef", "module_public": false}, "SSL_KEYWORDS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "urllib3.poolmanager.SSL_KEYWORDS", "name": "SSL_KEYWORDS", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing.Self", "kind": "Gdef", "module_public": false}, "Timeout": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.timeout.Timeout", "kind": "Gdef", "module_public": false}, "TracebackType": {".class": "SymbolTableNode", "cross_ref": "types.TracebackType", "kind": "Gdef", "module_public": false}, "URLSchemeUnknown": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.URLSchemeUnknown", "kind": "Gdef", "module_public": false}, "Url": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.url.Url", "kind": "Gdef", "module_public": false}, "_DEFAULT_BLOCKSIZE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "urllib3.poolmanager._DEFAULT_BLOCKSIZE", "name": "_DEFAULT_BLOCKSIZE", "type": "builtins.int"}}, "_TYPE_SOCKET_OPTIONS": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "urllib3.poolmanager.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.poolmanager.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.poolmanager.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.poolmanager.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.poolmanager.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.poolmanager.__package__", "name": "__package__", "type": "builtins.str"}}, "_default_key_normalizer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["key_class", "request_context"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.poolmanager._default_key_normalizer", "name": "_default_key_normalizer", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["key_class", "request_context"], "arg_types": [{".class": "TypeType", "item": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.timeout.Timeout", "builtins.float", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.retry.Retry", "builtins.bool", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "partial_fallback": "urllib3.poolmanager.PoolKey"}}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_default_key_normalizer", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.poolmanager.PoolKey"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "connection_requires_http_tunnel": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.proxy.connection_requires_http_tunnel", "kind": "Gdef", "module_public": false}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef", "module_public": false}, "key_fn_by_scheme": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "urllib3.poolmanager.key_fn_by_scheme", "name": "key_fn_by_scheme", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.timeout.Timeout", "builtins.float", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["urllib3.util.retry.Retry", "builtins.bool", "builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.TLSVersion", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.frozenset"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "partial_fallback": "urllib3.poolmanager.PoolKey"}], "type_ref": "functools.partial"}], "type_ref": "builtins.dict"}}}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "urllib3.poolmanager.log", "name": "log", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef", "module_public": false}, "parse_url": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.url.parse_url", "kind": "Gdef", "module_public": false}, "pool_classes_by_scheme": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "urllib3.poolmanager.pool_classes_by_scheme", "name": "pool_classes_by_scheme", "type": {".class": "Instance", "args": ["builtins.str", "builtins.type"], "type_ref": "builtins.dict"}}}, "port_by_scheme": {".class": "SymbolTableNode", "cross_ref": "urllib3.connection.port_by_scheme", "kind": "Gdef", "module_public": false}, "proxy_from_url": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["url", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "urllib3.poolmanager.proxy_from_url", "name": "proxy_from_url", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["url", "kw"], "arg_types": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "proxy_from_url", "ret_type": "urllib3.poolmanager.ProxyManager", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "ssl": {".class": "SymbolTableNode", "cross_ref": "ssl", "kind": "Gdef", "module_public": false}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_public": false}, "urljoin": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urljoin", "kind": "Gdef", "module_public": false}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\urllib3\\poolmanager.py"}