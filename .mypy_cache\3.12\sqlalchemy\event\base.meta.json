{"data_mtime": 1759504546, "dep_lines": [36, 40, 43, 42, 18, 20, 34, 42, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 10, 20, 5, 20, 20, 30, 30, 30, 30], "dependencies": ["sqlalchemy.event.attr", "sqlalchemy.event.registry", "sqlalchemy.util.typing", "sqlalchemy.util", "__future__", "typing", "weakref", "sqlalchemy", "builtins", "pyexpat.model", "pyexpat.errors", "_typeshed", "abc", "collections", "sqlalchemy.util.langhelpers"], "hash": "f804a25793eeb7d9d3b567abb6e60dfb395c8205f2f5c4c78ac1eabb1b30d713", "id": "sqlalchemy.event.base", "ignore_all": true, "interface_hash": "d5c20f2371187a036345f217e3357f0ec7dc17104d39fb28259f13fdaca52187", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\event\\base.py", "plugin_data": null, "size": 15726, "suppressed": [], "version_id": "1.8.0"}