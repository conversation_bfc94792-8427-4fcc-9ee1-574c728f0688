{"data_mtime": 1759503828, "dep_lines": [4, 5, 6, 1, 2, 9, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 25, 5, 20, 20, 30, 30, 30], "dependencies": ["pydantic.v1.fields", "pydantic.v1.main", "pydantic.v1.typing", "sys", "typing", "typing_extensions", "builtins", "pyexpat.model", "pyexpat.errors", "_typeshed", "abc", "pydantic.v1.utils"], "hash": "ba4d8d000c6a88d10b2a388787287129a20e87c1759585bf2f3ad6dd7ee86417", "id": "pydantic.v1.annotated_types", "ignore_all": true, "interface_hash": "57989a5d83f70e2c6ae7e6b87e6ce2e3c592268e9c2b88f30ef9485b05a1fde9", "mtime": 1757091875, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\pydantic\\v1\\annotated_types.py", "plugin_data": null, "size": 3157, "suppressed": [], "version_id": "1.8.0"}