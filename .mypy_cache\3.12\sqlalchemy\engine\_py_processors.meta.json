{"data_mtime": 1759503829, "dep_lines": [16, 18, 22, 23, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 20, 20, 30, 30, 30], "dependencies": ["__future__", "datetime", "decimal", "typing", "builtins", "pyexpat.model", "pyexpat.errors", "_decimal", "abc", "re"], "hash": "0d78108550ff2af4914c41b97e7e38be823a5f7fea51cec2b952ca4d2f123cb6", "id": "sqlalchemy.engine._py_processors", "ignore_all": true, "interface_hash": "b51c6d54e3152bf7be8d9e395526f25a4786c1a9be7d064bfc8d519e886e703e", "mtime": 1759106564, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "c:\\Users\\<USER>\\Dev\\smartanalytics-processors\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\_py_processors.py", "plugin_data": null, "size": 3880, "suppressed": [], "version_id": "1.8.0"}